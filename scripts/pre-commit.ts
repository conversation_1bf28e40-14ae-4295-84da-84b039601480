import { execa } from 'execa';
// import { EOL } from 'node:os';
import chalk from "chalk";

const error = chalk.bold.red;
const info = chalk.green;

async function start() {
    const { stdout:gitStdout } = await execa({stdout: ['pipe']})`git diff HEAD --name-only`;
    const lines = (gitStdout||'').split('\n').filter(Boolean);
    console.log('git diff:', lines.length);
    if (!lines.length) return;
    await execa({stderr: 'ignore', stdout: 'pipe'})`pnpm lint`;
}

start().catch(async (err) => {
    if (err.stdout) {
        console.log(error(err.stdout));
        await execa({stdout: ['pipe']})`git restore --staged .`;
        process.exit(1);
    } else {
        console.log('--', err);
    }
});