import chalk from "chalk";
import path from 'node:path';
import fsp from 'node:fs/promises';

type PagesConfig = { includes: string[], scanDir:string };

export async function getMpOption(
    command: "build" | "serve",
    isPreview: boolean | undefined
) {
    const opt = {};
    try {
        // 只有开发阶段读取
        if (command === "serve" && !isPreview) {
            const existDevPages = await existPagesDev();
            if (!existDevPages) {
                throw new Error('error: 请先创建.pages.dev.ts文件');
            }
            const pagesConfig = (await import(`./.pages.dev`)).default;
            await validPages(pagesConfig as any as PagesConfig);
            return pagesConfig;
        }
    } catch (err: any) {
        console.error(chalk.bold.red(err.message || err));
        process.exit(1);
    }
    return opt;
}

async function existPagesDev() {
    let exist = true;
    const filePath = path.join(__dirname, './.pages.dev.ts');
    await fsp.access(filePath).catch(err => {
        console.log(err);
        exist = false;
    });
    return exist;
}
async function validPages(pagesConfig: PagesConfig) {
    if (!pagesConfig) return false;
    if (Object.keys(pagesConfig).length === 0) return true;
    const pages = pagesConfig.includes;
    if (pages && Array.isArray(pages)) {
        const scanDir = pagesConfig.scanDir || path.join(__dirname, '../src/pages');
        const dirs = await fsp.readdir(scanDir);
        return pages.every(page => {
            const hasIn = dirs.includes(page);
            if (!hasIn) {
                throw new Error(`找不到目录：src/pages/${page}`);
            }
            return hasIn;
        });
    }
    return true;
}