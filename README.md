# 相关地址

首页地址：<http://ea.equali.cn/>

项目为多页面项目，所有页面都在`src/pages`目录下，目前有以下7个页面(模块)，在本地开发时可以根据需要来构建自己所开发的页面。具体往下看。

### 各模块地址

项目：<http://ea.equali.cn/projects/index.html>

EA：<http://ea.equali.cn/ea/index.html>

需求：<http://ea.equali.cn/requirements/index.html>

系统管理：<http://ea.equali.cn/sys_mgm/index.html>

合规：<http://ea.equali.cn/compliance/index.html>

测试：<http://ea.equali.cn/test/index.html>

工作流：<http://ea.equali.cn/workflows/index.html>

# 基本规范

1. 单文件组件、逻辑控制等文件名大写开头，驼峰风格，如：`UsersList.vue`、`UsersListCtrl.ts`
2. 模块的入口文件以小写开头，多单词之间用横线连接，如：`index.ts`, `xx-index.ts`
3. 文件夹以小写开头，多单词直接用下划线连接，如：`sys_mgm`，`ea_analysis`
4. 单个ts文件不要超过**300**行，否则请拆分。
5. 每个Store，请先定义属性，然后再方法。不要随手定义属性和方法，请调整顺序。

# 开始

1. node版本 `>=23.11.0`，pnpm版本 `>=10.8.1`

2. 请全局安装`pnpm`用于包管理器

3. 复制scripts目录下的`.pages.ts`并改名为`.pages.dev.ts`放置于`scripts`目录下

4. 根据需要在`.pages.dev.ts`中包含你本地需要构建的页面。不包含就是每次构建所有页面。

下面是一个`.pages.dev.ts`的示例

```ts
export default {
    includes: ['es', 'projects']
};
```

# 合作

1. 先`fork`该项目：<http://git.equali.cn:8099/btipd/frontend/btipd_fe为公库>
2. 从自己的仓库中拉取项目最新代码所在分支，并添加公库的`gitlab`地址到本地项目。
3. 在本地修改代码，并提交到自己的仓库中。
4. 将自己的仓库代码提交merge request到公库给指定的人
5. 代码被合并即完成。

注：前提是项目开始开发（修改代码前），公库必须创建一个当前版本的分支。
该分支为最近一次需要上线的功能。

具体操作示例：
假设现有公仓为：btit_fe，fork公仓到自己空间中为私仓：btit-fe-fork

1. 克隆私仓，执行一下命令：

   ```shell
   git clone http://git.equali.cn:8099/yinweixiong/btit-fe-fork.git
   ```

2. 执行：`cd btit-fe-fork`命令进入项目目录。
3. 添加公仓地址到私仓中(public)，执行以下命令：

   ```shell
   git remote add public http://git.equali.cn:8099/yinweixiong/btit_fe.git
   ```

4. 拉取公仓分支到本地：执行`git fetch public`
5. 找到当前版本分支，比如：FEATURE_20250323，从该分支创建本地同名分支

   ```shell
   git checkout -b FEATURE_20250323 public/FEATURE_20250323
   ```

6. 接下来在本地FEATURE_20250323分支进行开发并commit
7. 将代码push到自己的私仓，执行以下命令：

   ```shell
   git stash
   git pull public FEATURE_20250323
   git stash pop
   git push origin HEAD
   ```

   后面就可以去gitlab网站创建merge request了。
