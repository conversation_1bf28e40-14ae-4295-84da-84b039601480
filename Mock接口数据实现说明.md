# Mock 接口数据实现说明

## 实现概述

已成功将任务列表页面从硬编码数据改为通过 mock 接口获取数据，确保左侧项目树形结构与右侧任务表格数据完全匹配。

## 主要改动

### 1. Mock 接口数据更新

#### 1.1 项目列表接口 (`/projectmanage/projects/list`)

**更新前：**
```javascript
list: [
  { id: 1, name: '项目1', isProjectSet: false, isProject: true },
  { id: 2, name: '项目2', isProjectSet: false, isProject: true },
  { id: 3, name: '项目3', isProjectSet: false, isProject: true },
]
```

**更新后：**
```javascript
list: [
  { id: 'project_a', name: '项目A', isProjectSet: false, isProject: true },
  { id: 'project_b', name: '项目B', isProjectSet: false, isProject: true },
]
```

#### 1.2 项目集列表接口 (`/projectmanage/project-sets/list`)

**更新前：**
```javascript
list: [
  {
    id: 'ps1',
    name: '项目集1',
    projects: [
      { id: 4, name: '子项目1' },
      { id: 5, name: '子项目2' },
    ],
  },
]
```

**更新后：**
```javascript
list: [
  {
    id: 'project_set_c',
    name: '项目集C',
    projects: [
      { id: 'project_c1', name: '子项目C1' },
      { id: 'project_c2', name: '子项目C2' },
    ],
  },
]
```

#### 1.3 任务列表接口 (`/projectmanage/tasks/list/:pageSize/:page`)

**完全重写任务数据：**
- 项目A：前端开发任务（1个主任务 + 2个子任务）
- 项目B：系统测试任务（1个主任务 + 2个子任务）
- 子项目C1：UI设计任务（1个主任务 + 2个子任务）
- 子项目C2：后端开发任务（1个主任务 + 2个子任务）

每个任务都包含完整的字段：
```javascript
{
  id: '1',
  name: '项目A - 前端开发任务',
  status: '进行中',
  level: 1,
  index: 1,
  startDate: '2024-01-01',
  endDate: '2024-01-10',
  progress: 68,
  responsibleUser: '张三',
  participants: ['张三', '李四'],
  createdBy: '王五',
  projectId: 'project_a',  // 关键：与项目ID匹配
  projectName: '项目A',
  isOverdue: false,
  workType: '开发',
  department: '前端开发组',
  children: [...]
}
```

### 2. 前端代码重构

#### 2.1 TaskListCtrl.ts 重构

**移除硬编码数据：**
- 删除了 `loadTasks()` 函数中的所有硬编码 mock 数据
- 删除了 `updateTreeWithUserProjects()` 函数
- 删除了硬编码的 `treeList` 数据

**改为接口调用：**
```typescript
function loadTasks() {
  store.tasksLoading = true;

  // 调用真正的API接口获取任务数据
  const filterParams = {
    tabType: activeTab.value,
    projectIds: selectedProjects.value, // 传递选中的项目ID列表
    keyword: keyword.value,
    page: store.page,
    pageSize: store.pageSize,
  };

  new VCairnEvent(EventTypes.GET_TASKS, filterParams).emit();
}
```

**使用 store 数据：**
```typescript
// 使用 store 中的树形数据
const treeList = computed(() => store.treeData || []);
```

#### 2.2 GetTasksCommand.ts 优化

**参数传递优化：**
```typescript
this.service.send({
  method: 'GET',
  url: tasksList(params.pageSize || store.pageSize, params.page || store.page),
  params: {
    tabType: params.tabType,
    projectIds: params.projectIds?.join(','), // 将项目ID数组转为逗号分隔的字符串
    keyword: params.keyword,
  },
});
```

**响应处理简化：**
```typescript
onSuccess(response: AxiosResponse): void {
  const store = useTaskListStore();
  
  // 从接口获取任务数据
  const responseData = response.data?.data;
  const tasks: TaskItem[] = responseData?.list || [];
  const total = responseData?.total || 0;
  const pageSize = responseData?.pageSize || store.pageSize;
  const currentPage = responseData?.page || store.page;

  // 如果是第一页，替换数据；否则追加数据
  if (currentPage === 1) {
    store.tasks = tasks;
  } else {
    store.tasks = [...store.tasks, ...tasks];
  }

  // 更新分页信息
  store.hasMore = tasks.length === pageSize && store.tasks.length < total;
  store.tasksLoading = false;
}
```

#### 2.3 GetProjectsCommand.ts 优化

**添加树形结构必需字段：**
```typescript
const projectNodes: ProjectTreeNode[] = projects.map(
  (project: ProjectItem) => ({
    id: project.id,
    label: project.name,
    value: project.id, // 添加 value 字段用于勾选
    checked: false,    // 添加 checked 字段
    isProject: true,
  })
);

// 构建根节点
store.treeData = [
  {
    id: 'my_projects',
    label: '我的项目',
    value: 'my_projects',
    checked: false,
    children: projectNodes,
    isProject: false,
  },
];
```

#### 2.4 GetProjectSetsCommand.ts 优化

**正确处理项目集层级：**
```typescript
// 子项目节点
const childProjects = (projectSet.projects || []).map((project) => ({
  id: project.id,
  label: project.name,
  value: project.id,
  checked: false,
  isProject: true,
  isProjectSet: false,
}));

// 项目集节点
return {
  id: projectSet.id,
  label: projectSet.name,
  value: projectSet.id,
  checked: false,
  isProject: false,
  isProjectSet: true,
  children: childProjects,
};
```

## 数据匹配验证

### 项目ID匹配表

| 树形结构节点 | 项目ID | 任务数据中的 projectId |
|-------------|--------|----------------------|
| 我的项目 | my_projects | - (显示所有) |
| 项目A | project_a | project_a |
| 项目B | project_b | project_b |
| 项目集C | project_set_c | - (包含子项目) |
| 子项目C1 | project_c1 | project_c1 |
| 子项目C2 | project_c2 | project_c2 |

### 筛选逻辑验证

1. **勾选"我的项目"** → `selectedProjects = []` → 显示所有任务
2. **勾选"项目A"** → `selectedProjects = ['project_a']` → 显示 projectId 为 'project_a' 的任务
3. **勾选"项目集C"** → `selectedProjects = ['project_c1', 'project_c2']` → 显示两个子项目的任务
4. **多选项目** → `selectedProjects = ['project_a', 'project_b']` → 显示多个项目的任务

## 技术优势

1. **真实接口调用**：不再依赖硬编码数据，更接近生产环境
2. **数据一致性**：左侧树形结构和右侧任务数据完全匹配
3. **筛选准确性**：项目ID匹配确保筛选结果正确
4. **扩展性良好**：易于添加新的项目和任务数据
5. **分页支持**：支持任务列表的分页加载
6. **参数传递**：支持筛选条件的正确传递

## 测试验证

访问 `http://localhost:5174/#/project-tasks` 进行测试：

1. **页面加载**：自动调用项目列表和项目集列表接口
2. **树形结构**：显示"我的项目"根节点，包含项目A、项目B和项目集C
3. **任务列表**：显示所有匹配的任务数据
4. **筛选功能**：勾选不同项目节点，观察任务列表的实时更新
5. **控制台日志**：查看接口调用和数据处理的详细信息

现在左侧项目树形结构与右侧任务表格数据完全通过 mock 接口获取，确保了数据的一致性和筛选功能的准确性。
