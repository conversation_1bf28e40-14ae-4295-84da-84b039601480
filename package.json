{"name": "btit_fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "vue-tsc -b", "pre-commit": "node scripts/pre-commit.ts", "prepare": "husky"}, "dependencies": {"@antv/x6": "^2.18.1", "@bpmn-io/properties-panel": "^3.26.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "bpmn-js": "^18.4.0", "bpmn-js-properties-panel": "^5.35.0", "dhtmlx-gantt": "^9.0.5", "element-plus": "^2.9.5", "mitt": "^3.0.1", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "sortablejs": "^1.15.6", "v-cairn": "^1.1.4", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.13.8", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-legacy": "^7.1.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "chalk": "^5.4.1", "execa": "^9.5.2", "husky": "^9.1.7", "sass": "^1.85.1", "tsx": "^4.20.3", "typescript": "~5.7.2", "unplugin-auto-import": "^19.1.1", "unplugin-element-plus": "^0.9.1", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.0", "vite-plugin-mock": "^3.0.2", "vite-plugin-mpb": "^0.1.11", "vue-tsc": "^2.2.4"}, "pnpm": {"peerDependencyRules": {"allowedVersions": {"vite": "6.2.0"}}}, "engines": {"node": ">=23.11.0", "pnpm": ">=10.8.1", "npm": ">=10.9.2"}}