/**
 * 任务
 * @source POST /task/todo/page/{pageSize}/{curPage} [REQUEST]
 * @source POST /task/page/{pageSize}/{curPage} [REQUEST]
 */
export interface TaskRequest {
  tenantId?: number;
  /** 任务id */
  taskId?: string;
  /** 流程实例id */
  processInstanceId?: string;
  /** 执行id */
  executionId?: string;
  /** 业务key */
  businessKey?: string;
  /** 流程名称 */
  processName?: string;
  /** 任务名称 */
  taskName?: string;
  /** 发起人 */
  starter?: string;
  /** 审批人 */
  assignee?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 创建时间 */
  createTime?: string;
  /** 来源key */
  formKey?: string;
  /** 备注 */
  comment?: string;
}

/**
 * 技术能力
 * @source POST /projectmanage/technical-skills/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/technical-skills/list [REQUEST]
 */
export interface TechnicalSkillsQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 技术能力ID */
  skillId?: number;
  /** 父技术能力ID */
  parentId?: number;
  /** 技术能力编码 */
  skillCode?: string;
  /** 技术能力名称 */
  skillName?: string;
  /** 技术能力描述 */
  skillDesc?: string;
  /** 技术能力状态 */
  status?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 技术能力
 * @source POST /projectmanage/technical-skills/edit [REQUEST]
 * @source POST /projectmanage/technical-skills/add [REQUEST]
 */
export interface TechnicalSkillsEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 技术能力ID */
  skillId?: number;
  /** 父技术能力ID */
  parentId?: number;
  /** 技术能力编码 */
  skillCode?: string;
  /** 技术能力名称 */
  skillName?: string;
  /** 技术能力描述 */
  skillDesc?: string;
  /** 技术能力状态 */
  status?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 技术能力
 * @source POST /projectmanage/technical-skills/delete [REQUEST]
 */
export interface TechnicalSkillsDeleteRequest {
  tenantId?: number;
  /** 技术能力ID */
  skillId?: number;
}

/**
 * 计划模板信息
 * @source POST /projectmanage/team-template-info/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/team-template-info/list [REQUEST]
 */
export interface TeamTemplateInfoQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 计划模板ID */
  infoId?: number;
  /** 计划模板编号 */
  templateCode?: string;
  /** 计划模板名称 */
  templateName?: string;
  /** 项目类型 */
  projectType?: string;
  /** 项目级别 */
  projectLevel?: string;
  /** 项目分类 */
  projectCategory?: string;
  /** 品牌 */
  brand?: string;
  /** 产品线 */
  productLine?: string;
  /** 车型类别 */
  vehicleCategory?: string;
  /** 技术平台 */
  technicalPlatform?: string;
  /** 能源形式 */
  energyForm?: string;
  /** 销售区域 */
  salesArea?: string;
  /** 计划模板说明 */
  templateDescription?: string;
  /** 负责人ID */
  owner?: number;
  /** 来源项目编号 */
  srcProjectCode?: string;
  /** 来源项目名称 */
  srcProjectName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 计划模板信息
 * @source POST /projectmanage/team-template-info/edit [REQUEST]
 * @source POST /projectmanage/team-template-info/add [REQUEST]
 */
export interface TeamTemplateInfoEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 计划模板ID */
  infoId?: number;
  /** 计划模板编号 */
  templateCode?: string;
  /** 计划模板名称 */
  templateName?: string;
  /** 项目类型 */
  projectType?: string;
  /** 项目级别 */
  projectLevel?: string;
  /** 项目分类 */
  projectCategory?: string;
  /** 品牌 */
  brand?: string;
  /** 产品线 */
  productLine?: string;
  /** 车型类别 */
  vehicleCategory?: string;
  /** 技术平台 */
  technicalPlatform?: string;
  /** 能源形式 */
  energyForm?: string;
  /** 销售区域 */
  salesArea?: string;
  /** 计划模板说明 */
  templateDescription?: string;
  /** 负责人ID */
  owner?: number;
  /** 来源项目编号 */
  srcProjectCode?: string;
  /** 来源项目名称 */
  srcProjectName?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 计划模板信息
 * @source POST /projectmanage/team-template-info/delete [REQUEST]
 */
export interface TeamTemplateInfoDeleteRequest {
  tenantId?: number;
  /** 计划模板ID */
  infoId?: number;
}

/**
 * 任务变更记录
 * @source POST /projectmanage/task-change-log/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/task-change-log/list [REQUEST]
 */
export interface TaskChangeLogQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  taskChangeLogId?: number;
  /** 项目任务风险ID,风险回答 */
  taskId?: number;
  /** 是否为风险回答(1是,0 否) */
  projTaskFlag?: number;
  /** 变更原因 */
  changeReason?: string;
  /** 变更内容 */
  changeContent?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 任务变更记录
 * @source POST /projectmanage/task-change-log/edit [REQUEST]
 * @source POST /projectmanage/task-change-log/add [REQUEST]
 */
export interface TaskChangeLogEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  taskChangeLogId?: number;
  /** 项目任务风险ID,风险回答 */
  taskId?: number;
  /** 是否为风险回答(1是,0 否) */
  projTaskFlag?: number;
  /** 变更原因 */
  changeReason?: string;
  /** 变更内容 */
  changeContent?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 任务变更记录
 * @source POST /projectmanage/task-change-log/delete [REQUEST]
 */
export interface TaskChangeLogDeleteRequest {
  tenantId?: number;
  /** 自增主键 */
  taskChangeLogId?: number;
}

/**
 * 资源池
 * @source POST /projectmanage/resource-pool/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/resource-pool/list [REQUEST]
 */
export interface ResourcePoolQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 资源ID */
  resourceId?: number;
  /** 部门ID */
  departmentId?: number;
  /** 系统ID */
  systemId?: number;
  /** 技术能力ID */
  skillId?: number;
  /** 人员ID */
  personnelId?: number;
  /** 岗位ID */
  positionId?: number;
  /** 设备ID */
  equipmentId?: number;
  /** 资源类型(1 人力资源,2 设备资源) */
  resourceType?: number;
  /** 标准工时 */
  standardHours?: number;
  /** 饱和度 */
  saturation?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 资源池
 * @source POST /projectmanage/resource-pool/edit [REQUEST]
 * @source POST /projectmanage/resource-pool/add [REQUEST]
 */
export interface ResourcePoolEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 资源ID */
  resourceId?: number;
  /** 部门ID */
  departmentId?: number;
  /** 系统ID */
  systemId?: number;
  /** 技术能力ID */
  skillId?: number;
  /** 人员ID */
  personnelId?: number;
  /** 岗位ID */
  positionId?: number;
  /** 设备ID */
  equipmentId?: number;
  /** 资源类型(1 人力资源,2 设备资源) */
  resourceType?: number;
  /** 标准工时 */
  standardHours?: number;
  /** 饱和度 */
  saturation?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 资源池
 * @source POST /projectmanage/resource-pool/delete [REQUEST]
 */
export interface ResourcePoolDeleteRequest {
  tenantId?: number;
  /** 资源ID */
  resourceId?: number;
}

/**
 * 资源调拨
 * @source POST /projectmanage/resource-allocation/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/resource-allocation/list [REQUEST]
 */
export interface ResourceAllocationQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 调拨ID */
  allocationId?: number;
  /** 资源ID */
  resourceId?: number;
  /** 调拨工时 */
  allocatedHours?: number;
  /** 调拨日期 */
  allocationDate?: string;
  /** 调拨状态(1带确认,2 已确认,3 取消) */
  status?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 资源调拨
 * @source POST /projectmanage/resource-allocation/edit [REQUEST]
 * @source POST /projectmanage/resource-allocation/add [REQUEST]
 */
export interface ResourceAllocationEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 调拨ID */
  allocationId?: number;
  /** 资源ID */
  resourceId?: number;
  /** 调拨工时 */
  allocatedHours?: number;
  /** 调拨日期 */
  allocationDate?: string;
  /** 调拨状态(1带确认,2 已确认,3 取消) */
  status?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 资源调拨
 * @source POST /projectmanage/resource-allocation/delete [REQUEST]
 */
export interface ResourceAllocationDeleteRequest {
  tenantId?: number;
  /** 调拨ID */
  allocationId?: number;
}

/**
 * 项目
 * @source POST /projectmanage/project/tree [REQUEST]
 * @source POST /projectmanage/project/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project/list [REQUEST]
 */
export interface ProjectQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目ID，唯一标识 */
  projId?: number;
  /** 项目组合ID */
  portfolioId?: number;
  /** 项目集ID */
  programId?: number;
  /** 项目编码 */
  projCode?: string;
  /** 父项目ID */
  parentProjId?: number;
  /** 项目名称 */
  projName?: string;
  /** 项目类型 */
  projType?: number;
  /** 项目级别 */
  projLevel?: number;
  /** 项目分类 */
  projCategory?: number;
  /** 计划开始时间 */
  planStartDate?: string;
  /** 计划完成时间 */
  planFinishDate?: string;
  /** 实际完成时间 */
  actualFinishDate?: string;
  /** 实际开始时间 */
  actualStartDate?: string;
  /** 超期天数 */
  overdueDays?: number;
  /** 实际进度百分比 */
  actualProgress?: number;
  /** 计划进度百分比 */
  planProgress?: number;
  /** 项目状态 */
  status?: number;
  /** 责任人 */
  responsible?: number;
  /** 所属部门 */
  deptId?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新人 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 账户ID */
  acctId?: number;
  /** 原始项目ID */
  origProjId?: number;
  /** 来源项目ID */
  sourceProjId?: number;
  /** 关注人用户ID */
  follower?: number;
  /** 排序条件 */
  sorts?: SortRequest[];
  /** 责任人姓名 */
  responsibleName?: string;
  /** 责任人姓名 */
  sortFields?: string;
}

/**
 * 项目删除标签入参对象
 * @source POST /projectmanage/project/remove/tag [REQUEST]
 */
export interface ProjectTagDeleteRequest {
  tenantId?: number;
  /** 项目ID */
  projId: number;
  /** 项目标签ID */
  projTagId: number;
}

/**
 * 项目关注入参对象
 * @source POST /projectmanage/project/follow [REQUEST]
 */
export interface ProjectFollowEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键 */
  followId?: number;
  /** 项目ID */
  projId?: number;
  /** 用户ID */
  userId?: number;
  /** 状态(1关注 0 取消关注) */
  status?: number;
}

/**
 * 项目
 * @source POST /projectmanage/project/edit [REQUEST]
 */
export interface ProjectEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目ID，唯一标识 */
  projId?: number;
  /** 项目组合ID */
  portfolioId?: number;
  /** 项目集ID */
  programId?: number;
  /** 项目编码 */
  projCode?: string;
  /** 父项目ID */
  parentProjId?: number;
  /** 项目名称 */
  projName?: string;
  /** 项目类型 */
  projType?: number;
  /** 项目级别 */
  projLevel?: number;
  /** 项目分类 */
  projCategory?: number;
  /** 计划开始时间 */
  planStartDate?: string;
  /** 计划完成时间 */
  planFinishDate?: string;
  /** 实际完成时间 */
  actualFinishDate?: string;
  /** 实际开始时间 */
  actualStartDate?: string;
  /** 超期天数 */
  overdueDays?: number;
  /** 实际进度百分比 */
  actualProgress?: number;
  /** 计划进度百分比 */
  planProgress?: number;
  /** 项目状态 */
  status?: number;
  /** 责任人 */
  responsible?: number;
  /** 所属部门 */
  deptId?: number;
  /** 备注 */
  remark?: string;
  /** 账户ID */
  acctId?: number;
  /** 原始项目ID */
  origProjId?: number;
  /** 来源项目ID */
  sourceProjId?: number;
  /** 项目成员列表 */
  members?: ProjectObsEditRequest[];
}

/**
 * 项目角色
 * @source POST /projectmanage/project-obs/edit [REQUEST]
 * @source POST /projectmanage/project-obs/add [REQUEST]
 */
export interface ProjectObsEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** ID */
  projObsId?: number;
  /** 父OBSID */
  parentProjObsId?: number;
  /** 项目ID */
  projectId?: Record<string, any>;
  /** OBS名称 */
  obsName?: string;
  /** OBS描述 */
  obsDesc?: string;
  /** 展示区域 */
  displayArea?: number;
  /** 纵向暂时层级 */
  verticalDisplayHierarchy?: number;
  /** 项目角色ID(关联系统角色表ID) */
  projRoleId?: number;
  /** 项目角色类型 */
  projRoleType?: number;
  /** 责任人部门 */
  responsiableDept?: number;
  /** 责任人 */
  responsiable?: number;
  /** 渲染方向(1 横向 2 纵向) */
  displayDirection?: number;
  /** 工作流状态(来源项目角色审批状态数据字典) */
  workflowStatus?: Record<string, any>;
  /** 项目obs版本 */
  projObsVersion?: string;
  /** 是否当前版本标识(1 是,0 否) */
  currentVersion?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目
 * @source POST /projectmanage/project/delete [REQUEST]
 */
export interface ProjectDeleteRequest {
  tenantId?: number;
  /** 项目ID，唯一标识 */
  projId?: number;
}

/**
 * 项目新增入参
 * @source POST /projectmanage/project/add [REQUEST]
 */
export interface ProjectAddRequest {
  tenantId?: number;
  /** 项目名称 */
  projName?: string;
  /** 项目类型 */
  projType?: number;
  /** 项目级别 */
  projLevel?: number;
  /** 项目分类 */
  projCategory?: number;
  /** 所属部门 */
  deptId?: number;
  /** 计划开始时间 */
  planStartDate?: string;
  /** 计划完成时间 */
  planFinishDate?: string;
  /** 责任人 */
  responsible?: number;
  /** 备注 */
  remark?: string;
  /** 项目成员列表 */
  members?: StakeholderAddRequest[];
}

/**
 * 项目标签新增入参对象
 * @source POST /projectmanage/project/add/tag [REQUEST]
 */
export interface ConfigProjectTagAddRequest {
  tenantId?: number;
  /** 项目ID */
  projId: number;
  /** 标签列表 */
  tags: ConfigProjectTagEditRequest[];
}

/**
 * 项目标签
 * @source POST /projectmanage/config-project-tag/edit [REQUEST]
 * @source POST /projectmanage/config-project-tag/add [REQUEST]
 */
export interface ConfigProjectTagEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目标签ID */
  projTagId?: number;
  /** 父项目标签ID或者所属标签组ID */
  parentProjTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag: number;
  /** 项目标签标题 */
  projTagName: string;
  /** 项目标签描述 */
  projTagDesc?: string;
  /** 项目标签颜色 */
  projTagColor: string;
  /** 项目标签状态 (1 启用,0 禁用) */
  projTagStatus: number;
  /** 排序码 */
  sortNum?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目任务删除标签入参对象
 * @source POST /projectmanage/project-task/remove/tag [REQUEST]
 */
export interface ProjectTaskTagDeleteRequest {
  tenantId?: number;
  /** 项目任务ID */
  projTaskId: number;
  /** 项目任务标签ID */
  projTaskTagId: number;
}

/**
 * 拒绝项目任务入参
 * @source POST /projectmanage/project-task/reject [REQUEST]
 */
export interface ProjectTaskRejectRequest {
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 拒绝意见 */
  rejectOpinion?: string;
  /** 过程文档列表 */
  processDocs?: ProjectTaskProcessDocAddRequest[];
}

/**
 * 项目任务
 * @source POST /projectmanage/project-task/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-task/list [REQUEST]
 * @source POST /projectmanage/project-task/export [REQUEST]
 */
export interface ProjectTaskQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务父任务ID */
  parentProjTaskId?: number;
  /** 所属项目ID */
  projId?: number;
  /** 阶段ID */
  phaseProjTaskId?: number;
  /** 项目任务名称 */
  projTaskName?: string;
  /** 项目任务状态(1 未开始,2 进行中,3 已关闭) */
  projTaskStatus?: number;
  /** 项目任务责任人 */
  projTaskResponsible?: number;
  /** 项目任务责任人项目角色 */
  projTaskResponsibleProjRole?: string;
  /** 项目任务责任部门 */
  projTaskResponsibleDept?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业,级联关系待定) */
  taskType?: number;
  /** 任务作业类型(级联关系待定) */
  taskActivityType?: number;
  /** 专业 */
  major?: string;
  /** 系统 */
  systemId?: number;
  /** 项目任务说明 */
  projTaskDesc?: string;
  /** 项目任务验收标准 */
  projTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 项目任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是非阶段(1是,0否) */
  phaseFlag?: number;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 关注人用户ID */
  follower?: number;
  /** 排序条件 */
  sorts?: SortRequest[];
  /** 责任人姓名 */
  responsibleName?: string;
  /** 查询类型(1全部;2我负责的;3我参与的;4我指派的;5我代办的) */
  queryFlag?: number;
  /** 导出字段 */
  exportColumns?: string;
}

/**
 * 项目任务关注入参对象
 * @source POST /projectmanage/project-task/follow [REQUEST]
 */
export interface ProjectTaskFollowEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键 */
  followId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 用户ID */
  userId?: number;
  /** 状态(1关注 0 取消关注) */
  status?: number;
}

/**
 * 任务标签
 * @source POST /projectmanage/config-task-tag/edit [REQUEST]
 * @source POST /projectmanage/config-task-tag/add [REQUEST]
 */
export interface ConfigIndeTaskTagEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 任务标签ID */
  taskTagId: number;
  /** 父标签ID或者所属标签组ID */
  parentTaskTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag: number;
  /** 任务标签标题 */
  taskTagName: string;
  /** 任务标签描述 */
  taskTagDesc?: string;
  /** 任务标签颜色 */
  taskTagColor: string;
  /** 任务标签状态 (1 启用,0 禁用) */
  taskTagStatus: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目任务编辑入参
 * @source POST /projectmanage/project-task/edit [REQUEST]
 * @source POST /projectmanage/project-task/add [REQUEST]
 */
export interface ProjectTaskEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务父任务ID */
  parentProjTaskId?: number;
  /** 所属项目ID */
  projId?: number;
  /** 阶段ID */
  phaseProjTaskId?: number;
  /** 项目任务名称 */
  projTaskName?: string;
  /** 项目任务状态(1 未开始,2 进行中,3 已关闭) */
  projTaskStatus?: number;
  /** 项目任务责任人 */
  projTaskResponsible?: number;
  /** 项目任务责任人项目角色 */
  projTaskResponsibleProjRole?: string;
  /** 项目任务责任部门 */
  projTaskResponsibleDept?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业,级联关系待定) */
  taskType?: number;
  /** 任务作业类型(级联关系待定) */
  taskActivityType?: number;
  /** 专业 */
  major?: string;
  /** 系统 */
  systemId?: number;
  /** 项目任务说明 */
  projTaskDesc?: string;
  /** 项目任务验收标准 */
  projTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 项目任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是非阶段(1是,0否) */
  phaseFlag?: number;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 备注 */
  remark?: string;
  /** 任务标签列表 */
  tags?: ConfigIndeTaskTagEditRequest[];
  /** 参与人列表 */
  participants?: StakeholderAddRequest[];
  /** 附件列表 */
  attaments?: SysAttachmentEditRequest[];
  /** 前置任务列表 */
  predTasks?: ProjectTaskPredEditRequest[];
}

/**
 * 项目任务删除对象
 * @source POST /projectmanage/project-task/delete [REQUEST]
 */
export interface ProjectTaskDeleteRequest {
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
}

/**
 * 项目任务变更请求对象
 * @source POST /projectmanage/project-task/change [REQUEST]
 */
export interface ProjectTaskChangeRequest {
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务父任务ID */
  parentProjTaskId?: number;
  /** 所属项目ID */
  projId?: number;
  /** 阶段ID */
  phaseProjTaskId?: number;
  /** 项目任务名称 */
  projTaskName?: string;
  /** 项目任务责任人 */
  projTaskResponsible?: number;
  /** 项目任务责任人项目角色 */
  projTaskResponsibleProjRole?: string;
  /** 项目任务责任部门 */
  projTaskResponsibleDept?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业,级联关系待定) */
  taskType?: number;
  /** 任务作业类型(级联关系待定) */
  taskActivityType?: number;
  /** 专业 */
  major?: string;
  /** 系统 */
  systemId?: number;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 项目任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是非阶段(1是,0否) */
  phaseFlag?: number;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 任务标签列表 */
  tags?: ConfigIndeTaskTagEditRequest[];
  /** 参与人列表 */
  participants?: ProjectTaskParticipantChangeRequest[];
  /** 变更原因 */
  changeReason?: string;
}

/**
 * 项目任务标签新增入参对象
 * @source POST /projectmanage/project-task/add/tag [REQUEST]
 */
export interface ConfigProjectTaskTagAddRequest {
  tenantId?: number;
  /** 项目任务ID */
  projTaskId: number;
  /** 标签列表 */
  tags: ConfigProjectTaskTagEditRequest[];
}

/**
 * 项目任务标签
 * @source POST /projectmanage/config-project-task-tag/edit [REQUEST]
 * @source POST /projectmanage/config-project-task-tag/add [REQUEST]
 */
export interface ConfigProjectTaskTagEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务标签ID */
  projTaskTagId?: number;
  /** 父项目任务标签ID或者所属标签组ID */
  parentProjTaskTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag: number;
  /** 项目标签标题 */
  projTagName: string;
  /** 项目标签描述 */
  projTagDesc?: string;
  /** 项目标签颜色 */
  projTagColor: string;
  /** 项目标签状态 (1 启用,0 禁用) */
  projTagStatus: number;
  /** 排序码 */
  sortNum?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 接受项目任务入参
 * @source POST /projectmanage/project-task/accept [REQUEST]
 */
export interface ProjectTaskAcceptRequest {
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 接受意见 */
  acceptOpinion?: string;
  /** 过程文档列表 */
  processDocs?: ProjectTaskProcessDocAddRequest[];
}

/**
 * 项目任务风险表
 * @source POST /projectmanage/project-task-risk/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-task-risk/list [REQUEST]
 */
export interface ProjectTaskRiskQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务风险ID */
  projTaskRiskId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务风险名称 */
  projTaskRiskName?: string;
  /** 项目任务风险状态(1未开始, 2进行中,3已完成,4已关闭) */
  projTaskRiskStatus?: number;
  /** 风险分类 */
  projTaskRiskType?: number;
  /** 风险级别 (1高2中3低) */
  projTaskRiskLevel?: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 完成日期 */
  completionDate?: string;
  /** 项目任务风险负责人ID */
  projTaskResponsible?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目任务风险表
 * @source POST /projectmanage/project-task-risk/edit [REQUEST]
 */
export interface ProjectTaskRiskEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务风险ID */
  projTaskRiskId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务风险名称 */
  projTaskRiskName?: string;
  /** 项目任务风险状态(1未开始, 2进行中,3已完成,4已关闭) */
  projTaskRiskStatus?: number;
  /** 风险分类 */
  projTaskRiskType?: number;
  /** 风险级别 (1高2中3低) */
  projTaskRiskLevel?: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 完成日期 */
  completionDate?: string;
  /** 项目任务风险负责人ID */
  projTaskResponsible?: number;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  riskDocs?: ProjectTaskRiskDocAddRequest[];
}

/**
 * 项目任务风险表
 * @source POST /projectmanage/project-task-risk/delete [REQUEST]
 * @source POST /projectmanage/project-task-risk/convert/issue [REQUEST]
 * @source POST /projectmanage/project-task-risk/close [REQUEST]
 */
export interface ProjectTaskRiskDeleteRequest {
  tenantId?: number;
  /** 项目任务风险ID */
  projTaskRiskId?: number;
}

/**
 * 项目任务风险新增入参
 * @source POST /projectmanage/project-task-risk/add [REQUEST]
 */
export interface ProjectTaskRiskAddRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务风险名称 */
  projTaskRiskName?: string;
  /** 项目任务风险状态(1未开始, 2进行中,3已完成,4已关闭) */
  projTaskRiskStatus?: number;
  /** 风险分类 */
  projTaskRiskType?: number;
  /** 风险级别 (1高2中3低) */
  projTaskRiskLevel?: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 完成日期 */
  completionDate?: string;
  /** 项目任务风险负责人ID */
  projTaskResponsible?: number;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  riskDocs?: ProjectTaskRiskDocAddRequest[];
}

/**
 * 项目任务汇报
 * @source POST /projectmanage/project-task-report/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-task-report/list [REQUEST]
 */
export interface ProjectTaskReportQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务汇报ID */
  projTaskReportId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务进度，范围0.00到100.00 */
  progress?: number;
  /** 汇报内容 */
  projTaskDesc?: string;
  /** 汇报时间 */
  reportTime?: string;
  /** 项目任务汇报状态(2 已接受,0 待澄清,1 已汇报) */
  projTaskReportStatus?: number;
  /** 项目任务汇报工作流定义ID */
  workflowId?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目任务汇报
 * @source POST /projectmanage/project-task-report/edit [REQUEST]
 */
export interface ProjectTaskReportEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务汇报ID */
  projTaskReportId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务进度，范围0.00到100.00 */
  progress?: number;
  /** 数据类型(report,record) */
  dataType?: string;
  /** 汇报内容 */
  projTaskDesc?: string;
  /** 汇报时间 */
  reportTime?: string;
  /** 项目任务汇报状态(2 已接受,0 待澄清,1 已汇报) */
  projTaskReportStatus?: number;
  /** 项目任务汇报工作流定义ID */
  workflowId?: string;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  reportDocs?: ProjectTaskReportDocEditRequest[];
}

/**
 * 项目任务汇报
 * @source POST /projectmanage/project-task-report/delete [REQUEST]
 */
export interface ProjectTaskReportDeleteRequest {
  tenantId?: number;
  /** 项目任务汇报ID */
  projTaskReportId?: number;
}

/**
 * 项目任务汇报新增入参对象
 * @source POST /projectmanage/project-task-report/add [REQUEST]
 */
export interface ProjectTaskReportAddRequest {
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 汇报内容 */
  projTaskDesc?: string;
  /** 项目任务进度，范围0.00到100.00 */
  progress?: number;
  /** 实际开始时间 */
  actualStartTime?: string;
  /** 文档列表 */
  reportDocs?: ProjectTaskReportDocEditRequest[];
}

/**
 * 项目任务过程文档表
 * @source POST /projectmanage/project-task-process-doc/delete [REQUEST]
 */
export interface ProjectTaskProcessDocDeleteRequest {
  tenantId?: number;
  /** 自增主键 */
  projTaskProcessDocId?: number;
}

/**
 * 项目任务过程文档表
 * @source POST /projectmanage/project-task-process-doc/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-task-process-doc/list [REQUEST]
 */
export interface ProjectTaskProcessDocQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  projTaskProcessDocId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentProjTaskProcessDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 项目任务问题
 * @source POST /projectmanage/project-task-issue/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-task-issue/list [REQUEST]
 */
export interface ProjectTaskIssueQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务问题ID */
  projTaskIssueId?: number;
  /** 项目任务问题名称 */
  projTaskIssueName?: string;
  /** 项目任务问题状态(1未开始, 2进行中,3已完成,4已关闭) */
  projTaskIssueStatus?: number;
  /** 问题分类 */
  projTaskIssueType?: number;
  /** 问题级别 (1高2中3低) */
  projTaskIssueLevel?: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 项目任务问题负责人ID */
  projTaskResponsible?: number;
  /** 完成日期 */
  completionDate?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目任务问题
 * @source POST /projectmanage/project-task-issue/edit [REQUEST]
 */
export interface ProjectTaskIssueEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务问题ID */
  projTaskIssueId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务问题名称 */
  projTaskIssueName?: string;
  /** 项目任务问题状态(1未开始, 2进行中,3已完成,4已关闭) */
  projTaskIssueStatus?: number;
  /** 问题分类 */
  projTaskIssueType?: number;
  /** 问题级别 (1高2中3低) */
  projTaskIssueLevel?: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 项目任务问题负责人ID */
  projTaskResponsible?: number;
  /** 完成日期 */
  completionDate?: string;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  issueDocs?: ProjectTaskIssueDocAddRequest[];
}

/**
 * 项目任务问题
 * @source POST /projectmanage/project-task-issue/delete [REQUEST]
 * @source POST /projectmanage/project-task-issue/convert/risk [REQUEST]
 * @source POST /projectmanage/project-task-issue/close [REQUEST]
 */
export interface ProjectTaskIssueDeleteRequest {
  tenantId?: number;
  /** 项目任务问题ID */
  projTaskIssueId?: number;
}

/**
 * 项目任务问题新增请求参数
 * @source POST /projectmanage/project-task-issue/add [REQUEST]
 */
export interface ProjectTaskIssueAddRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务问题名称 */
  projTaskIssueName?: string;
  /** 项目任务问题状态(1未开始, 2进行中,3已完成,4已关闭) */
  projTaskIssueStatus?: number;
  /** 问题分类 */
  projTaskIssueType?: number;
  /** 问题级别 (1高2中3低) */
  projTaskIssueLevel?: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 项目任务问题负责人ID */
  projTaskResponsible?: number;
  /** 完成日期 */
  completionDate?: string;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  issueDocs?: ProjectTaskIssueDocAddRequest[];
}

/**
 * 独立任务回答
 * @source POST /projectmanage/project-task-issue-answer/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-task-issue-answer/list [REQUEST]
 */
export interface ProjectTaskIssueAnswerQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务问题回答ID */
  projTaskIssueAnswerId?: number;
  /** 项目任务问题ID */
  projTaskIssueId?: number;
  /** 项目任务问题回答时间 */
  projTaskIssueAnswerTime?: string;
  /** 项目任务问题回答内容 */
  projTaskIssueAnswer?: string;
  /** 项目任务问题讨论 */
  projTaskIssueDisscution?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateTime?: string;
  /** 修改时间 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 独立任务回答
 * @source POST /projectmanage/project-task-issue-answer/edit [REQUEST]
 */
export interface ProjectTaskIssueAnswerEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务问题回答ID */
  projTaskIssueAnswerId?: number;
  /** 项目任务问题ID */
  projTaskIssueId?: number;
  /** 项目任务问题回答时间 */
  projTaskIssueAnswerTime?: string;
  /** 项目任务问题回答内容 */
  projTaskIssueAnswer?: string;
  /** 项目任务问题讨论 */
  projTaskIssueDisscution?: string;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  issueAnswerDocs?: ProjectTaskIssueDocEditRequest[];
}

/**
 * 独立任务回答
 * @source POST /projectmanage/project-task-issue-answer/delete [REQUEST]
 */
export interface ProjectTaskIssueAnswerDeleteRequest {
  tenantId?: number;
  /** 项目任务问题回答ID */
  projTaskIssueAnswerId?: number;
}

/**
 * 项目任务问题回答入参对象
 * @source POST /projectmanage/project-task-issue-answer/add [REQUEST]
 */
export interface ProjectTaskIssueAnswerAddRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务问题ID */
  projTaskIssueId?: number;
  /** 项目任务问题回答时间 */
  projTaskIssueAnswerTime?: string;
  /** 项目任务问题回答内容 */
  projTaskIssueAnswer?: string;
  /** 项目任务问题讨论 */
  projTaskIssueDisscution?: string;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  issueDocs?: ProjectTaskIssueDocEditRequest[];
}

/**
 * 项目任务交付文档表
 * @source POST /projectmanage/project-task-delivery-doc/delete [REQUEST]
 */
export interface ProjectTaskDeliveryDocDeleteRequest {
  tenantId?: number;
  /** 自增主键 */
  projTaskDeliveryDocId?: number;
}

/**
 * 项目任务交付文档表
 * @source POST /projectmanage/project-task-delivery-doc/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-task-delivery-doc/list [REQUEST]
 */
export interface ProjectTaskDeliveryDocQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  projTaskDeliveryDocId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentProjTaskDeliveryDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识(关联附件表) */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 系统
 * @source POST /projectmanage/project-system/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-system/list [REQUEST]
 */
export interface ProjectSystemQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 系统ID,唯一标识 */
  systemId?: number;
  /** 父系统ID */
  parentSystemId?: number;
  /** 系统名 */
  systemName?: string;
  /** 排序码 */
  sortNum?: number;
  /** 系统描述 */
  systemDesc?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 最后修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
}

/**
 * 系统
 * @source POST /projectmanage/project-system/edit [REQUEST]
 * @source POST /projectmanage/project-system/add [REQUEST]
 */
export interface ProjectSystemEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 系统ID,唯一标识 */
  systemId?: number;
  /** 父系统ID */
  parentSystemId?: number;
  /** 系统名 */
  systemName?: string;
  /** 排序码 */
  sortNum?: number;
  /** 系统描述 */
  systemDesc?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 系统
 * @source POST /projectmanage/project-system/delete [REQUEST]
 */
export interface ProjectSystemDeleteRequest {
  tenantId?: number;
  /** 系统ID,唯一标识 */
  systemId?: number;
}

/**
 * 项目销售信息
 * @source POST /projectmanage/project-properties-sales/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-properties-sales/list [REQUEST]
 */
export interface ProjectPropertiesSalesQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 销售信息唯一标识符 */
  salesId?: number;
  /** 项目ID，关联项目基础信息表 */
  projId?: number;
  /** 市场区域(待定) */
  marketArea?: string;
  /** 市场类型(来源市场类型数据字典值,待定) */
  marketType?: string;
  /** 细分市场 */
  subMarket?: string;
  /** 目标用户群 */
  targetCustomerGroup?: string;
  /** 商品名 */
  productName?: string;
  /** 主销价格 */
  mainSalesPrice?: number;
  /** 价格区间 */
  priceRange?: string;
  /** 销售主体 */
  salesEntity?: string;
  /** 出口方式 */
  exportMethod?: string;
  /** 周期内销售目标 */
  salesTargetPeriod?: number;
  /** 分年度销售目标 */
  salesTargetAnnual?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateTime?: string;
  /** 修改时间 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目销售信息
 * @source POST /projectmanage/project-properties-sales/edit [REQUEST]
 * @source POST /projectmanage/project-properties-sales/add [REQUEST]
 */
export interface ProjectPropertiesSalesEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 销售信息唯一标识符 */
  salesId?: number;
  /** 项目ID，关联项目基础信息表 */
  projId?: number;
  /** 市场区域(待定) */
  marketArea?: string;
  /** 市场类型(来源市场类型数据字典值,待定) */
  marketType?: string;
  /** 细分市场 */
  subMarket?: string;
  /** 目标用户群 */
  targetCustomerGroup?: string;
  /** 商品名 */
  productName?: string;
  /** 主销价格 */
  mainSalesPrice?: number;
  /** 价格区间 */
  priceRange?: string;
  /** 销售主体 */
  salesEntity?: string;
  /** 出口方式 */
  exportMethod?: string;
  /** 周期内销售目标 */
  salesTargetPeriod?: number;
  /** 分年度销售目标 */
  salesTargetAnnual?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 项目销售信息
 * @source POST /projectmanage/project-properties-sales/delete [REQUEST]
 */
export interface ProjectPropertiesSalesDeleteRequest {
  tenantId?: number;
  /** 销售信息唯一标识符 */
  salesId?: number;
}

/**
 * 项目生产地信息
 * @source POST /projectmanage/project-properties-production/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-properties-production/list [REQUEST]
 */
export interface ProjectPropertiesProductionQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 生产地信息唯一标识符 */
  productionId?: number;
  /** 项目ID，关联项目基础信息表 */
  projId?: number;
  /** 生成信息描述(区域,基地,厂区 关系出到关系表 方案 待定) */
  productionDesc?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateTime?: string;
  /** 修改时间 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目生产地信息
 * @source POST /projectmanage/project-properties-production/edit [REQUEST]
 * @source POST /projectmanage/project-properties-production/add [REQUEST]
 */
export interface ProjectPropertiesProductionEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 生产地信息唯一标识符 */
  productionId?: number;
  /** 项目ID，关联项目基础信息表 */
  projId?: number;
  /** 生成信息描述(区域,基地,厂区 关系出到关系表 方案 待定) */
  productionDesc?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 项目生产地信息
 * @source POST /projectmanage/project-properties-production/delete [REQUEST]
 */
export interface ProjectPropertiesProductionDeleteRequest {
  tenantId?: number;
  /** 生产地信息唯一标识符 */
  productionId?: number;
}

/**
 * 项目基础信息
 * @source POST /projectmanage/project-properties-info/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-properties-info/list [REQUEST]
 */
export interface ProjectPropertiesInfoQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目唯一标识符 */
  projId?: number;
  /** 项目名称 */
  projName?: string;
  /** 项目类型(数据字典取值) */
  projType?: number;
  /** 项目级别(数据字典取值) */
  projLevel?: string;
  /** 项目分类(数据字典取值) */
  projCategory?: string;
  /** 主项目ID，用于关联子项目 */
  mainProjId?: number;
  /** 开发代码 */
  developmentCode?: string;
  /** 品牌ID (有级联关系) */
  brandId?: number;
  /** 产品线ID(有级联关系) */
  productLineId?: number;
  /** 所属事业部 */
  businessUnit?: string;
  /** 是否公共投资 */
  publicInvestmentFlag?: string;
  /** 是否新项目 */
  newProjFlag?: string;
  /** 创建人 */
  createUser?: number;
  /** 记录创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 记录最后更新时间 */
  updateTime?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remarks?: string;
}

/**
 * 项目基础信息
 * @source POST /projectmanage/project-properties-info/edit [REQUEST]
 * @source POST /projectmanage/project-properties-info/add [REQUEST]
 */
export interface ProjectPropertiesInfoEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目唯一标识符 */
  projId?: number;
  /** 项目名称 */
  projName?: string;
  /** 项目类型(数据字典取值) */
  projType?: number;
  /** 项目级别(数据字典取值) */
  projLevel?: string;
  /** 项目分类(数据字典取值) */
  projCategory?: string;
  /** 主项目ID，用于关联子项目 */
  mainProjId?: number;
  /** 开发代码 */
  developmentCode?: string;
  /** 品牌ID (有级联关系) */
  brandId?: number;
  /** 产品线ID(有级联关系) */
  productLineId?: number;
  /** 所属事业部 */
  businessUnit?: string;
  /** 是否公共投资 */
  publicInvestmentFlag?: string;
  /** 是否新项目 */
  newProjFlag?: string;
  /** 备注 */
  remarks?: string;
}

/**
 * 项目基础信息
 * @source POST /projectmanage/project-properties-info/delete [REQUEST]
 */
export interface ProjectPropertiesInfoDeleteRequest {
  tenantId?: number;
  /** 项目唯一标识符 */
  projId?: number;
}

/**
 * 项目财务信息
 * @source POST /projectmanage/project-properties-financial/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-properties-financial/list [REQUEST]
 */
export interface ProjectPropertiesFinancialQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 财务信息唯一标识符 */
  financialId?: number;
  /** 项目ID，关联项目基础信息表 */
  projId?: number;
  /** 研发主体所在国(来源国家数据字典值) */
  developmentCountry?: string;
  /** KO财务审批单ID */
  koFinancialApprovalId?: string;
  /** 是否含预研阶段费用 */
  includesPreResearchCosts?: number;
  /** 预研费用金额 */
  preResearchCosts?: number;
  /** 分项预算，详细描述各项预算内容 */
  budgetDetails?: string;
  /** 结项自动财务清算 */
  autoFinancialClearance?: number;
  /** 可延迟天数，用于财务清算的缓冲期 */
  allowableDelayDays?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateTime?: string;
  /** 修改时间 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目财务信息
 * @source POST /projectmanage/project-properties-financial/edit [REQUEST]
 * @source POST /projectmanage/project-properties-financial/add [REQUEST]
 */
export interface ProjectPropertiesFinancialEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 财务信息唯一标识符 */
  financialId?: number;
  /** 项目ID，关联项目基础信息表 */
  projId?: number;
  /** 研发主体所在国(来源国家数据字典值) */
  developmentCountry?: string;
  /** KO财务审批单ID */
  koFinancialApprovalId?: string;
  /** 是否含预研阶段费用 */
  includesPreResearchCosts?: number;
  /** 预研费用金额 */
  preResearchCosts?: number;
  /** 分项预算，详细描述各项预算内容 */
  budgetDetails?: string;
  /** 结项自动财务清算 */
  autoFinancialClearance?: number;
  /** 可延迟天数，用于财务清算的缓冲期 */
  allowableDelayDays?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目财务信息
 * @source POST /projectmanage/project-properties-financial/delete [REQUEST]
 */
export interface ProjectPropertiesFinancialDeleteRequest {
  tenantId?: number;
  /** 财务信息唯一标识符 */
  financialId?: number;
}

/**
 * 车型信息
 * @source POST /projectmanage/project-prop-vehicle/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-prop-vehicle/list [REQUEST]
 */
export interface ProjectPropVehicleQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目车型ID,唯一标识符 */
  projVehicleId?: number;
  /** 车型标签 */
  modelTag?: string;
  /** 项目ID */
  projId?: number;
  /** 车型类别ID */
  configModelTypeId?: string;
  /** 车型级别 */
  modelLevel?: string;
  /** 车型ID(待定) */
  modelTypeId?: number;
  /** 子车型ID(待定) */
  subModelTypeId?: string;
  /** 技术平台(来源技术平台数据字典值) */
  technicalPlatform?: string;
  /** 车身形式(来源于车身形式数据字典表,需要根据关联表做范围过滤) */
  bodyForm?: string;
  /** 能源形式(来源于能源形式数据字典值) */
  energyForm?: string;
  /** 子驱动方式(来源子驱动方式数据字典值) */
  driveMethod?: string;
  /** 发动机型号 */
  engine?: string;
  /** 电机型号 */
  motor?: string;
  /** 电池型号 */
  battery?: string;
  /** 总功率 */
  totalPower?: number;
  /** 电子电器系统 */
  electronicSystem?: string;
  /** 变速箱类型 */
  transmission?: string;
  /** 驾驶方式(来源驾驶方式数据字典值) */
  drivingMethod?: string;
  /** 物理架构(来源物理架构数据字典值) */
  physicalArchitecture?: string;
  /** 电子电器架构(来源电子电器架构数据字典值) */
  electronicArchitecture?: string;
  /** 根车型 */
  rootModel?: string;
  /** 智驶方案 */
  smartDrivingFunctionLevel?: string;
  /** 智驶方案 */
  smartDrivingHardwareTopology?: string;
  /** 车长 */
  carLength?: number;
  /** 车宽 */
  carWidth?: number;
  /** 车高 */
  carHeight?: number;
  /** 轴距 */
  wheelbase?: number;
  /** 开发范围简述 */
  developmentScope?: string;
  /** 规模代码 */
  scaleCode?: string;
  /** 市场竞品 */
  marketCompetitors?: string;
  /** 技术竞品 */
  technicalCompetitors?: string;
  /** 记录创建时间 */
  createTime?: string;
  /** 创建人 */
  createUser?: number;
  /** 记录最后更新时间 */
  updateTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 车型信息
 * @source POST /projectmanage/project-prop-vehicle/edit [REQUEST]
 * @source POST /projectmanage/project-prop-vehicle/add [REQUEST]
 */
export interface ProjectPropVehicleEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目车型ID,唯一标识符 */
  projVehicleId?: number;
  /** 车型标签 */
  modelTag?: string;
  /** 项目ID */
  projId?: number;
  /** 车型类别ID */
  configModelTypeId?: string;
  /** 车型级别 */
  modelLevel?: string;
  /** 车型ID(待定) */
  modelTypeId?: number;
  /** 子车型ID(待定) */
  subModelTypeId?: string;
  /** 技术平台(来源技术平台数据字典值) */
  technicalPlatform?: string;
  /** 车身形式(来源于车身形式数据字典表,需要根据关联表做范围过滤) */
  bodyForm?: string;
  /** 能源形式(来源于能源形式数据字典值) */
  energyForm?: string;
  /** 子驱动方式(来源子驱动方式数据字典值) */
  driveMethod?: string;
  /** 发动机型号 */
  engine?: string;
  /** 电机型号 */
  motor?: string;
  /** 电池型号 */
  battery?: string;
  /** 总功率 */
  totalPower?: number;
  /** 电子电器系统 */
  electronicSystem?: string;
  /** 变速箱类型 */
  transmission?: string;
  /** 驾驶方式(来源驾驶方式数据字典值) */
  drivingMethod?: string;
  /** 物理架构(来源物理架构数据字典值) */
  physicalArchitecture?: string;
  /** 电子电器架构(来源电子电器架构数据字典值) */
  electronicArchitecture?: string;
  /** 根车型 */
  rootModel?: string;
  /** 智驶方案 */
  smartDrivingFunctionLevel?: string;
  /** 智驶方案 */
  smartDrivingHardwareTopology?: string;
  /** 车长 */
  carLength?: number;
  /** 车宽 */
  carWidth?: number;
  /** 车高 */
  carHeight?: number;
  /** 轴距 */
  wheelbase?: number;
  /** 开发范围简述 */
  developmentScope?: string;
  /** 规模代码 */
  scaleCode?: string;
  /** 市场竞品 */
  marketCompetitors?: string;
  /** 技术竞品 */
  technicalCompetitors?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 车型信息
 * @source POST /projectmanage/project-prop-vehicle/delete [REQUEST]
 */
export interface ProjectPropVehicleDeleteRequest {
  tenantId?: number;
  /** 项目车型ID,唯一标识符 */
  projVehicleId?: number;
}

/**
 * 项目权限
 * @source POST /projectmanage/project-permission/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-permission/list [REQUEST]
 */
export interface ProjectPermissionQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** ID */
  rolePermId?: number;
  /** 项目ID */
  projectId?: number;
  /** 权限ID(关联权限表) */
  permissionId?: number;
  /** 状态(1启用 2停用) */
  status?: number;
  /** 数据类型(1是项目权限数据,2是模版默认权限数据) */
  dataType?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 最后修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
}

/**
 * 项目权限
 * @source POST /projectmanage/project-permission/edit [REQUEST]
 * @source POST /projectmanage/project-permission/add [REQUEST]
 */
export interface ProjectPermissionEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** ID */
  rolePermId?: number;
  /** 项目ID */
  projectId?: number;
  /** 权限ID(关联权限表) */
  permissionId?: number;
  /** 状态(1启用 2停用) */
  status?: number;
  /** 数据类型(1是项目权限数据,2是模版默认权限数据) */
  dataType?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目权限
 * @source POST /projectmanage/project-permission/delete [REQUEST]
 */
export interface ProjectPermissionDeleteRequest {
  tenantId?: number;
  /** ID */
  rolePermId?: number;
}

/**
 * 项目角色
 * @source POST /projectmanage/project-obs/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-obs/list [REQUEST]
 */
export interface ProjectObsQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** ID */
  projObsId?: number;
  /** 父OBSID */
  parentProjObsId?: number;
  /** 项目ID */
  projectId?: Record<string, any>;
  /** OBS名称 */
  obsName?: string;
  /** OBS描述 */
  obsDesc?: string;
  /** 展示区域 */
  displayArea?: number;
  /** 纵向暂时层级 */
  verticalDisplayHierarchy?: number;
  /** 项目角色ID(关联系统角色表ID) */
  projRoleId?: number;
  /** 项目角色类型 */
  projRoleType?: number;
  /** 责任人部门 */
  responsiableDept?: number;
  /** 责任人 */
  responsiable?: number;
  /** 渲染方向(1 横向 2 纵向) */
  displayDirection?: number;
  /** 工作流状态(来源项目角色审批状态数据字典) */
  workflowStatus?: Record<string, any>;
  /** 项目obs版本 */
  projObsVersion?: string;
  /** 是否当前版本标识(1 是,0 否) */
  currentVersion?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 最后修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
}

/**
 * 项目角色
 * @source POST /projectmanage/project-obs/delete [REQUEST]
 */
export interface ProjectObsDeleteRequest {
  tenantId?: number;
  /** ID */
  projObsId?: number;
}

/**
 * 项目obs模版
 * @source POST /projectmanage/project-obs-template/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-obs-template/list [REQUEST]
 */
export interface ProjectObsTemplateQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目obs模板ID,唯一标识 */
  projObsTemplateId?: number;
  /** 来源项目ID */
  sourceProjId?: number;
  /** OBS名称 */
  obsName?: string;
  /** OBS描述 */
  obsDesc?: string;
  /** 展示区域 */
  displayArea?: number;
  /** 纵向暂时层级 */
  verticalDisplayHierarchy?: number;
  /** 项目角色ID(关联系统角色表ID) */
  projRoleId?: number;
  /** 项目角色类型 */
  projRoleType?: number;
  /** 责任人部门 */
  responsiableDept?: number;
  /** 渲染方向(1 横向 2 纵向) */
  displayDirection?: number;
  /** 工作流状态(来源项目角色审批状态数据字典) */
  workflowStatus?: Record<string, any>;
  /** 项目obs模板版本 */
  projObsTempalteVersion?: string;
  /** 是否当前版本标识(1 是,0 否) */
  currentVersion?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 最后修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
}

/**
 * 项目obs模版
 * @source POST /projectmanage/project-obs-template/edit [REQUEST]
 * @source POST /projectmanage/project-obs-template/add [REQUEST]
 */
export interface ProjectObsTemplateEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目obs模板ID,唯一标识 */
  projObsTemplateId?: number;
  /** 来源项目ID */
  sourceProjId?: number;
  /** OBS名称 */
  obsName?: string;
  /** OBS描述 */
  obsDesc?: string;
  /** 展示区域 */
  displayArea?: number;
  /** 纵向暂时层级 */
  verticalDisplayHierarchy?: number;
  /** 项目角色ID(关联系统角色表ID) */
  projRoleId?: number;
  /** 项目角色类型 */
  projRoleType?: number;
  /** 责任人部门 */
  responsiableDept?: number;
  /** 渲染方向(1 横向 2 纵向) */
  displayDirection?: number;
  /** 工作流状态(来源项目角色审批状态数据字典) */
  workflowStatus?: Record<string, any>;
  /** 项目obs模板版本 */
  projObsTempalteVersion?: string;
  /** 是否当前版本标识(1 是,0 否) */
  currentVersion?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目obs模版
 * @source POST /projectmanage/project-obs-template/delete [REQUEST]
 */
export interface ProjectObsTemplateDeleteRequest {
  tenantId?: number;
  /** 项目obs模板ID,唯一标识 */
  projObsTemplateId?: number;
}

/**
 * 项目成员和角色关联
 * @source POST /projectmanage/project-member-role/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/project-member-role/list [REQUEST]
 */
export interface ProjectMemberRoleQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键 */
  memberRoleId?: number;
  /** 数据类型(1项目数据;2模版数据) */
  dataType?: number;
  /** 项目ID */
  projectId?: number;
  /** 用户ID */
  userId?: number;
  /** 角色ID */
  roleId?: number;
}

/**
 * 项目成员和角色关联
 * @source POST /projectmanage/project-member-role/edit [REQUEST]
 * @source POST /projectmanage/project-member-role/add [REQUEST]
 */
export interface ProjectMemberRoleEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键 */
  memberRoleId?: number;
  /** 数据类型(1项目数据;2模版数据) */
  dataType?: number;
  /** 项目ID */
  projectId?: number;
  /** 用户ID */
  userId?: number;
  /** 角色ID */
  roleId?: number;
}

/**
 * 项目成员和角色关联
 * @source POST /projectmanage/project-member-role/delete [REQUEST]
 */
export interface ProjectMemberRoleDeleteRequest {
  tenantId?: number;
  /** 主键 */
  memberRoleId?: number;
}

/**
 * 项目集
 * @source POST /projectmanage/program/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/program/list [REQUEST]
 */
export interface ProgramQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目集ID */
  programId?: number;
  /** 父节点ID */
  parentId?: number;
  /** 组合ID */
  portfolioId?: number;
  /** 项目集编码 */
  programCode?: string;
  /** 项目集名称 */
  programName?: string;
  /** 状态(1=规划,2=执行,3=暂停,4=完成) */
  status?: number;
  /** 排序序号 */
  sortNum?: number;
  /** 预算金额 */
  budget?: number;
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 负责人ID */
  managerId?: number;
  /** 创建人 */
  createUser?: number;
  /** 更新人 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 项目集
 * @source POST /projectmanage/program/edit [REQUEST]
 * @source POST /projectmanage/program/add [REQUEST]
 */
export interface ProgramEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目集ID */
  programId?: number;
  /** 父节点ID */
  parentId?: number;
  /** 组合ID */
  portfolioId?: number;
  /** 项目集编码 */
  programCode?: string;
  /** 项目集名称 */
  programName?: string;
  /** 状态(1=规划,2=执行,3=暂停,4=完成) */
  status?: number;
  /** 排序序号 */
  sortNum?: number;
  /** 预算金额 */
  budget?: number;
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 负责人ID */
  managerId?: number;
  /** 创建人 */
  createUser?: number;
  /** 更新人 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 项目集
 * @source POST /projectmanage/program/delete [REQUEST]
 */
export interface ProgramDeleteRequest {
  tenantId?: number;
  /** 项目集ID */
  programId?: number;
}

/**
 * 项目组合
 * @source POST /projectmanage/portfolio/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/portfolio/list [REQUEST]
 */
export interface PortfolioQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 组合ID */
  portfolioId?: number;
  /** 父节点ID */
  parentId?: number;
  /** 组合编码 */
  portfolioCode?: string;
  /** 组合名称 */
  portfolioName?: string;
  /** 所属立项 */
  belongs?: string;
  /** 预算 */
  budget?: number;
  /** 状态(1=规划,2=执行,3=暂停,4=完成,5=归档) */
  status?: number;
  /** 排序序号 */
  sortNum?: number;
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 负责人ID */
  managerId?: number;
  /** 创建人 */
  createUser?: number;
  /** 更新人 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 项目组合
 * @source POST /projectmanage/portfolio/edit [REQUEST]
 * @source POST /projectmanage/portfolio/add [REQUEST]
 */
export interface PortfolioEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 组合ID */
  portfolioId?: number;
  /** 父节点ID */
  parentId?: number;
  /** 组合编码 */
  portfolioCode?: string;
  /** 组合名称 */
  portfolioName?: string;
  /** 所属立项 */
  belongs?: string;
  /** 预算 */
  budget?: number;
  /** 状态(1=规划,2=执行,3=暂停,4=完成,5=归档) */
  status?: number;
  /** 排序序号 */
  sortNum?: number;
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 负责人ID */
  managerId?: number;
  /** 创建人 */
  createUser?: number;
  /** 更新人 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 项目组合
 * @source POST /projectmanage/portfolio/delete [REQUEST]
 */
export interface PortfolioDeleteRequest {
  tenantId?: number;
  /** 组合ID */
  portfolioId?: number;
}

/**
 * 计划模板信息
 * @source POST /projectmanage/plan-template-info/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/plan-template-info/list [REQUEST]
 */
export interface PlanTemplateInfoQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 计划模板ID */
  infoId?: number;
  /** 计划模板编号 */
  templateCode?: string;
  /** 计划模板名称 */
  templateName?: string;
  /** 项目类型 */
  projectType?: string;
  /** 项目级别 */
  projectLevel?: string;
  /** 项目分类 */
  projectCategory?: string;
  /** 品牌 */
  brand?: string;
  /** 产品线 */
  productLine?: string;
  /** 车型类别 */
  vehicleCategory?: string;
  /** 技术平台 */
  technicalPlatform?: string;
  /** 能源形式 */
  energyForm?: string;
  /** 销售区域 */
  salesArea?: string;
  /** 计划模板说明 */
  templateDescription?: string;
  /** 负责人ID */
  owner?: number;
  /** 来源项目编号 */
  srcProjectCode?: string;
  /** 来源项目名称 */
  srcProjectName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 计划模板信息
 * @source POST /projectmanage/plan-template-info/edit [REQUEST]
 * @source POST /projectmanage/plan-template-info/add [REQUEST]
 */
export interface PlanTemplateInfoEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 计划模板ID */
  infoId?: number;
  /** 计划模板编号 */
  templateCode?: string;
  /** 计划模板名称 */
  templateName?: string;
  /** 项目类型 */
  projectType?: string;
  /** 项目级别 */
  projectLevel?: string;
  /** 项目分类 */
  projectCategory?: string;
  /** 品牌 */
  brand?: string;
  /** 产品线 */
  productLine?: string;
  /** 车型类别 */
  vehicleCategory?: string;
  /** 技术平台 */
  technicalPlatform?: string;
  /** 能源形式 */
  energyForm?: string;
  /** 销售区域 */
  salesArea?: string;
  /** 计划模板说明 */
  templateDescription?: string;
  /** 负责人ID */
  owner?: number;
  /** 来源项目编号 */
  srcProjectCode?: string;
  /** 来源项目名称 */
  srcProjectName?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 计划模板信息
 * @source POST /projectmanage/plan-template-info/delete [REQUEST]
 */
export interface PlanTemplateInfoDeleteRequest {
  tenantId?: number;
  /** 计划模板ID */
  infoId?: number;
}

/**
 * 计划交付物
 * @source POST /projectmanage/plan-deliverable/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/plan-deliverable/list [REQUEST]
 */
export interface PlanDeliverableQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 交付物ID */
  deliverableId?: number;
  /** 数据类型(1项目计划 2 模版定义的默认项目计划) */
  dataType?: number;
  /** 业务类型(1 计划 2 任务) */
  businessType?: number;
  /** 计划ID，关联到具体的计划 */
  planId?: number;
  /** 国际ID */
  countryId?: number;
  /** 交付物名称 */
  deliverableName?: string;
  /** 交付物版本 */
  version?: string;
  /** 是否为当前版本 */
  isCurrent?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 计划交付物
 * @source POST /projectmanage/plan-deliverable/edit [REQUEST]
 * @source POST /projectmanage/plan-deliverable/add [REQUEST]
 */
export interface PlanDeliverableEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 交付物ID */
  deliverableId?: number;
  /** 数据类型(1项目计划 2 模版定义的默认项目计划) */
  dataType?: number;
  /** 业务类型(1 计划 2 任务) */
  businessType?: number;
  /** 计划ID，关联到具体的计划 */
  planId?: number;
  /** 国际ID */
  countryId?: number;
  /** 交付物名称 */
  deliverableName?: string;
  /** 交付物版本 */
  version?: string;
  /** 是否为当前版本 */
  isCurrent?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 计划交付物
 * @source POST /projectmanage/plan-deliverable/delete [REQUEST]
 */
export interface PlanDeliverableDeleteRequest {
  tenantId?: number;
  /** 交付物ID */
  deliverableId?: number;
}

/**
 * 拒绝独立任务入参
 * @source POST /projectmanage/inde-task/reject [REQUEST]
 */
export interface IndeTaskRejectRequest {
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 拒绝意见 */
  rejectOpinion?: string;
  /** 过程文档列表 */
  processDocs?: IndeTaskProcessDocAddRequest[];
}

/**
 * 独立任务
 * @source POST /projectmanage/inde-task/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/inde-task/list [REQUEST]
 * @source POST /projectmanage/inde-task/export [REQUEST]
 */
export interface IndeTaskQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务父任务ID */
  parentIndeTaskId?: number;
  /** 独立任务名称 */
  indeTaskName?: string;
  /** 独立任务状态(1 未开始,2 进行中,3 已关闭) */
  indeTaskStatus?: number;
  /** 独立任务责任人 */
  indeTaskResponsible?: number;
  /** 独立任务责任部门 */
  indeTaskResponsibleDept?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 独立任务验收标准 */
  indeTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 独立任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 独立任务说明 */
  indeTaskDesc?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 关注人用户ID */
  follower?: number;
  /** 关注标识(1已关注;2未关注) */
  followFlag?: number;
  /** 查询标识(1全部;2我负责的;3我参与的;4我指派的;5我代办的) */
  queryFlag?: number;
  /** 标签ID */
  taskTagId?: number;
  /** 排序条件 */
  sorts?: SortRequest[];
  /** 责任人姓名 */
  responsibleName?: string;
  /** 导出列 */
  exportColumns?: string;
}

/**
 * 独立任务关注入参对象
 * @source POST /projectmanage/inde-task/follow [REQUEST]
 */
export interface IndeTaskFollowEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键 */
  followId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 用户ID */
  userId?: number;
  /** 状态(1关注 0 取消关注) */
  status?: number;
}

/**
 * 独立任务
 * @source POST /projectmanage/inde-task/edit [REQUEST]
 */
export interface IndeTaskEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务父任务ID */
  parentIndeTaskId?: number;
  /** 独立任务名称 */
  indeTaskName?: string;
  /** 独立任务状态(1 未开始,2 进行中,3 已关闭) */
  indeTaskStatus?: number;
  /** 独立任务责任人 */
  indeTaskResponsible?: number;
  /** 独立任务责任部门 */
  indeTaskResponsibleDept?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 独立任务验收标准 */
  indeTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 独立任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 独立任务说明 */
  indeTaskDesc?: string;
  /** 指派人 */
  assigneer?: number;
  /** 指派时间 */
  assigneeTime?: string;
  /** 备注 */
  remark?: string;
  /** 任务标签列表 */
  tags?: ConfigIndeTaskTagEditRequest[];
  /** 参与人列表 */
  participants?: StakeholderEditRequest[];
  /** 过程文档列表 */
  processDocs?: IndeTaskProcessDocEditRequest[];
}

/**
 * 独立任务
 * @source POST /projectmanage/inde-task/delete [REQUEST]
 */
export interface IndeTaskDeleteRequest {
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId: number;
}

/**
 * 独立任务变更入参对象
 * @source POST /projectmanage/inde-task/change [REQUEST]
 */
export interface IndeTaskChangeRequest {
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId: number;
  /** 独立任务名称 */
  indeTaskName: string;
  /** 独立任务责任人 */
  indeTaskResponsible: number;
  /** 独立任务责任部门 */
  indeTaskResponsibleDept?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 独立任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 参与人列表 */
  participants?: IndeTaskParticipantChangeRequest[];
  /** 变更原因 */
  changeReason?: string;
}

/**
 * 独立任务
 * @source POST /projectmanage/inde-task/add [REQUEST]
 */
export interface IndeTaskAddRequest {
  tenantId?: number;
  /** 独立任务父任务ID */
  parentIndeTaskId?: number;
  /** 独立任务名称 */
  indeTaskName: string;
  /** 独立任务责任人 */
  indeTaskResponsible: number;
  /** 独立任务责任部门 */
  indeTaskResponsibleDept?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType?: number;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 独立任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 独立任务说明 */
  indeTaskDesc?: string;
  /** 参与人列表 */
  participants?: StakeholderAddRequest[];
  /** 过程文档列表 */
  processDocs?: IndeTaskProcessDocAddRequest[];
}

/**
 * 独立任务标签新增入参对象
 * @source POST /projectmanage/inde-task/add/tag [REQUEST]
 */
export interface ConfigIndeTaskTagAddRequest {
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId: number;
  /** 标签列表 */
  tags: ConfigIndeTaskTagEditRequest[];
}

/**
 * 接受独立任务入参
 * @source POST /projectmanage/inde-task/accept [REQUEST]
 */
export interface IndeTaskAcceptRequest {
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 接受意见 */
  acceptOpinion?: string;
  /** 过程文档列表 */
  processDocs?: IndeTaskProcessDocAddRequest[];
}

/**
 * 独立任务风险
 * @source POST /projectmanage/inde-task-risk/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/inde-task-risk/list [REQUEST]
 */
export interface IndeTaskRiskQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务风险ID */
  indeTaskRiskId?: number;
  /** 独立任务风险名称 */
  indeTaskRiskName?: string;
  /** 独立任务风险状态(1未开始, 2进行中,3已完成,4已关闭) */
  indeTaskRiskStatus?: number;
  /** 风险分类 */
  indeTaskRiskType?: number;
  /** 风险级别 (1高2中3低) */
  indeTaskRiskLevel?: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 完成日期 */
  completionDate?: string;
  /** 独立任务风险负责人ID */
  indeTaskResponsible?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 独立任务风险
 * @source POST /projectmanage/inde-task-risk/edit [REQUEST]
 */
export interface IndeTaskRiskEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务风险ID */
  indeTaskRiskId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务风险名称 */
  indeTaskRiskName?: string;
  /** 独立任务风险状态(1未开始, 2进行中,3已完成,4已关闭) */
  indeTaskRiskStatus?: number;
  /** 风险分类 */
  indeTaskRiskType?: number;
  /** 风险级别 (1高2中3低) */
  indeTaskRiskLevel?: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 完成日期 */
  completionDate?: string;
  /** 独立任务风险负责人ID */
  indeTaskResponsible?: number;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  riskDocs?: IndeTaskRiskDocAddRequest[];
}

/**
 * 独立任务风险
 * @source POST /projectmanage/inde-task-risk/delete [REQUEST]
 * @source POST /projectmanage/inde-task-risk/convert/issue [REQUEST]
 * @source POST /projectmanage/inde-task-risk/close [REQUEST]
 */
export interface IndeTaskRiskDeleteRequest {
  tenantId?: number;
  /** 独立任务风险ID */
  indeTaskRiskId?: number;
}

/**
 * 独立任务风险新增入参对象
 * @source POST /projectmanage/inde-task-risk/add [REQUEST]
 */
export interface IndeTaskRiskAddRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务风险名称 */
  indeTaskRiskName: string;
  /** 风险分类 */
  indeTaskRiskType: number;
  /** 风险级别 (1高2中3低) */
  indeTaskRiskLevel: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 独立任务风险负责人ID */
  indeTaskResponsible?: number;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  riskDocs?: IndeTaskRiskDocAddRequest[];
}

/**
 * 独立任务风险回答
 * @source POST /projectmanage/inde-task-risk-answer/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/inde-task-risk-answer/list [REQUEST]
 */
export interface IndeTaskRiskAnswerQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务风险回答ID */
  indeTaskRiskAnswerId?: number;
  /** 独立任务风险ID */
  indeTaskRiskId?: number;
  /** 独立任务风险回答时间 */
  indeTaskRiskAnswerTime?: string;
  /** 独立任务风险回答内容 */
  indeTaskRiskAnswer?: string;
  /** 独立任务风险讨论 */
  indeTaskRiskDisscution?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateTime?: string;
  /** 修改时间 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 独立任务风险回答
 * @source POST /projectmanage/inde-task-risk-answer/edit [REQUEST]
 */
export interface IndeTaskRiskAnswerEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务风险回答ID */
  indeTaskRiskAnswerId?: number;
  /** 独立任务风险ID */
  indeTaskRiskId?: number;
  /** 独立任务风险回答时间 */
  indeTaskRiskAnswerTime?: string;
  /** 独立任务风险回答内容 */
  indeTaskRiskAnswer?: string;
  /** 独立任务风险讨论 */
  indeTaskRiskDisscution?: string;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  issueDocs?: IndeTaskRiskDocEditRequest[];
}

/**
 * 独立任务风险回答
 * @source POST /projectmanage/inde-task-risk-answer/delete [REQUEST]
 */
export interface IndeTaskRiskAnswerDeleteRequest {
  tenantId?: number;
  /** 独立任务风险回答ID */
  indeTaskRiskAnswerId?: number;
}

/**
 * 独立任务风险回答入参对象
 * @source POST /projectmanage/inde-task-risk-answer/add [REQUEST]
 */
export interface IndeTaskRiskAnswerAddRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务风险ID */
  indeTaskRiskId: number;
  /** 独立任务风险回答内容 */
  indeTaskRiskAnswer?: string;
  /** 独立任务风险讨论 */
  indeTaskRiskDisscution?: string;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  issueDocs?: IndeTaskRiskDocEditRequest[];
}

/**
 * 独立任务汇报
 * @source POST /projectmanage/inde-task-report/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/inde-task-report/list [REQUEST]
 */
export interface IndeTaskReportQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 汇报ID */
  reportId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务进度，范围0.00到100.00 */
  progress?: number;
  /** 数据类型(report,record) */
  dataType?: string;
  /** 汇报内容 */
  indeTaskDesc?: string;
  /** 汇报时间 */
  reportTime?: string;
  /** 独立任务汇报状态(2 已接受,0 待澄清,1 已汇报) */
  indeTaskReportStatus?: number;
  /** 独立任务汇报工作流定义ID */
  workflowId?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 独立任务汇报
 * @source POST /projectmanage/inde-task-report/edit [REQUEST]
 * @source POST /projectmanage/inde-task-report/add [REQUEST]
 */
export interface IndeTaskReportEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 汇报ID */
  reportId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务进度，范围0.00到100.00 */
  progress?: number;
  /** 数据类型(report,record) */
  dataType?: string;
  /** 汇报内容 */
  indeTaskDesc?: string;
  /** 汇报时间 */
  reportTime?: string;
  /** 独立任务汇报状态(2 已接受,0 待澄清,1 已汇报) */
  indeTaskReportStatus?: number;
  /** 独立任务汇报工作流定义ID */
  workflowId?: string;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  reportDocs?: IndeTaskReportDocEditRequest[];
}

/**
 * 独立任务汇报
 * @source POST /projectmanage/inde-task-report/delete [REQUEST]
 */
export interface IndeTaskReportDeleteRequest {
  tenantId?: number;
  /** 汇报ID */
  reportId?: number;
}

/**
 * 独立任务进度汇报附件表
 * @source POST /projectmanage/inde-task-report-doc/to/process/doc [REQUEST]
 * @source POST /projectmanage/inde-task-report-doc/to/delivery/doc [REQUEST]
 * @source POST /projectmanage/inde-task-report-doc/delete [REQUEST]
 */
export interface IndeTaskReportDocDeleteRequest {
  tenantId?: number;
  /** 自增主键 */
  indeTaskReportDocId?: number;
}

/**
 * 独立任务过程文档表
 * @source POST /projectmanage/inde-task-process-doc/delete [REQUEST]
 */
export interface IndeTaskProcessDocDeleteRequest {
  tenantId?: number;
  /** 自增主键 */
  indeTaskProcessDocId?: number;
}

/**
 * 独立任务过程文档表
 * @source POST /projectmanage/inde-task-process-doc/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/inde-task-process-doc/list [REQUEST]
 */
export interface IndeTaskProcessDocQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  indeTaskProcessDocId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskProcessDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 独立任务会议纪要表
 * @source POST /projectmanage/inde-task-meeting-doc/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/inde-task-meeting-doc/list [REQUEST]
 */
export interface IndeTaskMeetingDocQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  indeTaskMeetingDocId?: number;
  /** 独立任务ID */
  indeTaskMeetingId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskMeetingDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 独立任务会议
 * @source POST /projectmanage/inde-task-meeting/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/inde-task-meeting/list [REQUEST]
 */
export interface IndeTaskMeetingQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 会议ID */
  indeTaskMeetingId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 会议主题 */
  indeTaskSubject?: string;
  /** 会议状态(1未开始, 2进行中, 3已完成, 4已取消) */
  indeTaskMeetingStatus?: number;
  /** 会议时间 */
  indeTaskMeetingTime?: string;
  /** 会议地点 */
  indeTaskLocation?: string;
  /** 会议链接 */
  indeTaskMeetingUrl?: string;
  /** 发起人ID */
  indeTaskInitiator?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 会议纪要 */
  docs?: IndeTaskMeetingDocQueryRequest[];
}

/**
 * 独立任务会议
 * @source POST /projectmanage/inde-task-meeting/delete [REQUEST]
 */
export interface IndeTaskMeetingDeleteRequest {
  tenantId?: number;
  /** 会议ID */
  indeTaskMeetingId?: number;
}

/**
 * 独立任务会议
 * @source POST /projectmanage/inde-task-meeting/add [REQUEST]
 */
export interface IndeTaskMeetingEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 会议ID */
  indeTaskMeetingId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 会议主题 */
  indeTaskSubject?: string;
  /** 会议状态(1未开始, 2进行中, 3已完成, 4已取消) */
  indeTaskMeetingStatus?: number;
  /** 会议时间 */
  indeTaskMeetingTime?: string;
  /** 会议地点 */
  indeTaskLocation?: string;
  /** 会议链接 */
  indeTaskMeetingUrl?: string;
  /** 发起人ID */
  indeTaskInitiator?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 独立任务会议纪要表
 * @source POST /projectmanage/inde-task-meeting-doc/delete [REQUEST]
 */
export interface IndeTaskMeetingDocDeleteRequest {
  tenantId?: number;
  /** 自增主键 */
  indeTaskMeetingDocId?: number;
}

/**
 * 独立任务问题
 * @source POST /projectmanage/inde-task-issue/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/inde-task-issue/list [REQUEST]
 */
export interface IndeTaskIssueQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务问题ID */
  indeTaskIssueId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务问题名称 */
  indeTaskIssueName?: string;
  /** 独立任务问题状态(1未开始, 2进行中,3已完成,4已关闭) */
  indeTaskIssueStatus?: number;
  /** 问题分类 */
  indeTaskIssueType?: number;
  /** 问题级别 (1高2中3低) */
  indeTaskIssueLevel?: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 完成日期 */
  completionDate?: string;
  /** 独立任务问题负责人ID */
  indeTaskResponsible?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 独立任务问题
 * @source POST /projectmanage/inde-task-issue/edit [REQUEST]
 */
export interface IndeTaskIssueEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务问题ID */
  indeTaskIssueId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务问题名称 */
  indeTaskIssueName: string;
  /** 独立任务问题状态(1未开始, 2进行中,3已完成,4已关闭) */
  indeTaskIssueStatus?: number;
  /** 问题分类 */
  indeTaskIssueType: number;
  /** 问题级别 (1高2中3低) */
  indeTaskIssueLevel?: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 完成日期 */
  completionDate?: string;
  /** 独立任务问题负责人ID */
  indeTaskResponsible?: number;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  issueDocs?: IndeTaskIssueDocAddRequest[];
}

/**
 * 独立任务问题
 * @source POST /projectmanage/inde-task-issue/delete [REQUEST]
 * @source POST /projectmanage/inde-task-issue/convert/risk [REQUEST]
 * @source POST /projectmanage/inde-task-issue/close [REQUEST]
 */
export interface IndeTaskIssueDeleteRequest {
  tenantId?: number;
  /** 独立任务问题ID */
  indeTaskIssueId?: number;
}

/**
 * 独立任务问题入参对象
 * @source POST /projectmanage/inde-task-issue/add [REQUEST]
 */
export interface IndeTaskIssueAddRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务问题ID */
  indeTaskIssueId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务问题名称 */
  indeTaskIssueName: string;
  /** 问题分类 */
  indeTaskIssueType: number;
  /** 问题级别 (1高2中3低) */
  indeTaskIssueLevel: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 独立任务问题负责人ID */
  indeTaskResponsible?: number;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  issueDocs?: IndeTaskIssueDocAddRequest[];
}

/**
 * 独立任务回答
 * @source POST /projectmanage/inde-task-issue-answer/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/inde-task-issue-answer/list [REQUEST]
 */
export interface IndeTaskIssueAnswerQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务问题回答ID */
  indeTaskIssueAnswerId?: number;
  /** 独立任务问题ID */
  indeTaskIssueId?: number;
  /** 独立任务问题回答时间 */
  indeTaskIssueAnswerTime?: string;
  /** 独立任务问题回答内容 */
  indeTaskIssueAnswer?: string;
  /** 独立任务问题讨论 */
  indeTaskIssueDisscution?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateTime?: string;
  /** 修改时间 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 独立任务回答
 * @source POST /projectmanage/inde-task-issue-answer/edit [REQUEST]
 */
export interface IndeTaskIssueAnswerEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务问题回答ID */
  indeTaskIssueAnswerId?: number;
  /** 独立任务问题ID */
  indeTaskIssueId?: number;
  /** 独立任务问题回答时间 */
  indeTaskIssueAnswerTime?: string;
  /** 独立任务问题回答内容 */
  indeTaskIssueAnswer?: string;
  /** 独立任务问题讨论 */
  indeTaskIssueDisscution?: string;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  issueAnswerDocs?: IndeTaskIssueDocEditRequest[];
}

/**
 * 独立任务回答
 * @source POST /projectmanage/inde-task-issue-answer/delete [REQUEST]
 */
export interface IndeTaskIssueAnswerDeleteRequest {
  tenantId?: number;
  /** 独立任务问题回答ID */
  indeTaskIssueAnswerId?: number;
}

/**
 * 独立任务回答入参对象
 * @source POST /projectmanage/inde-task-issue-answer/add [REQUEST]
 */
export interface IndeTaskIssueAnswerAddRequest {
  tenantId?: number;
  /** 独立任务问题ID */
  indeTaskIssueId: number;
  /** 独立任务问题回答内容 */
  indeTaskIssueAnswer?: string;
  /** 独立任务问题讨论 */
  indeTaskIssueDisscution?: string;
  /** 备注 */
  remark?: string;
  /** 文档列表 */
  issueDocs?: IndeTaskIssueDocEditRequest[];
}

/**
 * 独立任务交付文档表
 * @source POST /projectmanage/inde-task-delivery-doc/delete [REQUEST]
 */
export interface IndeTaskDeliveryDocDeleteRequest {
  tenantId?: number;
  /** 自增主键 */
  indeTaskDeliveryDocId?: number;
}

/**
 * 独立任务交付文档表
 * @source POST /projectmanage/inde-task-delivery-doc/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/inde-task-delivery-doc/list [REQUEST]
 */
export interface IndeTaskDeliveryDocQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  indeTaskDeliveryDocId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskDeliveryDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识(关联附件表) */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 需求
 * @source POST /projectmanage/demand/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/demand/list [REQUEST]
 * @source POST /projectmanage/demand/edit [REQUEST]
 * @source POST /projectmanage/demand/delete [REQUEST]
 * @source POST /projectmanage/demand/add [REQUEST]
 */
export interface DemandRequest {
  /** 租户ID */
  tenantId?: number;
  /** ID */
  demandId?: number;
  /** 需求层级 */
  demandLevel?: number;
  /** 项目ID */
  projectId?: number;
  /** 父ID */
  parentId?: number;
  /** 需求名称 */
  demandName?: string;
  /** 需求描述 */
  demandDesc?: string;
  /** 验收标准 */
  acceptCriteria?: string;
  /** 需求类型 */
  demandType?: number;
  /** 优先级 */
  priority?: string;
  /** 工时 */
  workHours?: number;
  /** 需求评审人 */
  demandReviewer?: number;
  /** 需求责任人 */
  responsible?: number;
  /** 需求责任人姓名 */
  responsibleName?: string;
  /** 需求提出人 */
  proposer?: number;
  /** 需求提出部门 */
  proposerDept?: string;
  /** 产品经理 */
  productManager?: number;
  /** 研发经理 */
  developManager?: number;
  /** 测试经理 */
  testingManager?: number;
  /** 是否影响业务流 */
  businessFlow?: number;
  /** 是否影响数据 */
  dataImpact?: number;
  /** 迫切程度 */
  urgency?: number;
  /** 迫切程度说明 */
  urgencyDesc?: string;
  /** 关注标记 */
  followFlag?: number;
  /** 关注人用户ID */
  follower?: number;
  /** 挂接项目标记(1:挂接项目,2;未挂接项目;3我的项目) */
  projectFlag?: number;
  /** 状态 */
  status?: number;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 修改人 */
  updatedBy?: number;
  /** 修改时间 */
  updatedAt?: string;
  /** 备注 */
  remark?: string;
  /** 是否删除( 0 否 1 是) */
  deleted?: number;
  projectCustomFieldRequest?: ProjectCustomFieldRequest;
  /** 需求附件 */
  attachments?: ProjectAttachmentRequest[];
}

/**
 * 需求阶段
 * @source POST /projectmanage/demand-phase/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/demand-phase/list [REQUEST]
 * @source POST /projectmanage/demand-phase/edit [REQUEST]
 * @source POST /projectmanage/demand-phase/delete [REQUEST]
 * @source POST /projectmanage/demand-phase/add [REQUEST]
 */
export interface DemandPhaseRequest {
  /** 租户ID，用于多租户隔离 */
  tenantId?: number;
  /** 主键ID */
  phaseId?: number;
  /** 需求ID */
  demandId?: number;
  /** 阶段类型(1.规划阶段、2.审批阶段,3.招采阶段,4.实施阶段,5.验收阶段) */
  phaseType?: number;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 备注信息 */
  remark?: string;
  /** 删除标记(0表示未删除，1表示已删除) */
  deleted?: number;
}

/**
 * 需求关注记录
 * @source POST /projectmanage/demand-follow/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/demand-follow/list [REQUEST]
 * @source POST /projectmanage/demand-follow/edit [REQUEST]
 * @source POST /projectmanage/demand-follow/delete [REQUEST]
 * @source POST /projectmanage/demand-follow/add [REQUEST]
 */
export interface DemandFollowRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键 */
  id?: number;
  /** 需求ID */
  demandId: number;
  /** 用户ID */
  userId: number;
  /** 状态(1关注 0 取消关注) */
  status?: number;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 修改人 */
  updatedBy?: number;
  /** 修改时间 */
  updatedAt?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记 (0 未删除 1 已删除) */
  deleted?: number;
}

/**
 * 需求实施阶段
 * @source POST /projectmanage/demand-execution-phase/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/demand-execution-phase/list [REQUEST]
 * @source POST /projectmanage/demand-execution-phase/edit [REQUEST]
 * @source POST /projectmanage/demand-execution-phase/delete [REQUEST]
 * @source POST /projectmanage/demand-execution-phase/add [REQUEST]
 */
export interface DemandExecutionPhaseRequest {
  /** 租户id */
  tenantId?: number;
  /** 自增主键 */
  id?: number;
  /** 关联需求ID */
  demandId?: number;
  /** 需求评审人 */
  reviewer?: string;
  /** 业务代表 */
  businessRepresentative?: string;
  /** 项目负责人 */
  projectLeader?: string;
  /** 产品经理 */
  productManager?: string;
  /** 研发经理 */
  rndManager?: string;
  /** 测试经理 */
  testManager?: string;
  /** 需求状态 */
  status?: number;
  /** 需求进度 */
  progress?: number;
  /** 开发需求数量 */
  developDemandCount?: number;
  /** 计划开始时间 */
  plannedStartTime?: string;
  /** 计划完成时间 */
  plannedFinishTime?: string;
  /** 实际开始时间 */
  actualStartTime?: string;
  /** 实际完成时间 */
  actualCompletionTime?: string;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 是否删除(0 否 1 是) */
  deleted?: number;
}

/**
 * 需求变更
 * @source POST /projectmanage/demand-change-phase/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/demand-change-phase/list [REQUEST]
 * @source POST /projectmanage/demand-change-phase/edit [REQUEST]
 * @source POST /projectmanage/demand-change-phase/delete [REQUEST]
 * @source POST /projectmanage/demand-change-phase/add [REQUEST]
 */
export interface DemandChangePhaseRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  id?: number;
  /** 关联需求ID */
  demandId?: number;
  /** 需求变更人 */
  changer?: string;
  /** 需求变更时间 */
  changeTime?: string;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 是否删除(0 否 1 是) */
  deleted?: number;
}

/**
 * 需求分析（评审）/审批阶段
 * @source POST /projectmanage/demand-approval-phase/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/demand-approval-phase/list [REQUEST]
 * @source POST /projectmanage/demand-approval-phase/edit [REQUEST]
 * @source POST /projectmanage/demand-approval-phase/delete [REQUEST]
 * @source POST /projectmanage/demand-approval-phase/add [REQUEST]
 */
export interface DemandApprovalPhaseRequest {
  /** 租户id */
  tenantId?: number;
  /** 自增主键 */
  id?: number;
  /** 关联需求ID */
  demandId?: number;
  /** 需求审批流程 */
  approvalProcess?: string;
  /** 需求审批环节 */
  approvalStep?: string;
  /** 当前环节审批人 */
  currentApprover?: string;
  /** 需求评审人 */
  reviewer?: string;
  /** 需求审批时间 */
  approvalTime?: string;
  /** 优先级 */
  priority?: string;
  /** 业务角色 */
  businessRole?: string;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 是否删除(0 否 1 是) */
  deleted?: number;
}

/**
 * 需求分析阶段
 * @source POST /projectmanage/demand-analysis-phase/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/demand-analysis-phase/list [REQUEST]
 * @source POST /projectmanage/demand-analysis-phase/edit [REQUEST]
 * @source POST /projectmanage/demand-analysis-phase/delete [REQUEST]
 * @source POST /projectmanage/demand-analysis-phase/add [REQUEST]
 */
export interface DemandAnalysisPhaseRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  id: number;
  /** 关联需求ID */
  demandId?: number;
  /** 需求提出部门 */
  proposerDept?: string;
  /** 需求提出人 */
  proposer?: number;
  /** 需求提出时间 */
  proposalTime?: string;
  /** 需求责任部门 */
  responsibleDept?: number;
  /** 需求负责人 */
  responsible?: number;
  /** 需求接收时间 */
  receiveTime?: string;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 是否删除(0 否 1 是) */
  deleted?: number;
}

/**
 * 任务标签
 * @source POST /projectmanage/config-task-tag/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-task-tag/list [REQUEST]
 */
export interface ConfigIndeTaskTagQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 任务标签ID */
  taskTagId?: number;
  /** 父标签ID或者所属标签组ID */
  parentTaskTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag?: number;
  /** 任务标签标题 */
  taskTagName?: string;
  /** 任务标签描述 */
  taskTagDesc?: string;
  /** 任务标签颜色 */
  taskTagColor?: string;
  /** 任务标签状态 (1 启用,0 禁用) */
  taskTagStatus?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 任务标签
 * @source POST /projectmanage/config-task-tag/delete [REQUEST]
 */
export interface ConfigIndeTaskTagDeleteRequest {
  tenantId?: number;
  /** 任务标签ID */
  taskTagId: number;
}

/**
 * 项目任务标签
 * @source POST /projectmanage/config-project-task-tag/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-project-task-tag/list [REQUEST]
 */
export interface ConfigProjectTaskTagQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务标签ID */
  projTaskTagId?: number;
  /** 父项目任务标签ID或者所属标签组ID */
  parentProjTaskTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag?: number;
  /** 项目标签标题 */
  projTagName?: string;
  /** 项目标签描述 */
  projTagDesc?: string;
  /** 项目标签颜色 */
  projTagColor?: string;
  /** 项目标签状态 (1 启用,0 禁用) */
  projTagStatus?: number;
  /** 排序码 */
  sortNum?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目任务标签
 * @source POST /projectmanage/config-project-task-tag/delete [REQUEST]
 */
export interface ConfigProjectTaskTagDeleteRequest {
  tenantId?: number;
  /** 项目任务标签ID */
  projTaskTagId?: number;
}

/**
 * 项目标签
 * @source POST /projectmanage/config-project-tag/tree [REQUEST]
 * @source POST /projectmanage/config-project-tag/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-project-tag/list [REQUEST]
 */
export interface ConfigProjectTagQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目标签ID */
  projTagId?: number;
  /** 父项目标签ID或者所属标签组ID */
  parentProjTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag?: number;
  /** 项目标签标题 */
  projTagName?: string;
  /** 项目标签描述 */
  projTagDesc?: string;
  /** 项目标签颜色 */
  projTagColor?: string;
  /** 项目标签状态 (1 启用,0 禁用) */
  projTagStatus?: number;
  /** 排序码 */
  sortNum?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 项目标签
 * @source POST /projectmanage/config-project-tag/delete [REQUEST]
 */
export interface ConfigProjectTagDeleteRequest {
  tenantId?: number;
  /** 项目标签ID */
  projTagId?: number;
}

/**
 * 配置项目角色类型
 * @source POST /projectmanage/config-project-role-type/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-project-role-type/list [REQUEST]
 */
export interface ConfigProjectRoleTypeQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 配置项目角色类型ID,唯一表示 */
  configProjRoleTypeId?: number;
  /** 项目ID */
  projId?: number;
  /** 项目角色类型 */
  projRoleTypeName?: string;
  /** 项目角色类型描述 */
  projRoleTypeDesc?: string;
  /** 项目角色类型展示框底色 */
  projRoleTypeColor?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 配置项目角色类型
 * @source POST /projectmanage/config-project-role-type/edit [REQUEST]
 * @source POST /projectmanage/config-project-role-type/add [REQUEST]
 */
export interface ConfigProjectRoleTypeEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 配置项目角色类型ID,唯一表示 */
  configProjRoleTypeId?: number;
  /** 项目ID */
  projId?: number;
  /** 项目角色类型 */
  projRoleTypeName?: string;
  /** 项目角色类型描述 */
  projRoleTypeDesc?: string;
  /** 项目角色类型展示框底色 */
  projRoleTypeColor?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 配置项目角色类型
 * @source POST /projectmanage/config-project-role-type/delete [REQUEST]
 */
export interface ConfigProjectRoleTypeDeleteRequest {
  tenantId?: number;
  /** 配置项目角色类型ID,唯一表示 */
  configProjRoleTypeId?: number;
}

/**
 * 配置项目角色类型
 * @source POST /projectmanage/config-proj-role-type/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-proj-role-type/list [REQUEST]
 */
export interface ConfigProjRoleTypeQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 配置项目角色类型ID,唯一表示 */
  configProjRoleTypeId?: number;
  /** 项目ID */
  projId?: number;
  /** 项目角色类型 */
  projRoleTypeName?: string;
  /** 项目角色类型描述 */
  projRoleTypeDesc?: string;
  /** 项目角色类型展示框底色 */
  projRoleTypeColor?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 配置项目角色类型
 * @source POST /projectmanage/config-proj-role-type/edit [REQUEST]
 * @source POST /projectmanage/config-proj-role-type/add [REQUEST]
 */
export interface ConfigProjRoleTypeEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 配置项目角色类型ID,唯一表示 */
  configProjRoleTypeId?: number;
  /** 项目ID */
  projId?: number;
  /** 项目角色类型 */
  projRoleTypeName?: string;
  /** 项目角色类型描述 */
  projRoleTypeDesc?: string;
  /** 项目角色类型展示框底色 */
  projRoleTypeColor?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 配置项目角色类型
 * @source POST /projectmanage/config-proj-role-type/delete [REQUEST]
 */
export interface ConfigProjRoleTypeDeleteRequest {
  tenantId?: number;
  /** 配置项目角色类型ID,唯一表示 */
  configProjRoleTypeId?: number;
}

/**
 * 产品线配置
 * @source POST /projectmanage/config-product-line/tree [REQUEST]
 * @source POST /projectmanage/config-product-line/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-product-line/list [REQUEST]
 */
export interface ConfigProductLineQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 产品线ID,唯一表示 */
  productLineId?: number;
  /** 父产品线ID */
  parentProductLineId?: number;
  /** 产品线名称 */
  productLineName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 产品线配置
 * @source POST /projectmanage/config-product-line/edit [REQUEST]
 * @source POST /projectmanage/config-product-line/add [REQUEST]
 */
export interface ConfigProductLineEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 产品线ID,唯一表示 */
  productLineId?: number;
  /** 父产品线ID */
  parentProductLineId?: number;
  /** 产品线名称 */
  productLineName?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 产品线配置
 * @source POST /projectmanage/config-product-line/delete [REQUEST]
 */
export interface ConfigProductLineDeleteRequest {
  tenantId?: number;
  /** 产品线ID,唯一表示 */
  productLineId?: number;
}

/**
 * 生产工厂配置
 * @source POST /projectmanage/config-product-factory/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-product-factory/list [REQUEST]
 */
export interface ConfigProductFactoryQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 生产厂区id */
  productionFactoryId?: number;
  /** 生产基地ID */
  productionBaseId?: number;
  /** 生产厂区名称 */
  productionFactoryName?: string;
  /** 生产厂区描述 */
  productionFactoryDesc?: string;
  /** 生产厂区状态 */
  productionFactoryStatus?: number;
  /** 生产工厂地址 */
  factoryLocation?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 生产工厂配置
 * @source POST /projectmanage/config-product-factory/edit [REQUEST]
 * @source POST /projectmanage/config-product-factory/add [REQUEST]
 */
export interface ConfigProductFactoryEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 生产厂区id */
  productionFactoryId?: number;
  /** 生产基地ID */
  productionBaseId?: number;
  /** 生产厂区名称 */
  productionFactoryName?: string;
  /** 生产厂区描述 */
  productionFactoryDesc?: string;
  /** 生产厂区状态 */
  productionFactoryStatus?: number;
  /** 生产工厂地址 */
  factoryLocation?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 生产工厂配置
 * @source POST /projectmanage/config-product-factory/delete [REQUEST]
 */
export interface ConfigProductFactoryDeleteRequest {
  tenantId?: number;
  /** 生产厂区id */
  productionFactoryId?: number;
}

/**
 * 生产基地配置
 * @source POST /projectmanage/config-product-base/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-product-base/list [REQUEST]
 */
export interface ConfigProductBaseQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 生产基地id */
  productionBaseId?: number;
  /** 生产区域(来源生产区域数据字典值) */
  productionArea?: string;
  /** 生产基地名称 */
  productionBaseName?: string;
  /** 生产基地描述 */
  productionBaseDesc?: string;
  /** 生产基地状态 */
  productionBaseStatus?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 生产基地配置
 * @source POST /projectmanage/config-product-base/edit [REQUEST]
 * @source POST /projectmanage/config-product-base/add [REQUEST]
 */
export interface ConfigProductBaseEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 生产基地id */
  productionBaseId?: number;
  /** 生产区域(来源生产区域数据字典值) */
  productionArea?: string;
  /** 生产基地名称 */
  productionBaseName?: string;
  /** 生产基地描述 */
  productionBaseDesc?: string;
  /** 生产基地状态 */
  productionBaseStatus?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 生产基地配置
 * @source POST /projectmanage/config-product-base/delete [REQUEST]
 */
export interface ConfigProductBaseDeleteRequest {
  tenantId?: number;
  /** 生产基地id */
  productionBaseId?: number;
}

/**
 * 车型配置
 * @source POST /projectmanage/config-model/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-model/list [REQUEST]
 */
export interface ConfigModelQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 车型类别ID,唯一表示 */
  modelTypeId?: number;
  /** 父车型ID */
  parentModelTypeId?: number;
  /** 车型类别(来源车型类别数据字典值) */
  model?: string;
  /** 车型名称 */
  modelName?: number;
  /** 车型描述 */
  modelDesc?: number;
  /** 车型状态 (1 启用,0 禁用) */
  modelStatus?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 车型配置
 * @source POST /projectmanage/config-model/edit [REQUEST]
 * @source POST /projectmanage/config-model/add [REQUEST]
 */
export interface ConfigModelEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 车型类别ID,唯一表示 */
  modelTypeId?: number;
  /** 父车型ID */
  parentModelTypeId?: number;
  /** 车型类别(来源车型类别数据字典值) */
  model?: string;
  /** 车型名称 */
  modelName?: number;
  /** 车型描述 */
  modelDesc?: number;
  /** 车型状态 (1 启用,0 禁用) */
  modelStatus?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 车型配置
 * @source POST /projectmanage/config-model/delete [REQUEST]
 */
export interface ConfigModelDeleteRequest {
  tenantId?: number;
  /** 车型类别ID,唯一表示 */
  modelTypeId?: number;
}

/**
 * 车型类别-车型级别配置
 * @source POST /projectmanage/config-model-level/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-model-level/list [REQUEST]
 */
export interface ConfigModelLevelQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 车型类别车型界别配置ID,唯一表示 */
  configModelLevelId?: number;
  /** 车型类别(来源车型类别数据字典值) */
  modelTypeId?: number;
  /** 车型级别(来源车型级别数据字典值) */
  modelLevel?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 车型类别-车型级别配置
 * @source POST /projectmanage/config-model-level/edit [REQUEST]
 * @source POST /projectmanage/config-model-level/add [REQUEST]
 */
export interface ConfigModelLevelEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 车型类别车型界别配置ID,唯一表示 */
  configModelLevelId?: number;
  /** 车型类别(来源车型类别数据字典值) */
  modelTypeId?: number;
  /** 车型级别(来源车型级别数据字典值) */
  modelLevel?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 车型类别-车型级别配置
 * @source POST /projectmanage/config-model-level/delete [REQUEST]
 */
export interface ConfigModelLevelDeleteRequest {
  tenantId?: number;
  /** 车型类别车型界别配置ID,唯一表示 */
  configModelLevelId?: number;
}

/**
 * 车型级别与车身形式匹配关系配置
 * @source POST /projectmanage/config-model-level-body/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-model-level-body/list [REQUEST]
 */
export interface ConfigModelLevelBodyQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 关系配置ID,唯一表示 */
  configModelLevelBodyId?: number;
  /** 车型级别(来源车型级别数据字典值) */
  modelLevel?: string;
  /** 车身形式(来源车型形式数据字典值) */
  bodyForm?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 车型级别与车身形式匹配关系配置
 * @source POST /projectmanage/config-model-level-body/edit [REQUEST]
 * @source POST /projectmanage/config-model-level-body/add [REQUEST]
 */
export interface ConfigModelLevelBodyEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 关系配置ID,唯一表示 */
  configModelLevelBodyId?: number;
  /** 车型级别(来源车型级别数据字典值) */
  modelLevel?: string;
  /** 车身形式(来源车型形式数据字典值) */
  bodyForm?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 车型级别与车身形式匹配关系配置
 * @source POST /projectmanage/config-model-level-body/delete [REQUEST]
 */
export interface ConfigModelLevelBodyDeleteRequest {
  tenantId?: number;
  /** 关系配置ID,唯一表示 */
  configModelLevelBodyId?: number;
}

/**
 * 国家和地区信息
 * @source POST /projectmanage/config-market-area/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-market-area/list [REQUEST]
 */
export interface ConfigMarketAreaQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 市场区域ID */
  marketAreaId?: number;
  /** 父市场区域ID */
  parentMarketAreaId?: number;
  /** 地区/国家编码 */
  marketAreaCode?: string;
  /** 地区/国家名称 */
  marketAreaName?: string;
  /** 是否是国家(1 是 0 否) */
  countryFlag?: number;
  /** 地区/国家描述 */
  marketAreaDesc?: string;
  /** 创建者ID */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新人 */
  updateUser?: number;
  /** 更新者ID */
  updateTime?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 国家和地区信息
 * @source POST /projectmanage/config-market-area/edit [REQUEST]
 * @source POST /projectmanage/config-market-area/add [REQUEST]
 */
export interface ConfigMarketAreaEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 市场区域ID */
  marketAreaId?: number;
  /** 父市场区域ID */
  parentMarketAreaId?: number;
  /** 地区/国家编码 */
  marketAreaCode?: string;
  /** 地区/国家名称 */
  marketAreaName?: string;
  /** 是否是国家(1 是 0 否) */
  countryFlag?: number;
  /** 地区/国家描述 */
  marketAreaDesc?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 国家和地区信息
 * @source POST /projectmanage/config-market-area/delete [REQUEST]
 */
export interface ConfigMarketAreaDeleteRequest {
  tenantId?: number;
  /** 市场区域ID */
  marketAreaId?: number;
}

/**
 * 关注配置
 * @source POST /projectmanage/config-follow/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-follow/list [REQUEST]
 */
export interface ConfigFollowQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键 */
  followId?: number;
  /** 关注对象类型(1 项目,2 计划 3 独立任务 4 项目任务 5 项目组合 6 项目集) */
  followType?: number;
  /** 关注对象ID */
  followObjectId?: number;
  /** 用户ID */
  userId?: number;
  /** 状态(1关注 0 取消关注) */
  status?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
}

/**
 * 关注配置
 * @source POST /projectmanage/config-follow/edit [REQUEST]
 * @source POST /projectmanage/config-follow/add [REQUEST]
 */
export interface ConfigFollowEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键 */
  followId?: number;
  /** 关注对象类型(1 项目,2 计划 3 独立任务 4 项目任务 5 项目组合 6 项目集) */
  followType?: number;
  /** 关注对象ID */
  followObjectId?: number;
  /** 用户ID */
  userId?: number;
  /** 状态(1关注 0 取消关注) */
  status?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 关注配置
 * @source POST /projectmanage/config-follow/delete [REQUEST]
 */
export interface ConfigFollowDeleteRequest {
  tenantId?: number;
  /** 主键 */
  followId?: number;
}

/**
 * 驱动方式-驱动方式值关系配置
 * @source POST /projectmanage/config-drive-method/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-drive-method/list [REQUEST]
 */
export interface ConfigDriveMethodQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 驱动形式关系配置ID,唯一表示 */
  configDriveMethodId?: number;
  /** 驱动方式(来源驱动方式数据字典值) */
  driveMethod?: string;
  /** 子驱动方式(来源子驱动方式数据字典值) */
  subDriveMethod?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 驱动方式-驱动方式值关系配置
 * @source POST /projectmanage/config-drive-method/edit [REQUEST]
 * @source POST /projectmanage/config-drive-method/add [REQUEST]
 */
export interface ConfigDriveMethodEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 驱动形式关系配置ID,唯一表示 */
  configDriveMethodId?: number;
  /** 驱动方式(来源驱动方式数据字典值) */
  driveMethod?: string;
  /** 子驱动方式(来源子驱动方式数据字典值) */
  subDriveMethod?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 驱动方式-驱动方式值关系配置
 * @source POST /projectmanage/config-drive-method/delete [REQUEST]
 */
export interface ConfigDriveMethodDeleteRequest {
  tenantId?: number;
  /** 驱动形式关系配置ID,唯一表示 */
  configDriveMethodId?: number;
}

/**
 * 事业部
 * @source POST /projectmanage/config-business-unit/tree [REQUEST]
 * @source POST /projectmanage/config-business-unit/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-business-unit/list [REQUEST]
 */
export interface ConfigBusinessUnitQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 事业部ID */
  businessUnitId?: number;
  /** 父事业部ID */
  parentBuinessUnitId?: number;
  /** 事业部名称 */
  businessUnitName?: string;
  /** 排序码 */
  sortNum?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 事业部
 * @source POST /projectmanage/config-business-unit/edit [REQUEST]
 * @source POST /projectmanage/config-business-unit/add [REQUEST]
 */
export interface ConfigBusinessUnitEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 事业部ID */
  businessUnitId?: number;
  /** 父事业部ID */
  parentBuinessUnitId?: number;
  /** 事业部名称 */
  businessUnitName?: string;
  /** 排序码 */
  sortNum?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 事业部
 * @source POST /projectmanage/config-business-unit/delete [REQUEST]
 */
export interface ConfigBusinessUnitDeleteRequest {
  tenantId?: number;
  /** 事业部ID */
  businessUnitId?: number;
}

/**
 * 品牌配置
 * @source POST /projectmanage/config-brand/tree [REQUEST]
 * @source POST /projectmanage/config-brand/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-brand/list [REQUEST]
 */
export interface ConfigBrandQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 品牌ID,唯一表示 */
  brandId?: number;
  /** 父品牌ID */
  parentBrandId?: number;
  /** 品牌名称 */
  brandName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 品牌配置
 * @source POST /projectmanage/config-brand/edit [REQUEST]
 * @source POST /projectmanage/config-brand/add [REQUEST]
 */
export interface ConfigBrandEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 品牌ID,唯一表示 */
  brandId?: number;
  /** 父品牌ID */
  parentBrandId?: number;
  /** 品牌名称 */
  brandName?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 品牌配置
 * @source POST /projectmanage/config-brand/delete [REQUEST]
 */
export interface ConfigBrandDeleteRequest {
  tenantId?: number;
  /** 品牌ID,唯一表示 */
  brandId?: number;
}

/**
 * 品牌-产品线-车型配置
 * @source POST /projectmanage/config-brand-productline-model/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/config-brand-productline-model/list [REQUEST]
 */
export interface ConfigBrandProductlineModelQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键,唯一标识 */
  bpmConfigId?: number;
  /** 品牌ID */
  brandId?: number;
  /** 产品线ID */
  productLineId?: number;
  /** 车型ID */
  modelId?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 品牌-产品线-车型配置
 * @source POST /projectmanage/config-brand-productline-model/edit [REQUEST]
 * @source POST /projectmanage/config-brand-productline-model/add [REQUEST]
 */
export interface ConfigBrandProductlineModelEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键,唯一标识 */
  bpmConfigId?: number;
  /** 品牌ID */
  brandId?: number;
  /** 产品线ID */
  productLineId?: number;
  /** 车型ID */
  modelId?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 品牌-产品线-车型配置
 * @source POST /projectmanage/config-brand-productline-model/delete [REQUEST]
 */
export interface ConfigBrandProductlineModelDeleteRequest {
  tenantId?: number;
  /** 品牌ID */
  brandId?: number;
}

/**
 * 项目日历
 * @source POST /projectmanage/calendar/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /projectmanage/calendar/list [REQUEST]
 */
export interface CalendarQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 日历唯一标识符 */
  calendarId?: number;
  /** 是否默认日历 */
  defaultFlag?: string;
  /** 日历名称 */
  calendarName?: string;
  /** 关联的项目ID */
  projId?: number;
  /** 基准日历ID */
  baseClndrId?: number;
  /** 最后修改日期 */
  lastChngDate?: string;
  /** 日历类型 */
  clndrType?: string;
  /** 每日标准工时数 */
  dayHrCnt?: number;
  /** 每周标准工时数 */
  weekHrCnt?: number;
  /** 每月标准工时数 */
  monthHrCnt?: number;
  /** 每年标准工时数 */
  yearHrCnt?: number;
  /** 是否资源私有日历 */
  rsrcPrivate?: string;
  /** 日历详细数据 */
  clndrData?: Record<string, any>;
  /** 记录创建时间 */
  createTime?: string;
  /** 记录创建者 */
  createUser?: number;
  /** 记录最后更新时间 */
  updateTime?: string;
  /** 记录最后更新者 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 记录删除时间 */
  remark?: string;
}

/**
 * 项目日历
 * @source POST /projectmanage/calendar/edit [REQUEST]
 * @source POST /projectmanage/calendar/add [REQUEST]
 */
export interface CalendarEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 日历唯一标识符 */
  calendarId?: number;
  /** 是否默认日历 */
  defaultFlag?: string;
  /** 日历名称 */
  calendarName?: string;
  /** 关联的项目ID */
  projId?: number;
  /** 基准日历ID */
  baseClndrId?: number;
  /** 最后修改日期 */
  lastChngDate?: string;
  /** 日历类型 */
  clndrType?: string;
  /** 每日标准工时数 */
  dayHrCnt?: number;
  /** 每周标准工时数 */
  weekHrCnt?: number;
  /** 每月标准工时数 */
  monthHrCnt?: number;
  /** 每年标准工时数 */
  yearHrCnt?: number;
  /** 是否资源私有日历 */
  rsrcPrivate?: string;
  /** 日历详细数据 */
  clndrData?: Record<string, any>;
  /** 记录删除时间 */
  remark?: string;
}

/**
 * 项目日历
 * @source POST /projectmanage/calendar/delete [REQUEST]
 */
export interface CalendarDeleteRequest {
  tenantId?: number;
  /** 日历唯一标识符 */
  calendarId?: number;
}

/**
 * 流程定义
 * @source POST /process/definition/page/{pageSize}/{pageNum} [REQUEST]
 */
export interface ProcessDefinitionRequest {
  tenantId?: number;
  /** 流程定义key */
  key?: string;
  /** 流程定义名称 */
  name?: string;
  /** 版本 */
  version?: number;
  /** 最新版本 */
  latest?: boolean;
}

/**
 * @source POST /process/definition/model/validate [REQUEST]
 */
export interface JsonNode {}

/**
 * @source POST /process/definition/model/save/{modelId} [REQUEST]
 */
export interface MultiValueMapStringString {
  all?: Record<string, any>;
  empty?: boolean;
}

/**
 * 用户信息
 * @source POST /permission/sys-user/register [REQUEST]
 * @source POST /permission/sys-user/edit [REQUEST]
 * @source POST /permission/sys-user/add [REQUEST]
 */
export interface SysUserEditRequest {
  /** 租户id */
  tenantId?: number;
  /** 用户ID */
  userId?: number;
  /** 用户名 */
  userName?: string;
  /** 用户账号 */
  userAccount?: string;
  /** 密码 */
  userPassword?: string;
  /** 电话 */
  telephone?: string;
  /** email */
  userEmail?: string;
  /** 性别 */
  sex?: string;
  /** 部门ID */
  deptId?: number;
  /** 访问类型(1 普通用户 2 管理员 3 外部用户) */
  accessType?: number;
  /** 备注 */
  userDesc?: string;
  /** 管理员备注 */
  remark?: string;
}

/**
 * 用户信息
 * @source POST /permission/sys-user/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-user/list [REQUEST]
 * @source POST /permission/sys-user/export [REQUEST]
 * @source POST /permission/sys-role/page/user/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-role/list/user [REQUEST]
 * @source POST /permission/sys-permission/page/user/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-permission/list/user [REQUEST]
 * @source POST /permission/sys-dept/findDeptListByUser [REQUEST]
 */
export interface SysUserQueryRequest {
  /** 租户id */
  tenantId?: number;
  /** 用户ID */
  userId?: number;
  /** 用户名 */
  userName?: string;
  /** 用户账号 */
  userAccount?: string;
  /** 密码 */
  userPassword?: string;
  /** 电话 */
  telephone?: string;
  /** email */
  userEmail?: string;
  /** 性别 */
  sex?: string;
  /** 部门ID */
  deptId?: number;
  /** 访问类型(1 普通用户 2 管理员 3 外部用户) */
  accessType?: number;
  /** 备注 */
  userDesc?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 管理员备注 */
  remark?: string;
}

/**
 * 角色信息
 * @source POST /permission/sys-user/page/role/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-user/list/role [REQUEST]
 * @source POST /permission/sys-role/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-role/list [REQUEST]
 * @source POST /permission/sys-permission/page/role/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-permission/list/role [REQUEST]
 */
export interface SysRoleQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 角色ID */
  roleId?: number;
  /** 父角色ID */
  parentRoleId?: number;
  /** 角色编码 */
  roleCode?: string;
  /** 角色名称 */
  roleName?: string;
  /** 显示顺序 */
  roleSort?: number;
  /** 角色状态 */
  status?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 成员数量 */
  memberCount?: number;
}

/**
 * 部门
 * @source POST /permission/sys-user/page/dept/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-user/list/dept [REQUEST]
 * @source POST /permission/sys-dept/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-dept/list [REQUEST]
 */
export interface SysDeptQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 部门id */
  deptId?: number;
  /** 部门编码 */
  deptNo?: string;
  /** 父部门id */
  parentId?: number;
  /** 所属组织ID */
  organizationId?: number;
  /** 祖级列表 */
  ancestors?: string;
  /** 部门名称 */
  deptName?: string;
  /** 显示顺序 */
  orderNum?: number;
  /** 负责人 */
  leader?: string;
  /** 联系电话 */
  phone?: string;
  /** 邮箱 */
  email?: string;
  /** 部门状态 */
  status?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
}

/**
 * 用户信息
 * @source POST /permission/sys-user/delete [REQUEST]
 */
export interface SysUserDeleteRequest {
  tenantId?: number;
  /** 用户ID */
  userId?: number;
}

/**
 * 修改密码
 * @source POST /permission/sys-user/change-password [REQUEST]
 */
export interface ChangePasswordRequest {
  /** 旧密码 */
  oldPassword: string;
  /** 新密码 */
  newPassword: string;
  /** 确认密码 */
  confirmPassword: string;
}

/**
 * 用户和角色关联
 * @source POST /permission/sys-user-role/delete [REQUEST]
 */
export interface SysUserRoleDeleteRequest {
  tenantId?: number;
  /** 用户ID */
  userId?: number;
  /** 角色ID */
  roleId?: number;
}

/**
 * 用户和角色关联
 * @source POST /permission/sys-user-role/add [REQUEST]
 */
export interface SysUserRoleEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 用户ID */
  userId?: number;
  /** 角色ID */
  roleId?: number;
}

/**
 * 角色权限关联表
 * @source POST /permission/sys-user-data-permission/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-user-data-permission/list [REQUEST]
 */
export interface SysUserDataPermissionQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 用户权限ID,主键 */
  userPermissionId?: number;
  /** 用户ID */
  userId?: number;
  /** 数据类型(project,dept,projectPlan等) */
  dataType?: string;
  /** 业务数据ID */
  dataId?: string;
  /** 全部授权标识(1是,0否) */
  allFlag?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 修改时间 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
}

/**
 * 角色权限关联表
 * @source POST /permission/sys-user-data-permission/edit [REQUEST]
 * @source POST /permission/sys-user-data-permission/add [REQUEST]
 */
export interface SysUserDataPermissionEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 用户权限ID,主键 */
  userPermissionId?: number;
  /** 用户ID */
  userId?: number;
  /** 数据类型(project,dept,projectPlan等) */
  dataType?: string;
  /** 业务数据ID */
  dataId?: string;
  /** 全部授权标识(1是,0否) */
  allFlag?: number;
  /** 修改时间 */
  remark?: string;
}

/**
 * 角色权限关联表
 * @source POST /permission/sys-user-data-permission/delete [REQUEST]
 */
export interface SysUserDataPermissionDeleteRequest {
  tenantId?: number;
  /** 用户权限ID,主键 */
  userPermissionId?: number;
}

/**
 * 权限
 * @source POST /permission/sys-role/page/permission/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-role/list/permission [REQUEST]
 * @source POST /permission/sys-permission/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-permission/list [REQUEST]
 */
export interface SysPermissionQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 权限ID,唯一标识 */
  permissionId?: number;
  /** 父权限ID */
  parentPermissionId?: number;
  /** 权限编码 */
  permissionCode?: string;
  /** 权限名称 */
  permissionName?: string;
  /** 权限描述 */
  description?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 角色Id集合 */
  roleIds?: number[];
}

/**
 * 角色信息
 * @source POST /permission/sys-role/edit [REQUEST]
 * @source POST /permission/sys-role/add [REQUEST]
 */
export interface SysRoleEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 角色ID */
  roleId?: number;
  /** 父角色ID */
  parentRoleId?: number;
  /** 角色编码 */
  roleCode?: string;
  /** 角色名称 */
  roleName?: string;
  /** 显示顺序 */
  roleSort?: number;
  /** 角色状态 */
  status?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 角色信息
 * @source POST /permission/sys-role/delete [REQUEST]
 */
export interface SysRoleDeleteRequest {
  tenantId?: number;
  /** 角色ID */
  roleId?: number;
}

/**
 * 角色权限关联
 * @source POST /permission/sys-role-permission/delete [REQUEST]
 */
export interface SysRolePermissionDeleteRequest {
  tenantId?: number;
  /** 角色ID */
  roleId?: number;
  /** 权限ID */
  permissionId?: number;
}

/**
 * 角色权限关联
 * @source POST /permission/sys-role-permission/add [REQUEST]
 */
export interface SysRolePermissionEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键 */
  rolePermissionId?: number;
  /** 角色ID */
  roleId?: number;
  /** 权限ID */
  permissionId?: number;
  /** 修改时间 */
  remark?: string;
}

/**
 * 角色和菜单关联
 * @source POST /permission/sys-role-menu/delete [REQUEST]
 */
export interface SysRoleMenuDeleteRequest {
  tenantId?: number;
  /** 角色ID */
  roleId?: number;
  /** 菜单ID */
  menuId?: number;
}

/**
 * 角色和菜单关联
 * @source POST /permission/sys-role-menu/add [REQUEST]
 */
export interface SysRoleMenuEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 角色ID */
  roleId?: number;
  /** 菜单ID */
  menuId?: number;
}

/**
 * 权限
 * @source POST /permission/sys-permission/edit [REQUEST]
 * @source POST /permission/sys-permission/add [REQUEST]
 */
export interface SysPermissionEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 权限ID,唯一标识 */
  permissionId?: number;
  /** 父权限ID */
  parentPermissionId?: number;
  /** 权限编码 */
  permissionCode?: string;
  /** 权限名称 */
  permissionName?: string;
  /** 权限描述 */
  description?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 权限
 * @source POST /permission/sys-permission/delete [REQUEST]
 */
export interface SysPermissionDeleteRequest {
  tenantId?: number;
  /** 权限ID,唯一标识 */
  permissionId?: number;
}

/**
 * 系统操作日志
 * @source POST /permission/sys-oper-log/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-oper-log/list [REQUEST]
 */
export interface SysOperLogQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键,唯一标识 */
  id?: number;
  /** 用户ID */
  userId?: number;
  /** 操作模块 */
  module?: string;
  /** 操作类型 */
  type?: string;
  /** 操作描述 */
  description?: string;
  /** 请求参数 */
  requestParam?: string;
  /** 响应结果 */
  responseResult?: string;
  /** 操作时间 */
  operTime?: string;
  /** 耗时 */
  costTime?: number;
  /** IP地址 */
  ipAddress?: string;
  /** 操作状态 */
  status?: number;
  /** 创建人 */
  createUser?: number;
  /** 错误信息 */
  errorMsg?: string;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * 系统操作日志
 * @source POST /permission/sys-oper-log/edit [REQUEST]
 * @source POST /permission/sys-oper-log/add [REQUEST]
 */
export interface SysOperLogEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键,唯一标识 */
  id?: number;
  /** 用户ID */
  userId?: number;
  /** 操作模块 */
  module?: string;
  /** 操作类型 */
  type?: string;
  /** 操作描述 */
  description?: string;
  /** 请求参数 */
  requestParam?: string;
  /** 响应结果 */
  responseResult?: string;
  /** 操作时间 */
  operTime?: string;
  /** 耗时 */
  costTime?: number;
  /** IP地址 */
  ipAddress?: string;
  /** 操作状态 */
  status?: number;
  /** 错误信息 */
  errorMsg?: string;
}

/**
 * 系统操作日志
 * @source POST /permission/sys-oper-log/delete [REQUEST]
 */
export interface SysOperLogDeleteRequest {
  tenantId?: number;
  /** 主键,唯一标识 */
  id?: number;
}

/**
 * 菜单权限
 * @source POST /permission/sys-menu/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-menu/list [REQUEST]
 */
export interface SysMenuQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 菜单ID */
  menuId?: number;
  /** 菜单名称 */
  menuName?: string;
  /** 父菜单ID */
  parentMenuId?: number;
  /** 显示顺序 */
  orderNum?: number;
  /** 路由地址 */
  path?: string;
  /** 组件路径 */
  component?: string;
  /** 路由参数 */
  query?: string;
  /** 路由名称 */
  routeName?: string;
  /** 是否为外链 */
  isFrame?: number;
  /** 是否缓存 */
  isCache?: number;
  /** 菜单类型 */
  menuType?: string;
  /** 菜单状态 */
  visible?: string;
  /** 菜单状态 */
  status?: number;
  /** 权限标识 */
  perms?: string;
  /** 菜单图标 */
  icon?: string;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** url */
  url?: string;
  /** 是否刷新 */
  isRefresh?: number;
  /** 打开方式 */
  target?: string;
}

/**
 * 菜单权限
 * @source POST /permission/sys-menu/edit [REQUEST]
 * @source POST /permission/sys-menu/add [REQUEST]
 */
export interface SysMenuEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 菜单ID */
  menuId?: number;
  /** 菜单名称 */
  menuName?: string;
  /** 父菜单ID */
  parentMenuId?: number;
  /** 显示顺序 */
  orderNum?: number;
  /** 路由地址 */
  path?: string;
  /** 组件路径 */
  component?: string;
  /** 路由参数 */
  query?: string;
  /** 路由名称 */
  routeName?: string;
  /** 是否为外链 */
  isFrame?: number;
  /** 是否缓存 */
  isCache?: number;
  /** 菜单类型 */
  menuType?: string;
  /** 菜单状态 */
  visible?: string;
  /** 菜单状态 */
  status?: number;
  /** 权限标识 */
  perms?: string;
  /** 菜单图标 */
  icon?: string;
  /** 备注 */
  remark?: string;
  /** url */
  url?: string;
  /** 是否刷新 */
  isRefresh?: number;
  /** 打开方式 */
  target?: string;
}

/**
 * 菜单权限
 * @source POST /permission/sys-menu/delete [REQUEST]
 */
export interface SysMenuDeleteRequest {
  tenantId?: number;
  /** 菜单ID */
  menuId?: number;
}

/**
 * 系统登录日志
 * @source POST /permission/sys-login-log/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-login-log/list [REQUEST]
 */
export interface SysLoginLogQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键,唯一标识 */
  id?: number;
  /** 用户ID */
  userId?: number;
  /** 登录时间 */
  loginTime?: string;
  /** IP地址 */
  ipAddress?: string;
  /** 用户代理 */
  userAgent?: string;
  /** 登录状态 */
  loginStatus?: number;
  /** 失败原因 */
  failureReason?: string;
}

/**
 * 系统登录日志
 * @source POST /permission/sys-login-log/edit [REQUEST]
 * @source POST /permission/sys-login-log/add [REQUEST]
 */
export interface SysLoginLogEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键,唯一标识 */
  id?: number;
  /** 用户ID */
  userId?: number;
  /** 登录时间 */
  loginTime?: string;
  /** IP地址 */
  ipAddress?: string;
  /** 用户代理 */
  userAgent?: string;
  /** 登录状态 */
  loginStatus?: number;
  /** 失败原因 */
  failureReason?: string;
}

/**
 * 系统登录日志
 * @source POST /permission/sys-login-log/delete [REQUEST]
 */
export interface SysLoginLogDeleteRequest {
  tenantId?: number;
  /** 主键,唯一标识 */
  id?: number;
}

/**
 * 国际化
 * @source POST /permission/sys-i18n-message/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-i18n-message/list [REQUEST]
 */
export interface SysI18nMessageQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 消息ID */
  messageId?: number;
  /** 模块(common,permission等) */
  moduleName?: string;
  /** 消息键名，用于唯一标识消息内容 */
  messageKey?: string;
  /** 语言代码，如en-US、zh-CN等 */
  languageCode?: string;
  /** 消息内容，支持占位符 */
  i18nMessage?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 国际化
 * @source POST /permission/sys-i18n-message/edit [REQUEST]
 * @source POST /permission/sys-i18n-message/add [REQUEST]
 */
export interface SysI18nMessageEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 消息ID */
  messageId?: number;
  /** 模块(common,permission等) */
  moduleName?: string;
  /** 消息键名，用于唯一标识消息内容 */
  messageKey?: string;
  /** 语言代码，如en-US、zh-CN等 */
  languageCode?: string;
  /** 消息内容，支持占位符 */
  i18nMessage?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 国际化
 * @source POST /permission/sys-i18n-message/delete [REQUEST]
 */
export interface SysI18nMessageDeleteRequest {
  tenantId?: number;
  /** 消息ID */
  messageId?: number;
}

/**
 * 维度类型
 * @source POST /permission/sys-dimension-type/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-dimension-type/list [REQUEST]
 */
export interface SysDimensionTypeQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 维度类型ID */
  typeId?: number;
  /** 维度类型编码 (DEPT, REGION, PROJECT)等 */
  typeCode?: string;
  /** 维度类型名称 */
  typeName?: string;
  /** 维度查询接口地址 */
  url?: string;
  /** 是否有效1 有效 0 失效 */
  status?: number;
  /** 维度类型描述 */
  typeDesc?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
}

/**
 * 维度类型
 * @source POST /permission/sys-dimension-type/edit [REQUEST]
 * @source POST /permission/sys-dimension-type/add [REQUEST]
 */
export interface SysDimensionTypeEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 维度类型ID */
  typeId?: number;
  /** 维度类型编码 (DEPT, REGION, PROJECT)等 */
  typeCode?: string;
  /** 维度类型名称 */
  typeName?: string;
  /** 维度查询接口地址 */
  url?: string;
  /** 是否有效1 有效 0 失效 */
  status?: number;
  /** 维度类型描述 */
  typeDesc?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 维度类型
 * @source POST /permission/sys-dimension-type/delete [REQUEST]
 */
export interface SysDimensionTypeDeleteRequest {
  tenantId?: number;
  /** 维度类型ID */
  typeId?: number;
}

/**
 * 数据权限
 * @source POST /permission/sys-dimension-scope/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-dimension-scope/list [REQUEST]
 */
export interface SysDimensionScopeQueryRequest {
  /** 租户id */
  tenantId?: number;
  /** ID */
  scopeId?: number;
  /** 维度类型ID */
  typeId?: number;
  /** 授权对象类型(1 用户,2 角色) */
  targetType?: number;
  /** 授权对象ID,如用户id,角色id等 */
  targetId?: number;
  /** 数据范围值,如部门id,项目id等 */
  scopeValue?: string;
  /** 父数据范围值(如父部门id,父项目id) */
  parentScopeValue?: string;
  /** 是否有效(1 有效 0 失效) */
  status?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
}

/**
 * 数据权限
 * @source POST /permission/sys-dimension-scope/edit [REQUEST]
 * @source POST /permission/sys-dimension-scope/add [REQUEST]
 */
export interface SysDimensionScopeEditRequest {
  /** 租户id */
  tenantId?: number;
  /** ID */
  scopeId?: number;
  /** 维度类型ID */
  typeId?: number;
  /** 授权对象类型(1 用户,2 角色) */
  targetType?: number;
  /** 授权对象ID,如用户id,角色id等 */
  targetId?: number;
  /** 数据范围值,如部门id,项目id等 */
  scopeValue?: string;
  /** 父数据范围值(如父部门id,父项目id) */
  parentScopeValue?: string;
  /** 是否有效(1 有效 0 失效) */
  status?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 数据权限
 * @source POST /permission/sys-dimension-scope/delete [REQUEST]
 */
export interface SysDimensionScopeDeleteRequest {
  tenantId?: number;
  /** ID */
  scopeId?: number;
}

/**
 * 部门
 * @source POST /permission/sys-dept/edit [REQUEST]
 * @source POST /permission/sys-dept/add [REQUEST]
 */
export interface SysDeptEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 部门id */
  deptId?: number;
  /** 部门编码 */
  deptNo?: string;
  /** 父部门id */
  parentId?: number;
  /** 所属组织ID */
  organizationId?: number;
  /** 祖级列表 */
  ancestors?: string;
  /** 部门名称 */
  deptName?: string;
  /** 显示顺序 */
  orderNum?: number;
  /** 负责人 */
  leader?: string;
  /** 联系电话 */
  phone?: string;
  /** 邮箱 */
  email?: string;
  /** 部门状态 */
  status?: number;
}

/**
 * 部门
 * @source POST /permission/sys-dept/delete [REQUEST]
 */
export interface SysDeptDeleteRequest {
  tenantId?: number;
  /** 部门id */
  deptId?: number;
}

/**
 * 合作伙伴信息
 * @source POST /permission/sys-business-partner/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /permission/sys-business-partner/list [REQUEST]
 */
export interface SysBusinessPartnerQueryRequest {
  /** 租户id */
  tenantId?: number;
  /** ID */
  partnerId?: number;
  /** 合作伙伴名称 */
  partnerName?: string;
  /** 合作伙伴类型,来源数据字典 */
  partnerType?: number;
  /** 合作伙伴性质,来源数据字典 */
  shipType?: number;
  /** 统一社会信用代码 */
  esccCode?: string;
  /** 企业邮箱 */
  enterpriseEmail?: string;
  /** 注册地址 */
  registeAddress?: string;
  /** 联系人姓名 */
  liaisonName?: string;
  /** 联系人电话 */
  liaisonPhone?: string;
  /** 联系人邮箱 */
  liaisonEmail?: string;
  /** 联系人通讯地址 */
  liaisonAddress?: string;
  /** 是否有效1 有效 0 失效 */
  status?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
}

/**
 * 合作伙伴信息
 * @source POST /permission/sys-business-partner/edit [REQUEST]
 * @source POST /permission/sys-business-partner/add [REQUEST]
 */
export interface SysBusinessPartnerEditRequest {
  /** 租户id */
  tenantId?: number;
  /** ID */
  partnerId?: number;
  /** 合作伙伴名称 */
  partnerName?: string;
  /** 合作伙伴类型,来源数据字典 */
  partnerType?: number;
  /** 合作伙伴性质,来源数据字典 */
  shipType?: number;
  /** 统一社会信用代码 */
  esccCode?: string;
  /** 企业邮箱 */
  enterpriseEmail?: string;
  /** 注册地址 */
  registeAddress?: string;
  /** 联系人姓名 */
  liaisonName?: string;
  /** 联系人电话 */
  liaisonPhone?: string;
  /** 联系人邮箱 */
  liaisonEmail?: string;
  /** 联系人通讯地址 */
  liaisonAddress?: string;
  /** 是否有效1 有效 0 失效 */
  status?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 合作伙伴信息
 * @source POST /permission/sys-business-partner/delete [REQUEST]
 */
export interface SysBusinessPartnerDeleteRequest {
  tenantId?: number;
  /** ID */
  partnerId?: number;
}

/**
 * 登录入参
 * @source POST /permission/login [REQUEST]
 */
export interface LoginRequest {
  tenantId?: number;
  /** 用户名 */
  username: string;
  /** 用户密码 */
  password: string;
}

/**
 * 字典类型
 * @source POST /basic/sys-dict-type/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /basic/sys-dict-type/list [REQUEST]
 */
export interface SysDictTypeQueryRequest {
  tenantId?: number;
  /** 字典名称 */
  dictName?: string;
  /** 字典类型 */
  dictType?: string;
  /** 状态 */
  status?: number;
}

/**
 * 字典类型
 * @source POST /basic/sys-dict-type/edit [REQUEST]
 * @source POST /basic/sys-dict-type/add [REQUEST]
 */
export interface SysDictTypeEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 字典主键 */
  dictId?: number;
  /** 字典名称 */
  dictName?: string;
  /** 字典类型 */
  dictType?: string;
  /** 状态 */
  status?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 字典类型
 * @source POST /basic/sys-dict-type/delete [REQUEST]
 */
export interface SysDictTypeDeleteRequest {
  tenantId?: number;
  /** 字典主键 */
  dictId?: number;
}

/**
 * 字典数据
 * @source POST /basic/sys-dict-data/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /basic/sys-dict-data/list [REQUEST]
 */
export interface SysDictDataQueryRequest {
  tenantId?: number;
  /** 字典类型 */
  dictType?: string;
}

/**
 * 字典数据
 * @source POST /basic/sys-dict-data/edit [REQUEST]
 * @source POST /basic/sys-dict-data/add [REQUEST]
 */
export interface SysDictDataEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 字典编码 */
  dictCode?: number;
  /** 字典排序 */
  sortNum?: number;
  /** 字典标签 */
  dictLabel?: string;
  /** 父字典值 */
  parentDictValue?: string;
  /** 字典键值 */
  dictValue?: string;
  /** 字典类型 */
  dictType?: string;
  /** 样式属性 */
  cssClass?: string;
  /** 表格回显样式 */
  listClass?: string;
  /** 是否默认 */
  isDefault?: string;
  /** 状态 */
  status?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 字典数据
 * @source POST /basic/sys-dict-data/delete [REQUEST]
 */
export interface SysDictDataDeleteRequest {
  tenantId?: number;
  /** 字典编码 */
  dictCode?: number;
}

/**
 * 文件
 * @source POST /basic/sys-attachment/delete [REQUEST]
 * @source POST /basic/cos/delete [REQUEST]
 */
export interface SysAttachmentDeleteRequest {
  tenantId?: number;
  /** 附件id */
  docId?: number;
  /** 文件唯一标识 */
  docKey?: string;
}

/**
 * 组织
 * @source POST /basic/organization/tree [REQUEST]
 * @source POST /basic/organization/page/{pageSize}/{pageNum} [REQUEST]
 * @source POST /basic/organization/list [REQUEST]
 */
export interface OrganizationQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 组织ID */
  organizationId?: number;
  /** 父组织ID，NULL表示根组织 */
  parentOrganizationId?: number;
  /** 是否为部门表示(1是,0否) */
  deptFlag?: number;
  /** 组织编码 */
  orgCode?: string;
  /** 组织名称 */
  orgName?: string;
  /** 组织描述 */
  orgDesc?: string;
  /** 组织状态 */
  status?: number;
  /** 排序码 */
  sortNum?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 组织
 * @source POST /basic/organization/edit [REQUEST]
 * @source POST /basic/organization/add [REQUEST]
 */
export interface OrganizationEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 组织ID */
  organizationId?: number;
  /** 父组织ID，NULL表示根组织 */
  parentOrganizationId?: number;
  /** 是否为部门表示(1是,0否) */
  deptFlag?: number;
  /** 组织编码 */
  orgCode?: string;
  /** 组织名称 */
  orgName?: string;
  /** 组织描述 */
  orgDesc?: string;
  /** 组织状态 */
  status?: number;
  /** 排序码 */
  sortNum?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 组织
 * @source POST /basic/organization/delete [REQUEST]
 */
export interface OrganizationDeleteRequest {
  tenantId?: number;
  /** 组织ID */
  organizationId?: number;
}

/**
 * 文件
 * @source POST /basic/cos/download [REQUEST]
 */
export interface SysAttachmentQueryRequest {
  /** 租户id */
  tenantId?: number;
  /** 附件id */
  attaId?: number;
  /** 附件名 */
  attaName?: string;
  /** 附件标识 */
  attaKey?: string;
  /** 附件地址 */
  attaPath?: string;
  /** 大小 */
  attaSize?: number;
  /** 附件格式 */
  attaType?: string;
  /** 请求id */
  requestId?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 最后修改时间 */
  updateTime?: string;
  /** 创建人姓名 */
  createUserName?: string;
  /** 修改人姓名 */
  updateUserName?: string;
  /** 查询类型 */
  queryType?: string;
}

/**
 * 响应基类
 */
export interface ApiResult {
  code?: number;
  /** 消息 */
  message?: string;
  /** 时间戳 */
  timestamp?: number;
  /** 返回数据对象 */
  data?: Record<string, any>;
}

/**
 * 技术能力
 */
export interface TechnicalSkillsResponse {
  /** 技术能力ID */
  skillId?: number;
  /** 父技术能力ID */
  parentId?: number;
  /** 技术能力编码 */
  skillCode?: string;
  /** 技术能力名称 */
  skillName?: string;
  /** 技术能力描述 */
  skillDesc?: string;
  /** 技术能力状态 */
  status?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 计划模板信息
 */
export interface TeamTemplateInfoResponse {
  /** 计划模板ID */
  infoId?: number;
  /** 计划模板编号 */
  templateCode?: string;
  /** 计划模板名称 */
  templateName?: string;
  /** 项目类型 */
  projectType?: string;
  /** 项目级别 */
  projectLevel?: string;
  /** 项目分类 */
  projectCategory?: string;
  /** 品牌 */
  brand?: string;
  /** 产品线 */
  productLine?: string;
  /** 车型类别 */
  vehicleCategory?: string;
  /** 技术平台 */
  technicalPlatform?: string;
  /** 能源形式 */
  energyForm?: string;
  /** 销售区域 */
  salesArea?: string;
  /** 计划模板说明 */
  templateDescription?: string;
  /** 负责人ID */
  owner?: number;
  /** 来源项目编号 */
  srcProjectCode?: string;
  /** 来源项目名称 */
  srcProjectName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 任务变更记录
 */
export interface TaskChangeLogResponse {
  /** 自增主键 */
  taskChangeLogId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 项目任务风险ID,风险回答 */
  taskId?: number;
  /** 是否为风险回答(1是,0 否) */
  projTaskFlag?: number;
  /** 变更原因 */
  changeReason?: string;
  /** 变更内容 */
  changeContent?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 资源池
 */
export interface ResourcePoolResponse {
  /** 资源ID */
  resourceId?: number;
  /** 部门ID */
  departmentId?: number;
  /** 系统ID */
  systemId?: number;
  /** 技术能力ID */
  skillId?: number;
  /** 人员ID */
  personnelId?: number;
  /** 岗位ID */
  positionId?: number;
  /** 设备ID */
  equipmentId?: number;
  /** 资源类型(1 人力资源,2 设备资源) */
  resourceType?: number;
  /** 标准工时 */
  standardHours?: number;
  /** 饱和度 */
  saturation?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 资源调拨
 */
export interface ResourceAllocationResponse {
  /** 调拨ID */
  allocationId?: number;
  /** 资源ID */
  resourceId?: number;
  /** 调拨工时 */
  allocatedHours?: number;
  /** 调拨日期 */
  allocationDate?: string;
  /** 调拨状态(1带确认,2 已确认,3 取消) */
  status?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 项目成员
 */
export interface ProjectMemberResponse {
  /** ID */
  memberId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 计划模版团队ID */
  teamId?: number;
  /** 数据类型(1 是项目成员数据 2 是模版定义的成员数据) */
  dataType?: number;
  /** 项目ID */
  projectId?: number;
  /** 用户ID */
  userId?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 最后修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
}

/**
 * 项目
 */
export interface ProjectResponse {
  /** 项目ID，唯一标识 */
  projId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 项目组合ID */
  portfolioId?: number;
  /** 项目集ID */
  programId?: number;
  /** 项目编码 */
  projCode?: string;
  /** 父项目ID */
  parentProjId?: number;
  /** 项目名称 */
  projName?: string;
  /** 项目类型 */
  projType?: number;
  /** 项目级别 */
  projLevel?: number;
  /** 项目分类 */
  projCategory?: number;
  /** 计划开始时间 */
  planStartDate?: string;
  /** 计划完成时间 */
  planFinishDate?: string;
  /** 实际完成时间 */
  actualFinishDate?: string;
  /** 实际开始时间 */
  actualStartDate?: string;
  /** 超期天数 */
  overdueDays?: number;
  /** 实际进度百分比 */
  actualProgress?: number;
  /** 计划进度百分比 */
  planProgress?: number;
  /** 项目状态 */
  status?: number;
  /** 责任人 */
  responsible?: number;
  /** 所属部门 */
  deptId?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新人 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 账户ID */
  acctId?: number;
  /** 原始项目ID */
  origProjId?: number;
  /** 来源项目ID */
  sourceProjId?: number;
  /** 项目成员列表 */
  members?: ProjectMemberResponse[];
  /** 责任人姓名 */
  responsibleName?: string;
  /** 所属部门名称 */
  deptName?: string;
  /** 子项目列表 */
  children?: ProjectResponse[];
}

/**
 * 排序入参
 */
export interface SortRequest {
  /** 字段名 */
  field?: string;
  /** direction(asc/desc) */
  direction?: string;
}

/**
 * 项目干系人
 */
export interface StakeholderAddRequest {
  tenantId?: number;
  /** 用户ID(关联用户表) */
  userId: number;
}

/**
 * 项目任务过程文档表
 */
export interface ProjectTaskProcessDocAddRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  projTaskProcessDocId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentProjTaskProcessDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 任务标签
 */
export interface ConfigIndeTaskTagResponse {
  /** 任务标签ID */
  taskTagId?: number;
  /** 父标签ID或者所属标签组ID */
  parentTaskTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag?: number;
  /** 任务标签标题 */
  taskTagName?: string;
  /** 任务标签描述 */
  taskTagDesc?: string;
  /** 任务标签颜色 */
  taskTagColor?: string;
  /** 任务标签状态 (1 启用,0 禁用) */
  taskTagStatus?: number;
  /** 排序码 */
  sortNum?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 参与人响应对象
 */
export interface ParticipantResponse {
  /** 参与人ID */
  userId?: number;
  /** 参与人姓名 */
  userName?: string;
}

/**
 * 项目任务前置
 */
export interface ProjectTaskPredResponse {
  /** 主键 */
  taskPredId?: number;
  /** 项目任务id */
  projTaskId?: number;
  /** 前缀项目任务ID */
  predProjTaskId?: number;
  /** 项目ID */
  projId?: number;
  /** 前缀任务所属的计划ID */
  predPlanId?: number;
  /** 前缀任务所属的项目ID */
  predProjId?: number;
  /** 前置类型(4中关系:SF,FF,FS,SS) */
  predType?: number;
  /** 延迟小时数 */
  lagHrCnt?: number;
  /** 是否强制约束(0 否 1 是) */
  isForce?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 项目任务
 */
export interface ProjectTaskResponse {
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务父任务ID */
  parentProjTaskId?: number;
  /** 所属项目ID */
  projId?: number;
  /** 阶段ID */
  phaseProjTaskId?: number;
  /** 项目任务名称 */
  projTaskName?: string;
  /** 项目任务状态(1 未开始,2 进行中,3 已关闭) */
  projTaskStatus?: number;
  /** 项目任务责任人 */
  projTaskResponsible?: number;
  /** 项目任务责任人项目角色 */
  projTaskResponsibleProjRole?: string;
  /** 项目任务责任部门 */
  projTaskResponsibleDept?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业,级联关系待定) */
  taskType?: number;
  /** 任务作业类型(级联关系待定) */
  taskActivityType?: number;
  /** 专业 */
  major?: string;
  /** 系统 */
  systemId?: number;
  /** 项目任务说明 */
  projTaskDesc?: string;
  /** 项目任务验收标准 */
  projTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 项目任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是非阶段(1是,0否) */
  phaseFlag?: number;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 任务标签列表 */
  tags?: ConfigIndeTaskTagResponse[];
  /** 参与人列表 */
  participants?: ParticipantResponse[];
  /** 附件列表 */
  attaments?: SysAttachmentResponse[];
  /** 前置任务列表 */
  predTasks?: ProjectTaskPredResponse[];
  /** 责任人姓名 */
  responsibleName?: string;
  /** 项目名称 */
  projectName?: string;
}

/**
 * 文件
 */
export interface SysAttachmentResponse {
  /** 附件id */
  docId?: number;
  /** 租户id */
  tenantId?: number;
  /** 附件名 */
  docName?: string;
  /** 附件标识 */
  docKey?: string;
  /** 附件地址 */
  docPath?: string;
  /** 大小 */
  docSize?: number;
  /** 附件格式 */
  docType?: string;
  /** 请求id */
  requestId?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 最后修改时间 */
  updateTime?: string;
  /** 创建人姓名 */
  createUserName?: string;
  /** 修改人姓名 */
  updateUserName?: string;
}

/**
 * 项目任务前置
 */
export interface ProjectTaskPredEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 主键 */
  taskPredId?: number;
  /** 项目任务id */
  projTaskId?: number;
  /** 前缀项目任务ID */
  predProjTaskId?: number;
  /** 项目ID */
  projId?: number;
  /** 前缀任务所属的计划ID */
  predPlanId?: number;
  /** 前缀任务所属的项目ID */
  predProjId?: number;
  /** 前置类型(4中关系:SF,FF,FS,SS) */
  predType?: number;
  /** 延迟小时数 */
  lagHrCnt?: number;
  /** 是否强制约束(0 否 1 是) */
  isForce?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 文件
 */
export interface SysAttachmentEditRequest {
  /** 租户id */
  tenantId?: number;
  /** 附件id */
  docId?: number;
  /** 附件名 */
  docName?: string;
  /** 附件标识 */
  docKey?: string;
  /** 附件地址 */
  docPath?: string;
  /** 大小 */
  docSize?: number;
  /** 附件格式 */
  docType?: string;
  /** 请求id */
  requestId?: string;
}

/**
 * 项目任务变更参与人入参对象
 */
export interface ProjectTaskParticipantChangeRequest {
  tenantId?: number;
  /** 主键ID */
  holderId?: number;
  /** 用户ID(关联用户表) */
  userId: number;
}

/**
 * 项目任务风险表
 */
export interface ProjectTaskRiskResponse {
  /** 项目任务风险ID */
  projTaskRiskId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务风险名称 */
  projTaskRiskName?: string;
  /** 项目任务风险状态(1未开始, 2进行中,3已完成,4已关闭) */
  projTaskRiskStatus?: number;
  /** 风险分类 */
  projTaskRiskType?: number;
  /** 风险级别 (1高2中3低) */
  projTaskRiskLevel?: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 完成日期 */
  completionDate?: string;
  /** 项目任务风险负责人ID */
  projTaskResponsible?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 项目任务风险,风险回答附件新增入参
 */
export interface ProjectTaskRiskDocAddRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务风险ID,风险回答 */
  projTaskRiskId?: number;
  /** 是否为风险回答(1是,0 否) */
  answerFlag?: number;
  /** 父文档ID,或文件夹ID */
  parentProjTaskRiskDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 项目任务汇报
 */
export interface ProjectTaskReportResponse {
  /** 项目任务汇报ID */
  projTaskReportId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务进度，范围0.00到100.00 */
  progress?: number;
  /** 汇报内容 */
  projTaskDesc?: string;
  /** 汇报时间 */
  reportTime?: string;
  /** 项目任务汇报状态(2 已接受,0 待澄清,1 已汇报) */
  projTaskReportStatus?: number;
  /** 项目任务汇报工作流定义ID */
  workflowId?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 附件列表 */
  attaments?: SysAttachmentResponse[];
}

/**
 * 项目任务进度汇报附件表
 */
export interface ProjectTaskReportDocEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  projTaskReportDocId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentProjTaskReportDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 项目任务过程文档表
 */
export interface ProjectTaskProcessDocResponse {
  /** 自增主键 */
  projTaskProcessDocId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentProjTaskProcessDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 大小 */
  docSize?: number;
  /** 附件格式 */
  docType?: string;
  /** 创建人姓名 */
  createUserName?: string;
  /** 更新人姓名 */
  updateUserName?: string;
}

/**
 * 项目任务问题
 */
export interface ProjectTaskIssueResponse {
  /** 项目任务问题ID */
  projTaskIssueId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务问题名称 */
  projTaskIssueName?: string;
  /** 项目任务问题状态(1未开始, 2进行中,3已完成,4已关闭) */
  projTaskIssueStatus?: number;
  /** 问题分类 */
  projTaskIssueType?: number;
  /** 问题级别 (1高2中3低) */
  projTaskIssueLevel?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 项目任务问题负责人ID */
  projTaskResponsible?: number;
  /** 完成日期 */
  completionDate?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 项目任务问题,问题回答附件新增入参
 */
export interface ProjectTaskIssueDocAddRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务问题ID,问题回答 */
  projTaskIssueId?: number;
  /** 是否为问题回答(1是,0 否) */
  answerFlag?: number;
  /** 父文档ID,或文件夹ID */
  parentProjTaskIssueDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 独立任务回答
 */
export interface ProjectTaskIssueAnswerResponse {
  /** 项目任务问题回答ID */
  projTaskIssueAnswerId?: number;
  /** 项目任务问题ID */
  projTaskIssueId?: number;
  /** 项目任务问题回答时间 */
  projTaskIssueAnswerTime?: string;
  /** 项目任务问题回答内容 */
  projTaskIssueAnswer?: string;
  /** 项目任务问题讨论 */
  projTaskIssueDisscution?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateTime?: string;
  /** 修改时间 */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 项目任务问题,问题回答附件表
 */
export interface ProjectTaskIssueDocEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  projTaskIssueDocId?: number;
  /** 项目任务问题ID,问题回答 */
  projTaskIssueId?: number;
  /** 是否为问题回答(1是,0 否) */
  answerFlag?: number;
  /** 父文档ID,或文件夹ID */
  parentProjTaskIssueDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 项目任务交付文档表
 */
export interface ProjectTaskDeliveryDocResponse {
  /** 自增主键 */
  projTaskDeliveryDocId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentProjTaskDeliveryDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识(关联附件表) */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 大小 */
  docSize?: number;
  /** 附件格式 */
  docType?: string;
  /** 创建人姓名 */
  createUserName?: string;
  /** 更新人姓名 */
  updateUserName?: string;
}

/**
 * 系统
 */
export interface ProjectSystemResponse {
  /** 系统ID,唯一标识 */
  systemId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 父系统ID */
  parentSystemId?: number;
  /** 系统名 */
  systemName?: string;
  /** 排序码 */
  sortNum?: number;
  /** 系统描述 */
  systemDesc?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 最后修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 项目销售信息
 */
export interface ProjectPropertiesSalesResponse {
  /** 销售信息唯一标识符 */
  salesId?: number;
  /** 项目ID，关联项目基础信息表 */
  projId?: number;
  /** 市场区域(待定) */
  marketArea?: string;
  /** 市场类型(来源市场类型数据字典值,待定) */
  marketType?: string;
  /** 细分市场 */
  subMarket?: string;
  /** 目标用户群 */
  targetCustomerGroup?: string;
  /** 商品名 */
  productName?: string;
  /** 主销价格 */
  mainSalesPrice?: number;
  /** 价格区间 */
  priceRange?: string;
  /** 销售主体 */
  salesEntity?: string;
  /** 出口方式 */
  exportMethod?: string;
  /** 周期内销售目标 */
  salesTargetPeriod?: number;
  /** 分年度销售目标 */
  salesTargetAnnual?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateTime?: string;
  /** 修改时间 */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 项目生产地信息
 */
export interface ProjectPropertiesProductionResponse {
  /** 生产地信息唯一标识符 */
  productionId?: number;
  /** 项目ID，关联项目基础信息表 */
  projId?: number;
  /** 生成信息描述(区域,基地,厂区 关系出到关系表 方案 待定) */
  productionDesc?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateTime?: string;
  /** 修改时间 */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 项目基础信息
 */
export interface ProjectPropertiesInfoResponse {
  /** 项目唯一标识符 */
  projId?: number;
  /** 项目名称 */
  projName?: string;
  /** 项目类型(数据字典取值) */
  projType?: number;
  /** 项目级别(数据字典取值) */
  projLevel?: string;
  /** 项目分类(数据字典取值) */
  projCategory?: string;
  /** 主项目ID，用于关联子项目 */
  mainProjId?: number;
  /** 开发代码 */
  developmentCode?: string;
  /** 品牌ID (有级联关系) */
  brandId?: number;
  /** 产品线ID(有级联关系) */
  productLineId?: number;
  /** 所属事业部 */
  businessUnit?: string;
  /** 是否公共投资 */
  publicInvestmentFlag?: string;
  /** 是否新项目 */
  newProjFlag?: string;
  /** 创建人 */
  createUser?: number;
  /** 记录创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 记录最后更新时间 */
  updateTime?: string;
  /** 备注 */
  remarks?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 项目财务信息
 */
export interface ProjectPropertiesFinancialResponse {
  /** 财务信息唯一标识符 */
  financialId?: number;
  /** 项目ID，关联项目基础信息表 */
  projId?: number;
  /** 研发主体所在国(来源国家数据字典值) */
  developmentCountry?: string;
  /** KO财务审批单ID */
  koFinancialApprovalId?: string;
  /** 是否含预研阶段费用 */
  includesPreResearchCosts?: number;
  /** 预研费用金额 */
  preResearchCosts?: number;
  /** 分项预算，详细描述各项预算内容 */
  budgetDetails?: string;
  /** 结项自动财务清算 */
  autoFinancialClearance?: number;
  /** 可延迟天数，用于财务清算的缓冲期 */
  allowableDelayDays?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateTime?: string;
  /** 修改时间 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 车型信息
 */
export interface ProjectPropVehicleResponse {
  /** 项目车型ID,唯一标识符 */
  projVehicleId?: number;
  /** 车型标签 */
  modelTag?: string;
  /** 项目ID */
  projId?: number;
  /** 车型类别ID */
  configModelTypeId?: string;
  /** 车型级别 */
  modelLevel?: string;
  /** 车型ID(待定) */
  modelTypeId?: number;
  /** 子车型ID(待定) */
  subModelTypeId?: string;
  /** 技术平台(来源技术平台数据字典值) */
  technicalPlatform?: string;
  /** 车身形式(来源于车身形式数据字典表,需要根据关联表做范围过滤) */
  bodyForm?: string;
  /** 能源形式(来源于能源形式数据字典值) */
  energyForm?: string;
  /** 子驱动方式(来源子驱动方式数据字典值) */
  driveMethod?: string;
  /** 发动机型号 */
  engine?: string;
  /** 电机型号 */
  motor?: string;
  /** 电池型号 */
  battery?: string;
  /** 总功率 */
  totalPower?: number;
  /** 电子电器系统 */
  electronicSystem?: string;
  /** 变速箱类型 */
  transmission?: string;
  /** 驾驶方式(来源驾驶方式数据字典值) */
  drivingMethod?: string;
  /** 物理架构(来源物理架构数据字典值) */
  physicalArchitecture?: string;
  /** 电子电器架构(来源电子电器架构数据字典值) */
  electronicArchitecture?: string;
  /** 根车型 */
  rootModel?: string;
  /** 智驶方案 */
  smartDrivingFunctionLevel?: string;
  /** 智驶方案 */
  smartDrivingHardwareTopology?: string;
  /** 车长 */
  carLength?: number;
  /** 车宽 */
  carWidth?: number;
  /** 车高 */
  carHeight?: number;
  /** 轴距 */
  wheelbase?: number;
  /** 开发范围简述 */
  developmentScope?: string;
  /** 规模代码 */
  scaleCode?: string;
  /** 市场竞品 */
  marketCompetitors?: string;
  /** 技术竞品 */
  technicalCompetitors?: string;
  /** 记录创建时间 */
  createTime?: string;
  /** 创建人 */
  createUser?: number;
  /** 记录最后更新时间 */
  updateTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 项目权限
 */
export interface ProjectPermissionResponse {
  /** ID */
  rolePermId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 项目ID */
  projectId?: number;
  /** 权限ID(关联权限表) */
  permissionId?: number;
  /** 状态(1启用 2停用) */
  status?: number;
  /** 数据类型(1是项目权限数据,2是模版默认权限数据) */
  dataType?: number;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 最后修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  sysPermissionResponse?: SysPermissionResponse;
}

/**
 * 权限
 */
export interface SysPermissionResponse {
  /** 权限ID,唯一标识 */
  permissionId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 父权限ID */
  parentPermissionId?: number;
  /** 权限编码 */
  permissionCode?: string;
  /** 权限名称 */
  permissionName?: string;
  /** 权限描述 */
  description?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 项目角色
 */
export interface ProjectObsResponse {
  /** ID */
  projObsId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 父OBSID */
  parentProjObsId?: number;
  /** 项目ID */
  projectId?: Record<string, any>;
  /** OBS名称 */
  obsName?: string;
  /** OBS描述 */
  obsDesc?: string;
  /** 展示区域 */
  displayArea?: number;
  /** 纵向暂时层级 */
  verticalDisplayHierarchy?: number;
  /** 项目角色ID(关联系统角色表ID) */
  projRoleId?: number;
  /** 项目角色类型 */
  projRoleType?: number;
  /** 责任人部门 */
  responsiableDept?: number;
  /** 责任人 */
  responsiable?: number;
  /** 渲染方向(1 横向 2 纵向) */
  displayDirection?: number;
  /** 工作流状态(来源项目角色审批状态数据字典) */
  workflowStatus?: Record<string, any>;
  /** 项目obs版本 */
  projObsVersion?: string;
  /** 是否当前版本标识(1 是,0 否) */
  currentVersion?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 最后修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 项目obs模版
 */
export interface ProjectObsTemplateResponse {
  /** 项目obs模板ID,唯一标识 */
  projObsTemplateId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 来源项目ID */
  sourceProjId?: number;
  /** OBS名称 */
  obsName?: string;
  /** OBS描述 */
  obsDesc?: string;
  /** 展示区域 */
  displayArea?: number;
  /** 纵向暂时层级 */
  verticalDisplayHierarchy?: number;
  /** 项目角色ID(关联系统角色表ID) */
  projRoleId?: number;
  /** 项目角色类型 */
  projRoleType?: number;
  /** 责任人部门 */
  responsiableDept?: number;
  /** 渲染方向(1 横向 2 纵向) */
  displayDirection?: number;
  /** 工作流状态(来源项目角色审批状态数据字典) */
  workflowStatus?: Record<string, any>;
  /** 项目obs模板版本 */
  projObsTempalteVersion?: string;
  /** 是否当前版本标识(1 是,0 否) */
  currentVersion?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 最后修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 项目成员和角色关联
 */
export interface ProjectMemberRoleResponse {
  /** 主键 */
  memberRoleId?: number;
  /** 数据类型(1项目数据;2模版数据) */
  dataType?: number;
  /** 项目ID */
  projectId?: number;
  /** 用户ID */
  userId?: number;
  /** 角色ID */
  roleId?: number;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 项目集
 */
export interface ProgramResponse {
  /** 项目集ID */
  programId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 父节点ID */
  parentId?: number;
  /** 组合ID */
  portfolioId?: number;
  /** 项目集编码 */
  programCode?: string;
  /** 项目集名称 */
  programName?: string;
  /** 状态(1=规划,2=执行,3=暂停,4=完成) */
  status?: number;
  /** 排序序号 */
  sortNum?: number;
  /** 预算金额 */
  budget?: number;
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 负责人ID */
  managerId?: number;
  /** 创建人 */
  createdBy?: number;
  /** 更新人 */
  updatedBy?: number;
  /** 删除标记 */
  deleted?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 项目组合
 */
export interface PortfolioResponse {
  /** 组合ID */
  portfolioId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 父节点ID */
  parentId?: number;
  /** 组合编码 */
  portfolioCode?: string;
  /** 组合名称 */
  portfolioName?: string;
  /** 状态(1=规划,2=执行,3=暂停,4=完成,5=归档) */
  status?: number;
  /** 排序序号 */
  sortNum?: number;
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 负责人ID */
  managerId?: number;
  /** 创建人 */
  createdBy?: number;
  /** 更新人 */
  updatedBy?: number;
  /** 删除标记 */
  deleted?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 计划模板信息
 */
export interface PlanTemplateInfoResponse {
  /** 计划模板ID */
  infoId?: number;
  /** 计划模板编号 */
  templateCode?: string;
  /** 计划模板名称 */
  templateName?: string;
  /** 项目类型 */
  projectType?: string;
  /** 项目级别 */
  projectLevel?: string;
  /** 项目分类 */
  projectCategory?: string;
  /** 品牌 */
  brand?: string;
  /** 产品线 */
  productLine?: string;
  /** 车型类别 */
  vehicleCategory?: string;
  /** 技术平台 */
  technicalPlatform?: string;
  /** 能源形式 */
  energyForm?: string;
  /** 销售区域 */
  salesArea?: string;
  /** 计划模板说明 */
  templateDescription?: string;
  /** 负责人ID */
  owner?: number;
  /** 来源项目编号 */
  srcProjectCode?: string;
  /** 来源项目名称 */
  srcProjectName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 计划交付物
 */
export interface PlanDeliverableResponse {
  /** 交付物ID */
  deliverableId?: number;
  /** 数据类型(1项目计划 2 模版定义的默认项目计划) */
  dataType?: number;
  /** 业务类型(1 计划 2 任务) */
  businessType?: number;
  /** 计划ID，关联到具体的计划 */
  planId?: number;
  /** 国际ID */
  countryId?: number;
  /** 交付物名称 */
  deliverableName?: string;
  /** 交付物版本 */
  version?: string;
  /** 是否为当前版本 */
  isCurrent?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 独立任务过程文档
 */
export interface IndeTaskProcessDocAddRequest {
  tenantId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskProcessDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag: number;
  /** 附件名 */
  docName: string;
  /** 附件唯一标识 */
  docKey: string;
  /** 附件id(关联附件表) */
  docId: number;
}

/**
 * 独立任务过程文档表
 */
export interface IndeTaskProcessDocResponse {
  /** 自增主键 */
  indeTaskProcessDocId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskProcessDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 大小 */
  docSize?: number;
  /** 附件格式 */
  docType?: string;
  /** 创建人姓名 */
  createUserName?: string;
  /** 更新人姓名 */
  updateUserName?: string;
}

/**
 * 独立任务
 */
export interface IndeTaskResponse {
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务父任务ID */
  parentIndeTaskId?: number;
  /** 独立任务名称 */
  indeTaskName?: string;
  /** 独立任务状态(1 未开始,2 进行中,3 已关闭) */
  indeTaskStatus?: number;
  /** 独立任务责任人 */
  indeTaskResponsible?: number;
  /** 独立任务责任部门 */
  indeTaskResponsibleDept?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 独立任务验收标准 */
  indeTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 独立任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 独立任务说明 */
  indeTaskDesc?: string;
  /** 指派人 */
  assigneer?: number;
  /** 指派时间 */
  assigneeTime?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 任务标签列表 */
  tags?: ConfigIndeTaskTagResponse[];
  /** 参与人列表 */
  participants?: ParticipantResponse[];
  /** 过程文档列表 */
  processDocs?: IndeTaskProcessDocResponse[];
  /** 责任人姓名 */
  responsibleName?: string;
}

/**
 * 独立任务过程文档表
 */
export interface IndeTaskProcessDocEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  indeTaskProcessDocId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskProcessDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 项目干系人
 */
export interface StakeholderEditRequest {
  /** 租户ID，用于多租户隔离 */
  tenantId?: number;
  /** 主键ID */
  holderId?: number;
  /** 干系人类型(1 计划参与人 2 任务参与人 3 日程参与人 4 项目组合管理员 5 项目集管理员) */
  pmType?: number;
  /** 业务对象id(比如项目计划id) */
  objectId?: number;
  /** 用户ID(关联用户表) */
  userId?: number;
  /** 备注信息 */
  remark?: string;
}

/**
 * 独立任务变更参与人入参对象
 */
export interface IndeTaskParticipantChangeRequest {
  tenantId?: number;
  /** 主键ID */
  holderId?: number;
  /** 用户ID(关联用户表) */
  userId: number;
}

/**
 * 独立任务风险
 */
export interface IndeTaskRiskResponse {
  /** 独立任务风险ID */
  indeTaskRiskId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务风险名称 */
  indeTaskRiskName?: string;
  /** 独立任务风险状态(1未开始, 2进行中,3已完成,4已关闭) */
  indeTaskRiskStatus?: number;
  /** 风险分类 */
  indeTaskRiskType?: number;
  /** 风险级别 (1高2中3低) */
  indeTaskRiskLevel?: number;
  /** 是否公开 (1是0否) */
  disCloseFlag?: number;
  /** 公开回复 (1是0否) */
  publicReplyFlag?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 完成日期 */
  completionDate?: string;
  /** 独立任务风险负责人ID */
  indeTaskResponsible?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 独立任务风险负责人姓名 */
  indeTaskResponsibleName?: string;
  /** 独立任务风险提出人姓名 */
  createUserName?: string;
}

/**
 * 独立任务风险,风险回答附件入参对象
 */
export interface IndeTaskRiskDocAddRequest {
  /** 租户ID */
  tenantId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskRiskDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag: number;
  /** 附件名 */
  docName: string;
  /** 附件唯一标识 */
  docKey: string;
  /** 附件id(关联附件表) */
  docId: number;
}

/**
 * 独立任务风险回答
 */
export interface IndeTaskRiskAnswerResponse {
  /** 独立任务风险回答ID */
  indeTaskRiskAnswerId?: number;
  /** 独立任务风险ID */
  indeTaskRiskId?: number;
  /** 独立任务风险回答时间 */
  indeTaskRiskAnswerTime?: string;
  /** 独立任务风险回答内容 */
  indeTaskRiskAnswer?: string;
  /** 独立任务风险讨论 */
  indeTaskRiskDisscution?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateTime?: string;
  /** 修改时间 */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 独立任务风险,风险回答附件表
 */
export interface IndeTaskRiskDocEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  indeTaskRiskDocId?: number;
  /** 独立任务风险ID,风险回答 */
  indeTaskRiskId?: number;
  /** 是否为风险回答(1是,0 否) */
  answerFlag?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskRiskDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 独立任务进度汇报附件表
 */
export interface IndeTaskReportDocResponse {
  /** 自增主键 */
  indeTaskReportDocId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskReportDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 独立任务汇报
 */
export interface IndeTaskReportResponse {
  /** 汇报ID */
  reportId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务进度，范围0.00到100.00 */
  progress?: number;
  /** 汇报内容 */
  indeTaskDesc?: string;
  /** 汇报时间 */
  reportTime?: string;
  /** 独立任务汇报状态(2 已接受,0 待澄清,1 已汇报) */
  indeTaskReportStatus?: number;
  /** 独立任务汇报工作流定义ID */
  workflowId?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 附件列表 */
  attaments?: SysAttachmentResponse[];
  /** 文档列表 */
  reportDocs?: IndeTaskReportDocResponse[];
}

/**
 * 独立任务进度汇报附件表
 */
export interface IndeTaskReportDocEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  indeTaskReportDocId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskReportDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 独立任务会议纪要表
 */
export interface IndeTaskMeetingDocResponse {
  /** 自增主键 */
  indeTaskMeetingDocId?: number;
  /** 独立任务ID */
  indeTaskMeetingId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskMeetingDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 大小 */
  docSize?: number;
  /** 附件格式 */
  docType?: string;
  /** 创建人姓名 */
  createUserName?: string;
  /** 更新人姓名 */
  updateUserName?: string;
}

/**
 * 独立任务会议
 */
export interface IndeTaskMeetingResponse {
  /** 会议ID */
  indeTaskMeetingId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 会议主题 */
  indeTaskSubject?: string;
  /** 会议状态(1未开始, 2进行中, 3已完成, 4已取消) */
  indeTaskMeetingStatus?: number;
  /** 会议时间 */
  indeTaskMeetingTime?: string;
  /** 会议地点 */
  indeTaskLocation?: string;
  /** 会议链接 */
  indeTaskMeetingUrl?: string;
  /** 发起人ID */
  indeTaskInitiator?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 会议纪要 */
  docs?: IndeTaskMeetingDocResponse[];
  /** 参与人员 */
  participants?: ParticipantResponse[];
}

/**
 * 独立任务交付文档表
 */
export interface IndeTaskDeliveryDocResponse {
  /** 自增主键 */
  indeTaskDeliveryDocId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskDeliveryDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识(关联附件表) */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 大小 */
  docSize?: number;
  /** 附件格式 */
  docType?: string;
  /** 创建人姓名 */
  createUserName?: string;
  /** 更新人姓名 */
  updateUserName?: string;
}

/**
 * 独立任务问题,问题回答附件表
 */
export interface IndeTaskIssueDocResponse {
  /** 自增主键 */
  indeTaskIssueDocId?: number;
  /** 独立任务问题ID,问题回答 */
  indeTaskIssueId?: number;
  /** 是否为问题回答(1是,0 否) */
  answerFlag?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskIssueDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 独立任务问题
 */
export interface IndeTaskIssueResponse {
  /** 独立任务问题ID */
  indeTaskIssueId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务问题名称 */
  indeTaskIssueName?: string;
  /** 独立任务问题状态(1未开始, 2进行中,3已完成,4已关闭) */
  indeTaskIssueStatus?: number;
  /** 问题分类 */
  indeTaskIssueType?: number;
  /** 问题级别 (1高2中3低) */
  indeTaskIssueLevel?: number;
  /** 截止时间 */
  closingTime?: string;
  /** 完成日期 */
  completionDate?: string;
  /** 独立任务问题负责人ID */
  indeTaskResponsible?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 独立任务问题负责人项目 */
  indeTaskResponsibleName?: string;
  /** 问题提出人姓名 */
  createUserName?: string;
  /** 交付文档列表 */
  deliveryDocs?: IndeTaskDeliveryDocResponse[];
  /** 文档列表 */
  issueDocs?: IndeTaskIssueDocResponse[];
}

/**
 * 独立任务问题,问题回答附件入参对象
 */
export interface IndeTaskIssueDocAddRequest {
  /** 租户ID */
  tenantId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskIssueDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 独立任务回答
 */
export interface IndeTaskIssueAnswerResponse {
  /** 独立任务问题回答ID */
  indeTaskIssueAnswerId?: number;
  /** 独立任务问题ID */
  indeTaskIssueId?: number;
  /** 独立任务问题回答时间 */
  indeTaskIssueAnswerTime?: string;
  /** 独立任务问题回答内容 */
  indeTaskIssueAnswer?: string;
  /** 独立任务问题讨论 */
  indeTaskIssueDisscution?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateTime?: string;
  /** 修改时间 */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 文档列表 */
  issueDocs?: IndeTaskIssueDocResponse[];
}

/**
 * 独立任务问题,问题回答附件表
 */
export interface IndeTaskIssueDocEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  indeTaskIssueDocId?: number;
  /** 独立任务问题ID,问题回答 */
  indeTaskIssueId?: number;
  /** 是否为问题回答(1是,0 否) */
  answerFlag?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskIssueDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 需求分析阶段
 */
export interface DemandAnalysisPhaseResponse {
  /** 自增主键 */
  id?: number;
  /** 租户ID */
  tenantId?: number;
  /** 关联需求ID */
  demandId?: number;
  /** 需求提出部门 */
  proposerDept?: string;
  /** 需求提出部门名称 */
  proposerDeptName?: string;
  /** 需求提出人 */
  proposer?: number;
  /** 需求提出人姓名 */
  proposerName?: string;
  /** 需求提出时间 */
  proposalTime?: string;
  /** 需求责任部门 */
  responsibleDept?: string;
  /** 需求负责人 */
  responsible?: number;
  /** 需求接收时间 */
  receiveTime?: string;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 是否删除(0 否 1 是) */
  deleted?: number;
}

/**
 * 需求分析（评审）/审批阶段
 */
export interface DemandApprovalPhaseResponse {
  /** 自增主键 */
  id?: number;
  /** 租户id */
  tenantId?: number;
  /** 关联需求ID */
  demandId?: number;
  /** 需求审批流程 */
  approvalProcess?: string;
  /** 需求审批环节 */
  approvalStep?: string;
  /** 当前环节审批人 */
  currentApprover?: string;
  /** 需求评审人 */
  reviewer?: string;
  /** 需求审批时间 */
  approvalTime?: string;
  /** 优先级 */
  priority?: string;
  /** 业务角色 */
  businessRole?: string;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 是否删除(0 否 1 是) */
  deleted?: number;
}

/**
 * 需求变更
 */
export interface DemandChangePhaseResponse {
  /** 自增主键 */
  id?: number;
  /** 租户ID */
  tenantId?: number;
  /** 关联需求ID */
  demandId?: number;
  /** 需求变更人 */
  changer?: string;
  /** 需求变更时间 */
  changeTime?: string;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 是否删除(0 否 1 是) */
  deleted?: number;
}

/**
 * 需求实施阶段
 */
export interface DemandExecutionPhaseResponse {
  /** 自增主键 */
  id?: number;
  /** 租户id */
  tenantId?: number;
  /** 关联需求ID */
  demandId?: number;
  /** 需求评审人 */
  reviewer?: string;
  /** 业务代表 */
  businessRepresentative?: string;
  /** 项目负责人 */
  projectLeader?: string;
  /** 产品经理 */
  productManager?: string;
  /** 研发经理 */
  rndManager?: string;
  /** 测试经理 */
  testManager?: string;
  /** 需求状态 */
  status?: number;
  /** 需求进度 */
  progress?: number;
  /** 开发需求数量 */
  developDemandCount?: number;
  /** 计划开始时间 */
  plannedStartTime?: string;
  /** 计划完成时间 */
  plannedFinishTime?: string;
  /** 实际开始时间 */
  actualStartTime?: string;
  /** 实际完成时间 */
  actualCompletionTime?: string;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 是否删除(0 否 1 是) */
  deleted?: number;
}

/**
 * 需求阶段
 */
export interface DemandPhaseResponse {
  /** 主键ID */
  phaseId?: number;
  /** 需求ID */
  demandId?: number;
  /** 租户ID，用于多租户隔离 */
  tenantId?: number;
  /** 阶段类型(1.规划阶段、2.审批阶段,3.招采阶段,4.实施阶段,5.验收阶段) */
  phaseType?: number;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新人 */
  updatedBy?: number;
  /** 更新时间 */
  updatedAt?: string;
  /** 备注信息 */
  remark?: string;
  /** 删除标记(0表示未删除，1表示已删除) */
  deleted?: number;
  analysisPhase?: DemandAnalysisPhaseResponse;
  approvalPhase?: DemandApprovalPhaseResponse;
  changePhase?: DemandChangePhaseResponse;
  executionPhase?: DemandExecutionPhaseResponse;
}

/**
 * 需求
 */
export interface DemandResponse {
  /** ID */
  demandId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 需求层级 */
  demandLevel?: number;
  /** 项目ID */
  projectId?: number;
  /** 父ID */
  parentId?: number;
  /** 需求名称 */
  demandName?: string;
  /** 需求描述 */
  demandDesc?: string;
  /** 验收标准 */
  acceptCriteria?: string;
  /** 需求类型 */
  demandType?: number;
  /** 优先级 */
  priority?: string;
  /** 工时 */
  workHours?: number;
  /** 需求评审人 */
  demandReviewer?: number;
  /** 需求责任人 */
  responsible?: number;
  /** 需求责任人姓名 */
  responsibleName?: string;
  /** 产品经理 */
  productManager?: number;
  /** 研发经理 */
  developManager?: number;
  /** 测试经理 */
  testingManager?: number;
  /** 是否影响业务流 */
  businessFlow?: number;
  /** 是否影响数据 */
  dataImpact?: number;
  /** 迫切程度 */
  urgency?: number;
  /** 迫切程度说明 */
  urgencyDesc?: string;
  /** 状态 */
  status?: number;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 修改人 */
  updatedBy?: number;
  /** 修改时间 */
  updatedAt?: string;
  /** 备注 */
  remark?: string;
  /** 是否删除( 0 否 1 是) */
  deleted?: number;
  customFieldResponse?: ProjectCustomFieldResponse;
  demandPhase?: DemandPhaseResponse;
}

/**
 * 自定义字段
 */
export interface ProjectCustomFieldResponse {
  /** 业务对象ID */
  objectId?: number;
  /** 项目业务类型(1 项目,2 需求) */
  pmType?: number;
  /** 利益相关方 */
  stakeholder?: string;
  /** 来源决策体系 */
  decisionSystem?: string;
  /** 业务域 */
  businessDomain?: string;
  /** 业务组件 */
  businessComponent?: string;
  /** 业务流程 */
  businessProcess?: string;
  /** 应用域 */
  appDomain?: string;
  /** IT系统 */
  itSystemId?: number;
  /** IT系统名称 */
  itSystemName?: number;
  /** 模块 */
  moduleId?: number;
  /** 模块名称 */
  moduleName?: number;
}

/**
 * 项目附件
 */
export interface ProjectAttachmentRequest {
  tenantId?: number;
  /** 自增主键 */
  id?: number;
  /** 项目附件类型(1 项目计划 2 需求 3 需求分发) */
  pmType?: number;
  /** 父ID */
  parentId?: number;
  /** 是否文件夹(0 否,1 是) */
  isFolder: number;
  /** 附件名 */
  attaName?: string;
  /** 附件唯一标识 */
  attaKey: string;
  /** 附件id(关联附件表) */
  attaId?: number;
  /** 附件路径 */
  attaPath?: string;
  /** 大小 */
  attaSize?: number;
  /** 附件格式 */
  attaType?: string;
}

/**
 * 自定义字段
 */
export interface ProjectCustomFieldRequest {
  tenantId?: number;
  /** 业务对象ID */
  objectId?: number;
  /** 项目业务类型(1 项目,2 需求) */
  pmType?: number;
  /** 利益相关方 */
  stakeholder?: string;
  /** 来源决策体系 */
  decisionSystem?: string;
  /** 业务域 */
  businessDomain?: string;
  /** 业务组件 */
  businessComponent?: string;
  /** 业务流程 */
  businessProcess?: string;
  /** 应用域 */
  appDomain?: string;
  /** IT系统 */
  itSystemId?: number;
  /** 模块 */
  moduleId?: number;
}

/**
 * 需求关注记录
 */
export interface DemandFollowResponse {
  /** 主键 */
  id?: number;
  /** 租户ID */
  tenantId?: number;
  /** 需求ID */
  demandId?: number;
  /** 需求名称 */
  demandName?: number;
  /** 用户ID */
  userId?: number;
  /** 用户名 */
  userName?: number;
  /** 状态(1关注 0 取消关注) */
  status?: number;
  /** 创建人 */
  createdBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 修改人 */
  updatedBy?: number;
  /** 修改时间 */
  updatedAt?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记 (0 未删除 1 已删除) */
  deleted?: number;
}

/**
 * 项目任务标签
 */
export interface ConfigProjectTaskTagResponse {
  /** 项目任务标签ID */
  projTaskTagId?: number;
  /** 父项目任务标签ID或者所属标签组ID */
  parentProjTaskTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag?: number;
  /** 项目标签标题 */
  projTagName?: string;
  /** 项目标签描述 */
  projTagDesc?: string;
  /** 项目标签颜色 */
  projTagColor?: string;
  /** 项目标签状态 (1 启用,0 禁用) */
  projTagStatus?: number;
  /** 排序码 */
  sortNum?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 项目标签
 */
export interface ConfigProjectTagResponse {
  /** 项目标签ID */
  projTagId?: number;
  /** 父项目标签ID或者所属标签组ID */
  parentProjTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag?: number;
  /** 项目标签标题 */
  projTagName?: string;
  /** 项目标签描述 */
  projTagDesc?: string;
  /** 项目标签颜色 */
  projTagColor?: string;
  /** 项目标签状态 (1 启用,0 禁用) */
  projTagStatus?: number;
  /** 排序码 */
  sortNum?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 子项目标签列表 */
  children?: ConfigProjectTagResponse[];
}

/**
 * 配置项目角色类型
 */
export interface ConfigProjectRoleTypeResponse {
  /** 配置项目角色类型ID,唯一表示 */
  configProjRoleTypeId?: number;
  /** 项目ID */
  projId?: number;
  /** 项目角色类型 */
  projRoleTypeName?: string;
  /** 项目角色类型描述 */
  projRoleTypeDesc?: string;
  /** 项目角色类型展示框底色 */
  projRoleTypeColor?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 配置项目角色类型
 */
export interface ConfigProjRoleTypeResponse {
  /** 配置项目角色类型ID,唯一表示 */
  configProjRoleTypeId?: number;
  /** 项目ID */
  projId?: number;
  /** 项目角色类型 */
  projRoleTypeName?: string;
  /** 项目角色类型描述 */
  projRoleTypeDesc?: string;
  /** 项目角色类型展示框底色 */
  projRoleTypeColor?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 产品线配置
 */
export interface ConfigProductLineResponse {
  /** 产品线ID,唯一表示 */
  productLineId?: number;
  /** 父产品线ID */
  parentProductLineId?: number;
  /** 产品线名称 */
  productLineName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 子产品线配置列表 */
  children?: ConfigProductLineResponse[];
}

/**
 * 生产工厂配置
 */
export interface ConfigProductFactoryResponse {
  /** 生产厂区id */
  productionFactoryId?: number;
  /** 生产基地ID */
  productionBaseId?: number;
  /** 生产厂区名称 */
  productionFactoryName?: string;
  /** 生产厂区描述 */
  productionFactoryDesc?: string;
  /** 生产厂区状态 */
  productionFactoryStatus?: number;
  /** 生产工厂地址 */
  factoryLocation?: string;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 生产基地配置
 */
export interface ConfigProductBaseResponse {
  /** 生产基地id */
  productionBaseId?: number;
  /** 生产区域(来源生产区域数据字典值) */
  productionArea?: string;
  /** 生产基地名称 */
  productionBaseName?: string;
  /** 生产基地描述 */
  productionBaseDesc?: string;
  /** 生产基地状态 */
  productionBaseStatus?: number;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 车型配置
 */
export interface ConfigModelResponse {
  /** 车型类别ID,唯一表示 */
  modelTypeId?: number;
  /** 父车型ID */
  parentModelTypeId?: number;
  /** 车型类别(来源车型类别数据字典值) */
  model?: string;
  /** 车型名称 */
  modelName?: number;
  /** 车型描述 */
  modelDesc?: number;
  /** 车型状态 (1 启用,0 禁用) */
  modelStatus?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 车型类别-车型级别配置
 */
export interface ConfigModelLevelResponse {
  /** 车型类别车型界别配置ID,唯一表示 */
  configModelLevelId?: number;
  /** 车型类别(来源车型类别数据字典值) */
  modelTypeId?: number;
  /** 车型级别(来源车型级别数据字典值) */
  modelLevel?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 车型级别与车身形式匹配关系配置
 */
export interface ConfigModelLevelBodyResponse {
  /** 关系配置ID,唯一表示 */
  configModelLevelBodyId?: number;
  /** 车型级别(来源车型级别数据字典值) */
  modelLevel?: string;
  /** 车身形式(来源车型形式数据字典值) */
  bodyForm?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 国家和地区信息
 */
export interface ConfigMarketAreaResponse {
  /** 市场区域ID */
  marketAreaId?: number;
  /** 父市场区域ID */
  parentMarketAreaId?: number;
  /** 地区/国家编码 */
  marketAreaCode?: string;
  /** 地区/国家名称 */
  marketAreaName?: string;
  /** 是否是国家(1 是 0 否) */
  countryFlag?: number;
  /** 地区/国家描述 */
  marketAreaDesc?: string;
  /** 创建者ID */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新人 */
  updateUser?: number;
  /** 更新者ID */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 关注配置
 */
export interface ConfigFollowResponse {
  /** 主键 */
  followId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 关注对象类型(1 项目,2 计划 3 独立任务 4 项目任务 5 项目组合 6 项目集) */
  followType?: number;
  /** 关注对象ID */
  followObjectId?: number;
  /** 用户ID */
  userId?: number;
  /** 状态(1关注 0 取消关注) */
  status?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 驱动方式-驱动方式值关系配置
 */
export interface ConfigDriveMethodResponse {
  /** 驱动形式关系配置ID,唯一表示 */
  configDriveMethodId?: number;
  /** 驱动方式(来源驱动方式数据字典值) */
  driveMethod?: string;
  /** 子驱动方式(来源子驱动方式数据字典值) */
  subDriveMethod?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 事业部
 */
export interface ConfigBusinessUnitResponse {
  /** 事业部ID */
  businessUnitId?: number;
  /** 父事业部ID */
  parentBuinessUnitId?: number;
  /** 事业部名称 */
  businessUnitName?: string;
  /** 排序码 */
  sortNum?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 子事业部列表 */
  children?: ConfigBusinessUnitResponse[];
}

/**
 * 品牌配置
 */
export interface ConfigBrandResponse {
  /** 品牌ID,唯一表示 */
  brandId?: number;
  /** 父品牌ID */
  parentBrandId?: number;
  /** 品牌名称 */
  brandName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 子品牌配置列表 */
  children?: ConfigBrandResponse[];
}

/**
 * 品牌-产品线-车型配置
 */
export interface ConfigBrandProductlineModelResponse {
  /** 主键,唯一标识 */
  bpmConfigId?: number;
  /** 品牌ID */
  brandId?: number;
  /** 产品线ID */
  productLineId?: number;
  /** 车型ID */
  modelId?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 项目日历
 */
export interface CalendarResponse {
  /** 日历唯一标识符 */
  calendarId?: number;
  /** 是否默认日历 */
  defaultFlag?: string;
  /** 日历名称 */
  calendarName?: string;
  /** 关联的项目ID */
  projId?: number;
  /** 基准日历ID */
  baseClndrId?: number;
  /** 最后修改日期 */
  lastChngDate?: string;
  /** 日历类型 */
  clndrType?: string;
  /** 每日标准工时数 */
  dayHrCnt?: number;
  /** 每周标准工时数 */
  weekHrCnt?: number;
  /** 每月标准工时数 */
  monthHrCnt?: number;
  /** 每年标准工时数 */
  yearHrCnt?: number;
  /** 是否资源私有日历 */
  rsrcPrivate?: string;
  /** 日历详细数据 */
  clndrData?: Record<string, any>;
  /** 记录创建时间 */
  createTime?: string;
  /** 记录创建者 */
  createUser?: number;
  /** 记录最后更新时间 */
  updateTime?: string;
  /** 记录最后更新者 */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 记录删除时间 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 用户信息
 */
export interface SysUserResponse {
  /** 用户ID */
  userId?: number;
  /** 租户id */
  tenantId?: number;
  /** 用户名 */
  userName?: string;
  /** 用户账号 */
  userAccount?: string;
  /** 密码 */
  userPassword?: string;
  /** 电话 */
  telephone?: string;
  /** email */
  userEmail?: string;
  /** 性别 */
  sex?: number;
  /** 部门ID */
  deptId?: number;
  /** 访问类型(1 普通用户 2 管理员 3 外部用户) */
  accessType?: number;
  /** 备注 */
  userDesc?: string;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 管理员备注 */
  remark?: string;
}

/**
 * 角色权限关联表
 */
export interface SysUserDataPermissionResponse {
  /** 用户权限ID,主键 */
  userPermissionId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 用户ID */
  userId?: number;
  /** 数据类型(project,dept,projectPlan等) */
  dataType?: string;
  /** 业务数据ID */
  dataId?: string;
  /** 全部授权标识(1是,0否) */
  allFlag?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 修改时间 */
  remark?: string;
}

/**
 * 角色信息
 */
export interface SysRoleResponse {
  /** 角色ID */
  roleId?: number;
  /** 父角色ID */
  parentRoleId?: number;
  /** 角色编码 */
  roleCode?: string;
  /** 角色名称 */
  roleName?: string;
  /** 显示顺序 */
  roleSort?: number;
  /** 角色状态 */
  status?: number;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 成员数量 */
  memberCount?: number;
}

/**
 * 系统操作日志
 */
export interface SysOperLogResponse {
  /** 主键,唯一标识 */
  id?: number;
  /** 用户ID */
  userId?: number;
  /** 操作模块 */
  module?: string;
  /** 操作类型 */
  type?: string;
  /** 操作描述 */
  description?: string;
  /** 请求参数 */
  requestParam?: string;
  /** 响应结果 */
  responseResult?: string;
  /** 操作时间 */
  operTime?: string;
  /** 耗时 */
  costTime?: number;
  /** IP地址 */
  ipAddress?: string;
  /** 操作状态 */
  status?: number;
  /** 创建人 */
  createUser?: number;
  /** 错误信息 */
  errorMsg?: string;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 菜单权限
 */
export interface SysMenuResponse {
  /** 菜单ID */
  menuId?: number;
  /** 菜单名称 */
  menuName?: string;
  /** 父菜单ID */
  parentMenuId?: number;
  /** 显示顺序 */
  orderNum?: number;
  /** 路由地址 */
  path?: string;
  /** 组件路径 */
  component?: string;
  /** 路由参数 */
  query?: string;
  /** 路由名称 */
  routeName?: string;
  /** 是否为外链 */
  isFrame?: number;
  /** 是否缓存 */
  isCache?: number;
  /** 菜单类型 */
  menuType?: string;
  /** 菜单状态 */
  visible?: string;
  /** 菜单状态 */
  status?: number;
  /** 权限标识 */
  perms?: string;
  /** 菜单图标 */
  icon?: string;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** url */
  url?: string;
  /** 是否刷新 */
  isRefresh?: number;
  /** 打开方式 */
  target?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 系统登录日志
 */
export interface SysLoginLogResponse {
  /** 主键,唯一标识 */
  id?: number;
  /** 用户ID */
  userId?: number;
  /** 登录时间 */
  loginTime?: string;
  /** IP地址 */
  ipAddress?: string;
  /** 用户代理 */
  userAgent?: string;
  /** 登录状态 */
  loginStatus?: number;
  /** 失败原因 */
  failureReason?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 国际化
 */
export interface SysI18nMessageResponse {
  /** 消息ID */
  messageId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 模块(common,permission等) */
  moduleName?: string;
  /** 消息键名，用于唯一标识消息内容 */
  messageKey?: string;
  /** 语言代码，如en-US、zh-CN等 */
  languageCode?: string;
  /** 消息内容，支持占位符 */
  i18nMessage?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 维度类型
 */
export interface SysDimensionTypeResponse {
  /** 维度类型ID */
  typeId?: number;
  /** 维度类型编码 (DEPT, REGION, PROJECT)等 */
  typeCode?: string;
  /** 维度类型名称 */
  typeName?: string;
  /** 维度查询接口地址 */
  url?: string;
  /** 是否有效1 有效 0 失效 */
  status?: number;
  /** 维度类型描述 */
  typeDesc?: string;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 数据权限
 */
export interface SysDimensionScopeResponse {
  /** ID */
  scopeId?: number;
  /** 维度类型ID */
  typeId?: number;
  /** 授权对象类型(1 用户,2 角色) */
  targetType?: number;
  /** 授权对象ID,如用户id,角色id等 */
  targetId?: number;
  /** 租户id */
  tenantId?: number;
  /** 数据范围值,如部门id,项目id等 */
  scopeValue?: string;
  /** 父数据范围值(如父部门id,父项目id) */
  parentScopeValue?: string;
  /** 是否有效(1 有效 0 失效) */
  status?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
}

/**
 * 部门
 */
export interface SysDeptResponse {
  /** 部门id */
  deptId?: number;
  /** 部门编码 */
  deptNo?: string;
  /** 父部门id */
  parentId?: number;
  /** 所属组织ID */
  organizationId?: number;
  /** 祖级列表 */
  ancestors?: string;
  /** 部门名称 */
  deptName?: string;
  /** 显示顺序 */
  orderNum?: number;
  /** 负责人 */
  leader?: string;
  /** 联系电话 */
  phone?: string;
  /** 邮箱 */
  email?: string;
  /** 部门状态 */
  status?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 合作伙伴信息
 */
export interface SysBusinessPartnerResponse {
  /** ID */
  partnerId?: number;
  /** 合作伙伴名称 */
  partnerName?: string;
  /** 合作伙伴类型,来源数据字典 */
  partnerType?: number;
  /** 合作伙伴性质,来源数据字典 */
  shipType?: number;
  /** 统一社会信用代码 */
  esccCode?: string;
  /** 企业邮箱 */
  enterpriseEmail?: string;
  /** 注册地址 */
  registeAddress?: string;
  /** 租户id */
  tenantId?: number;
  /** 联系人姓名 */
  liaisonName?: string;
  /** 联系人电话 */
  liaisonPhone?: string;
  /** 联系人邮箱 */
  liaisonEmail?: string;
  /** 联系人通讯地址 */
  liaisonAddress?: string;
  /** 是否有效1 有效 0 失效 */
  status?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人 */
  updateUser?: number;
  /** 修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 用户登录信息
 */
export interface UserLoginResponse {
  /** token */
  token?: string;
  /** 过期时间 */
  expireTime?: number;
}

/**
 * 字典类型
 */
export interface SysDictTypeResponse {
  /** 字典主键 */
  dictId?: number;
  /** 字典名称 */
  dictName?: string;
  /** 字典类型 */
  dictType?: string;
  /** 状态 */
  status?: number;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 字典数据
 */
export interface SysDictDataResponse {
  /** 字典编码 */
  dictCode?: number;
  /** 字典排序 */
  sortNum?: number;
  /** 字典标签 */
  dictLabel?: string;
  /** 字典键值 */
  dictValue?: string;
  /** 父字典值 */
  parentDictValue?: string;
  /** 父字典标签 */
  parentDictLabel?: string;
  /** 字典类型 */
  dictType?: string;
  /** 样式属性 */
  cssClass?: string;
  /** 表格回显样式 */
  listClass?: string;
  /** 是否默认 */
  isDefault?: string;
  /** 状态 */
  status?: number;
  /** 创建者 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新者 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
}

/**
 * 组织
 */
export interface OrganizationResponse {
  /** 组织ID */
  organizationId?: number;
  /** 父组织ID，NULL表示根组织 */
  parentOrganizationId?: number;
  /** 是否为部门表示(1是,0否) */
  deptFlag?: number;
  /** 组织编码 */
  orgCode?: string;
  /** 组织名称 */
  orgName?: string;
  /** 组织描述 */
  orgDesc?: string;
  /** 组织状态 */
  status?: number;
  /** 排序码 */
  sortNum?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 子组织列表 */
  children?: OrganizationResponse[];
}

/**
 * Cos文件
 */
export interface CosFileResponse {
  /** 文件名称 */
  fileName?: string;
  /** 文件路径 */
  filePath?: string;
  /** 文件类型 */
  fileType?: string;
  /** 文件大小 */
  fileSize?: number;
  /** 文件唯一标识 */
  fileKey?: string;
  /** etag */
  etag?: string;
}

/**
 * 用户信息
 */
export interface UserInfoResponse {
  user?: SysUserQueryRequest;
  /** 权限集合 */
  roles?: string[];
  /** 权限集合 */
  permissions?: string[];
}
