// index.js
import axios from 'axios';
import fs from 'fs';
import path from 'path';

const SWAGGER_URL = 'http://www.equali.cn:4092/ipd/api/platform/v3/api-docs';

function swaggerTypeToTs(type) {
  const map = {
    string: 'string',
    integer: 'number',
    number: 'number',
    boolean: 'boolean',
    array: 'any[]',
    object: 'Record<string, any>',
  };
  return map[type] || 'any';
}

function resolveRef(ref) {
  return ref.split('/').pop();
}

function formatSourceInfo(sourceInfo = []) {
  return sourceInfo.map(
    ({ method, path, usage }) =>
      `@source ${method.toUpperCase()} ${path} [${usage}]`
  );
}

function genInterfaces(defs, schemaUsageMap) {
  const out = [];
  for (const [name, schema] of Object.entries(defs)) {
    const lines = [];
    lines.push('/**');
    if (schema.description) lines.push(` * ${schema.description}`);
    const usageLines = formatSourceInfo(schemaUsageMap[name]);
    usageLines.forEach((l) => lines.push(` * ${l}`));
    lines.push(' */');
    lines.push(`export interface ${name} {`);

    const req = schema.required || [];
    for (const [p, meta] of Object.entries(schema.properties || {})) {
      let typ = 'any';
      if (meta.$ref) typ = resolveRef(meta.$ref);
      else if (meta.type === 'array') {
        const it = meta.items || {};
        typ = it.$ref
          ? `${resolveRef(it.$ref)}[]`
          : `${swaggerTypeToTs(it.type)}[]`;
      } else typ = swaggerTypeToTs(meta.type);

      if (meta.description) lines.push(`  /** ${meta.description} */`);
      const opt = req.includes(p) ? '' : '?';
      lines.push(`  ${p}${opt}: ${typ};`);
    }

    lines.push('}\n');
    out.push(lines.join('\n'));
  }
  return out.join('\n');
}

function extractSchemaUsage(swagger) {
  const usageMap = {};
  for (const [url, methods] of Object.entries(swagger.paths)) {
    for (const [method, op] of Object.entries(methods)) {
      // Request body
      const reqSchema = op.requestBody?.content?.['application/json']?.schema;
      if (reqSchema?.$ref) {
        const name = resolveRef(reqSchema.$ref);
        usageMap[name] = usageMap[name] || [];
        usageMap[name].push({ method, path: url, usage: 'REQUEST' });
      }
      // Response body
      const resSchema =
        op.responses?.['200']?.content?.['application/json']?.schema;
      if (resSchema?.$ref) {
        const name = resolveRef(resSchema.$ref);
        usageMap[name] = usageMap[name] || [];
        usageMap[name].push({ method, path: url, usage: 'RESPONSE' });
      }
      // Query & Path Params
      const params = op.parameters || [];
      params.forEach((param) => {
        const name = `${method.toUpperCase()}${url.replace(/[\/{}]/g, '_')}__${
          param.in
        }`;
        usageMap[name] = usageMap[name] || [];
        usageMap[name].push({
          method,
          path: url,
          usage: `${param.in.toUpperCase()} PARAM`,
        });
      });
    }
  }
  return usageMap;
}

async function main() {
  const swagger = (await axios.get(SWAGGER_URL)).data;
  const defs = swagger.components?.schemas;
  if (!defs) return console.error('❌ 找不到 components.schemas');

  const usageMap = extractSchemaUsage(swagger);
  const ts = genInterfaces(defs, usageMap);
  fs.writeFileSync(path.resolve('swagger/output.ts'), ts);
  console.log('✅ 已生成 output.ts 带来源注释');
}

main();
