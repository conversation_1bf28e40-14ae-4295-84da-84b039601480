# 任务标签管理页面颜色编辑Bug修复测试

## 修复内容
修复了任务标签管理页面中颜色编辑功能的bug，问题原因是rowId计算逻辑不一致导致编辑状态管理混乱。

## 修复的关键点
1. **统一rowId计算逻辑**：确保`startEditRow`、`startEditField`、`isFieldEditing`等函数都使用与`getRowId`一致的逻辑
2. **保留用户编辑数据**：在`startEditRow`函数中添加检查，如果已存在编辑数据则不覆盖用户的修改
3. **避免重复生成临时ID**：在`isFieldEditing`函数中不生成新的临时ID

## 测试步骤
1. 打开任务标签管理页面：http://localhost:5174/#/tasks/task_tags/manage
2. 点击"新建任务标签"按钮创建新行
3. 立即点击颜色选择器修改颜色（例如改为红色 #F56C6C）
4. 点击"保存"按钮
5. 验证颜色是否保持为用户选择的颜色，而不是恢复为默认的蓝色

## 预期结果
- ✅ 创建新行后能够直接修改颜色字段
- ✅ 颜色修改在界面上正确显示
- ✅ 点击保存后颜色字段保持用户选择的颜色
- ✅ 不需要先修改其他字段作为前置条件

## 修复的文件
- `src/pages/tasks/views/task_tags/manage/TaskTagManageStore.ts`
  - 修复了`startEditRow`函数的rowId计算逻辑
  - 添加了编辑数据保护机制
  - 修复了`startEditField`和`isFieldEditing`函数的rowId计算

## 技术细节
修复前的问题：
```typescript
// 问题代码 - 会生成新的临时ID
const rowId = row.taskTagId || `temp_${tempRowCounter.value++}`;
```

修复后的代码：
```typescript
// 修复代码 - 使用一致的rowId计算逻辑
const rowId = String(row.taskTagId || (row as any).tempId || `temp_${tempRowCounter.value++}`);

// 保护用户编辑数据
const existingEditData = editingRows.value.get(rowId);
if (existingEditData) {
  existingEditData.editState = RowEditState.EDITING;
  return;
}
```
