---
description: 
globs: 
alwaysApply: true
---

# BT平台前端交互规则

## 1. 列表界面默认排序

没有特殊要求的列表界面，如任务/问题/风险，默认按创建时间倒序排序。

## 2. 列表页面刷新场景

1. 在列表界面的任何一页通过浏览器或者页面刷新按钮刷新页面，则该列表查询条件清空，返回第一页，恢复默认的排序。
2. 如用户在列表界面设置了查询条件，离开了当前界面，再重新进入该页面，则相当于该页面刷新，该之前设置的列表查询条件清空，返回列表第一页，恢复默认的排序。

## 3. 分页列表界面新增交互

在分页的列表界面中，如任务/问题/风险，不管是在分页的任何一页，或者是否带列表查询条件的情况下新增，都清空查询条件，返回第1页，在第1页第1行新增数据行。

## 4. 分页列表更新和删除交互

在分页的列表界面中，如任务/问题/风险，如一页展示10条数据，不同页进行更新和删除的交互如下：

1. 在1、2......n页更新某条数据，如把任务的完成状态改为"已完成"，查询条件保留，列表排序不变。
2. 在带或者不带列表查询条件的情况下，在1、2......n页删除某条数据，如把第1页的10条数据中的第5条删除，则当前页面只显示9条数据，查询条件保留，列表排序不变。
3. 实例：如某列表有8页，第8页有一条数据，在在带或者不带列表查询条件的情况下，删掉第8页上的这条数据，则第8页上无数据。
   - 如返回上一页到第7页，则查询条件依然保留，但第8页已不存在。
   - 如刷新页面，则列表查询条件清空，返回列表第一页，恢复默认排序，同第2条第1项。

> **注意**：列表查询条件是提供给用户的临时的查询，是浏览列表的辅助功能，重要性程度低，不需要保存，不需要考虑太多状态，按照页面的一次性功能处理。

## 5. BT平台页面右上角"+"新建按钮规则

页面右上角的"+"新建按钮，新建的是在本模块的业务对象，即需求管理模块新建的就是需求，项目管理模块新建的就是项目，问题管理模块新建的就是问题；项目模块库，是属于项目管理模块，界面右上角"+"是新增项目。

## 6. 每个页面右上角的"放大镜"图标，即搜索功能

1. 模糊+全局搜索当前页面上展示内容，高亮显示，不区分字段和位置。
2. 高亮显示拉动滚轴的当前页面展示的字段和数据，在自定义字段中不显示的字段不进行搜索。
3. 如用户在列表界面设置了列表过滤条件，仅在过滤条件结果呈现的当前页面上进行搜索，不搜索被搜索条件过滤掉的数据。

## 7. 界面导出功能

### 7.1 导出功能

1. 一般来说，列表界面有导出功能。导出功能的按钮为名为"Excel"的复合按钮，包括【导出】、【导入】、【导入模板】三个一套的按钮。
2. 导出功能的原则为"所见即所得"
   - 即根据界面展示的字段，以及表头过滤条件进行有条件的导出，而非全量导出。
   - 界面展示的字段即拖动横、纵滚动轴所展示的列表所展示的字段，也即"自定义字段"界面，打钩展示的字段。

### 7.2 导入功能

导入功能的原则为"全量字段导入"：

1. 导入模板为列表全量字段带格式，即"自定义字段界面"的所有字段均需出现在导入模板上，不管在界面展示与否都是导入模板中的字段。
2. 导入模板中的字段有值域的，导出模板带选项。
3. 使用导入模板编辑列表与在界面直接编辑列表的功能等效，导入模板导入后系统校验模板中的字段值格式，以及进行必填字段校验，界面浮框提示，用错误字段所在序号及字段名进行提示。

## 8. 列表字段对齐格式

列表高度：48px

| # | 字段名 | 宽度 | 对齐格式 | 说明 | 备注 |
|---|--------|------|----------|------|------|
| 1 | #（序号） | 32 | 列表居中<br>树表靠左 | 树表中，序号的位数表示层级 | |
| 2 | 操作 | 48 | 图标居中 | 形成正方形，表头过滤脚标 | |
| 3 | 金额 | 120 | 靠右，加千分符 | 目前都是人民币不用加货币符号，后续多币种加单位符号 | |
| 4 | 时间 | 日期+时间（秒）：176<br>日期+时间（分）：160<br>日期：88 | 靠左 | 示例：2025-09-08 23:45:00<br>日期和时间隔一个字符，24小时制<br>需求中明确是精确到分还是秒 | |
| 5 | 进度 | - | 靠左 | 进度条+百分比 | |
| 6 | 人名 | 120 | 靠左 | 示例：sz234 吴艳华<br>工号+姓名格式，中间间隔一个字符，工号为五位数<br>不管是企业内部职工还是外包员工都是此格式 | |
| 7 | 开关 | 96 | 居中 | 除特殊说明，默认为关 | |
| 8 | 其他字段 | 144 | 靠左 | 如无其他特殊说明的字段均靠左 | |
