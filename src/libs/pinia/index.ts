import {createPinia, type StoreGeneric} from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import { useLoginStore } from '@pages/main/views/login/LoginStore.ts';

// 由于多页应用，store之间需要共享
// 收集需要共享数据的各个模块，各个页面的store，这里作为统一存取
// 注意：这里只缓存需要页面间共享数据的store

// 定义store对应的键
type StoreKey = 'loginStore';

const storeMap:Map<StoreKey, () => StoreGeneric> = new Map([
    ['loginStore', useLoginStore]
]);

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

function getStore(key: StoreKey) {
    const getStore = storeMap.get(key);
    const store = getStore?.call(null);
    if (!store) {
        throw new Error(`$key store 么有缓存`);
    }
    return store;
}


export {
    pinia,
    getStore
}
