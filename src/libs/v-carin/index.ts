import type {App} from "vue";
import {<PERSON>airn, type VCairnConnector} from "v-cairn";
import {appConfig} from "@/app-config.ts";

export function startVCairn(app: App, connector: VCairnConnector) {
    app.use(VCairn, {
        timeout: 300000,
        baseURL: `${appConfig.protocol}://${appConfig.apiBaseUrl}${appConfig.apiPath}`,
        withCredentials: true
    });
    app.config.globalProperties.connector = connector;
}