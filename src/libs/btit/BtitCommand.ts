import {SPECIAL_ERROR_CODES} from "@/app-config.ts";
import {VCairnCommand, VCairnEvent} from "v-cairn";
import type {AxiosResponse} from "v-cairn";
import type {ResponseData} from "@/libs/btit/types.ts";

export class BtitCommand extends VCairnCommand {

    protected errorInSuccess(response: AxiosResponse): boolean {
        const responseData = response.data as ResponseData;
        return responseData.code !== 200;
    }

    override serverError(error: any) {
        this.onFail(error.response as AxiosResponse);
    }

    // 子类可以覆盖默认行为
    protected onFailDefaultHandle(response: AxiosResponse) {
        const responseData = response.data as ResponseData;
        console.log(responseData.message);
    }

    protected onFail(response: AxiosResponse) {
        const code = response.data.code as keyof typeof SPECIAL_ERROR_CODES;
        const codeHandleItem = SPECIAL_ERROR_CODES[code];
        if (codeHandleItem) {
            if (codeHandleItem.handler) {
                codeHandleItem.handler(SPECIAL_ERROR_CODES[code], response);
            } else {
                new VCairnEvent(code, [SPECIAL_ERROR_CODES[code], response.data]).emit();
            }
        } else {
            this.onFailDefaultHandle(response);
        }
    }


}