import {BtitCommand} from "@/libs/btit/BtitCommand.ts";
import type {VCairnEvent} from "v-cairn";

export class BtitSequenceCommand extends BtitCommand {
    public nextEvents: VCairnEvent[]|undefined;

    constructor(nextEvents?: VCairnEvent[]) {
        super();
        this.nextEvents = nextEvents;
    }

    executeNextCommand() {
        if (this.nextEvents) {
            this.nextEvents.forEach(evt => {
                evt.emit();
            })

        }
    }
}