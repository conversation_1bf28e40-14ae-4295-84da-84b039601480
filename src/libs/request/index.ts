import axios, { type AxiosRequestConfig, type AxiosResponse } from 'axios';

import type { ResponseData } from './types.ts';
import { appConfig, SPECIAL_ERROR_CODES } from '@/app-config.ts';
import evtBus from '@/libs/request/evtBus.ts';
import { ElMessageBox } from 'element-plus';

function hasErrorInSuccess(response: AxiosResponse) {
  const responseData = response.data as ResponseData;
  return responseData?.code !== 200;
}

function defaultAlert(message: string, title: string = '提示') {
  ElMessageBox.alert(message, title, { type: 'error' }).catch((e: Error) =>
    console.log(e)
  );
}

function errorInSuccessHandler(response: AxiosResponse) {
  const respondData = response.data as ResponseData;
  try {
    const code =
      respondData.code.toString() as keyof typeof SPECIAL_ERROR_CODES;
    const codeHandleItem = SPECIAL_ERROR_CODES[code];
    if (codeHandleItem) {
      if (codeHandleItem.handler) {
        codeHandleItem.handler(SPECIAL_ERROR_CODES[code], response);
      } else {
        evtBus.emit(code, [SPECIAL_ERROR_CODES[code], response]);
      }
    } else {
      defaultAlert(respondData.message);
    }
  } catch (error: any) {
    console.log('errorInSuccessHandler', error);
    defaultAlert(error?.message || respondData.message);
  }
}

export function defaultSuccessHandler(response: AxiosResponse) {
  console.log('response', response);
  return !hasErrorInSuccess(response)
    ? (response.data as ResponseData)?.data || {}
    : errorInSuccessHandler(response);
}

export function defaultErrorHandler(error: any) {
  if (error.response) {
    errorInSuccessHandler(error.response);
  } else {
    console.log(error);
    defaultAlert(error?.message || '未知错误');
  }
}

function createRequest(value?: AxiosRequestConfig) {
  return axios.create(value || {});
}

const request = createRequest({
  timeout: 300000,
  baseURL: `${appConfig.protocol}://${appConfig.apiBaseUrl}${appConfig.apiPath}`,
  withCredentials: true,
});

export async function send(options: AxiosRequestConfig) {
  return request(options)
    .then(defaultSuccessHandler)
    .catch(defaultErrorHandler);
}

// export function addRequestInterceptor(axios: AxiosInstance, fun: any) {
//     return axios.interceptors.request.use(fun);
// }
// export function addResponseInterceptor(axios: AxiosInstance, fun: any) {
//     return axios.interceptors.response.use(fun);
// }
// export function removeInterceptor(axios: AxiosInstance, id:number, type: 'request'|'response' = 'response') {
//     axios.interceptors[type].eject(id);
// }
