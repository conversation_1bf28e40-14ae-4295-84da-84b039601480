import type { ResponseData } from '@/libs/request/types.ts';
import { ElMessageBox } from 'element-plus';
import type { AxiosResponse } from 'axios';

export type ErrorCodeHandleItem = {
  label: string;
  desc: string;
  handler?: (item: ErrorCodeHandleItem, response: AxiosResponse) => void;
};

export const appConfig = {
  host: import.meta.env.VITE_HOST+import.meta.env.BASE_URL,
  apiBaseUrl: import.meta.env.VITE_API, // localhost:8888 //ea.equali.cn:4082
  apiPath: '/ipd/api/platform',
  protocol: 'http',
};
export const SPECIAL_ERROR_CODES: Record<string, ErrorCodeHandleItem> = {
  '401': {
    label: 'reLogin',
    desc: '需重新登录，重定向到登录界面',
    handler: () => {
      window.location.href = window.location.origin + '/#/login';
    },
  },
  '403': {
    label: 'noPermission',
    desc: '权限不足，请联系管理员',
    handler: (_: ErrorCodeHandleItem, response: AxiosResponse) => {
      const data = response.data as ResponseData;
      ElMessageBox.alert(data.message, '权限不足', { type: 'error' }).catch(
        (e) => console.log(e)
      );
    },
  },
};
export const tableConfig = {
  optColumnAttr: {
    align: 'center',
    width: 48,
    className: 'tr-more',
  },
};
