/// <reference types="vite/client" />
declare module "*.vue" {
    import { DefineComponent } from "vue"
    const component: DefineComponent<{}, {}, any>
    export default component
}

declare module "bpmn-js-properties-panel" {
    const BpmnPropertiesPanelModule;
    const BpmnPropertiesProviderModule;
    const PaletteProviderModule;
    export {
        BpmnPropertiesPanelModule,
        BpmnPropertiesProviderModule,
        PaletteProviderModule
    }
}