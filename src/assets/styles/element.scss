html:root {
  --el-color-primary: var(--color-primary);
  --el-color-danger: var(var(--color-danger));
  --el-text-color-placeholder: var(--text-color-placeholder);
  --el-border-color: var(--border-color);
  --el-border-radius-base: var(--border-radius);
}

html {
  .el-scrollbar {
    .el-scrollbar__bar {
      .el-scrollbar__thumb {
        background: transparent;
        opacity: 1;
        &::after {
          content: '';
          position: absolute;
          border-radius: var(--el-border-radius-base);
          background: #d9d9d8;
        }
      }
      &.is-horizontal {
        height: 8px;
        &::before,
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 16px;
          height: 100%;
          background: url('/arrow-left.png') no-repeat center;
          background-size: contain;
        }
        &::after {
          left: unset;
          right: 0;
          background-image: url('/arrow-right.png');
        }
        .el-scrollbar__thumb {
          &::after {
            height: 100%;
            top: 0;
            left: 16px;
            right: 16px;
          }
        }
      }
      &.is-vertical {
        width: 8px;
        &::before,
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 16px;
          background: url('/arrow-up.png') no-repeat center;
          background-size: contain;
        }
        &::after {
          top: unset;
          bottom: 0;
          background-image: url('/arrow-down.png');
        }
        .el-scrollbar__thumb {
          &::after {
            width: 100%;
            left: 0;
            top: 16px;
            bottom: 16px;
          }
        }
      }
    }
  }

  .el-icon,
  .el-menu .el-icon,
  .el-menu-item.is-active .el-icon {
    color: var(--color-icon);
  }

  .el-icon.is-loading {
    color: #fff;
  }

  .el-input {
    --el-input-icon-color: var(--color-icon);
    .el-input__suffix-inner {
      .el-icon {
        order: 2;
      }
      .el-input__clear {
        order: 1;
        color: transparent;
        background: url('/input-clear.png') no-repeat center;
        background-size: 85%;
      }
      .el-input__count {
        order: 3;
      }
    }
    .el-input__wrapper {
      box-shadow: unset;
      border: 1px solid var(--form-border-color);
      &:hover {
        border-color: var(--form-hover-border-color);
      }
      &.is-focus {
        box-shadow: var(--active-shadow);
      }
    }
    &.readonly {
      .el-input__wrapper {
        // box-shadow: 0 0 0 1px var(--el-input-border-color,var(--el-border-color)) inset;
        border-color: rgba(217, 217, 217, 1);
        background-color: rgba(245, 245, 245, 1);
      }
    }
  }

  .el-textarea {
    .el-textarea__inner {
      box-shadow: unset;
      border: 1px solid var(--form-border-color);
      &:hover {
        border-color: var(--form-hover-border-color);
      }
      &.is-focus {
        box-shadow: var(--active-shadow);
      }
    }
    &.readonly {
      .el-textarea__inner {
        border-color: rgba(217, 217, 217, 1);
        background-color: rgba(245, 245, 245, 1);
      }
    }
  }

  .el-select {
    .el-select__wrapper {
      box-shadow: unset;
      border: 1px solid var(--form-border-color);
      &.is-hovering:not(.is-focused) {
        box-shadow: unset;
      }
    }
    .el-select__suffix {
      .el-select__caret {
        color: #737277;
      }
    }
  }

  .el-button {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: -4px;
      bottom: -4px;
      left: -4px;
      right: -4px;
      border-radius: 7px;
      border: 2px solid transparent;
    }
    &:hover {
      color: rgba(51, 51, 51, 1);
      background-color: rgba(236, 236, 239, 1);
      border-color: rgba(137, 136, 141, 1);
    }
    &:active {
      &::after {
        border-color: rgba(102, 143, 214, 1);
      }
    }
    &:focus-visible {
      outline: unset;
    }
    &--primary {
      // border: 1px solid transparent;
      &:hover {
        color: #fff;
        background-color: var(--el-color-primary);
        border-color: transparent;
        box-shadow: inset 0px 0px 0 1px rgba(44, 71, 130, 1);
      }
      &:active {
        &::after {
          border-color: rgba(102, 143, 214, 1);
        }
      }
    }
    &--danger {
      &:hover {
        color: #fff;
        background-color: var(--el-color-danger);
        border-color: transparent;
        box-shadow: inset 0px 0px 0 1px var(--el-color-danger);
      }
      &:active {
        &::after {
          border-color: var(--el-color-danger);
        }
      }
    }
    &.is-disabled {
      background-color: rgba(238, 238, 238, 1);
      &:hover {
        background-color: rgba(238, 238, 238, 1);
      }
      &:active {
        &::after {
          border-color: transparent;
        }
      }
    }
  }

  .el-checkbox {
    height: auto;
  }

  .el-radio {
    --el-radio-input-border: 1px solid var(--form-border-color);
    .is-checked {
      .el-radio__inner {
        background: transparent;
        &::after {
          width: 6px;
          height: 6px;
          background-color: var(--el-color-primary);
        }
      }
    }
  }

  .el-tabs {
    --el-tabs-header-height: 48px;
    &.el-tabs--top {
      .el-tabs__active-bar {
        &::after {
          position: absolute;
          left: -15px;
          right: -15px;
          bottom: 0;
          height: 100%;
          content: '';
          background: var(--el-color-primary);
        }
      }
      .el-tabs__item {
        margin: 0 5px;
        &:nth-child(2) {
          padding-left: 20px;
        }
        &:last-child {
          padding-right: 20px;
        }
      }
    }
  }

  .el-popper {
    --el-popover-padding: 8px;
    margin-top: -8px;
    .el-popper__arrow {
      opacity: 0;
    }
    &[role='tooltip'] {
      .el-popper__arrow {
        opacity: 1;
      }
    }
  }

  .el-dropdown-menu {
    &.border {
      .el-dropdown-menu__item {
        border-bottom: 1px solid rgba(233, 233, 233, 1);
        &:last-of-type {
          border-bottom: 0;
        }
      }
    }
  }

  .el-table {
    --el-table-row-hover-bg-color: var(--table-row-hover-bg-color);
    border-top: var(--el-table-border);
    &--border {
      border-top: 0;
    }
    thead th {
      color: #161616;
    }
    .cell {
      padding: 0 8px;
    }
    .el-table-column--selection {
      text-align: center;
    }
    .el-table-column--selection .cell {
      padding-left: 0;
      padding-right: 0;
    }
    .el-table__expand-icon {
      width: 14px;
      height: 14px;
      line-height: 14px;
      .el-icon {
        font-size: 14px;
        font-weight: bold;
      }
    }
  }

  .el-tree {
    --el-tree-node-content-height: 32px;
    .el-tree-node__expand-icon {
      background: url('/tree-arrow.png') no-repeat center;
      background-size: 50%;
      svg {
        opacity: 0;
      }
    }
  }

  .el-dialog {
    --el-dialog-title-font-size: 16px;
    padding-left: 24px;
    padding-right: 24px;
    .el-dialog__title {
      color: rgba(0, 0, 0, 0.85);
      font-weight: bold;
    }
    .el-dialog__headerbtn {
      width: 62px;
      height: 56px;
    }
    .el-dialog__close {
      font-size: 22px;
    }
    .el-dialog__body {
      padding-top: var(--el-dialog-padding-primary);
    }
  }

  .el-date-editor {
    .el-input__prefix {
      order: 3;
      .el-icon {
        margin-left: 8px;
        margin-right: 0;
        background: url('/icon-calendar.png') no-repeat center;
        background-size: contain;
        svg {
          opacity: 0;
        }
      }
    }
    .el-input__suffix {
      order: 1;
    }
  }

  .el-date-picker {
    .el-date-table td .el-date-table-cell .el-date-table-cell__text {
      border-radius: var(--el-border-radius-base);
    }
  }

  .el-dropdown__popper {
    --el-dropdown-menuItem-hover-fill: var(--hover-bg-color);
    --el-dropdown-menuItem-hover-color: var(--el-text-color-regular);
  }

  .el-message-box {
    --el-messagebox-padding-primary: 18px;

    padding-left: 24px;
    padding-right: 24px;
    padding-bottom: 24px;
    .el-message-box__title {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: bold;
      line-height: 22px;
    }
    .el-message-box__headerbtn {
      right: 12px;
      top: 10px;
    }
    .el-message-box__close {
      font-size: 26px;
    }
    .el-message-box__container {
      gap: 8px;
      .el-message-box__status {
        font-size: 18px;
      }
    }
  }

  .el-form-item.is-error .el-input-tag__wrapper,
  .el-form-item.is-error .el-input-tag__wrapper.is-focus,
  .el-form-item.is-error .el-input-tag__wrapper:focus,
  .el-form-item.is-error .el-input-tag__wrapper:hover,
  .el-form-item.is-error .el-input__wrapper,
  .el-form-item.is-error .el-input__wrapper.is-focus,
  .el-form-item.is-error .el-input__wrapper:focus,
  .el-form-item.is-error .el-input__wrapper:hover,
  .el-form-item.is-error .el-select__wrapper,
  .el-form-item.is-error .el-select__wrapper.is-focus,
  .el-form-item.is-error .el-select__wrapper:focus,
  .el-form-item.is-error .el-select__wrapper:hover,
  .el-form-item.is-error .el-textarea__inner,
  .el-form-item.is-error .el-textarea__inner.is-focus,
  .el-form-item.is-error .el-textarea__inner:focus,
  .el-form-item.is-error .el-textarea__inner:hover {
    border-color: var(--color-danger);
    box-shadow: var(--error-shadow);
  }
}

.upload-file {
  width: 100%;
  .el-upload .el-upload-dragger {
    padding-top: 8px;
    padding-bottom: 8px;
    background-color: rgba(76, 118, 192, 0.04);
    border: 1px dashed rgba(76, 118, 192, 0.85);
  }
  .title {
    color: rgba(76, 118, 192, 1);
    font-size: 12px;
    line-height: 16px;
  }
  .tip {
    font-size: 10px;
    color: rgba(0, 0, 0, 0.45);
    line-height: 12px;
    margin-top: 6px;
  }
}
