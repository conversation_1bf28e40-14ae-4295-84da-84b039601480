:root {
  --color-primary: #4d75c4;
  --color-danger: #dd2b0e;
  --color-icon: #737278;
  --text-color-placeholder: #999999;
  --hover-bg-color: #ECECEF;
  --border-color: #dcdfe6;
  --form-border-color: #C1C1C3;
  --form-hover-border-color: #333237;
  --base-margin: 8px;
  --border-radius: 4px;
  --icon-size: 16px;
  --active-shadow: inset 0 0 0 1px #fff,0 0 0 1px #fff,0 0 0 3px var(--color-primary);
  --error-shadow: inset 0 0 0 1px #C1C1C3;
  --table-row-hover-bg-color: #E8F3FC;
  --select-option-bg: #F2F2F2; 

  font-family: "Microsoft YaHei", "PingFang SC", "sans-serif";
  box-sizing: border-box;
}
*,
::before,
::after {
  box-sizing: inherit;
}
body {
  font-size: 14px;
  line-height: 1.4;
  padding: 0;
  margin: 0;
  overflow: hidden;
}
#app {
  min-width: 730px;
}

//滚动条-start
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
  border: 1px solid transparent; /* 透明边框模拟外边距 */
  background-clip: content-box;
}
:hover::-webkit-scrollbar-thumb,
::-webkit-scrollbar-thumb:hover {
  background-color: #D9D9D8;
}
::-webkit-scrollbar-button {
  width: 0px;
  height: 0px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 0;
  border: 1px solid transparent;
}
:hover::-webkit-scrollbar-button,
::-webkit-scrollbar-button:hover {
  background-size: contain;
}
::-webkit-scrollbar-button:single-button:vertical:decrement {
  height: 16px;
  background-image: url('/arrow-up.png'); 
}
::-webkit-scrollbar-button:single-button:vertical:increment {
  height: 16px;
  background-image: url('/arrow-down.png'); 
}
::-webkit-scrollbar-button:single-button:horizontal:decrement {
  width: 16px;
  background-image: url('/arrow-left.png');
}
::-webkit-scrollbar-button:single-button:horizontal:increment {
  width: 16px;
  background-image: url('/arrow-right.png');
}

.scrollbar-show ::-webkit-scrollbar-thumb {
  background: #D9D9D8;
}
.scrollbar-show ::-webkit-scrollbar-button {
  background-size: contain;
}
// 滚动条-end

.hidden {
  display: none;
}

.flex {
  display: flex;
  &-1 {
    flex: 1;
  }
  &-wrap {
    flex-wrap: wrap;
  }
  &-center {
    align-items: center;
  }
  &-jc-center {
    justify-content: center;
  }
  &-jc-sb {
    justify-content: space-between;
  }
}

.link {
  color: #4D75C4;
  cursor: pointer;
}

.w-100 {
  width: 100%;
}

// table相关
.table {
  .tr-more {
    .cell {
      overflow: visible;
    }
  }
  .more-opt {
    position: relative;
    width: 32px;
    height: 32px;
    margin-left: -100%;
    outline: unset;
    .icon {
      position: absolute;
      top: 50%;
      left: 100%;
      display: flex;
      align-items: center;
      width: 100%;
      height: 100%;
      font-size: 16px;
      font-weight: bold;
      transform: translateY(-50%) rotate(90deg);
      cursor: pointer;
      border-radius: var(--border-radius);
     
    }
    &:hover {
      .icon {
        background: var(--hover-bg-color);
      }
    }
    &:active {
      .icon {
        box-shadow: var(--active-shadow);
      }
    }
  }
}

.ipt-table {
  td .cell {
    padding-top: 8px;
    padding-bottom: 8px;
    margin-top: -6px;
    margin-bottom: -6px;
  }
}

.header {
  .right-opt {
    .item {
      margin-left: var(--base-margin);
    }
    .icon {
      font-size: 16px;
      cursor: pointer;
    }
  }
}

.icon-tooltip {
  font-size: var(--icon-size);
  height: 1em;
  line-height: 1em;
}

.no-data {
  padding: 36px 0;
  .img {
    display: block;
    width: 120px;
    margin: 0 auto;
  }
  .txt {
    line-height: 1.6;
    margin-top: 16px;
  }
}