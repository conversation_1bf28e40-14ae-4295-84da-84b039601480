// 公用类地址
import {appConfig} from "@/app-config.ts";

export const host = `${appConfig.protocol}://${appConfig.host}`;

export const logoTextImage = {
    src: `${import.meta.env.BASE_URL}brand/logo-txt.png`,
    alt: 'BTIPD'
}
export const logoImage = {
    src: `${import.meta.env.BASE_URL}brand/logo-img.png`,
    alt: 'BTIPD'
}

// 登录页
export const mainLoginUrl = `${host}#/login`;
// main首页
// export const mainUrl = `${host}`;
// projects 首页
export const projectsUrl = `${host}projects/index.html`;
// ea首页
// export const eaMainUrl = `${host}/ea/index.html`;
// 登录接口
export const loginUrl = '/permission/login';
// 获取用户信息，包括权限角色
export const userInfo = '/permission/getInfo';
// 获取当前用户可见路由？
export const userRoutes = '/permission/getRouters';
// 获取用户列表
export const userList = '/permission/sys-user/list';
// 文件上传
export const uploadFile = '/basic/cos/upload';
// 获取字典数据列表
export const dictDataList = '/basic/sys-dict-data/list';