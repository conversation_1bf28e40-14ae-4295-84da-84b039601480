// tasks模块的后端服务地址

// ==================== 项目管理相关接口 ====================
// 获取项目列表
export const projectsTreeList = '/projectmanage/project/tree';

// ==================== 项目任务相关接口 ====================
// 获取项目任务分页列表
export const projectTasksList = '/projectmanage/project-task/tree';

// 获取项目任务列表
export const projectTasksListAll = '/projectmanage/project-task/list';

// 新增项目任务
export const addProjectTask = '/projectmanage/project-task/add';

// 修改项目任务
export const editProjectTask = '/projectmanage/project-task/edit';

// 删除项目任务
export const deleteProjectTask = '/projectmanage/project-task/delete';

// 批量删除项目任务
export const batchDeleteProjectTask =
  '/projectmanage/project-task/batch/delete';

// 接受项目任务
export const acceptProjectTask = '/projectmanage/project-task/accept';

// 驳回项目任务
export const rejectProjectTask = '/projectmanage/project-task/reject';

// 项目任务变更
export const changeProjectTask = '/projectmanage/project-task/change';

// 项目任务添加关注
export const followProjectTask = '/projectmanage/project-task/follow';

// 项目任务添加标签
export const addProjectTaskTag = '/projectmanage/project-task/add/tag';

// 项目任务删除标签
export const removeProjectTaskTag = '/projectmanage/project-task/delete/tag';

// 导出项目任务
export const exportProjectTask = '/projectmanage/project-task/export';

// 导入项目任务
export const importProjectTask = (planId: string | number) =>
  `/projectmanage/project-task/import/${planId}`;

// 导出项目任务模板
export const exportProjectTaskTemplate = '/projectmanage/project-task/template';

// 获取项目任务详情
export const projectTaskDetail = (id: string | number) =>
  `/projectmanage/project-task/detail/${id}`;

// ==================== 流程任务相关接口 ====================
// 获取我的待办任务列表
export const myTodoTasksList = (pageSize: number, curPage: number) =>
  `/task/todo/page/${pageSize}/${curPage}`;

// 获取所有待办任务列表
export const allTodoTasksList = (pageSize: number, curPage: number) =>
  `/task/page/${pageSize}/${curPage}`;

// 用taskid查询formkey
export const getTaskInfo = (taskId: string | number) => `/task/info/${taskId}`;

// 用processInstanceId查询当前激活的任务列表
export const getCurrentTasks = (processInstanceId: string | number) =>
  `/task/find/task/current/${processInstanceId}`;

// 办理一个用户任务
export const completeTask = (taskId: string | number) =>
  `/task/complete/${taskId}`;

// ==================== 独立任务相关接口 ====================
// 获取独立任务分页列表
export const independentTasksList = (pageSize: number, pageNum: number) =>
  `/projectmanage/inde-task/page/${pageSize}/${pageNum}`;

// 新增独立任务
export const addIndependentTask = '/projectmanage/inde-task/add';

// 查询独立任务详情
export const getIndependentTaskDetail = (id: string | number) =>
  `/projectmanage/inde-task/${id}`;

// 修改独立任务
export const editIndependentTask = '/projectmanage/inde-task/edit';

// 删除独立任务
export const deleteIndependentTask = '/projectmanage/inde-task/delete';

// 接受独立任务
export const acceptIndependentTask = '/projectmanage/inde-task/accept';

// 驳回独立任务
export const rejectIndependentTask = '/projectmanage/inde-task/reject';

// 独立任务变更
export const changeIndependentTask = '/projectmanage/inde-task/change';

// 独立任务关注
export const followIndependentTask = '/projectmanage/inde-task/follow';

// 独立任务添加标签
export const addIndependentTaskTag = '/projectmanage/inde-task/add/tag';

// 独立任务删除标签
export const removeIndependentTaskTag = '/projectmanage/inde-task/remove/tag';

// 导出独立任务模板
export const exportIndependentTaskTemplate =
  '/projectmanage/inde-task/template';

// 导入独立任务
export const importIndependentTask = '/projectmanage/inde-task/import';

// 导出独立任务
export const exportIndependentTask = '/projectmanage/inde-task/export';

// ==================== 任务标签相关接口 ====================

// 获取任务标签任务列表
export const taskTagsTaskList = '/projectmanage/config-task-tag/task/list';

// 获取任务标签列表
export const taskTagsList = '/projectmanage/config-task-tag/list';
// 新增任务标签
export const addTaskTag = '/projectmanage/config-task-tag/add';
// 编辑任务标签
export const editTaskTag = '/projectmanage/config-task-tag/edit';
// 删除任务标签
export const deleteTaskTag = '/projectmanage/config-task-tag/delete';

// 批量保存任务标签
export const batchSaveTaskTag = '/projectmanage/config-task-tag/batch/save';

// @deprecated 请使用 taskTagsList
export const taskTags = taskTagsList;

// ==================== 工作流相关接口 ====================
// 获取工作流定义列表
export const workflowDefinitionList = '/process/definition/list';

// ==================== 兼容性接口（保持向后兼容）====================
// @deprecated 请使用 projectTasksList
export const tasksList = projectTasksList;

// @deprecated 请使用 projectTaskDetail
export const taskDetail = projectTaskDetail;

// @deprecated 请使用 deleteProjectTask
export const deleteTask = deleteProjectTask;

// @deprecated 请使用 acceptProjectTask
export const acceptTask = acceptProjectTask;

// @deprecated 请使用 rejectProjectTask
export const rejectTask = rejectProjectTask;

// 我的任务
export const myTasksList = '/projectmanage/my-task/list';
