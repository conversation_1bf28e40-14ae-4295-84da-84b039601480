import { ref, computed } from "vue";
import type { EventsType, PropsType } from "./types";
import { isNumber } from "element-plus/es/utils/types.mjs";

export function BtPageCtrl(emits: EventsType, props: PropsType) {
  const inputValue = ref<number>(props.modelValue);

  const pages = computed(()=>{
    const size = isNumber(props.size) ? props.size : 10;
    return Math.ceil(props.total / size);
  })
  
  function handleInput(value: string) {
    let num = parseInt(value);
    if(num > pages.value) {
      inputValue.value = pages.value;
    } else {
      inputValue.value = num;
    }
    emits("update:modelValue", inputValue.value);
    if(num && num > 0 && num <= pages.value) {
      emits("change",num);
    }
  };
  function handleChange (value: number){
    let num = 0;
    if(inputValue.value) {
      num = Number(inputValue.value) + value;
      num = num > pages.value ? pages.value : num;
      num = num <= 1 ? 1 : num;
    } else {
      num = 1;
    }
    inputValue.value = num;
    emits("update:modelValue", num);
    emits("change", num);
  };

  return {
    handleInput,
    handleChange,
    inputValue,
    pages,
  };
}
