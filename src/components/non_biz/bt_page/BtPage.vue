<script setup lang="ts">
import { watch } from "vue";
import { BtPageCtrl } from "./BtPageCtrl.ts";
import type {EventsType,PropsType} from "./types.ts";
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";

const props = withDefaults(
    defineProps<PropsType>(),
    {
      modelValue: 1,
      total: 0,
      size: 10,
    }
);

watch(
  () => props.modelValue,
  (newVal) => {
    inputValue.value = newVal;
  }
);

const emits = defineEmits<EventsType>();
const {
  inputValue, pages, handleInput, handleChange,
} = BtPageCtrl(emits,props);


</script>
<template>
  <div class="bt_page">
    <div class="txt">共{{ total }}条/{{ pages }}页</div>
    <div class="ul">
        <el-button class="li btn" @click="handleChange(-1)">
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <div class="li jump">
            <el-input  class="ipt" v-model="inputValue" @input="handleInput" :formatter="(value:string) => value.replace(/[^\d]/g, '')"></el-input>
            <span>页</span>
        </div>
        <el-button class="li btn" @click="handleChange(1)">
            <el-icon><ArrowRight /></el-icon>
        </el-button>
    </div>
  </div>
</template>
<style scoped lang="scss">
.bt_page {
  display: inline-flex;
  align-items: center;
  .txt {
    font-size: 14px;
    color: #000;
    line-height: 22px;
  }
  .ul {
    display: flex;
    align-items: center;
    margin-left: 12px;
    .li {
      display: flex;
      align-items: center;
      height: 28px;
      padding: 0 8px;
      // border-radius: var(--el-border-radius-base);
      // border: 1px solid var(--el-border-color);
      margin: 0 2px;
    }
    .btn {
      cursor: pointer;
    }
    .jump {
      padding: 0;
      border: 0;
      margin-right: 6px;
      .ipt {
        width: 48px;
        height: 100%;
      }
      span {
        margin-left: 4px;
      }
    }
  }
}
</style>
