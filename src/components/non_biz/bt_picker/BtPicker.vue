<script setup lang="ts">
import type { EventsType, PropsType } from "./types.ts";
import { BtPickerCtrl } from './BtPickerCtrl.ts';
import SelectList from "./SelectList.vue";

const props = withDefaults(
    defineProps<PropsType>(),
    {
      title: '',
      multiple: false,
      modelValue: () => [],
      list: () => [],
      placeholder: '请选择',
      collapseTags: true,
    }
);
const emits = defineEmits<EventsType>();
const {
  visible, togglePopover, optionAll,
  selectValue, targetList, targetSelect, handleSelectChange,
  close, confirm, resetList,
} = BtPickerCtrl(emits,props);

</script>
<template>
  <div ref="selectRef" class="bt_picker" >
    <el-select
      class="select"
      popper-class="hidden"
      v-model="selectValue"
      clearable
      :multiple="multiple"
      :collapse-tags="collapseTags"
      :max-collapse-tags="maxCollapseTags"
      :placeholder="placeholder"
      @change="handleSelectChange"
      @click="togglePopover(true)"
    >
      <el-option v-for="(item,index) in optionAll" :key="index" :label="item.label" :value="item.value"></el-option>
    </el-select>
    <el-dialog 
      v-model="visible" 
      :title="title" 
      width="500" 
      draggable
      @closed="resetList"
    >
      <div class="bt_picker_cont">
        <div class="container">
          <div class="box">
            <SelectList :title="multiple?'已选择':title" :type="type" :multiple="multiple" :checkStrictly="checkStrictly" :list="targetList" :showSearch="showSearch" v-model="targetSelect"  ></SelectList>
          </div>
        </div>
        <div class="footer">
          <el-button @click="close">取消</el-button>
          <el-button @click="confirm" type="primary">确认</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped lang="scss">
.bt_picker {
  :deep(.select) {
    .el-select__wrapper {
      &:hover {
        border-color: var(--form-hover-border-color);
      }
      // box-shadow: 0 0 0 1px var(--el-border-color) inset;
    }
    .el-select__caret {
      background: url("/icon-transfer.png") no-repeat center;
      background-size: contain;
      color: transparent;
      &.el-select__clear {
        background: unset;
        color: var(--el-select-input-color);
      }
    }
    .el-select__suffix .el-icon {
      transform: unset !important;
    }
  }
  &.visible {
    :deep(.select) {
      .el-select__wrapper {
        box-shadow: unset;
        border: 1px solid var(--form-border-color);
      }
      .el-select__suffix {
        .el-icon {
          transform: rotate(180deg);
        }
      }
    }
  }
}
.el-form-item.is-error {
  .bt_picker {
    :deep(.select) {
      .el-select__wrapper {
        border-color: var(--color-danger);
        box-shadow: var(--error-shadow);
      }
    }
  }
}
.bt_picker_cont {
  // padding: 0 24px;
  margin-top: -8px;
  >.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    .title {
      color: rgba(0,0,0,0.85);
      font-size: 16px;
      font-weight: bold;
    }
    .close {
      font-size: 18px;
      cursor: pointer;
    }
  }
  >.footer {
    display: flex;
    justify-content: flex-end;
    padding: 24px 0 8px 0;
  }
  .container {
    display: flex;
    align-items: center;
    // padding: 16px 0;
    .box {
      width: 100%;
      height: 374px;
      border-radius: var(--el-border-radius-base);
      background-color: rgba(255,255,255,1);
      border: 1px solid var(--el-border-color);
    }
    .center {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 16px;
      .btn {
        width: 24px;
        height: 24px;
        margin: 8px auto;
      }
    }
  }
  
}
</style>
