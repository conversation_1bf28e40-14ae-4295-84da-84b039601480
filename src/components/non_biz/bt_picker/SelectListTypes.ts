export interface ListItem<T = any> {
  [key: string]: T;
}

export type PropsType = {
  type?: string,
  title: string;
  multiple?: boolean;
  list: ListItem[];
  modelValue: any;
  showSearch?: boolean;
  checkStrictly?: boolean;
};

export type EventsType = {
  (e: 'update:modelValue', key: any): void
  (e: "change", key: any): void;
};

export type EmitsType<T extends Record<string, any[]>> = {
  [K in keyof T]: (evt: K, ...args: T[K]) => void;
}[keyof T];
