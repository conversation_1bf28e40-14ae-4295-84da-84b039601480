import { ref, computed, watch, nextTick  } from "vue";
import type { EventsType, PropsType, ListItem } from "./types";

function treeToList(tree: ListItem[], pid: string | string = '0'): ListItem[] {
  const res = [];
  for(let item of tree) {
    res.push({
      label: item.label,
      value: item.value,
      parent: pid,
    })
    if(item.children) {
      res.push(...treeToList(item.children,item.value));
    }
  }
  return res;
}

export function BtPickerCtrl(emits: EventsType, props: PropsType) {
  const visible = ref(false);
  function togglePopover(val: boolean) {
    visible.value = val;
  }

  const optionAll = computed(()=>{
    return treeToList(props.list);
  })

  const targetList = computed(()=>{
    return props.list
  })
  const targetSelect = ref(props.modelValue)

  const selectValue = ref(props.modelValue);
  function handleSelectChange() {
    targetSelect.value = selectValue.value
    emits("update:modelValue",selectValue.value);
    emits("change",selectValue.value);
  }

  watch(
    () => props.modelValue,
    (newVal) => {
      selectValue.value = newVal;
      resetList()
    }
  );

  function confirm() {
    selectValue.value = targetSelect.value
    emits("update:modelValue",selectValue.value);
    emits("change",selectValue.value);
    close();
  }
  function close() {
    visible.value = false;
  }
  function resetList() {
    targetSelect.value = props.multiple ? [] : ''
    nextTick(()=>{
      targetSelect.value = props.modelValue
    })
  }


  return {
    visible,
    togglePopover,
    optionAll,
    selectValue,
    handleSelectChange,
    confirm,
    close,
    targetList,
    targetSelect,
    resetList,
  };
}
