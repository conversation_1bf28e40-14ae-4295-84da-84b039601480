import { ref, computed, watch, useTemplateRef, onMounted } from "vue";
import type { EventsType, PropsType, ListItem } from "./SelectListTypes";
import { ElTree } from 'element-plus';

function countTree(list: ListItem[]): number {
  let num = 0;
  for(let item of list) {
    num += 1;
    if(item.children) {
      num += countTree(item.children);
    }
  }
  return num;
}

export function SelectListCtrl(emits: EventsType, props: PropsType) {
  const listNum = computed(()=>{
    return countTree(props.list);
  })

  const keyword = ref("");
  const openSearch = ref(false);
  function toggleSearch() {
    openSearch.value = !openSearch.value;
  }

  const optionList = computed(() => {
    return props.list.filter((v) => {
      return v.label.includes(keyword.value);
    });
  });

  const selectValue = ref(props.modelValue);
  function handleChange(value: any) {
    emits("update:modelValue", value);
    emits("change", value);
  }

  const treeRef = useTemplateRef<InstanceType<typeof ElTree>>('treeRef')
  const treeProps = {
    children: "children",
    label: "label",
  };
  function handleNodeClick(e: any) {
    if(props.multiple) return
    selectValue.value = e.value
    handleChange(selectValue.value)
  }
  function handleTreeChange(_value: ListItem[],e: any) {
    selectValue.value = e?.checkedKeys;
    handleChange(e?.checkedKeys as ListItem[]);
  }
  const treeFilterNode = (value: string, data: ListItem) => {
    if (!value) return true
    return data.label.includes(value)
  }

  watch(keyword, (val) => {
    treeRef.value!.filter(val)
  })

  watch(
    () => props.modelValue,
    (newVal) => {
      selectValue.value = newVal;
      setSelect()
    }
  );

  onMounted(()=>{
    setSelect()
  })

  function setSelect() {
    if(props.multiple) {
      treeRef.value!.setCheckedKeys(props.modelValue)
    } else {
      treeRef.value!.setCurrentKey(props.modelValue)
    }
  }


  return {
    listNum,
    keyword,
    openSearch,
    toggleSearch,
    optionList,
    selectValue,
    handleChange,
    treeProps,
    handleNodeClick,
    handleTreeChange,
    treeFilterNode,
  };
}
