<script setup lang="ts">
import { ref } from "vue"

const visible = ref(false);

</script>
<template>
  <div class="bt_tooltip" @mouseenter="visible = true" @mouseleave="visible = false" @click="visible = false">
    <el-tooltip v-bind="$attrs" :visible="visible">
      <template v-for="slot in Object.keys($slots)" #[slot] :key="slot">
        <slot :name="slot" />
      </template>
    </el-tooltip>
  </div>
</template>
<style scoped lang="scss">
.bt_tooltip {
  
}
</style>
