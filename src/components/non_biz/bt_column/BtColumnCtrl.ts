import { ref, watch } from "vue";
import type { EventsType, PropsType, ListItem } from "./types.ts";

export function BtColumnCtrl(emits: EventsType, props: PropsType) {
  const dialogVisible = ref(false);
  function open() {
    dialogVisible.value = true;
  }
  function close() {
    dialogVisible.value = false;
  }

  function handleChange(value: ListItem[]) {
    emits("update:modelValue", value);
    emits("change", value);
  }
  
  const list = ref(<ListItem[]>[]);
  const selected = ref(<string[]>[]);
  function updateList(data: ListItem[]) {
    list.value = [...data];
    selected.value = data.filter(v=>v.show).map(v=>{return v.field});
  }
  function allowDrop(_draggingNode: any, _dropNode: any, type: string) {
    return type != 'inner';
  }
  function handleTreeCheck(_value: any, e: any) {
    selected.value = e?.checkedKeys;
  }
 

  function afterClose() {
    updateList(props.modelValue);
  }
  function confirm() {
    const fieldMap: ListItem = {};
    selected.value.forEach(v=>{fieldMap[v]=1});
    list.value.forEach(v=>{
      v.show = fieldMap[v.field] ? true : false;
    })
    handleChange(list.value);
    close();
  }

  watch(()=>props.modelValue,(newVal)=>{
    updateList(newVal);
  })

  updateList(props.modelValue)
  

  return {
    dialogVisible,
    close,
    open,
    list,
    handleChange,
    selected,
    allowDrop,
    handleTreeCheck,
    afterClose,
    confirm,
  };
}
