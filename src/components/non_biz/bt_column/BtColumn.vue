<script setup lang="ts">
import { BtColumnCtrl } from './BtColumnCtrl.ts';
import type { PropsType, EventsType } from './types.ts';
import { Setting } from '@element-plus/icons-vue';

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: () => [],
});

const emits = defineEmits<EventsType>();
const {
  dialogVisible,
  open,
  close,
  list,
  afterClose,
  allowDrop,
  selected,
  handleTreeCheck,
  confirm,
} = BtColumnCtrl(emits, props);

defineExpose({ open, close });
</script>
<template>
  <div class="bt_column_box">
    <slot>
      <BtTooltip
        class="icon-tooltip"
        effect="dark"
        content="自定义字段"
        placement="bottom"
      >
        <el-icon class="item icon" @click="open"><Setting /></el-icon>
      </BtTooltip>
    </slot>
    <el-dialog
      class="bt_column"
      v-model="dialogVisible"
      title="新建项目"
      width="800"
      :before-close="close"
      @closed="afterClose"
      style="margin-top: 0; margin-bottom: 0; height: 100%"
    >
      <template #header="{ titleId, titleClass }">
        <div class="header">
          <h4 :id="titleId" :class="titleClass">自定义字段</h4>
          <span class="txt">勾选需要显示的列，拖动列名进行排序。</span>
        </div>
      </template>
      <el-tree
        class="tree"
        :data="list"
        draggable
        node-key="field"
        default-expand-all
        :default-checked-keys="selected"
        show-checkbox
        :allow-drop="allowDrop"
        check-on-click-node
        @check="handleTreeCheck"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close">取消</el-button>
          <el-button type="primary" @click="confirm"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style scoped lang="scss">
.bt_column_box {
  font-size: var(--icon-size);
  line-height: 1em;
  height: auto;
}
.bt_column {
  .header {
    display: flex;
    align-items: center;
    .txt {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      margin-left: 20px;
    }
  }
  .tree {
    margin-top: calc(0px - var(--el-dialog-padding-primary));
    --el-tree-node-content-height: 40px;
    border: 1px solid var(--el-border-color-lighter);
    max-height: 60vh;
    overflow-y: auto;
    :deep(.el-tree-node) {
      border-top: 1px solid var(--el-border-color-lighter);
      &:first-of-type {
        border-top: 0;
      }
    }
  }
}
</style>
