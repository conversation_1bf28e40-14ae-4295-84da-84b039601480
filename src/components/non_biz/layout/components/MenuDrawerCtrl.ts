import { computed, ref } from "vue";

interface ListItem<T = any> {
  [key: string]: T;
}

function treeToList(tree: ListItem[]): ListItem[] {
  const res = [];
  for(let item of tree) {
    res.push({
      ...item,
      children: [],
    })
    if(item.children) {
      res.push(...treeToList(item.children));
    }
  }
  return res;
}

function getTreeIdx(list: ListItem[], pidx: number[] = []): ListItem {
  let result: ListItem = {};
  for(let i = 0; i < list.length; i++) {
    result[list[i].name] = [...pidx,i];
    if(list[i].children) {
      result = Object.assign({},result,getTreeIdx(list[i].children,result[list[i].name]));
    }
  }
  return result;
}

export function MenuDrawerCtrl() {
  const show = ref(false);

  function change(val: boolean) {
    show.value = val;
  }

  const collectList = computed(()=>{
    let result: ListItem[] = [];
    for(let i = 0; i < menuList.value.length; i++){
      const item = menuList.value[i];
      let base = item.base;
      let list = treeToList(item.list);
      for(let menu of list) {
        if(menu.collect) {
          result.push({...menu,path: base+menu.path, index: i});
        }
      }
    }
    return result;
  })
  const menuList = ref([
    {
      name: '项目', base: '/projects/index.html#',
      list: [
        { name: '项目库', path: '/library', icon: '项目库', collect: false, },
        { name: '项目群', path: '/group', icon: '项目群', collect: false, },
        { name: '我的项目', path: '/my', icon: '我的项目', collect: true, },
      ]
    },
    {
      name: 'EA', base: '/ea/index.html#',
      list: [
        { name: 'EA库', path: '/ea_libs', icon: 'EA库', collect: false, },
        { name: 'EA画布', path: '/ea_canvas', icon: 'EA画布', collect: false, },
        { name: 'EA治理', path: '/ea_mgm', icon: 'EA治理', collect: false, },
        { name: 'EA分析', path: '/ea_analysis', icon: 'EA分析', collect: false, },
        { name: '我的EA', path: '/my_ea', icon: '我的EA', collect: false, },
        { name: '元模型', path: '/meta_model', icon: '元模型', collect: false, },
      ]
    },
    {
      name: '需求', base: '/requirements/index.html#',
      list: [
        { name: '需求库', path: '/library', icon: '需求库', collect: false, },
        { name: '需求分析', path: '/analysis', icon: '需求分析', collect: false, },
        { name: '需求价值', path: '/value', icon: '需求价值', collect: false, },
        { name: '我的需求', path: '/my', icon: '我的需求', collect: false, },
      ]
    },
    {
      name: '合规', base: '/ea/index.html#',
      list: [
        { name: '合规文件', path: '/ea_libs', icon: '合规文件', collect: false, },
        { name: '合规需求库', path: '/ea_canvas', icon: '合规需求库', collect: false, },
        { name: '合规知识库', path: '/ea_mgm', icon: '合规知识库', collect: false, },
      ]
    },
    {
      name: '测试', base: '/ea/index.html#',
      list: [
        { name: '用例库', path: '/ea_libs', icon: '用例库', collect: false, },
        { name: '测试策略', path: '/ea_canvas', icon: '测试策略', collect: false, },
        { name: '功能测试', path: '/ea_mgm', icon: '功能测试', collect: false, },
        { name: '性能测试', path: '/ea_mgm', icon: '性能测试', collect: false, },
        { name: '接口测试', path: '/ea_mgm', icon: '接口测试', collect: false, },
        { name: '安全测试', path: '/ea_mgm', icon: '安全测试', collect: false, },
      ]
    },
    {
      name: '系统管理', base: '/sys_mgm/index.html#',
      list: [
        { name: '组织管理', path: '/organization', icon: '组织管理', collect: false, },
        { name: '权限管理', path: '/ea_canvas', icon: '权限管理', collect: false, },
        { 
          name: '系统设置', path: '/ea_mgm', icon: '系统设置', collect: false, 
          children: [
            { name: '菜单管理', path: '/', icon: '', collect: false, },
            { name: '字典管理', path: '/', icon: '', collect: false, },
            { name: '日志管理', path: '/', icon: '', collect: false, },
          ]
        },
      ]
    },
  ])
  const menuIdx = computed(()=>{
    let list = [];
    for(let item of menuList.value) {
      list.push(getTreeIdx(item.list))
    }
    return list;
  })

  function changeCollect(index: number, name: string) {
    const ids = menuIdx.value[index][name];
    let list = menuList.value[index].list;
    for(let i = 1; i < ids.length; i++) {
      list = list[ids[i-1]]!.children || [];
    }
    const lastIds = ids[ids.length-1];
    list[lastIds].collect = !list[lastIds].collect;
  }

  function jump(url: string){
    location.href = url;
  }

  return {
    show,
    change,
    menuList,
    collectList,
    changeCollect,
    jump,
  };
}
