<script setup lang="ts">
import SvgIcon from "@/components/non_biz/svg_icon/SvgIcon.vue";
import { SidebarCtrl } from "./SidebarCtrl";
import type { PropsType } from "./SidebarType";
import MenuDrawer from "./MenuDrawer.vue";
import {useLayoutStore} from "@/components/non_biz/layout/LayoutStore.ts";

withDefaults(defineProps<PropsType>(), {
  activeMenu: '',
});

const layoutStore = useLayoutStore();
const { 
  logoTextImage, keyword, formatPath, drawer, toggleDrawer,
} = SidebarCtrl();
</script>

<template>
  <el-aside :class="{ sidebar: true, collapse: layoutStore.isCollapse }">
    <div class="head">
      <img class="logo" :src="logoTextImage.src" :alt="logoTextImage.alt" />
      <div class="opt">
        <BtTooltip
          class="tooltip"
          effect="dark"
          content="隐藏侧边栏"
          placement="bottom"
        >
          <div class="item"><SvgIcon class="icon" name="collapse" @click="layoutStore.toggleCollapse" /></div>
        </BtTooltip>
        <BtTooltip
          class="tooltip"
          effect="dark"
          content="菜单"
          placement="bottom"
        >
          <div class="item"><SvgIcon class="icon " name="menu" @click="toggleDrawer" /></div>
        </BtTooltip>
      </div>
    </div>
    <div class="operate ">
      <ul class="quick">
        <BtTooltip
          class="tooltip"
          effect="dark"
          content="我负责的任务"
          placement="bottom"
        >
          <li><SvgIcon class="icon" name="功能图标1" /></li>
        </BtTooltip>
        <BtTooltip
          class="tooltip "
          effect="dark"
          content="我参与的任务"
          placement="bottom"
        >
          <li><SvgIcon class="icon" name="功能图标2" /></li>
        </BtTooltip>
        <BtTooltip
          class="tooltip "
          effect="dark"
          content="我待办的任务"
          placement="bottom"
        >
          <li><SvgIcon class="icon" name="功能图标3" /></li>
        </BtTooltip>
      </ul>
      <div class="search">
        <input class="ipt" v-model="keyword" />
        <div v-show="!keyword" class="placeholder">
          <SvgIcon class="icon" name="collapse" />
          <span>搜索或转到...</span>
        </div>
      </div>
    </div>
    <el-menu
      router
      :default-active="activeMenu"
      class="menu"
    >
      <el-menu-item
        v-for="(menu, index) in menus"
        :key="index"
        :index="formatPath(menu.path)"
      >
        <SvgIcon class="icon" :name="menu?.meta?.icon" />
        <template #title>
          {{ menu?.meta?.title }}
        </template>
      </el-menu-item>
    </el-menu>
    <MenuDrawer v-model="drawer"></MenuDrawer>
  </el-aside>
</template>

<style scoped lang="scss">
.sidebar {
  width: 200px;
  background: #fbfbfd;
  transition: all 0.6s;
  border-right: 1px solid var(--el-border-color);
  .head {
    width: 100%;
    height: 48px;
    display: flex;
    align-items: center;
    padding: 8px;
    // flex-wrap: wrap;
    background: #f2f1f4;
    .logo {
      max-width: 100%;
      width: 63px;
      height: 20px;
      object-fit: contain;
      margin: 6px 0;
    }
    .opt {
      padding: 4px;
      line-height: 1;
      flex: 1;
      text-align: right;
      white-space: nowrap;
      .tooltip {
        display: inline-block;
        margin-left: 16px;
        &:first-of-type {
          margin-left: 0;
        }
      }
      .item {
        display: inline-block;
      }
      .icon {
        font-size: 16px;
        cursor: pointer;
      }
    }
  }
  .operate {
    padding: 8px;
    background: #f2f1f4;
    .quick {
      display: flex;
      align-items: center;
      justify-content: space-between;
      list-style: none;
      padding: 0;
      margin: 0 auto;
      li {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 58px;
        height: 32px;
        background: #ffffff;
        border: 1px solid rgba(235, 234, 238, 1);
        .icon {
          font-size: 16px;
        }
      }
    }
    .search {
      position: relative;
      width: 100%;
      height: 32px;
      background: #ffffff;
      border: 1px solid rgba(235, 234, 238, 1);
      margin-top: 8px;
      white-space: nowrap;
      overflow: hidden;
      .ipt {
        position: relative;
        width: 100%;
        height: 100%;
        padding: 0 10px;
        background: transparent;
        border: 0;
        outline: unset;
        z-index: 1;
        &:focus + .placeholder {
          opacity: 0;
        }
      }
      .placeholder {
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: rgba(51, 51, 51, 1);
        z-index: 0;
        .icon {
          font-size: 14px !important;
          margin-right: 6px;
        }
      }
    }
  }
  >:deep(.el-menu) {
    position: relative;
    padding: 4px;
    background: transparent;
    border-right: 0;
    .el-menu-item {
      height: 32px;
      line-height: 32px;
      border-radius: var(--el-border-radius-base);
      margin: 4px 0;
      &::before {
        position: absolute;
        content: "";
        top: 50%;
        left: 4px;
        transform: translateY(-50%);
        width: 3px;
        height: 22px;
        background: var(--el-color-primary);
        opacity: 0;
      }
      &:hover {
        background: #e9e8ed;
      }
      &.is-active {
        color: inherit;
        background: #e9e8ed;
        &::before {
          opacity: 1;
        }
      }
    }
  }
  &.collapse {
    width: 0;
    .hide-collapse {
      display: none;
    }
    .head {
      height: auto;
    }
  }
}
</style>
