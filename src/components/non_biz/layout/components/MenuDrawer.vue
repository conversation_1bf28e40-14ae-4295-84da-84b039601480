<script setup lang="ts">
import { watch } from "vue";
import { MenuDrawerCtrl } from "./MenuDrawerCtrl";
import { Star, StarFilled } from "@element-plus/icons-vue";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
});

const { show, menuList, collectList, changeCollect, jump } = MenuDrawerCtrl();

show.value = props.modelValue;

watch(
  () => props.modelValue,
  (newVal) => {
    show.value = newVal;
  }
);
</script>

<template>
  <el-drawer
    v-model="show"
    direction="ttb"
    size="350"
    modal-class="menu-drawer"
    :with-header="false"
  >
    <div class="menu-box">
      <div class="list">
        <div class="li">
          <div class="title">收藏菜单</div>
          <el-menu class="menu">
            <el-menu-item
              v-for="(menu, mIdx) in collectList"
              :index="mIdx + ''"
              @click="jump(menu.path)"
            >
              <template #title>
                <div class="name-box">
                  <SvgIcon v-if="menu.icon" class="icon" :name="menu.icon" />
                  <div class="n-right">
                    <el-text truncated class="name s2">{{ menu.name }}</el-text>
                    <el-icon
                      :class="{ collect: true, on: menu.collect }"
                      @click.stop="changeCollect(menu.index, menu.name)"
                    >
                      <StarFilled v-if="menu.collect" />
                      <Star v-else />
                    </el-icon>
                  </div>
                </div>
              </template>
            </el-menu-item>
          </el-menu>
        </div>
      </div>
      <div class="flex-1" style="flex: 1; overflow: hidden;">
        <div class="list s2">
          <div v-for="(item, index) in menuList" :key="index" class="li">
            <div class="title">{{ item.name }}</div>
            <el-menu class="menu">
              <template v-for="(menu, mIdx) in item.list" :key="mIdx">
                <el-menu-item
                  v-if="!menu.children"
                  :index="mIdx + ''"
                  @click="jump(item.base + menu.path)"
                >
                  <template #title>
                    <div class="name-box">
                      <SvgIcon
                        v-if="menu.icon"
                        class="icon"
                        :name="menu.icon"
                      />
                      <div class="n-right">
                        <el-text truncated class="name">{{
                          menu.name
                        }}</el-text>
                        <el-icon
                          :class="{ collect: true, on: menu.collect }"
                          @click.stop="changeCollect(index, menu.name)"
                        >
                          <StarFilled v-if="menu.collect" />
                          <Star v-else />
                        </el-icon>
                      </div>
                    </div>
                  </template>
                </el-menu-item>
                <el-sub-menu v-else :index="mIdx + ''">
                  <template #title>
                    <div class="name-box">
                      <SvgIcon
                        v-if="menu.icon"
                        class="icon"
                        :name="menu.icon"
                      />
                      <div class="n-right">
                        <el-text truncated class="name">{{ menu.name }}</el-text>
                      </div>
                    </div>
                  </template>
                  <el-menu-item
                    v-for="(child, cIdx) in menu.children"
                    :index="mIdx + '-' + cIdx"
                    @click="jump(item.base + child.path)"
                  >
                    <template #title>
                      <div class="name-box">
                        <SvgIcon
                          v-if="child.icon"
                          class="icon"
                          :name="child.icon"
                        />
                        <div class="n-right">
                          <el-text truncated class="name">{{
                            child.name
                          }}</el-text>
                          <el-icon
                            :class="{ collect: true, on: child.collect }"
                            @click.stop="changeCollect(index, child.name)"
                          >
                            <StarFilled v-if="child.collect" />
                            <Star v-else />
                          </el-icon>
                        </div>
                      </div>
                    </template>
                  </el-menu-item>
                </el-sub-menu>
              </template>
            </el-menu>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>
<style lang="scss">
.menu-drawer {
  top: 48px;
  .el-drawer__body {
    padding: 0;
  }
  .menu-box {
    display: flex;
    height: 100%;
  }
  .list {
    display: flex;
    height: 100%;
    padding: 10px;
    &.s2 {
      overflow-x: auto;
    }
    .li {
      min-width: 200px;
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-right: 10px;
      &:last-of-type {
        margin-right: 0;
      }
      .title {
        font-size: 14px;
        font-weight: bold;
        line-height: 22px;
        padding: 6px 6px;
      }
      .menu {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        .name-box {
          display: flex;
          align-items: center;
          flex: 1;
          width: 100%;
          .n-right {
            flex: 1;
            display: flex;
            align-items: center;
            overflow: hidden;
            margin-right: -10px;
          }
          .icon {
            position: relative;
            left: 6px;
            margin-left: -24px;
            margin-right: 0;
            + .n-right {
              padding-left: 10px;
            }
          }
          .name {
            flex: 1;
          }
        }
        .collect {
          order: 2;
          font-size: 16px;
          opacity: 0;
          // margin-right: -10px;
          &.on {
            opacity: 1;
            font-size: 22px;
            color: #fba30a;
          }
        }
      }
    }
  }
  .el-menu {
    --el-menu-item-height: 32px;
    --el-menu-sub-item-height: 32px;
    // --el-menu-base-level-padding: 10px;
    border: 0;
    .el-menu-item {
      // padding: 0 8px !important;
      margin: 6px 0;
      &:hover {
        .collect {
          opacity: 1;
        }
      }
    }
  }
}
</style>
