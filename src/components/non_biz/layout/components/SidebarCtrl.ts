import { ref } from "vue";
import { logoTextImage } from "@/service_url/shared";

export function SidebarCtrl() {
  const keyword = ref("");

  function formatPath(path: string) {
    return path[0] == '/' ? path : '/'+path;
  }

  const drawer = ref(false);
  function toggleDrawer(){
    drawer.value = !drawer.value;
  }

  return {
    logoTextImage,
    keyword,
    formatPath,
    drawer,
    toggleDrawer,
  };
}
