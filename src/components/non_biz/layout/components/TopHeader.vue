<script setup lang="ts">
import { useRoute } from "vue-router";
import { Plus } from "@element-plus/icons-vue";
import { logoImage } from "@/service_url/shared";
import { useLayoutStore } from "@/components/non_biz/layout/LayoutStore.ts";
const route = useRoute();
const layoutStore = useLayoutStore();

function handleAdd() {
  layoutStore.addFunc && layoutStore.addFunc();
}
</script>

<template>
  <div class="top-header">
    <div class="left">
      <BtTooltip
        style="height: 1em;"
        effect="dark"
        content="展开侧边栏"
        placement="bottom"
        v-if="layoutStore.isCollapse"
      >
        <div class="collapse">
          <SvgIcon size="16" class="icon" name="collapse" @click="layoutStore.toggleCollapse" />
        </div>
      </BtTooltip>
      <el-breadcrumb class="breadcrumb" separator="/">
        <template v-for="(item, index) in route.matched">
          <el-breadcrumb-item
            v-if="item?.meta?.title"
            :key="index"
            :to="{ path: item.path }"
          >
            {{ item?.meta?.title }}
          </el-breadcrumb-item>
        </template>
      </el-breadcrumb>
    </div>
    <div class="right">
      <BtTooltip
        effect="dark"
        content="新建"
        placement="bottom"
      >
        <div class="item" @click="handleAdd">
          <el-icon><Plus /></el-icon>
        </div>
      </BtTooltip>
      <BtTooltip
        effect="dark"
        content="轻咨询"
        placement="bottom"
      >
        <div class="item name">轻</div>
      </BtTooltip>
      <BtTooltip
        effect="dark"
        content="客服"
        placement="bottom"
      >
        <div class="item">
          <SvgIcon class="icon " name="客服" />
        </div>
      </BtTooltip>
      <BtTooltip
        effect="dark"
        content="消息提示"
        placement="bottom"
      >
        <div class="item">
          <SvgIcon class="icon " name="消息提示" />
        </div>
      </BtTooltip>
      <BtTooltip
        effect="dark"
        content="个人账号"
        placement="bottom"
      >
        <div class="item">
          <img :src="logoImage.src" alt="" />
        </div>
      </BtTooltip>
    </div>
  </div>
</template>

<style scoped lang="scss">
.top-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  .left {
    display: flex;
    min-width: 0;
    align-items: center;
    .collapse {
      display: inline-block;
      margin-right: 6px;
      line-height: 16px;
      height: 16px;
    }
    .icon {
      cursor: pointer;
    }
    .breadcrumb {
      font-size: 12px;
      :deep(.el-breadcrumb__item) {
        .el-breadcrumb__inner {
          color: rgba(0, 0, 0, 0.45);
        }
        .el-breadcrumb__separator {
          margin: 0 4px;
        }
        &:last-of-type {
          .el-breadcrumb__inner {
            color: rgba(0, 0, 0, 0.65);
          }
        }
      }
    }
  }

  .right {
    display: flex;
    align-items: center;
    .item {
			display: flex;
			align-items: center;
			justify-content: center;
      width: 20px;
      height: 20px;
      margin-left: 16px;
			cursor: pointer;
			&.name {
				font-size: 12px;
				line-height: 14px;
				color: #fff;
				border-radius: 50%;
				background: linear-gradient(125.5deg, rgba(0,0,0,1) -6.07%,rgba(231,135,255,1) -6.07%,rgba(129,119,241,1) 100.84%);
			}
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
      }
      .icon {}
    }
  }
}
</style>
