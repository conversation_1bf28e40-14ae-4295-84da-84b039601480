import { ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import type { PropsType, Menu } from "./types"

export function LayoutCtrl(props: PropsType) {
  const currentRoute = useRoute();
  const router = useRouter();

  const menus = ref(<Menu[]>[]);
  if(props.menus && props.menus.length > 0) {
    menus.value = props.menus;
  } else {
    menus.value = getDefaultMenu();
  }

  const activeMenu = ref('');
  setActiveMenu();
  function setActiveMenu(){
    for(let item of menus.value) {
      if( currentRoute.fullPath.includes(item.path) ) {
        activeMenu.value = (item.path[0] == '/' ? '' : '/') + item.path;
        break;
      }
    }
  }

  watch(
    () => currentRoute.path,
    () => {
      setActiveMenu();
    }
  )

  function getDefaultMenu(): Menu[] {
    let result: Menu[] = [];
    let list = router.options.routes;
    if(list.length <= 1 && list[0].path == '/') {
      list = list[0]?.children || [];
    }
    for(let item of list) {
      if(item.meta?.hideMenu) continue;
      result.push({
        path: item.path,
        fullPath: item.path,
        meta: item?.meta || {},
      })
    }
    return result;
  }
  

  // const path = currentRoute.matched[1]
    // ? currentRoute.matched[1].path
    // : currentRoute.matched[0].path;

  return {
    menus,
    activeMenu,
    currentRoute,
  };
}
