import {defineStore} from "pinia";
import { ref, watch } from "vue";
import { useRoute } from 'vue-router';


export const useLayoutStore = defineStore('layoutStore', () => {
    const isCollapse = ref(false);

    function toggleCollapse() {
        isCollapse.value = !isCollapse.value;
    }

    const addFunc = ref(()=>{});
    function setAddAction(func: any) {
        addFunc.value = func;
    }

    const route = useRoute();
    watch(
        () => route.path,
        ()=>{
            addFunc.value = ()=>{};
        }
    )

    return { isCollapse, toggleCollapse, addFunc, setAddAction, }
});