<script setup lang="ts">
import { RouterView } from "vue-router";
import Sidebar from "./components/Sidebar.vue";
import TopHeader from "./components/TopHeader.vue";
import type { PropsType } from "./types.ts";
import { LayoutCtrl } from "./LayoutCtrl.ts";

const props = withDefaults(defineProps<PropsType>(), {
  menus: () => [],
});

const { menus, activeMenu } = LayoutCtrl(props);
</script>

<template>
  <div class="main-layout">
    <el-container class="container">
      <Sidebar :menus="menus" :activeMenu="activeMenu"></Sidebar>
      <el-container>
        <el-header class="header">
          <TopHeader></TopHeader>
        </el-header>
        <el-main class="main">
          <RouterView />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.main-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  min-width: 1280px; // 暂设
  > .container {
    overflow: hidden;
  }
  .header {
    --el-header-padding: 0 10px;
    --el-header-height: 48px;
    border-bottom: 1px solid rgba(215, 215, 215, 1);
  }
  .main {
    --el-main-padding: 0;
    height: 100%;
  }
}
</style>
