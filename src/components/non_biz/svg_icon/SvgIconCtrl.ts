import { ref } from "vue";

type ModuleType = Record<string, () => Promise<string>>;

const targetDir = '../../../assets/svg_icons';
const modules = import.meta.glob(`../../../assets/svg_icons/*.svg`, { query: '?raw', import: 'default' });
const svgCache: Record<string, string> = {};

export const useSvgIcon = async (name?: string) => {
    const c = ref<string>('');
    if (!name) return { c };
    try {
        if (svgCache[`${targetDir}/${name}.svg`]) {
            c.value = svgCache[`${targetDir}/${name}.svg`];
        } else {
            c.value = await (modules as unknown as ModuleType)[`${targetDir}/${name}.svg`]();
            svgCache[`${targetDir}/${name}.svg`] = c.value;
        }
        c.value = c.value.replace(/fill="black"/g, 'fill="currentColor"');
    } catch (error) {
        console.log(`加载svg图标文件失败: ${name}`);
    }
    return { c };
};
