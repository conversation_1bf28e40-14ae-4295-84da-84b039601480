<template>
  <template v-if="iconData">
    <el-icon v-html="iconData" :size="props.size" :color="props.color"></el-icon>
  </template>
  <template v-else>
    <el-icon :size="props.size" :color="props.color">
      <component :is="props.name"></component>
    </el-icon>
  </template>
</template>
<script setup lang="ts">
  import { useSvgIcon } from './SvgIconCtrl.ts';
  import {computed} from "vue";
  const props = defineProps({
    name: { type: String },
    size: { type: [String, Number], default: 16 },
    color: { type: String, default: 'black' }
  });
  const iconContent = await useSvgIcon(props.name).catch(e => console.log(e));
  const iconData = computed(() => {
    return iconContent!.c.value || '';
  });
</script>