<script setup lang="ts">
import type { EventsType, PropsType } from "./types";
import { BtSelectCtrl } from './BtSelectCtrl.ts';
import { Search } from "@element-plus/icons-vue";

const props = withDefaults(
    defineProps<PropsType>(),
    {
      type: 'list',
      modelValue: '',
      list: () => [],
      placeholder: '请选择',
      showAll: false,
      allValue: 'all',
      showSearch: false,
      collapseTags: true,
    }
);
const emits = defineEmits<EventsType>();
const {
  visible, togglePopover, optionAll, optionList, filterWord,
  selectValue, handleSelectChange, handleSingleSelect,
  clientWidth, keyword, handleSearch,
  checkAll, handleCheckAllChange, handleCheckItemChange,
  treeProps, treeChecked, handleNodeClick, handleTreeItemAll,
  treeFilterNode,
} = BtSelectCtrl(emits,props);

selectValue.value = props.modelValue

</script>
<template>
  <div :class="{'bt_select':true, 'visible': visible }" ref="selectRef">
    <el-popover
      ref="popoverRef"
      placement="bottom"
      :width="clientWidth"
      trigger="click"
      popper-style="padding: 0"
      @show="togglePopover(true)"
      @hide="togglePopover(false)"
    >
      <template #reference>
        <el-select
          class="select"
          popper-class="hidden"
          v-model="selectValue"
          :clearable="clearable"
          :multiple="multiple"
          :collapse-tags="collapseTags"
          :max-collapse-tags="maxCollapseTags"
          :placeholder="placeholder"
          @change="handleSelectChange"
        >
          <el-option v-if="showAll" label="全部" :value="allValue"></el-option>
          <el-option v-for="(item,index) in optionAll" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </template>
      <div class="bt_select_cont">
        <div v-if="showSearch" class="search">
          <el-input v-model="keyword" clearable :validateEvent="false" @clear="handleSearch">
            <template #suffix>
              <el-icon class="btn" @click="handleSearch" ><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <template v-if="type == 'tree'">
          <el-tree
            ref="treeRef"
            class="tree"
            :data="list"
            :props="treeProps"
            :expand-on-click-node="false"
            :filter-node-method="treeFilterNode"
          >
            <template #default="{ node, data }">
              <div class="tree-node" @click="handleNodeClick(data)">
                <el-checkbox v-if="multiple" :model-value="treeChecked[data.value]"  >
                  <el-text class="txt" truncated>{{ node.label }}</el-text>
                </el-checkbox>
                <el-text v-else class="txt" truncated>{{ node.label }}</el-text>
                <span v-if="multiple && data.children && data.children.length" class="link" @click.stop="handleTreeItemAll(data)" >全选</span>
              </div>
            </template>
          </el-tree>
        </template>
        <template v-else>
          <div v-if="multiple" class="options">
            <div v-if="showAll && !filterWord" class="li s2">
              <el-checkbox
                v-model="checkAll"
                label="全部"
                @change="handleCheckAllChange"
              >
              </el-checkbox>
            </div>
            <el-checkbox-group
              v-model="selectValue"
              @change="handleCheckItemChange"
            >
              <div class="li" v-for="(item, index) in optionList" :key="index">
                <el-checkbox :value="item.value">
                  <el-text truncated>{{ item.label }}</el-text>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
          <div v-else class="options">
            <div v-if="showAll && !filterWord" class="li s2" @click="handleSingleSelect(allValue)">
              <span>全部</span>
              <div class="line"></div>
            </div>
            <div 
              v-for="(item, index) in optionList" 
              :key="index" 
              :class="{li:true,on:selectValue==item.value}"
              @click="handleSingleSelect(item.value)"
            >
              <el-text class="txt" truncated>{{ item.label }}</el-text>
            </div>
          </div>
        </template>
      </div>
    </el-popover>
  </div>
</template>
<style scoped lang="scss">
.bt_select {
  :deep(.select) {
    .el-select__wrapper {
      &:hover {
        border-color: var(--form-hover-border-color);
      }
      // box-shadow: 0 0 0 1px var(--el-border-color) inset;
    }
  }
  &.visible {
    :deep(.select) {
      .el-select__wrapper {
        box-shadow: var(--active-shadow);
      }
      .el-select__suffix {
        .el-icon {
          transform: rotate(180deg);
        }
      }
    }
  }
}
.el-form-item.is-error {
  .bt_select {
    :deep(.select) {
      .el-select__wrapper {
        border-color: var(--color-danger);
        box-shadow: var(--error-shadow);
      }
    }
  }
}
.bt_select_cont {
  padding: 8px;
  .search {
    margin-bottom: 6px;
    .btn {
      cursor: pointer;
    }
  }
  .options {
    .li {
      position: relative;
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      height: 32px;
      margin: 0 -8px;
      padding: 0 16px;
      cursor: pointer;
      &:hover {
        background: #ececef;
      }
      &.s2 {
        height: 36px;
        line-height: 36px;
        &::after {
          content: "";
          position: absolute;
          left: 8px;
          right: 8px;
          bottom: 0;
          height: 1px;
          background: #d7d7d7;
        }
      }
      &.on {
        .txt {
          color: var(--el-color-primary);
        }
      }
      :deep(.el-checkbox) {
        display: flex;
        width: 100%;
        height: 100%;
        .el-checkbox__label {
          flex: 1;
          overflow: hidden;
        }
      }
    }
  }
  .tree-node {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    .el-checkbox {
      margin-right: 4px;
      overflow: hidden;
      flex: 1;
      :deep(.el-checkbox__label) {
        overflow: hidden;
        margin-top: 1px;
      }
    }
    .txt {
      flex: 1;
    }
    .link {
      margin: 0 8px;
    }
  }
}
</style>
