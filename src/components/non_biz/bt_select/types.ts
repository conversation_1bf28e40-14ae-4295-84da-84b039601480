export interface ListItem<T = any> {
    [key: string]: T;
  }

export type PropsType = {
    type?: string,
    modelValue: string | number | ListItem[],
    list: ListItem[],
    multiple?: boolean,
    clearable?: boolean,
    collapseTags?: boolean, 
    maxCollapseTags?: number,
    placeholder?: string,
    showAll?: boolean,
    allValue?: string | number,
    showSearch?: boolean,
    width?: string,
};

export type EventsType = {
  (e: 'update:modelValue', key: string | number | ListItem[]): void
  (e: 'change', key: string | number | ListItem[]): void
};

export type EmitsType<T extends Record<string, any[]>> = {
    [K in keyof T]: (evt: K, ...args: T[K]) => void;
}[keyof T];