import { ref, computed, onMounted, useTemplateRef, nextTick } from "vue";
import type { CheckboxValueType } from "element-plus";
import type { EventsType, PropsType, ListItem } from "./types";
import { ElTree, ElPopover } from 'element-plus';


function treeToList(tree: ListItem[]): ListItem[] {
  const res = [];
  for(let item of tree) {
    res.push({
      label: item.label,
      value: item.value,
    })
    if(item.children) {
      res.push(...treeToList(item.children));
    }
  }
  return res;
}

export function BtSelectCtrl(emits: EventsType, props: PropsType) {
  const visible = ref(false);
  const popoverRef = useTemplateRef<InstanceType<typeof ElPopover>>('popoverRef');
  function togglePopover(val: boolean) {
    visible.value = val;
  }

  const selectRef = useTemplateRef('selectRef');
  const clientWidth = ref("");
  onMounted(() => {
    if(props.width) {
      clientWidth.value = props.width;
    }
    const dom = selectRef.value as HTMLElement;
    clientWidth.value = String(dom.clientWidth)+'px';
    nextTick(()=>{
      clientWidth.value = String(dom.clientWidth)+'px';
    })
  });

  const selectValue = ref<any>();
  function handleSelectChange(value: any) {
    handleChange(value);
    if(props.multiple) {
      checkAll.value = value.length === props.list.length;
      nextTick(()=>{
        selectValue.value = [...value];
      })
    }
    if(props.type == 'tree') {
      let temp: ListItem = {};
      for(let item of selectValue.value) {
        temp[item] = true;
      }
      treeChecked.value = temp;
    }
  }
  function handleChange(value: any) {
    selectValue.value = value;
    emits("update:modelValue", value);
    emits("change", value);
  };

  //单选
  function handleSingleSelect(value: string | number) {
    handleChange(value);
    popoverRef?.value?.hide();
  }
  // 多选
  const checkAll = ref(false);
  function handleCheckAllChange(val: CheckboxValueType) {
    if (!val) {
      selectValue.value = [];
    } else {
      selectValue.value = props.list.map((v) => {
        return v.value;
      });
    }
    handleChange(selectValue.value);
  };
  function handleCheckItemChange(value: CheckboxValueType[]){
    checkAll.value = value.length === props.list.length;
    handleSelectChange(selectValue.value)
  };
  // 树
  const treeRef = useTemplateRef<InstanceType<typeof ElTree>>('treeRef')
  const treeProps = {
    children: "children",
    label: "label",
  };
  const treeChecked = ref(<ListItem>{});
  const treeCheckedIdx = computed(()=>{
    let idx:ListItem = {};
    for(let i = 0; i < selectValue.value.length; i++){
      idx[selectValue.value[i].value] = i;
    }
    return idx;
  })
  function handleNodeClick(e: ListItem) {
    if(!props.multiple) {
      handleChange(e.value);
      popoverRef?.value?.hide();
      return;
    }
    if(treeChecked.value[e.value]) {
      selectValue.value.splice(treeCheckedIdx.value[e.value],1);
      treeChecked.value[e.value] = false;
    } else {
      selectValue.value.push(e.value);
      treeChecked.value[e.value] = true;
    }
    handleChange(selectValue.value);
  };
  function handleTreeItemAll(data: ListItem) {
    const checked = !treeChecked.value[data.value];
    const list = getTreeAll([data]);
    const tempSelectValue = [...selectValue.value];
    for(let val of list) {
      if(checked && !treeChecked.value[val]) {
        tempSelectValue.push(val);
      }else if (!checked && treeChecked.value[val]) {
        tempSelectValue.splice( tempSelectValue.indexOf(val) ,1);
      }
    }
    handleSelectChange(tempSelectValue);
   
  };
  function getTreeAll(list: ListItem[]): string[] {
    const result = [];
    for(let item of list) {
      result.push(item.value);
      if(item.children) {
        result.push(...getTreeAll(item.children));
      }
    }
    return result;
  }
  const treeFilterNode = (value: string, data: ListItem) => {
    if (!value) return true
    return data.label.includes(value)
  }


  const optionAll = computed(()=>{
    if(props.type == 'tree') {
      return treeToList(props.list);
    }
    return props.list;
  })
  const optionList = computed(()=>{
    return optionAll.value.filter(v=>{
      return v.label.includes(filterWord.value);
    })
  })
  //搜索过滤
  const filterWord = ref('');
  const keyword = ref("");
  function handleSearch() {
    filterWord.value = keyword.value;
    if(props.type == 'tree') {
      treeRef.value?.filter(filterWord.value);
    }
  }


  return {
    visible,
    togglePopover,
    optionAll,
    optionList,
    selectValue,
    handleChange,
    handleSelectChange,
    handleSingleSelect,
    clientWidth,
    keyword,
    filterWord,
    handleSearch,
    checkAll,
    handleCheckAllChange,
    handleCheckItemChange,
    treeProps,
    treeChecked,
    treeFilterNode,
    handleNodeClick,
    handleTreeItemAll,
  };
}
