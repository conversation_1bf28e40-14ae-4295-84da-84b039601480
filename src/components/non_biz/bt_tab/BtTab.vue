<script setup lang="ts">
import { watch } from "vue";
import { BtTabCtrl } from "./BtTabCtrl.ts";
import type {EventsType,PropsType} from "./types.ts";
import { ArrowDownBold } from "@element-plus/icons-vue";

const props = withDefaults(
    defineProps<PropsType>(),
    {
      type: '',
      value: 0,
      list: () => [],
      max: 7,
      nameKey: 'name',
    }
);

const emits = defineEmits<EventsType>();
const {
  current, change,
} = BtTabCtrl(emits);

current.value = props.value;

watch(()=>props.value,(newVal)=>{
  current.value = newVal;
})

</script>
<template>
  <div class="bt-tag">
    <el-tabs
      v-model="current"
      :type="type"
      :class="{ tabs: true, out: current >= max }"
      @tab-change="change"
    >
      <el-tab-pane
        v-for="(item, index) in list.slice(0, max)"
        :key="index"
        :label="item[nameKey]"
        :name="index"
        :class="{ 'tab-more': index >= max }"
      ></el-tab-pane>
    </el-tabs>
    <el-dropdown
      v-if="list.length > max"
      placement="bottom-start"
      trigger="click"
    >
      <div class="more">
        <el-icon class="icon"><ArrowDownBold /></el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu class="more-dropmenu">
          <el-dropdown-item
            v-for="(item, index) in list.slice(max)"
            :key="index"
            :class="{ on: max + index == current }"
            @click="change(max + index)"
            >{{ item[nameKey] }}</el-dropdown-item
          >
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <div class="right">
      <slot name="right"></slot>
    </div>
  </div>
</template>
<style scoped lang="scss">
.bt-tag {
  position: relative;
  display: flex;
  &::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0px;
    width: 100%;
    height: 1px;
    background: rgba(233, 233, 233, 1);
  }
  :deep(.tabs) {
    &.out {
      .el-tabs__active-bar {
        opacity: 0;
      }
    }
    .el-tabs__header {
      margin-bottom: 0;
    }
    .el-tabs__nav-wrap:after {
      display: none;
    }
  }
  .more {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 47px;
    height: 47px;
    border-radius: var(--el-border-radius-base);
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border: 1px solid rgba(242, 242, 242, 1);
    cursor: pointer;
    outline: unset;

    .icon {
      font-size: 12px;
      color: var(--el-color-primary);
      transition: all 0.5s;
    }
    &[aria-expanded="true"] {
      .icon {
        transform: rotate(180deg);
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    padding-right: 20px;
    margin-left: auto;
  }
}
.more-dropmenu {
  :deep(.el-dropdown-menu__item) {
    &.on {
      color: var(--el-color-primary);
      border-bottom: 1px solid var(--el-color-primary);
    }
  }
}
</style>
