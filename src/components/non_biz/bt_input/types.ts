
export type PropsType = {
    modelValue?: string | number,
};

export type EventsType = {
    (e: 'update:modelValue', key: string | number): void
    (e: 'change', key: string | number): void
    (e: 'input', key: string | number): void
    (e: 'clear'): void
    (e: 'focus'): void
    (e: 'blur'): void
};

export type EmitsType<T extends Record<string, any[]>> = {
    [K in keyof T]: (evt: K, ...args: T[K]) => void;
}[keyof T];