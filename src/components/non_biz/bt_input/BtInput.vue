<script setup lang="ts">
import { BtInputCtrl } from "./BtInputCtrl.ts";
import type { PropsType, EventsType } from "./types.ts";

withDefaults(defineProps<PropsType>(), {
  modelValue: "",
});

const emits = defineEmits<EventsType>();
const { handleInput } = BtInputCtrl(emits);

</script>
<template>
  <div class="bt_input">
    <el-input
      v-bind="$attrs"
      :model-value="modelValue"
      @update:model-value="handleInput"
       @clear="emits('clear')"
    >
      <template v-for="slot in Object.keys($slots)" #[slot] :key="slot">
        <slot :name="slot" />
      </template>
    </el-input>
    <div v-if="$attrs['show-word-limit'] !== undefined" class="count">
      <span :class="{'c2':String(modelValue).length}" >{{ String(modelValue).length }}</span>/{{ $attrs?.maxlength }}
    </div>
  </div>
</template>
<style scoped lang="scss">
.bt_input {
  position: relative;
  width: 100%;
  .count {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    font-size: 12px;
    color: rgba(0,0,0,0.25);
    .c2 {
      color: rgba(0,0,0,1);
    }
  }
  :deep(.el-input__count) {
    opacity: 0;
  }
  &[type="textarea"] {
    .count {
      height: 14px;
      line-height: 14px;
      top: unset;
      bottom: 5px;
      transform: unset;
      background: #fff;
    }
  }
}
</style>
