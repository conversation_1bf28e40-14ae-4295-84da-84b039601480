import { ref, computed, watch, useTemplateRef } from "vue";
import type { EventsType, PropsType, ListItem } from "./SelectListTypes";
import { ElTree } from 'element-plus';

function countTree(list: ListItem[]): number {
  let num = 0;
  for(let item of list) {
    num += 1;
    if(item.children) {
      num += countTree(item.children);
    }
  }
  return num;
}

export function SelectListCtrl(emits: EventsType, props: PropsType) {
  const listNum = computed(()=>{
    // if(props.type == 'list') {
    //   return props.list.length;
    // }
    return countTree(props.list);
  })

  const keyword = ref("");
  const openSearch = ref(false);
  function toggleSearch() {
    openSearch.value = !openSearch.value;
  }

  const optionList = computed(() => {
    return props.list.filter((v) => {
      return v.label.includes(keyword.value);
    });
  });

  const selectValue = ref(props.modelValue);
  function handleChange(value: ListItem[]) {
    emits("update:modelValue", value);
    emits("change", value);
  }

  const treeRef = useTemplateRef<InstanceType<typeof ElTree>>('treeRef')
  const treeProps = {
    children: "children",
    label: "label",
  };
  function handleTreeChange(_value: ListItem[],e: any) {
    selectValue.value = e?.checkedKeys;
    handleChange(e?.checkedKeys as ListItem[]);
  }
  const treeFilterNode = (value: string, data: ListItem) => {
    if (!value) return true
    return data.label.includes(value)
  }

  watch(keyword, (val) => {
    treeRef.value!.filter(val)
  })

  watch(
    () => props.modelValue,
    (newVal) => {
      selectValue.value = newVal;
    }
  );

  return {
    listNum,
    keyword,
    openSearch,
    toggleSearch,
    optionList,
    selectValue,
    handleChange,
    treeProps,
    handleTreeChange,
    treeFilterNode,
  };
}
