import { ref, computed, onMounted, nextTick, watch  } from "vue";
import type { EventsType, PropsType, ListItem } from "./types";

function treeToList(tree: ListItem[], pid: string | string = '0'): ListItem[] {
  const res = [];
  for(let item of tree) {
    res.push({
      label: item.label,
      value: item.value,
      parent: pid,
    })
    if(item.children) {
      res.push(...treeToList(item.children,item.value));
    }
  }
  return res;
}

function listToTree(list: ListItem[]): ListItem[] {
  const map: ListItem = {};
  const roots: ListItem[] = [];

  list.forEach(v=>{map[v.value] = {...v, children: []}});

  list.forEach(item=>{
    const parent = map[item.parent];
    if(!parent) {
      roots.push(map[item.value]);
    } else {
      parent.children.push(map[item.value]);
    }
  })

  return roots;
}

export function BtTransferCtrl(emits: EventsType, props: PropsType) {
  const visible = ref(false);
  function togglePopover(val: boolean) {
    visible.value = val;
  }

  const optionAll = computed(()=>{
    if(props.type == 'tree') {
      return treeToList(props.list);
    }
    return props.list;
  })

  const selectValue = ref(props.modelValue);
  function handleSelectChange() {
    emits("update:modelValue",selectValue.value);
    emits("change",selectValue.value);
  }
  function resetList() {
    sourceList.value = props.list;
    sourceSelected.value = props.modelValue;
    targetList.value = [];
    targetSelected.value = [];
    changeSourceOrTarget('source');
  }

  onMounted(()=>{
    sourceSelected.value = props.modelValue as string[];
    changeSourceOrTarget('source');
  })

  watch(
    () => props.modelValue,
    (newVal) => {
      selectValue.value = newVal;
      nextTick(()=>{
        resetList();
      })
    }
  );

  const sourceSelected = ref(<string[]|number[]>[]);
  const targetSelected = ref(<string[]|number[]>[]);
  const sourceList = ref(props.list);
  const targetList = ref(<ListItem[]>[]);

  const sourceAll = computed(()=>{
    if(props.type == 'tree') {
      return treeToList(sourceList.value);
    }
    return sourceList.value;
  })
  const targetAll = computed(()=>{
    if(props.type == 'tree') {
      return treeToList(targetList.value);
    }
    return targetList.value;
  })

  const sourceMap = computed(()=>{
    const res: ListItem = {};
    for(let item of sourceAll.value) {
      res[item.value] = 1;
    }
    return res;
  })
  const targetMap = computed(()=>{
    const res: ListItem = {};
    for(let item of targetAll.value) {
      res[item.value] = 1;
    }
    return res;
  })

  function changeSourceOrTarget(type: string) {
    if(props.type == 'tree') {
      treeMove(type);
    } else {
      listMove(type);
    }
  } 

  function listMove(type: string) {
    let res = getMoveList(type);

    if(type == 'source') {
      sourceList.value = res.list;
      targetList.value = res.toList;
      sourceSelected.value = [];
    } else {
      targetList.value = res.list;
      sourceList.value = res.toList;
      targetSelected.value = [];
    }
  }

  function treeMove(type: string) {
    let res = getMoveList(type);

    if(type == 'source') {
      sourceList.value = listToTree(res.list);
      targetList.value = listToTree(res.toList);
      sourceSelected.value = [];
    } else {
      targetList.value = listToTree(res.list);
      sourceList.value = listToTree(res.toList);
      targetSelected.value = [];
    }
  }

  function getMoveList(type: string): ListItem {
    let selectedMap: ListItem = {};
    let selected: string[] | number[];
    let toMap: ListItem = {};
    let curList: ListItem[] = [];

    if(type == 'source') {
      selected = sourceSelected.value;
      toMap = {...targetMap.value};
      curList = sourceAll.value;
    } else {
      selected = targetSelected.value;
      toMap = {...sourceMap.value};
      curList = targetAll.value;
    }

    for(let item of selected) {
      selectedMap[item] = 1;
    }
    // 遍历当前列表，排除要迁移的选中项
    let list: ListItem[] = [];
    for(let item of curList) {
      if(selectedMap[item.value]) {
        toMap[item.value] = 1;
      } else {
        list.push(item);
      }
    }
    // 生成迁移目标的列表数据
    let toList: ListItem[] = [];
    for(let item of optionAll.value) {
      if(toMap[item.value]) {
        toList.push(item);
      }
    }
    return {
      list, toList,
    }
  }
  

  // const popoverRef = useTemplateRef<InstanceType<typeof ElPopover>>('popoverRef');
  function confirm() {
    let list: any[] = [];
    for(let item of targetAll.value) {
      list.push(item.value);
    }
    selectValue.value = list;
    emits("update:modelValue",selectValue.value);
    emits("change",selectValue.value);
    close();
  }
  function close() {
    visible.value = false;
    // popoverRef.value!.hide();
  }
  

  return {
    visible,
    togglePopover,
    optionAll,
    selectValue,
    handleSelectChange,
    sourceList,
    sourceSelected,
    targetList,
    targetSelected,
    changeSourceOrTarget,
    confirm,
    close,
    resetList,
  };
}
