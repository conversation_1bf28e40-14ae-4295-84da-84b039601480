export interface ListItem<T = any> {
  [key: string]: T;
}

export type PropsType = {
  type?: string,
  title: string;
  list: ListItem[];
  modelValue: string[] | number[];
  showSearch?: boolean,
  checkStrictly?: boolean,
};

export type EventsType = {
  (e: 'update:modelValue', key: ListItem[]): void
  (e: "change", key: ListItem[]): void;
};

export type EmitsType<T extends Record<string, any[]>> = {
  [K in keyof T]: (evt: K, ...args: T[K]) => void;
}[keyof T];
