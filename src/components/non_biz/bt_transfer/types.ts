export interface ListItem<T = any> {
    [key: string]: T;
  }

export type PropsType = {
    title?: string,
    type?: string,
    modelValue: string[] | number[],
    list: ListItem[],
    collapseTags?: boolean, 
    maxCollapseTags?: number,
    placeholder?: string,
    showSearch?: boolean,
    checkStrictly?: boolean,
};

export type EventsType = {
  (e: 'update:modelValue', key: string[] | number[]): void
  (e: 'change', key: string[] | number[]): void
};

export type EmitsType<T extends Record<string, any[]>> = {
    [K in keyof T]: (evt: K, ...args: T[K]) => void;
}[keyof T];