<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import { SelectListCtrl } from "./SelectListCtrl.ts";
import type { EventsType, PropsType } from "./SelectListTypes";
import {withBaseUrl} from "@/utils/routeUtil.ts";
const props = withDefaults(defineProps<PropsType>(), {
  type: 'list',
  title: "",
  list: () => [],
  moduleValue: () => [],
});
const emits = defineEmits<EventsType>();
const { 
  listNum,
  keyword, openSearch, toggleSearch,
  treeProps, selectValue, handleTreeChange,
  treeFilterNode,
} = SelectListCtrl(emits, props);


</script>
<template>
  <div class="select-list">
    <div class="header">
      <div class="title">
        <span>{{ title }}</span>
        <span class="num">（{{ selectValue.length }}/{{ listNum }}）</span>
      </div>
      <el-icon v-if="showSearch" class="btn" @click="toggleSearch" ><Search /></el-icon>
    </div>
    <div v-show="openSearch" class="search">
      <el-input v-model="keyword" :validateEvent="false" clearable >
        <template #suffix>
          <el-icon class="btn"  ><Search /></el-icon>
        </template>
      </el-input>
    </div>
    <div class="list">
      <!-- <template v-if="type == 'tree'"> -->
        <el-tree
          ref="treeRef"
          :class="{tree: true, 's2': list.length == listNum }"
          show-checkbox
          default-expand-all	
          node-key="value"
          :check-strictly="checkStrictly"
          :data="list"
          :props="treeProps"
          :expand-on-click-node="false"
          :filter-node-method="treeFilterNode"
          @check="handleTreeChange"
        >
          <template #default="{ node }">
            <el-text  class="txt" truncated>{{ node.label }}</el-text>
          </template>
          <template #empty>
            <div class="empty">
              <img :src="withBaseUrl('empty.png')" alt="empty">
              <div class="txt">暂无数据</div>
            </div>
          </template>
          </el-tree>
      <!-- </template> -->
      <!-- <template v-else>
        <el-checkbox-group v-model="selectValue" @change="handleChange" :validateEvent="false">
          <div class="li" v-for="(item, index) in optionList" :key="index">
            <el-checkbox :value="item.value">
              <el-text truncated>{{ item.label }}</el-text>
            </el-checkbox>
          </div>
        </el-checkbox-group>
      </template> -->
      
    </div>
  </div>
</template>
<style lang="scss" scoped>
.select-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    padding: 0 14px;
    border-bottom: 1px solid #e7e7e7;
    flex-shrink: 0;
    .title {
      font-size: 14px;
      line-height: 17px;
      color: rgba(51, 51, 51, 1);
      .num {
        color: #7f7f7f;
      }
    }
    .btn {
      font-size: 16px;
      cursor: pointer;
    }
  }
  .search {
    margin: 8px 14px 0px 14px;
    :deep(.el-input__wrapper) {
      box-shadow: unset !important;
      border: 1px solid var(--form-border-color) !important;
      &:hover {
        border-color: var(--form-hover-border-color) !important;
      }
      &.is-focus {
        box-shadow: var(--active-shadow) !important;
      }
    }
  }
  .list {
    flex: 1;
    padding: 8px 0;
    overflow: auto;
    
    .li {
      width: 100%;
      height: 30px;
      &:hover {
        background: #ececef;
      }
      .el-checkbox {
        width: 100%;
        height: 100%;
        padding: 0 14px;
      }
    }
    .tree {
      height: 100%;
      :deep(.el-tree-node__content) {
        >.el-icon {
          margin-left: 10px;
        }
      }
      &.s2 {
        :deep(.el-tree-node__expand-icon) {
          width: 4px;
          padding: 0;
        }
      }
    }
  }
  .empty {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img {
      width: 100px;
      height: 63px;
    }
    .txt {
      font-size: 14px;
      color: rgba(127,127,127,1);
      line-height: 17px;
      margin-top: 18px;
    }
  }
}
</style>
