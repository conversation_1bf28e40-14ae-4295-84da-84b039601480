<script setup lang="ts">
import { BtThFilterCtrl } from './BtThFilterCtrl.ts';
import { Search as ElSearch } from '@element-plus/icons-vue';
// import { ClickOutside as vClickOutside } from 'element-plus'
import type { EventsType, PropsType } from './types.ts';

const props = withDefaults(defineProps<PropsType>(), {
  title: '',
  info: () => {
    return {};
  },
  inputType: 'input',
  showOrder: false,
  showSearch: false,
  placeholder: '',
});
const emits = defineEmits<EventsType>();

const {
  buttonRef,
  afterLeave,
  checkAll,
  handleCheckAllChange,
  handleCheckItemChange,
  keyword,
  optionList,
  changeOrder,
  confirm,
  hasVal,
  curModel,
  curOrder,
  handleDateRangeChange,
} = BtThFilterCtrl(emits, props);
</script>
<template>
  <div
    :class="{
      bt_th_filter: true,
      on: hasVal,
    }"
  >
    <span class="title">{{ title }}</span>
    <div class="btn" ref="buttonRef">
      <SvgIcon class="icon" name="角标" size="10" color="#333" />
    </div>
    <el-popover
      ref="popoverRef"
      :virtual-ref="buttonRef"
      trigger="click"
      virtual-triggering
      :width="inputType === 'dateRange' ? '350' : '250'"
      popper-style="padding: 0;"
      @after-leave="afterLeave"
    >
      <div class="bt_th_filter_cont">
        <!-- 输入框类型 -->
        <div v-if="type == 'input'" class="search" @click.stop>
          <el-date-picker
            v-if="inputType == 'date'"
            class="date-select"
            v-model="curModel"
            type="date"
            placeholder="请选择"
            value-format="YYYY-MM-DD"
          />
          <el-date-picker
            v-else-if="inputType == 'dateRange'"
            class="date-select"
            v-model="curModel"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            clearable
            :teleported="false"
            :popper-class="'bt-th-filter-date-range-popper'"
            @change="handleDateRangeChange"
            @mousedown.stop
            @click.stop
          />
          <el-input
            v-else
            v-model="curModel"
            :placeholder="placeholder || '请输入关键词搜索'"
            clearable
            :validateEvent="false"
          >
            <template #suffix>
              <slot name="inputSuffix">
                <el-icon class="btn" @click="confirm"><ElSearch /></el-icon>
              </slot>
            </template>
          </el-input>
        </div>
        <!-- 类型为单选或多选且showSearch为true -->
        <div
          v-if="showSearch && (type == 'checkbox' || type == 'radio')"
          class="search"
        >
          <el-input
            v-model="keyword"
            placeholder="请输入关键词搜索"
            clearable
            :validateEvent="false"
          >
            <template #suffix>
              <el-icon class="btn"><ElSearch /></el-icon>
            </template>
          </el-input>
        </div>
        <!-- 单选类型 -->
        <div v-if="type == 'radio'" class="options">
          <el-radio-group v-model="curModel" @change="handleCheckItemChange">
            <div v-show="!keyword" class="li">
              <el-radio :value="''">全部</el-radio>
            </div>
            <div class="li" v-for="(item, index) in optionList" :key="index">
              <el-radio :value="item.value">
                <el-text truncated>{{ item.label }}</el-text>
              </el-radio>
            </div>
          </el-radio-group>
          <div v-show="keyword && !optionList.length" class="empty">
            暂无相关选项
          </div>
        </div>
        <!-- 多选类型 -->
        <div v-if="type == 'checkbox'" class="options">
          <div v-show="!keyword">
            <div class="li s2">
              <el-checkbox
                v-model="checkAll"
                label="全部"
                @change="handleCheckAllChange"
              >
              </el-checkbox>
            </div>
          </div>
          <el-checkbox-group v-model="curModel" @change="handleCheckItemChange">
            <div class="li" v-for="(item, index) in optionList" :key="index">
              <el-checkbox :value="item.value">
                <el-text truncated>{{ item.label }}</el-text>
              </el-checkbox>
            </div>
          </el-checkbox-group>
          <div v-show="keyword && !optionList.length" class="empty">
            暂无相关选项
          </div>
        </div>
        <!-- 显示排序按钮 -->
        <div v-if="showOrder" class="order">
          <div
            :class="{ item: true, on: curOrder == 'asc' }"
            @click="changeOrder('asc')"
          >
            <SvgIcon class="icon" name="升序" />
            <span class="txt">升序</span>
          </div>
          <div
            :class="{ item: true, on: curOrder == 'desc' }"
            @click="changeOrder('desc')"
          >
            <SvgIcon class="icon" name="降序" />
            <span class="txt">降序</span>
          </div>
        </div>
      </div>
    </el-popover>
  </div>
</template>
<style lang="scss" scoped>
.bt_th_filter {
  position: relative;
  width: 100%;
  .btn {
    position: absolute;
    bottom: 0px;
    right: -4px;
    display: inline-block;
    line-height: 1;
    cursor: pointer;
    .icon {
      font-size: 10px;
    }
  }
  &.on {
    .title {
      color: var(--color-primary);
    }
    .btn .icon {
      color: var(--color-primary);
    }
  }
}
.bt_th_filter_cont {
  padding: 8px;
  .search {
    margin-bottom: 6px;
    .btn {
      cursor: pointer;
    }
  }
  :deep(.date-select) {
    width: 100%;
  }

  :deep(.el-date-table-cell__text) {
    border-radius: var(--el-border-radius-base) !important;
  }

  // 确保日期范围选择器的弹窗层级足够高
  :global(.bt-th-filter-date-range-popper) {
    z-index: 9999 !important;

    // 确保日期范围选择器的弹窗不会被父级 popover 遮挡
    .el-picker-panel {
      z-index: 10000 !important;
    }
  }

  // 日期范围选择器操作按钮样式
  .date-range-actions {
    padding: 8px;
    border-top: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    gap: 8px;
  }
  .order {
    display: flex;
    align-items: center;
    .item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 4px;
      border-right: 1px solid var(--border-color);
      cursor: pointer;
      .icon {
        font-size: 18px !important;
        font-weight: bold !important;
        margin-right: 4px;
      }
      .txt {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        line-height: 1;
      }
      &.on {
        .icon,
        .txt {
          color: var(--color-primary) !important;
        }
      }
    }
  }
  .options {
    .li {
      position: relative;
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      width: 100%;
      height: 32px;
      margin: 0 -8px;
      padding: 0 16px;
      cursor: pointer;
      &:hover {
        background: #ececef;
      }
      &.s2 {
        height: 36px;
        line-height: 36px;
        &::after {
          content: '';
          position: absolute;
          left: 8px;
          right: 8px;
          bottom: 0;
          height: 1px;
          background: #d7d7d7;
        }
      }
      &.on {
        .txt {
          color: var(--el-color-primary);
        }
      }
      :deep(.el-radio) {
        width: 100%;
      }
      :deep(.el-checkbox) {
        display: flex;
        width: 100%;
        height: 100%;
        .el-checkbox__label {
          flex: 1;
          color: #333;
          overflow: hidden;
        }
      }
    }
  }
  .empty {
    padding: 8px;
    color: #999;
    text-align: center;
  }
  .tree-node {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    .el-checkbox {
      margin-right: 4px;
      overflow: hidden;
      flex: 1;
      :deep(.el-checkbox__label) {
        overflow: hidden;
        margin-top: 1px;
      }
    }
    .txt {
      flex: 1;
    }
    .link {
      margin: 0 8px;
    }
  }
}
</style>
