export interface ListItem<T = any> {
  [key: string]: T;
}

export type ConfigInfo = {
  search?: boolean; // 显示输入框
  order?: boolean; // 显示排序
  options?: ListItem[]; // 显示选项，optionType控制单选/多选
  optionType?: 'radio' | 'checkbox';
  searchType?: 'input' | 'date' | 'dateRange'; // 输入框类型，date为日期选择，dateRange为日期范围选择
  placeholder?: string; // 输入框placeholder
};

export type PropsType = {
  modelValue: string | number | string[] | number[];
  order?: string;
  title?: string;
  type?: 'input' | 'radio' | 'checkbox';
  inputType?: 'input' | 'date' | 'dateRange';
  showSearch?: boolean;
  showOrder?: boolean;
  options?: ListItem[];
  placeholder?: string;
};

export type EventsType = {
  (e: 'update:modelValue', key: string | number | string[] | number[]): void;
  (e: 'update:order', key: string): void;
  (e: 'change', key: string | number | string[] | number[]): void;
};
