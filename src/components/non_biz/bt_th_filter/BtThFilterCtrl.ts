import { ref, computed, watch, useTemplateRef } from 'vue';
import type { CheckboxValueType } from 'element-plus';
import type { EventsType, PropsType, ListItem } from './types';

export function BtThFilterCtrl(emits: EventsType, props: PropsType) {
  const buttonRef = useTemplateRef<any>('buttonRef');
  const popoverRef = useTemplateRef<any>('popoverRef');

  const curModel = ref(props.modelValue);
  const curOrder = ref(props.order);
  watch(
    () => props.modelValue,
    (newVal) => {
      curModel.value = newVal;
    }
  );
  watch(
    () => props.order,
    (newVal) => {
      curOrder.value = newVal;
    }
  );

  const hasVal = computed(() => {
    if (props.type == 'input') {
      if (props.inputType == 'dateRange' && Array.isArray(curModel.value)) {
        return (
          curModel.value.length === 2 && curModel.value[0] && curModel.value[1]
        );
      } else if (curModel.value) {
        return true;
      }
    } else if (props.type == 'checkbox' && Array.isArray(curModel.value)) {
      return curModel.value.length > 0;
    } else if (props.type == 'radio' && curModel.value) {
      return true;
    }

    if (props.showOrder && curOrder.value) {
      return true;
    }

    return false;
  });

  function isListEqual(
    arr1: string[] | number[],
    arr2: string[] | number[]
  ): boolean {
    if (arr1.length != arr2.length) return false;
    let map: ListItem = {};
    for (let item of arr1) {
      map[item] = 1;
    }
    for (let item of arr2) {
      if (map[item] === undefined) {
        return false;
      }
    }
    return true;
  }

  function afterLeave() {
    let change = false;
    const oldVal = props.modelValue; // props.modelValue;
    const newVal = curModel.value;
    if (props.type == 'input') {
      if (props.inputType == 'dateRange') {
        // 对于日期范围，比较数组内容
        change = !isListEqual(
          (oldVal as string[]) || [],
          (newVal as string[]) || []
        );
      } else {
        change = oldVal != newVal;
      }
    } else if (props.type == 'checkbox') {
      change = !isListEqual(
        (oldVal as string[] | number[]) || [],
        (newVal as string[] | number[]) || []
      );
    } else if (props.type == 'radio') {
      change = oldVal != newVal;
    }

    if (props.showOrder && curOrder.value != props.order) {
      change = true;
    }

    if (change) {
      handleChange();
    }
  }

  function handleChange() {
    emits('update:modelValue', curModel.value);
    emits('update:order', curOrder.value || '');
    emits('change', curModel.value);
  }
  function confirm() {
    handleChange();
    popoverRef.value.hide();
  }

  const keyword = ref('');

  // 日期范围选择器事件处理
  function handleDateRangeChange(value: any) {
    // 处理两种情况：
    // 1. 完整的日期范围选择（两个日期都选择了）
    // 2. 清除操作（value 为 null、undefined 或空数组）

    const isCompleteSelection =
      Array.isArray(value) && value.length === 2 && value[0] && value[1];
    const isClearOperation =
      !value || (Array.isArray(value) && value.length === 0);

    if (isCompleteSelection || isClearOperation) {
      // 延迟关闭，确保日期选择器的弹窗先关闭
      setTimeout(() => {
        handleChange();
        popoverRef.value.hide();
      }, 100);
    }
  }

  const optionAll = computed(() => {
    const list: ListItem[] = props.options ? props.options : [];
    return list;
  });
  const optionList = computed(() => {
    return optionAll.value.filter((v) => {
      return v.label.includes(keyword.value);
    });
  });

  function changeOrder(type: string) {
    curOrder.value = curOrder.value == type ? '' : type;
    // 触发 change 事件，通知父组件排序变更
    handleChange();
  }

  const checkAll = ref(false);
  function handleCheckAllChange(val: CheckboxValueType) {
    if (!val) {
      curModel.value = [];
    } else {
      curModel.value = optionAll.value.map((v) => {
        return v.value;
      });
    }
  }
  function handleCheckItemChange(value: CheckboxValueType[]) {
    checkAll.value = value.length === optionAll.value.length;
  }

  return {
    buttonRef,
    afterLeave,
    curModel,
    curOrder,
    hasVal,
    keyword,
    optionAll,
    optionList,
    checkAll,
    handleCheckAllChange,
    handleCheckItemChange,
    changeOrder,
    confirm,
    handleDateRangeChange,
  };
}
