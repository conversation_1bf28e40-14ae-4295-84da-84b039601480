
export type PropsType = {
    modelValue?: string | number,
    placeholder?: string,
    open?: boolean,
};

export type EventsType = {
    (e: 'update:modelValue', key: string | number): void
    (e: 'input', key: string | number): void
    (e: 'search', key: string | number): void
    (e: 'clear', ): void
};

export type EmitsType<T extends Record<string, any[]>> = {
    [K in keyof T]: (evt: K, ...args: T[K]) => void;
}[keyof T];