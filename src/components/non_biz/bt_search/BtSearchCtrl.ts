import { ref } from "vue";
import type { EventsType } from "./types.ts";

export function BtSearchCtrl(emits: EventsType) {
  function handleInput(value: string | number) {
    emits("update:modelValue", value);
    emits("input", value);
  }

  function handleSearch(val: string|number) {
    emits("search",val);
  }

  const inputShow = ref(false);
  function toggleInputShow(){
    inputShow.value = !inputShow.value;
  }

  function handleClear() {
    emits("clear");
  }

  return {
    handleInput,
    handleSearch,
    handleClear,
    inputShow,
    toggleInputShow,
  };
}
