<script setup lang="ts">
import { BtSearchCtrl } from "./BtSearchCtrl.ts";
import type { PropsType, EventsType } from "./types.ts";
import { Search as ElSearch } from "@element-plus/icons-vue";

withDefaults(defineProps<PropsType>(), {
  modelValue: "",
  placeholder: "请输入",
});

const emits = defineEmits<EventsType>();
const { handleInput, handleSearch, handleClear, inputShow, toggleInputShow } = BtSearchCtrl(emits);

</script>
<template>
  <div class="bt_search">
    <BtInput
      :class="{'ipt': true, 'show': inputShow}"
      :model-value="modelValue"
      :placeholder="placeholder"
      clearable
      @update:model-value="handleInput"
      @keyup.enter.stop="handleSearch(modelValue)"
      @clear="handleClear"
    >
    </BtInput>
    <BtTooltip
      class="icon-tooltip "
      effect="dark"
      content="搜索"
      placement="bottom"
    >
      <el-icon class="icon" @click="toggleInputShow" ><ElSearch /></el-icon>
    </BtTooltip>
  </div>
</template>
<style scoped lang="scss">
.bt_search {
  position: relative;
  display: flex;
  align-items: center;
  .btn {
    height: 16px;
  }
  .ipt {
    width: 0px;
    overflow: hidden;
    transition: all .3s;
    &.show {
      width: 215px;
      margin-right: var(--base-margin);
      overflow: visible;
    }
  }
  .icon {
    font-size: 16px;
    cursor: pointer;
  }
}
</style>
