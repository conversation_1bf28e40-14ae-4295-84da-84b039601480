<script setup lang="ts">
import type { PropsType } from "./types"

withDefaults(defineProps<PropsType>(), {
  list: () => [],
});
</script>
<template>
  <div class="history-log">
    <div v-if="title" class="header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="list">
      <div v-for="(item,index) in list" :key="index" class="li">
        <el-image v-if="item.img" class="img" :src="item.img" fit="cover"></el-image>
        <div class="info">
          <div class="title">{{ item.title }}</div>
          <div class="date">{{ item.date }}</div>
          <div class="cont">{{ item.content }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.history-log {
  .header {
    margin-bottom: 16px;
    .title {
      font-size: 16px;
      font-weight: bold;
    }
  }
  .list {
    position: relative;
    &::before {
      content: "";
      position: absolute;
      left: 9px;
      width: 1px;
      height: 100%;
      background: var(--border-color);
    }
    .li {
      position: relative;
      padding-left: 30px;
      padding-bottom: 30px;
      .img {
        position: absolute;
        left: 0;
        top: 0;
        width: 20px;
        height: 20px;
        border-radius: 50%;
      }
      .info {
        color: #000;
        line-height: 1.4;
        >div {
          margin-bottom: 4px;
        }
      }
    }
  }
}
</style>
