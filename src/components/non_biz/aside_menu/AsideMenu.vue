<script setup lang="ts">
import type {AsideMenuEvents, PropsType} from "./types.ts";
import { ElMenu, ElMenuItem, ElSubMenu } from "element-plus";

const props = withDefaults(
      defineProps<PropsType>(),
      {
        data: () => [],
        selected: undefined
      }
  );
  const emits = defineEmits<AsideMenuEvents>();

</script>

<template>
  <el-menu
	  :router="false"
      :default-active="props.selected"
      class="aside-menu"
      @open="(key, keyPath) => emits('open', key, keyPath)"
      @close="(key, keyPath) => emits('close', key, keyPath)"
      @select="(key, keyPath) => emits('select-changed', key, keyPath)"
  >
    <template :key="item.id" v-for="item in props.data">
      <template v-if="item.children && item.children.length">
        <el-sub-menu :index="item.id">
          <template #title>
            <SvgIcon size="20" class="icon" :name="item.icon" />
            <span>{{ item.label }}</span>
          </template>
          <el-menu-item :index="subItem.id" :key="subItem.id" v-for="subItem in item.children">
            <SvgIcon size="20" class="icon" :name="subItem.icon" />
            <span>{{ subItem.label }}</span>
          </el-menu-item>
        </el-sub-menu>
      </template>
      <template v-else>
        <el-menu-item :index="item.id" :key="item.id">
          <SvgIcon size="20" class="icon" :name="item.icon" />
          <span>{{ item.label }}</span>
        </el-menu-item>
      </template>
    </template>
  </el-menu>
</template>

<style scoped lang="scss">
  .aside-menu {
    min-height: 100%;
  }
</style>