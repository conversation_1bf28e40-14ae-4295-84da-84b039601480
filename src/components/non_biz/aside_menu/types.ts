import { ElIcon } from 'element-plus';

export type AsideMenuItem = {
    id: string,
    label: string,
    icon?: string | typeof ElIcon,
    children?: AsideMenuItem[]
};

export type AsideMenuEvents = {
    open: [key: string, keyPath: string[]],
    close: [key: string, keyPath: string[]],
    'select-changed': [key: string, keyPath: string[]]
}

export type PropsType = {
    data: Array<AsideMenuItem>,
    selected?: AsideMenuItem['id'],
}