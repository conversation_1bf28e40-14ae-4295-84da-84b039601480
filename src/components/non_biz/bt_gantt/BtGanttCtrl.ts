import { useTemplateRef, onMounted, onUnmounted, watch, computed } from 'vue';
import type { EventsType, PropsType, ListItem } from './types.ts';

import { gantt } from 'dhtmlx-gantt';
import {withBaseUrl} from "@/utils/routeUtil.ts";

const treeArrow = withBaseUrl('tree-arrow.png');

export function BtGanttCtrl(_emits: EventsType, props: PropsType) {
  const ganttRef = useTemplateRef<any>('ganttRef');

  const defConfig = {
    scale_height: 3 * 24,
    min_column_width: 50,
    row_height: 45,
    bar_height: 25,
    readonly: true, // 只读模式
    fit_tasks: true, // 是否自动延长时间范围以适应所有显示的任务--效果：拖动任务时间范围延长，可显示区域会自动变化延长
    autofit: true, // 宽度是否自适应
    show_quick_info: true, // 是否显示quickInfo(甘特图详情小框)
    work_time: false, // 是否允许在工作时间而不是日历时间中计算任务的持续时间
    date_format: '%Y-%m-%d', // 设置日期格式
  };
  const defTemp = {
    task_text: function () {
      return '';
    },
    scale_row_class: function (scale: ListItem) {
      return scale.css ? scale.css() : '';
    },
    grid_folder: function (item: any) {
      return (
        "<div class='gantt_custom_tree_icon " +
        (item.$open ? 'open' : '') +
        " ' ><img src='" +
        treeArrow +
        "' /></div>"
      );
    },
  };
  const defPlugin = {
    tooltip: true,
    fullscreen: true,
    export_api: true,
  };
  const dateLevel: any[] = [
    {
      name: 'day',
      scale_height: 3 * 24,
      scales: [
        {
          unit: 'day',
          step: 7,
          format: '%Y年%m月',
          css: () => 'left noborder',
          sticky: false,
        },
        {
          unit: 'day',
          step: 7,
          format: function (date: any) {
            return (
              '第' + Math.ceil((gantt.columnIndexByDate(date) + 1) / 7) + '周'
            );
          },
          css: () => 'left noborder',
          sticky: false,
        },
        { unit: 'day', step: 1, format: '%d日', sticky: false },
      ],
    },
    {
      name: 'week',
      scale_height: 2 * 24,
      scales: [
        {
          unit: 'day',
          step: 7,
          format: '%Y年%m月%d日',
          css: () => 'left noborder',
          sticky: false,
        },
        {
          unit: 'day',
          step: 7,
          format: function (date: any) {
            return '第' + (gantt.columnIndexByDate(date) + 1) + '周';
          },
          css: () => 'left noborder',
          sticky: false,
        },
      ],
    },
    {
      name: 'month',
      scale_height: 2 * 24,
      scales: [{ unit: 'month', step: 1, format: '%Y年 %m月', sticky: false }],
    },
    {
      name: 'year',
      scale_height: 2 * 24,
      scales: [{ unit: 'year', step: 1, format: '%Y年' }],
    },
  ];

  function init() {
    gantt.setSkin('btit');

    const plugin = Object.assign({}, defPlugin, props.plugins);
    gantt.plugins(plugin);

    const config = Object.assign({}, defConfig, props.config);
    for (let key in config) {
      gantt.config[key] = config[key];
    }

    const templates = Object.assign({}, defTemp, props.templates);
    if (plugin.tooltip && config.columns && !templates.tooltip_text) {
      templates.tooltip_text = function (_start: any, _end: any, task: any) {
        let html = '';
        for (let item of config.columns) {
          let val = task[item.name];
          if (item.name == 'progress') {
            val = Math.floor(task[item.name] * 100) + '%';
          } else if (val instanceof Date) {
            val = gantt.date.date_to_str('%Y-%m-%d')(val);
          }
          html += `<div>${item.label}：${val}</div>`;
        }
        return html;
      };
    }
    for (let key in templates) {
      gantt.templates[key] = templates[key];
    }

    gantt.ext.zoom.init({ levels: dateLevel });
    gantt.ext.zoom.setLevel(props.dateType || 'day');

    gantt.init(ganttRef.value);
    gantt.parse(props.data as any);
  }
  function render() {
    gantt.clearAll();
    const config = Object.assign({}, defConfig, props.config);
    for (let key in config) {
      gantt.config[key] = config[key];
    }
    gantt.parse(props.data as any);
  }

  const dataLevel = computed(() => {
    const result: ListItem = {};
    const map: ListItem = {};

    const getLevel = (id: number) => {
      let level = 0;
      // return level;
      while (map[id] !== undefined) {
        level += 1;
        id = map[id];
      }
      return level;
    };
    for (let item of props.data.data) {
      map[item.id] = item.parent || 0;
    }
    for (let item of props.data.data) {
      result[item.id] =
        result[item.parent] !== undefined
          ? result[item.parent] + 1
          : getLevel(item.id);
    }
    return result;
  });
  function treeOpen(level: number) {
    gantt.eachTask(function (task) {
      task.$open =
        level == 0 || dataLevel.value[task.id] <= level ? true : false;
    });
    gantt.render();
  }
  function treeClose(level: number) {
    gantt.eachTask(function (task) {
      task.$open =
        level == 0 || dataLevel.value[task.id] == level ? false : task.$open;
    });
    gantt.render();
  }
  function expand() {
    gantt.expand();
  }
  function collapse() {
    gantt.collapse();
  }
  const exportStyle = `
  html:root {
    --dhx-gantt-font-family: '';
    --dhx-gantt-base-colors-primary: #0288D1;
    --dhx-gantt-base-colors-select: #ffebbc;
    --dhx-gantt-base-colors-border: rgb(233, 233, 233);
    --dhx-gantt-task-text-font-size: 14px;
    --dhx-gantt-task-text-font-weight: 500;
    --dhx-gantt-heading-font-size: 22px;
    --dhx-gantt-heading-font-weight: 300;
    --dhx-gantt-caption-font-size: 14px;
    --dhx-gantt-caption-font-weight: 400;
    --dhx-gantt-scale-color: #767676;
    --dhx-gantt-container-color: #3f3f3f;
    --dhx-gantt-border-radius: 0;
    --dhx-gantt-box-shadow-s: 0 3px 5px 0 rgba(0, 0, 0, .1);
    --dhx-gantt-box-shadow-m: 0px 4px 24px 0px rgba(44, 47, 60, .36);
    --dhx-gantt-box-shadow-l: 0px 4px 24px 0px rgba(44, 47, 60, .56);
    --dhx-gantt-scale-background: rgba(0,0,0,0);
    --dhx-gantt-scale-color: #42464b;
    --dhx-gantt-task-color: #1e2022;
    --dhx-gantt-project-background: #c7d8f7;
    --dhx-gantt-milestone-background: #DB7DC5;
    --dhx-gantt-task-background: #EBEEF5;
    --dhx-gantt-task-border: 1px solid #409EFF;
    --dhx-gantt-task-progress-color: #4d75c4;
    --dhx-gantt-project-progress-color: #9ab9f1;
    --dhx-gantt-link-background: #ffb96d;
    --dhx-gantt-lightbox-title-background: #f4f2ea;
    --dhx-gantt-popup-background: #fcfaf3;
    --dhx-gantt-popup-color: var(--dhx-gantt-container-color);
    --dhx-gantt-popup-border: 1px solid #cac8bd;
  }
  .gantt_scale_line.noborder {
    border-top: 0;
  }
  .gantt_scale_cell {
    font-size: 12px;
  }
  .gantt_scale_cell.left {
    text-align: left;
    padding-left: 6px;
    padding-right: 6px;
  }
  .gantt_grid_head_cell {
    font-weight: bold;
    text-align: left;
    padding-left: 6px;
    padding-right: 6px;
  }
  .gantt_tree_icon {
    position: relative;
    z-index: 1;
    opacity: 0;
  }
  .gantt_custom_tree_icon {
    position: relative;
    display: flex;
    align-items: center;
    width: 20px;
    object-fit: contain;
    margin-left: -20px;
  }
  .gantt_custom_tree_icon img{
    width: 10px;
    height: 10px;
    object-fit: contain;
  }
  .gantt_custom_tree_icon.open img{
    transform: rotate(90deg);
  }
  `;
  function exportTo(type: string, config: ListItem) {
    config.raw = true;
    config.header = `<style>${exportStyle}</style>`;
    if (type == 'png') {
      gantt.exportToPNG(config);
    } else if (type == 'pdf') {
      gantt.exportToPDF(config);
    } else if (type == 'excel') {
      gantt.exportToExcel(config);
    }
  }

  function filterLogic(task: ListItem, match = false) {
    match = match || false;
    gantt.eachTask(function (child) {
      if (filterLogic(child)) {
        match = true;
      }
    }, task.id);
    const filterValue = props.filterValue || '';
    if (task.text.toLowerCase().indexOf(filterValue.toLowerCase()) > -1) {
      match = true;
    }
    return match;
  }
  gantt.attachEvent(
    'onBeforeTaskDisplay',
    function (_id: string, task: ListItem) {
      if (!props.filterValue) {
        return true;
      }
      return filterLogic(task);
    }
  );

  watch(
    () => props.dateType,
    (newVal) => {
      gantt.ext.zoom.setLevel(newVal as string);
    }
  );
  watch(
    () => props.data,
    (newVal) => {
      gantt.clearAll();
      const config = Object.assign({}, defConfig, props.config);
      for (let key in config) {
        gantt.config[key] = config[key];
      }
      gantt.parse(newVal as any);
    }
  );
  watch(
    () => props.filterValue,
    () => {
      gantt.render();
    }
  );

  onMounted(() => {
    init();
  });

  onUnmounted(() => {
    document.querySelector('.gantt_tooltip')?.remove();
  });

  return {
    treeOpen,
    treeClose,
    expand,
    collapse,
    exportTo,
    render,
  };
}
