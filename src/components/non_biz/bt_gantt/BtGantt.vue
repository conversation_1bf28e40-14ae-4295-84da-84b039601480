<script setup lang="ts">
import "dhtmlx-gantt/codebase/dhtmlxgantt.css";
import type { EventsType, PropsType } from "./types";
import { BtGanttCtrl } from "./BtGanttCtrl";

const props = withDefaults(
    defineProps<PropsType>(),
    {
      data: () => { return { data: [] } },
      config: () => {return {}},
      templates: () => {return {}},
      plugins: () => {return {}},
    }
);

const emits = defineEmits<EventsType>();
const { treeOpen, treeClose, expand, collapse, exportTo, render } = BtGanttCtrl(emits,props);

defineExpose({treeOpen, treeClose, expand, collapse, exportTo, render });
</script>

<template>
  <div class="bt_gantt scrollbar-show">
    <div class="chart" ref="ganttRef"></div>
  </div>
</template>
<style lang="scss" >
:root[data-gantt-theme=btit] {
  --dhx-gantt-theme: btit;
  --dhx-gantt-font-family: '';
  --dhx-gantt-base-colors-primary: #0288D1;
  --dhx-gantt-base-colors-select: var(--table-row-hover-bg-color);
  --dhx-gantt-base-colors-hover-color: var(--table-row-hover-bg-color);
  --dhx-gantt-base-colors-border: rgb(233, 233, 233);
  --dhx-gantt-task-text-font-size: 14px;
  --dhx-gantt-task-text-font-weight: 500;
  --dhx-gantt-heading-font-size: 22px;
  --dhx-gantt-heading-font-weight: 300;
  --dhx-gantt-caption-font-size: 14px;
  --dhx-gantt-caption-font-weight: 400;
  --dhx-gantt-scale-color: #767676;
  --dhx-gantt-container-color: #3f3f3f;
  --dhx-gantt-border-radius: 0;
  --dhx-gantt-box-shadow-s: 0 3px 5px 0 rgba(0, 0, 0, .1);
  --dhx-gantt-box-shadow-m: 0px 4px 24px 0px rgba(44, 47, 60, .36);
  --dhx-gantt-box-shadow-l: 0px 4px 24px 0px rgba(44, 47, 60, .56);
  --dhx-gantt-scale-background: rgba(0,0,0,0);
  --dhx-gantt-scale-color: #42464b;
  --dhx-gantt-task-color: #1e2022;
  --dhx-gantt-project-background: #c7d8f7;
  --dhx-gantt-milestone-background: #DB7DC5;
  --dhx-gantt-task-background: #EBEEF5;
  --dhx-gantt-task-border: 1px solid #409EFF;
  --dhx-gantt-task-progress-color: #4d75c4;
  --dhx-gantt-project-progress-color: #9ab9f1;
  --dhx-gantt-link-background: #ffb96d;
  --dhx-gantt-lightbox-title-background: #f4f2ea;
  --dhx-gantt-popup-background: #fcfaf3;
  --dhx-gantt-popup-color: var(--dhx-gantt-container-color);
  --dhx-gantt-popup-border: 1px solid #cac8bd;
}
.gantt_scale_line.noborder {
  border-top: 0;
}
.gantt_scale_cell {
  font-size: 12px;
}
.gantt_scale_cell.left {
  text-align: left;
  padding-left: 6px;
  padding-right: 6px;
}
.gantt_grid_head_cell {
  font-weight: bold;
  text-align: left;
  padding-left: 6px;
  padding-right: 6px;
}
.gantt_tree_icon {
  position: relative;
  z-index: 1;
  opacity: 0;
}
.gantt_custom_tree_icon {
  position: relative;
  display: flex;
  align-items: center;
  width: 20px;
  object-fit: contain;
  margin-left: -20px;
}
.gantt_custom_tree_icon img{
  width: 10px;
  height: 10px;
  object-fit: contain;
}
.gantt_custom_tree_icon.open img{
  transform: rotate(90deg);
}

.bt_gantt {
  width: 100%;
  height: 100%;
  .chart {
    width: 100%;
    height: 100%;
    z-index: 5 !important;
  }
}
</style>
