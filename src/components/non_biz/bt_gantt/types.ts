export interface ListItem<T = any> {
  [key: string]: T;
}

export type GanttItem = {
  id: number;
  parent: number;
  text: string;
  start_date: string;
  end_date: string;
  duration?: number;
  progress: number;
  [key: string]: any;
}

export type PropsType = {
  data: ListItem;
  config?: ListItem;
  templates?: ListItem;
  plugins?: ListItem;
  dateType?: string | number;
  filterValue?: string;
};

export type EventsType = {
    (e: 'update:modelValue', key: string | number): void
    (e: 'change', key: string | number): void
};

