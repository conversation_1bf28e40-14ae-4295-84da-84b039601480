import { computed, ref, watch, useTemplateRef } from 'vue';
import type { EventsType, PropsType, ListItem } from './types';
import { ElTree } from 'element-plus';

function getTreeIdx(list: ListItem[], pidx: number[] = []): ListItem {
  let result: ListItem = {};
  for (let i = 0; i < list.length; i++) {
    result[list[i].value] = [...pidx, i];
    if (list[i].children) {
      result = Object.assign(
        {},
        result,
        getTreeIdx(list[i].children, result[list[i].value])
      );
    }
  }
  return result;
}

function treeToList(tree: ListItem[], pid: number | string = '0'): ListItem[] {
  const res = [];
  for (let item of tree) {
    res.push({
      ...item,
      label: item.label,
      value: item.value,
      parent: pid,
      children: null,
    });
    if (item.children) {
      res.push(...treeToList(item.children, item.value));
    }
  }
  return res;
}

function listToTree(list: ListItem[]): ListItem[] {
  const map: ListItem = {};
  const roots: ListItem[] = [];

  list.forEach((v) => {
    map[v.value] = { ...v, children: [] };
  });

  list.forEach((item) => {
    const parent = map[item.parent];
    if (!parent) {
      roots.push(map[item.value]);
    } else {
      parent.children.push(map[item.value]);
    }
  });

  return roots;
}

function getTreeMap(list: ListItem[], key: string): ListItem {
  let result: ListItem = {};
  for (let item of list) {
    result[item[key]] = 1;
    if (item.children) {
      result = Object.assign({}, result, getTreeMap(item.children, key));
    }
  }
  return result;
}

function generateKey(): string {
  return new Date().getTime().toString();
}

export function BtTreeCtrl(emits: EventsType, props: PropsType) {
  function tabChange(idx: number) {
    emits('tabChange', idx);
  }

  const currentNode = ref(<ListItem>{});
  const treeRef = useTemplateRef<InstanceType<typeof ElTree>>('treeRef');
  const treeList = ref(props.modelValue);
  const treeIds = computed(() => {
    return getTreeIdx(treeList.value);
  });
  const treeProps = {
    children: 'children',
    label: 'label',
    indent: props.indent,
  };
  const treeFilterNode = (value: string, data: ListItem) => {
    if (!value) return true;
    return data.label.includes(value);
  };
  function handleTreeListChange(list: ListItem[]) {
    treeList.value = [...list];
    emits('update:modelValue', treeList.value);
    emits('change', treeList.value);
  }
  watch(
    () => props.modelValue,
    (newVal) => {
      treeList.value = newVal;
    }
  );

  const keyword = ref('');
  const showSearch = ref(false);
  function toggleSearch() {
    showSearch.value = !showSearch.value;
  }
  watch(keyword, (val) => {
    treeRef.value!.filter(val);
  });

  const inputNode = ref('');
  function closeInput() {
    inputNode.value = '';
    handleTreeListChange(treeList.value);
  }

  const floatShow = ref(false);
  const floatStyle = ref({
    top: '',
    left: '',
  });
  function setCurrentNode(info: ListItem) {
    const ids = treeIds.value[info.data.value];
    let list = treeList.value;
    for (let i = 1; i < ids.length; i++) {
      list = list[ids[i - 1]].children;
    }
    const lastIds = ids[ids.length - 1];
    info.hasPrev = lastIds > 0;
    info.hasNext = lastIds < list.length - 1;
    info.hasParent = ids.length > 1;
    info.hasChild = list[lastIds].children && list[lastIds].children.length > 0;
    info.hasSibling = lastIds > 0;
    currentNode.value = info;
    treeRef.value?.setCurrentKey();
    treeRef.value?.setCurrentKey(info.data.value);
  }
  function hideFloatBox() {
    floatShow.value = false;
    document.removeEventListener('click', hideFloatBox);
  }
  function handleNodeRightClick(e: MouseEvent, info: ListItem) {
    if (keyword.value) return;
    e.preventDefault();
    setCurrentNode(info);
    floatStyle.value.top = e.pageY + 'px';
    floatStyle.value.left = e.pageX - 140 + 'px';
    floatShow.value = true;
    document.addEventListener('click', hideFloatBox);
  }

  const actionList = [
    {
      name: '添加',
      action: 'add',
      children: [
        { name: '上方添加页面', action: 'add_up' },
        { name: '下方添加页面', action: 'add_down' },
        { name: '子页面', action: 'add_child' },
      ],
    },
    {
      name: '移动',
      action: 'move',
      children: [
        { name: '上移', action: 'move_up' },
        { name: '下移', action: 'move_down' },
        { name: '降级', action: 'level_down' },
        { name: '升级', action: 'level_up' },
      ],
    },
    { name: '删除', action: 'delete' },
    { name: '剪切', action: 'cut' },
    { name: '复制', action: 'copy' },
    { name: '粘贴', action: 'paste' },
    { name: '重命名', action: 'rename' },
  ];
  function handleAction(type: string) {
    if (type == 'add_up' || type == 'add_down') {
      addSameLevel('up');
    } else if (type == 'add_down') {
      addSameLevel('down');
    } else if (type == 'add_child') {
      addChild();
    } else if (type == 'move_up') {
      moveSameLevel('up');
    } else if (type == 'move_down') {
      moveSameLevel('down');
    } else if (type == 'level_up') {
      moveLevelUp();
    } else if (type == 'level_down') {
      moveLevelDown();
    } else if (type == 'cut' || type == 'copy') {
      cutAndCopy(type);
    } else if (type == 'paste') {
      handlePaste();
    } else if (type == 'rename') {
      handleRename();
    }

    if (!['cut', 'copy', 'paste'].includes(type)) {
      copyNode.value = {};
      copyType.value = '';
    }
  }
  function addSameLevel(type: string) {
    const ids = treeIds.value[currentNode.value.data.value];
    let list = treeList.value;
    for (let i = 1; i < ids.length; i++) {
      list = list[ids[i - 1]].children;
    }
    const temp = {
      label: '新增' + props.subname,
      value: generateKey(),
      children: [],
    };
    let index = ids[ids.length - 1];
    index += type == 'down' ? 1 : 0;
    hideFloatBox();
    list.splice(index, 0, temp);
    handleTreeListChange(treeList.value);
    setCurrentNode({
      data: temp,
      level: ids.length,
    });
    inputNode.value = temp.value;
  }
  function addChild() {
    const ids = treeIds.value[currentNode.value.data.value];
    let list = treeList.value;
    for (let i = 1; i < ids.length; i++) {
      list = list[ids[i - 1]].children;
    }
    if (!list[ids[ids.length - 1]].children) {
      list[ids[ids.length - 1]].children = [];
    }
    list = list[ids[ids.length - 1]].children;
    const temp = {
      label: '新增' + props.subname,
      value: generateKey(),
      children: [],
    };
    hideFloatBox();
    list.push(temp);
    handleTreeListChange(treeList.value);
    setCurrentNode({
      data: temp,
      level: ids.length,
    });
    inputNode.value = temp.value;
  }
  function moveSameLevel(type: string) {
    if (
      (type == 'up' && !currentNode.value.hasPrev) ||
      (type == 'down' && !currentNode.value.hasNext)
    ) {
      return false;
    }
    const ids = treeIds.value[currentNode.value.data.value];
    let list = treeList.value;
    for (let i = 1; i < ids.length; i++) {
      list = list[ids[i - 1]].children;
    }

    let oldIdx = ids[ids.length - 1];
    let newIdx = oldIdx;
    newIdx += type == 'up' ? -1 : 1;

    let temp = list[oldIdx];
    list[oldIdx] = list[newIdx];
    list[newIdx] = temp;

    hideFloatBox();
    handleTreeListChange(treeList.value);
  }
  function moveLevelUp() {
    if (!currentNode.value.hasParent) return;
    const ids = treeIds.value[currentNode.value.data.value];
    let list = treeList.value;
    for (let i = 1; i < ids.length - 1; i++) {
      list = list[ids[i - 1]].children;
    }

    let allList = treeToList(treeList.value);

    const targetNode = allList.find((v) => v.value == list[0].value);
    for (let i = 0; i < allList.length; i++) {
      if (allList[i].value == currentNode.value.data.value) {
        allList[i].parent = targetNode?.parent;
      }
    }

    hideFloatBox();
    handleTreeListChange(listToTree(allList));
  }
  function moveLevelDown() {
    if (!currentNode.value.hasSibling) return;
    const ids = treeIds.value[currentNode.value.data.value];
    let list = treeList.value;
    for (let i = 1; i < ids.length; i++) {
      list = list[ids[i - 1]].children;
    }
    const lastIds = ids[ids.length - 1];
    const sibling = list[lastIds - 1];

    let allList = treeToList(treeList.value);
    for (let i = 0; i < allList.length; i++) {
      if (allList[i].value == currentNode.value.data.value) {
        allList[i].parent = sibling.value;
      }
    }

    hideFloatBox();
    handleTreeListChange(listToTree(allList));
  }
  function handleDelete() {
    const ids = treeIds.value[currentNode.value.data.value];
    let list = treeList.value;
    for (let i = 1; i < ids.length; i++) {
      list = list[ids[i - 1]].children;
    }

    list.splice(ids[ids.length - 1], 1);

    hideFloatBox();
    handleTreeListChange(treeList.value);
  }
  function handleRename() {
    inputNode.value = currentNode.value.data.value;
    hideFloatBox();
  }
  const copyNode = ref(<ListItem>{});
  const copyNodeIds = computed(() => {
    return getTreeMap([copyNode.value], 'value');
  });
  const copyType = ref('');
  function cutAndCopy(type: string) {
    const info = currentNode.value.data;
    copyType.value = type;
    copyNode.value = info;
    hideFloatBox();
  }
  function handlePaste() {
    const delIds = treeIds.value[copyNode.value.value];

    const ids = treeIds.value[currentNode.value.data.value];
    let list = treeList.value;
    for (let i = 1; i < ids.length; i++) {
      list = list[ids[i - 1]].children;
    }
    const lastIds = ids[ids.length - 1];
    list[lastIds].children = list[lastIds].children || [];

    let node = JSON.parse(JSON.stringify(copyNode.value));

    if (copyType.value == 'copy') {
      updateNodeValue(node);
    }

    list[lastIds].children.push(node);

    if (copyType.value == 'cut') {
      let list2 = treeList.value;
      for (let i = 1; i < delIds.length; i++) {
        list2 = list2[delIds[i - 1]].children;
      }
      list2.splice(delIds[delIds.length - 1], 1);
    }

    copyNode.value = {};
    copyType.value = '';
    hideFloatBox();
    handleTreeListChange(treeList.value);
  }
  function updateNodeValue(info: ListItem, value: string = '') {
    info.value = value ? value : generateKey();
    if (info.children) {
      for (let i = 0; i < info.children.length; i++) {
        updateNodeValue(info.children[i], value + i);
      }
    }
  }

  const showTabAction = ref(false);
  function openTabAction() {
    showTabAction.value = true;
    document.addEventListener('click', closeTabAction);
  }
  function closeTabAction() {
    showTabAction.value = false;
    document.removeEventListener('click', closeTabAction);
  }
  function addTreeNode() {
    const temp = {
      label: '新增' + props.subname,
      value: generateKey(),
      children: [],
    };
    treeList.value.push(temp);
    handleTreeListChange(treeList.value);
    setCurrentNode({ data: temp, level: 1 });
    inputNode.value = temp.value;
  }

  const addVisible = ref(false);
  const addFormRef = useTemplateRef<any>('addFormRef');
  const addForm = ref({ name: '' });
  const addRules = ref({
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  });
  function closeAdd() {
    addVisible.value = false;
    addFormRef.value.resetFields();
  }
  async function confirmAdd() {
    await addFormRef.value.validate();
    emits('tabAdd', addForm.value.name);
    closeAdd();
  }

  // 处理复选框变化
  function handleCheck() {
    // 获取所有选中的节点
    const checkedNodes = treeRef.value?.getCheckedNodes() || [];
    // 当复选框状态变化时，触发 change 事件
    emits('change', checkedNodes);
  }

  function handleNodeClick(data: any, node: any) {
    // Element Plus el-tree 的 node-click 事件传递的数据
    // data: 节点数据，node: 节点对象
    emits('nodeClick', { data, node });
  }

  return {
    tabChange,
    currentNode,
    treeList,
    treeProps,
    treeFilterNode,
    treeRef,
    floatShow,
    floatStyle,
    handleNodeRightClick,
    hideFloatBox,
    actionList,
    handleAction,
    inputNode,
    closeInput,
    handleDelete,
    copyNode,
    copyNodeIds,
    copyType,
    keyword,
    showSearch,
    toggleSearch,
    addVisible,
    addForm,
    addRules,
    closeAdd,
    confirmAdd,
    showTabAction,
    openTabAction,
    addTreeNode,
    handleCheck,
    handleNodeClick,
  };
}
