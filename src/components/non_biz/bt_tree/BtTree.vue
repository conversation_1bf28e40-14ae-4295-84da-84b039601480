<script setup lang="ts">
import { BtTreeCtrl } from './BtTreeCtrl.ts';
import type { EventsType, PropsType } from './types.ts';
import { Search as ElSearch, Plus, ArrowRight } from '@element-plus/icons-vue';
import {withBaseUrl} from "@/utils/routeUtil.ts";

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: () => [],
  showAdd: true,
  showCheckbox: false,
  checkStrictly: false,
  indent: 24,
  showExpandIcon: false,
});

const emits = defineEmits<EventsType>();
const {
  currentNode,
  treeList,
  treeProps,
  treeFilterNode,
  treeRef,
  tabChange,
  actionList,
  floatShow,
  floatStyle,
  hideFloatBox,
  handleNodeRightClick,
  inputNode,
  closeInput,
  handleAction,
  handleDelete,
  copyNode,
  copyNodeIds,
  copyType,
  keyword,
  showSearch,
  toggleSearch,
  addVisible,
  addForm,
  addRules,
  closeAdd,
  confirmAdd,
  showTabAction,
  openTabAction,
  addTreeNode,
  handleCheck,
  handleNodeClick,
} = BtTreeCtrl(emits, props);

// 暴露内部的 treeRef 和搜索功能给父组件
defineExpose({
  treeRef,
  toggleSearch,
});
</script>
<template>
  <div class="bt_tree">
    <div class="header">
      <BtTab :list="tabList" :max="100" :value="curTab" @change="tabChange">
        <template #right>
          <slot name="right">
            <div class="opt flex flex-center">
              <div class="item" v-if="showAdd">
                <BtTooltip
                  class="icon-tooltip"
                  effect="dark"
                  content="新增"
                  placement="bottom"
                  @click.stop="openTabAction"
                >
                  <el-icon class="icon"><Plus /></el-icon>
                </BtTooltip>
                <div :class="{ list: true, show: showTabAction }">
                  <div class="li" @click="addTreeNode">{{ subname }}</div>
                  <div class="li" @click="addVisible = true">{{ name }}</div>
                </div>
              </div>
              <div class="item" @click="toggleSearch">
                <BtTooltip
                  class="icon-tooltip"
                  effect="dark"
                  content="搜索"
                  placement="bottom"
                >
                  <el-icon class="icon"><ElSearch /></el-icon>
                </BtTooltip>
              </div>
            </div>
          </slot>
        </template>
      </BtTab>
    </div>
    <div class="container">
      <div class="search">
        <div v-show="showSearch">
          <el-input
            class="ipt"
            v-model="keyword"
            placeholder="请输入"
            clearable
          ></el-input>
        </div>
      </div>
      <el-tree
        ref="treeRef"
        class="tree"
        :data="treeList"
        node-key="value"
        default-expand-all
        :expand-on-click-node="false"
        :props="treeProps"
        :filter-node-method="treeFilterNode"
        :show-checkbox="showCheckbox"
        :check-strictly="checkStrictly"
        @check="handleCheck"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <div
            :class="{
              'tree-node': true,
              cut: copyType == 'cut' && copyNodeIds[data.value],
            }"
            @click="hideFloatBox"
            @contextmenu="handleNodeRightClick($event, node)"
          >
            <el-input
              v-if="inputNode == data.value"
              v-model="data.label"
              autofocus
              @blur="closeInput"
            ></el-input>
            <!-- 支持外部插槽自定义节点内容 -->
            <slot v-else name="node" :node="node" :data="data">
              <span>{{ node.label }}</span>
            </slot>
          </div>
        </template>
        <template #empty>
          <div class="empty">
            <img :src="withBaseUrl('empty.png')" alt="empty" />
            <div class="txt">暂无数据</div>
          </div>
        </template>
      </el-tree>
    </div>
    <div
      :class="{ 'float-box': true, show: floatShow }"
      :style="floatStyle"
      @click.stop
    >
      <div class="list">
        <div
          v-for="(item, index) in actionList"
          :key="index"
          :class="{
            li: true,
            hide:
              item.action == 'paste' &&
              (!copyNode.value || copyNodeIds[currentNode.data.value]) &&
              (!copyType || copyType == 'cut'),
          }"
          @click="handleAction(item.action)"
        >
          <el-popconfirm
            v-if="item.action == 'delete'"
            width="220"
            title="确认要删除选中项以及其子项吗？"
            @confirm="handleDelete"
          >
            <template #reference>
              <span class="tit">{{ item.name }}</span>
            </template>
            <template #actions="{ confirm, cancel }">
              <el-button size="small" @click="cancel">取消</el-button>
              <el-button type="danger" size="small" @click="confirm">
                删除
              </el-button>
            </template>
          </el-popconfirm>
          <span v-else class="tit">{{ item.name }}</span>
          <template v-if="item.children">
            <el-icon class="icon"><ArrowRight /></el-icon>
            <div class="ul">
              <div
                v-for="(child, cidx) in item.children"
                :key="cidx"
                :class="{
                  li: true,
                  disabled:
                    (!currentNode.hasPrev && child.action == 'move_up') ||
                    (!currentNode.hasNext && child.action == 'move_down') ||
                    (!currentNode.hasParent && child.action == 'level_up') ||
                    (!currentNode.hasSibling && child.action == 'level_down'),
                }"
                @click.stop="handleAction(child.action)"
              >
                <span class="tit">{{ child.name }}</span>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="addVisible"
      :title="'新建' + name"
      width="500"
      :before-close="closeAdd"
    >
      <el-form ref="addFormRef" class="form" :model="addForm" :rules="addRules">
        <el-form-item :label="name + '名称'" prop="name">
          <el-input
            v-model="addForm.name"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeAdd">取消</el-button>
          <el-button type="primary" @click="confirmAdd"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style scoped lang="scss">
.bt_tree {
  width: 100%;
  height: 100%;
  border: 1px solid var(--el-border-color);
  .header {
    .opt {
      position: relative;
      z-index: 2;
      .item {
        position: relative;
        margin-left: 16px;
        .icon {
          position: relative;
          font-size: 16px;
          cursor: pointer;
          z-index: 2;
        }
        .list {
          position: absolute;
          top: 0;
          right: 30px;
          width: 100px;
          border-radius: var(--el-border-radius-base);
          background-color: #ffffff;
          font-size: 12px;
          color: rgba(22, 22, 22, 1);
          box-shadow: 0px 0px 3px 0px rgba(217, 217, 217, 1);
          padding: 8px 0;
          display: none;
          transition: all 0.3s;
          &.show {
            display: block;
          }
          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 100%;
            width: 30px;
            height: 100%;
          }
          .li {
            line-height: 32px;
            text-align: center;
            padding: 0 10px;
            cursor: pointer;
            &:hover {
              background: var(--el-fill-color-light);
            }
          }
        }
        // &:hover {
        //   .list {
        //     opacity: 1;
        //     z-index: 1;
        //   }
        // }
      }
    }
  }
  .container {
    :deep(.tree) {
      --el-tree-node-content-height: 40px;
      .el-tree-node {
        position: relative;
      }
      .tree-node {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        padding-left: v-bind(
          '(!showCheckbox && !showExpandIcon) ? "20px" : "0px"'
        );
        padding-right: 20px;
        &.cut {
          opacity: 0.5;
        }
      }
      .el-tree-node__expand-icon {
        margin-right: 4px;
        display: v-bind('showExpandIcon ? "inline-flex" : "none"');
      }
      > .el-tree-node > .el-tree-node__content > .tree-node {
        font-weight: bold;
      }
      .is-current > .el-tree-node__content {
        background: var(--el-fill-color-light);
      }
    }
  }
  .search {
    margin: 8px 10px;
    margin-bottom: 0;
  }
  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 100px;
    img {
      width: 100px;
      height: 63px;
    }
    .txt {
      font-size: 14px;
      color: rgba(127, 127, 127, 1);
      line-height: 17px;
      margin-top: 18px;
    }
  }
  .float-box {
    position: fixed;
    border-radius: var(--el-border-radius-base);
    background-color: #ffffff;
    font-size: 12px;
    color: rgba(22, 22, 22, 1);
    box-shadow: 0px 0px 3px 0px rgba(217, 217, 217, 1);
    padding: 8px 0;
    opacity: 0;
    z-index: -1;
    transition: all 0.3s;
    &.show {
      z-index: 10;
      opacity: 1;
    }
    .list {
      width: 130px;
    }
    .li {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      padding-left: 20px;
      padding-right: 10px;
      cursor: pointer;
      .icon {
        margin-left: auto;
      }
      .tit {
        flex: 1;
        line-height: 32px;
      }
      .ul {
        display: none;
        position: absolute;
        top: 0;
        left: 100%;
        width: 100px;
        padding: 8px 0;
        border-radius: var(--el-border-radius-base);
        background-color: #ffffff;
        box-shadow: 0px 0px 3px 0px rgba(217, 217, 217, 1);
        .li {
          padding: 0 10px;
          justify-content: center;
          text-align: center;
        }
      }
      &.disabled {
        // display: none;
        opacity: 0.4;
        cursor: not-allowed;
      }
      &:hover {
        background: var(--el-fill-color-light);
        .ul {
          display: block;
        }
      }
    }
  }
}
</style>
