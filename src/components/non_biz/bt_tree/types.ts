export interface ListItem<T = any> {
  [key: string]: T;
}

export type PropsType = {
  name: string;
  subname: string;
  tabList: ListItem[];
  curTab: number;
  modelValue: ListItem[];
  showAdd?: boolean;
  showCheckbox?: boolean;
  checkStrictly?: boolean;
  indent?: number;
  showExpandIcon?: boolean;
};

export type EventsType = {
  (e: 'update:modelValue', key: ListItem[]): void;
  (e: 'change', key: ListItem[]): void;
  (e: 'tabChange', key: number): void;
  (e: 'tabAdd', key: string): void;
  (e: 'nodeClick', payload: { data: any; node: any }): void;
};
