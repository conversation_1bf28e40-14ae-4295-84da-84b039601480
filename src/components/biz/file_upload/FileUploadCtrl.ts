import { ref, onMounted, watch } from "vue"
import type { EventsType, PropsType, UploadFile, FileItem, Response } from "./types";
import { appConfig } from '@/app-config'
import { uploadFile } from "@/service_url/shared";

export function FileUploadCtrl(emits: EventsType, props: PropsType) {
  const action = (import.meta.env.VITE_API_PROXY || '') + appConfig.apiPath + uploadFile

  const fileList = ref(<UploadFile[]>[])

  function handleSuccess(_response: Response, _file: UploadFile, uploadFiles: UploadFile[]) {
    const list: FileItem[] = []
    uploadFiles.forEach(v=>{
      list.push(v.response.data)
    })
    handleValueChange(list)
  }
  function handleRemove(_file: UploadFile, uploadFiles: UploadFile[]) {
    const list: FileItem[] = []
    uploadFiles.forEach(v=>{
      list.push(v.response.data)
    })
    handleValueChange(list)
  }

  function handleValueChange(list: FileItem[]) {
    emits("update:modelValue", list);
    emits("change", list);
  }

  function setFileList(list: FileItem[]) {
    const uid = new Date().getTime() - list.length
    const newList: UploadFile[] = []
    for(let i = 0; i < list.length; i++) {
      const item = list[i]
      newList.push({
        name: item.fileName,
        size: item.fileSize,
        response: { data: item },
        uid: uid + i,
        status: 'success'
      })
    }
    fileList.value = newList
  }

  onMounted(() => {
    if(!props.modelValue) return
    setFileList(props.modelValue)
  })

  watch(()=>props.modelValue,(newVal)=>{
    if(newVal?.length !== fileList.value.length) {
      setFileList(newVal||[])
    }
  },{deep: true})

  return {
    action,
    fileList,
    handleSuccess,
    handleRemove,
  };
}
