<script setup lang="ts">
import { FileUploadCtrl } from "./FileUploadCtrl.ts";
import type { PropsType, EventsType } from "./types.ts";
import { defineExpose } from 'vue';

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: () => [],
});

const emits = defineEmits<EventsType>();
const { action, fileList, handleSuccess, handleRemove } = FileUploadCtrl(emits,props);

// 添加空的open方法，目前上传按钮不做功能处理
function open() {
  // 暂时为空，后续可以在这里实现上传功能
  console.log('文件上传功能暂未实现');
}

defineExpose({
  open
});
</script>
<template>
  <div class="file_upload">
    <el-upload
      v-bind="$attrs"
      :action="action"
      with-credentials
      v-model:file-list="fileList"
      :on-success="handleSuccess"
      :on-remove="handleRemove"
    >
      <template v-for="slot in Object.keys($slots)" #[slot] :key="slot">
        <slot :name="slot" />
      </template>
    </el-upload>
  </div>
</template>
<style scoped lang="scss">
.file_upload {
  position: relative;
  width: 100%;
}
</style>
