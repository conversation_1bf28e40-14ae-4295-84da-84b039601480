import type { UploadFile as ElUploadFile } from 'element-plus'

export type FileItem = {
  etag: string;
  fileKey: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
}

export type Response = {
  code?: number;
  data: FileItem;
  timestamp?: number;
}

export interface UploadFile extends ElUploadFile {
  response: Response;
}

export type PropsType = {
    modelValue?: FileItem[]
};

export type EventsType = {
    (e: 'update:modelValue', list: FileItem[]): void
    (e: 'change', list: FileItem[]): void
};
