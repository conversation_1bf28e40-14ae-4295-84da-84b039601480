/**
 * Actual function that does all the work. Returns an array
 with all the info.

 * My Assumption is that most of the browsers will have
 arial set as their default sans-serif font.

 */

class Detector {
    h = document.getElementsByTagName("BODY")[0];
    s = document.createElement("SPAN");
    baseFonts = ['monospace', 'sans-serif', 'serif'];
    defaultWidth:Record<string, number> = {};
    defaultHeight:Record<string, number> = {};
    testSize = '72px';
    textString = 'mmmmmmmmmmlli';

    constructor() {
        this.s.style.fontSize = this.testSize;
        this.s.innerHTML = this.textString;
        for (const fontName of this.baseFonts) {
            this.h.appendChild(this.s);
            this.s.style.fontFamily = fontName;
            this.defaultWidth[fontName] = this.s.offsetWidth; //width for the default font
            this.defaultHeight[fontName] = this.s.offsetHeight; //height for the defualt font
            this.h.removeChild(this.s);
        }
    }

    detailedTest(font:string) {
        let detected = false;
        const f:[string, boolean] = [font, detected];
        for (let fontName of this.baseFonts) {
            this.s.style.fontFamily = font + ',' + fontName; // name of the font along with the base font for fallback.
            this.h.appendChild(this.s);
            const matched = (this.s.offsetWidth != this.defaultWidth[fontName] || this.s.offsetHeight != this.defaultHeight[fontName]);
            this.h.removeChild(this.s);
            detected = detected || matched;
        }
        f[1] = detected;
        return f;
    }

    test(font:string){
        const f = this.detailedTest(font);
        return f[1];
    }

}

const sampleFonts = [
    'cursive', 'monospace', 'serif', 'sans-serif', 'fantasy',
    'Arial', 'Arial Black', 'Arial Narrow', 'Arial Rounded MT Bold', 'Bookman Old Style',
    'Bradley Hand ITC', 'Century', 'Century Gothic', 'Comic Sans MS', 'Courier', 'Courier New',
    'Georgia', 'Gentium', 'Impact', 'King', 'Lucida Console', 'Lalit', 'Modena', 'Monotype Corsiva',
    'Papyrus', 'Tahoma', 'TeX', 'Times', 'Times New Roman', 'Trebuchet MS', 'Verdana', 'Verona',
    '宋体', '微软雅黑', '黑体', '方正姚体', '方正舒体', '华文彩云', '华文仿宋', '华文行楷', '华文隶书', '华文楷体', '华文新魏',
    '新宋体', '幼圆', '楷体', '隶书'
];

export const getFontList = (fonts?: string[]) => {
    const d = new Detector();
    const result = [];
    fonts = fonts || sampleFonts;
    for (let i=0; i<fonts.length; i++) {
        const font = fonts[i];
        result.push(d.detailedTest(font));
    }
    return result.filter(i => {
        return i[1];
    });
}
