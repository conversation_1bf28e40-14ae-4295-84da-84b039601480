/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data: any[], id: string = 'id', parentId: string = 'parentId', children: string = 'children') {
    let config = {
      id: id,
      parentId: parentId,
      childrenList: children
    };
  
    var childrenListMap = <any>{};
    var nodeIds = <any>{};
    var tree = [];
  
    for (let d of data) {
      let parentId = d[config.parentId];
      if (childrenListMap[parentId] == null) {
        childrenListMap[parentId] = [];
      }
      nodeIds[d[config.id]] = d;
      childrenListMap[parentId].push(d);
    }
  
    for (let d of data) {
      let parentId = d[config.parentId];
      if (nodeIds[parentId] == null) {
        tree.push(d);
      }
    }
  
    for (let t of tree) {
      adaptToChildrenList(t);
    }
  
    function adaptToChildrenList(o: any) {
      if (childrenListMap[o[config.id]] !== null && childrenListMap[o[config.id]] !== undefined) {
        o[config.childrenList] = childrenListMap[o[config.id]];
      }
      if (o[config.childrenList]) {
        for (let c of o[config.childrenList]) {
          adaptToChildrenList(c);
        }
      }
    }
    return tree;
  }