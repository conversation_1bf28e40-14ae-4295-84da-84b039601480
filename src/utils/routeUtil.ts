import {getStore} from "@/libs/pinia";
import {mainLoginUrl, projectsUrl} from "@/service_url/shared.ts";
import type {NavigationGuardNext, RouteLocationNormalized, RouteLocationNormalizedLoaded} from "vue-router";
import {useLoginStore} from "@pages/main/views/login/LoginStore.ts";

function checkLogin(to:RouteLocationNormalized, __:RouteLocationNormalizedLoaded) {
    return () => {
        const loginStore = getStore('loginStore') || useLoginStore();
        const isLogin = loginStore.isLoggedIn;
        if (!isLogin) {
            if (to.name !== 'btit_login') {
                const loginUrl = new URL(mainLoginUrl)
                loginUrl.searchParams.set('redirect', window.location.href);
                window.location.href = loginUrl.href;
            }
            return true;
        }
        return isLogin;
    };
}

function checkAnother1(_:RouteLocationNormalized, __:RouteLocationNormalizedLoaded) {
    return () => {
        return true;
    };
}

function checkAnother2(_:RouteLocationNormalized, __:RouteLocationNormalizedLoaded) {
    return () => {
        return true;
    };
}

export function gotoPrePage() {
    const curUrl = window.location.href;
    const url = new URL(curUrl);
    const redirectUrl = url.searchParams.get('redirect');
    if (redirectUrl) {
        window.location.href = redirectUrl;
    } else {
        window.location.href = projectsUrl;
    }
}

export function withBaseUrl(path:string) {
    return `${import.meta.env.BASE_URL}${path}`;
}

export function onBeforeEach(
    to:RouteLocationNormalized,
    from:RouteLocationNormalizedLoaded,
    next:NavigationGuardNext
) {
    const canNext = [
        checkLogin(to, from),
        checkAnother1(to, from),
        checkAnother2(to, from)
    ].every(fun => fun());
    canNext && next();
}