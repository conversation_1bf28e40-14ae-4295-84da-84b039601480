<template>
  <div class="my-schedule">
    <!-- 左侧菜单栏 -->
    <div class="sidebar">
      <!-- 标题和通知 -->
      <div class="sidebar-header">
        <div class="title">
          <el-icon class="title-icon"><Bell /></el-icon>
          <span>日程</span>
        </div>
        <div class="user-avatar">
          <el-avatar :size="20">轻</el-avatar>
        </div>
        <el-button class="add-btn" type="primary" size="small" @click="handleCreateSchedule">
          <el-icon><Plus /></el-icon>
        </el-button>
      </div>

      <!-- 搜索框 -->
      <div class="search-section">
        <el-input 
          v-model="searchKeyword" 
          placeholder="搜索或转到..." 
          :prefix-icon="Search"
          size="small"
          clearable
        />
      </div>

      <!-- 我的日程分类 -->
      <div class="schedule-categories">
        <div class="category-title">我的日程</div>
        <div class="category-list">
          <div 
            v-for="category in scheduleCategories" 
            :key="category.id"
            :class="['category-item', { active: selectedCategoryId === category.id }]"
            @click="selectCategory(category.id)"
          >
            <el-checkbox v-model="category.visible" @change="toggleCategoryVisibility(category.id)" />
            <span class="category-name">{{ category.name }}</span>
          </div>
        </div>
        
        <el-button class="add-calendar-btn" size="small" text @click="handleAddCalendar">
          <el-icon><Plus /></el-icon>
          添加日历
        </el-button>
      </div>

      <!-- 其他日程 -->
      <div class="other-schedules">
        <div class="category-title">其他日程</div>
        <div class="category-list">
          <div 
            v-for="schedule in otherSchedules" 
            :key="schedule.id"
            :class="['category-item', { active: selectedCategoryId === schedule.id }]"
            @click="selectCategory(schedule.id)"
          >
            <el-checkbox v-model="schedule.visible" @change="toggleCategoryVisibility(schedule.id)" />
            <span class="category-name">{{ schedule.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部工具栏 -->
      <div class="toolbar">
        <div class="date-navigation">
          <el-button-group>
            <el-button @click="goToPrevious">
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            <el-button @click="goToNext">
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </el-button-group>
          <span class="current-date">{{ currentDateText }}</span>
        </div>
        
        <div class="view-controls">
          <el-button-group>
            <el-button 
              v-for="view in viewOptions" 
              :key="view.value"
              :type="currentView === view.value ? 'primary' : 'default'"
              @click="switchView(view.value)"
            >
              {{ view.label }}
            </el-button>
          </el-button-group>
        </div>

        <el-button type="primary" @click="handleCreateSchedule">创建日程</el-button>
      </div>

      <!-- 日历视图 -->
      <div class="calendar-container">
        <!-- 周视图 -->
        <div v-if="currentView === 'week'" class="week-view">
          <!-- 时间轴 -->
          <div class="time-axis">
            <div class="time-header"></div>
            <div 
              v-for="hour in 24" 
              :key="hour" 
              class="time-slot"
            >
              {{ formatHour(hour - 1) }}
            </div>
          </div>

          <!-- 日期列 -->
          <div class="date-columns">
            <!-- 日期头部 -->
            <div class="date-headers">
              <div 
                v-for="date in weekDates" 
                :key="formatDate(date, 'YYYY-MM-DD')"
                :class="['date-header', { today: isSameDay(date, today) }]"
              >
                <div class="day-name">{{ getDayName(date) }}</div>
                <div class="day-number">{{ date.getDate() }}</div>
              </div>
            </div>

            <!-- 日程内容 -->
            <div class="schedule-grid">
              <div 
                v-for="date in weekDates" 
                :key="formatDate(date, 'YYYY-MM-DD')"
                class="day-column"
              >
                <div 
                  v-for="hour in 24" 
                  :key="hour" 
                  :class="['hour-slot', { current: isCurrentHour(date, hour - 1) }]"
                  @click="handleTimeSlotClick(date, hour - 1)"
                >
                  <!-- 日程事件 -->
                  <div 
                    v-for="event in getEventsForTimeSlot(date, hour - 1)" 
                    :key="event.id"
                    :class="['schedule-event', `category-${event.categoryId}`]"
                    :style="getEventStyle(event)"
                    @click.stop="selectEvent(event)"
                  >
                    <div class="event-title">{{ event.title }}</div>
                    <div class="event-time">{{ formatEventTime(event) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 月视图 -->
        <div v-else-if="currentView === 'month'" class="month-view">
          <el-calendar 
            v-model="selectedDate"
            @click="handleDateClick"
          >
            <template #dateCell="{ data }">
              <div class="calendar-day">
                <div class="day-number">{{ data.day.split('-').pop() }}</div>
                <div class="day-events">
                  <div 
                    v-for="event in getDayEvents(data.day)" 
                    :key="event.id"
                    :class="['mini-event', `category-${event.categoryId}`]"
                    @click.stop="selectEvent(event)"
                  >
                    {{ event.title }}
                  </div>
                </div>
              </div>
            </template>
          </el-calendar>
        </div>

        <!-- 年视图 -->
        <div v-else-if="currentView === 'year'" class="year-view">
          <div class="year-grid">
            <div 
              v-for="month in 12" 
              :key="month" 
              class="month-mini"
              @click="selectMonth(month)"
            >
              <div class="month-name">{{ getMonthName(month) }}</div>
              <!-- 简化的月份日历 -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧详情面板 -->
    <div v-if="selectedEvent" class="detail-panel">
      <div class="panel-header">
        <h3>日程详情</h3>
        <el-button text @click="closeDetailPanel">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="panel-content">
        <div class="event-info">
          <h4>{{ selectedEvent.title }}</h4>
          <p class="event-time">
            {{ formatEventFullTime(selectedEvent) }}
          </p>
          <p v-if="selectedEvent.description" class="event-description">
            {{ selectedEvent.description }}
          </p>
        </div>

        <div class="event-actions">
          <el-button @click="editEvent">编辑</el-button>
          <el-button type="danger" @click="deleteEvent">删除</el-button>
        </div>
      </div>
    </div>

    <!-- 创建/编辑日程弹窗 -->
    <el-dialog 
      v-model="showScheduleDialog" 
      :title="isEditMode ? '编辑日程' : '创建日程'"
      width="600px"
    >
      <el-form :model="scheduleForm" :rules="scheduleRules" ref="scheduleFormRef" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="scheduleForm.title" placeholder="请输入日程标题" />
        </el-form-item>
        
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker 
            v-model="scheduleForm.startTime"
            type="datetime"
            placeholder="选择开始时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
          />
        </el-form-item>

        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker 
            v-model="scheduleForm.endTime"
            type="datetime"
            placeholder="选择结束时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
          />
        </el-form-item>

        <el-form-item label="分类" prop="categoryId">
          <el-select v-model="scheduleForm.categoryId" placeholder="选择分类">
            <el-option 
              v-for="category in scheduleCategories" 
              :key="category.id"
              :label="category.name" 
              :value="category.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="scheduleForm.description" 
            type="textarea" 
            placeholder="请输入日程描述"
            :rows="3"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showScheduleDialog = false">取消</el-button>
        <el-button type="primary" @click="saveSchedule" :loading="saving">
          {{ isEditMode ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance } from 'element-plus';
import {
  Bell,
  Plus,
  Search,
  ArrowLeft,
  ArrowRight,
  Close,
} from '@element-plus/icons-vue';

// 日期处理工具函数
function formatDate(date: Date, format: string = 'YYYY-MM-DD'): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return format
    .replace('YYYY', year.toString())
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes);
}

function getWeekDates(date: Date): Date[] {
  const startOfWeek = new Date(date);
  const day = startOfWeek.getDay();
  const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
  startOfWeek.setDate(diff);
  
  return Array.from({ length: 7 }, (_, i) => {
    const d = new Date(startOfWeek);
    d.setDate(startOfWeek.getDate() + i);
    return d;
  });
}

function isSameDay(date1: Date, date2: Date): boolean {
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate();
}

function addWeeks(date: Date, weeks: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() + weeks * 7);
  return result;
}

function addMonths(date: Date, months: number): Date {
  const result = new Date(date);
  result.setMonth(result.getMonth() + months);
  return result;
}

function addYears(date: Date, years: number): Date {
  const result = new Date(date);
  result.setFullYear(result.getFullYear() + years);
  return result;
}

function getWeekNumber(date: Date): number {
  const start = new Date(date.getFullYear(), 0, 1);
  const diff = (date.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
  return Math.ceil((diff + start.getDay() + 1) / 7);
}

function getDayName(date: Date): string {
  const days = ['日', '一', '二', '三', '四', '五', '六'];
  return days[date.getDay()];
}

function getMonthName(month: number): string {
  return `${month}月`;
}

// 响应式数据
const searchKeyword = ref('');
const selectedCategoryId = ref(1);
const currentView = ref<'week' | 'month' | 'year'>('week');
const selectedDate = ref(new Date());
const today = ref(new Date());
const selectedEvent = ref<any>(null);
const showScheduleDialog = ref(false);
const isEditMode = ref(false);
const saving = ref(false);
const scheduleFormRef = ref<FormInstance>();

// 视图选项
const viewOptions = [
  { label: '周', value: 'week' },
  { label: '月', value: 'month' },
  { label: '年', value: 'year' },
];

// 日程分类
const scheduleCategories = ref([
  { id: 1, name: '个人日程', visible: true, color: '#409EFF' },
  { id: 2, name: '工作任务', visible: true, color: '#67C23A' },
  { id: 3, name: '会议', visible: true, color: '#E6A23C' },
]);

// 其他日程
const otherSchedules = ref([
  { id: 4, name: '团队日程', visible: false, color: '#F56C6C' },
  { id: 5, name: '项目日程', visible: false, color: '#909399' },
]);

// 日程表单
const scheduleForm = ref({
  title: '',
  startTime: '',
  endTime: '',
  categoryId: 1,
  description: '',
});

// 表单验证规则
const scheduleRules = {
  title: [{ required: true, message: '请输入日程标题', trigger: 'blur' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  categoryId: [{ required: true, message: '请选择分类', trigger: 'change' }],
};

// 示例日程数据
const scheduleEvents = ref([
  {
    id: 1,
    title: '项目讨论会议',
    startTime: '2025-01-15 14:30',
    endTime: '2025-01-15 16:00',
    categoryId: 3,
    description: '项目讨论会议',
  },
  {
    id: 2,
    title: '周例会',
    startTime: '2025-01-16 10:00',
    endTime: '2025-01-16 11:30',
    categoryId: 3,
    description: '周例会',
  },
  {
    id: 3,
    title: '技术评审',
    startTime: '2025-01-17 15:00',
    endTime: '2025-01-17 17:00',
    categoryId: 2,
    description: '技术评审',
  },
]);

// 计算属性
const currentDateText = computed(() => {
  if (currentView.value === 'week') {
    const weekDates = getWeekDates(selectedDate.value);
    const start = weekDates[0];
    const end = weekDates[6];
    const weekNum = getWeekNumber(selectedDate.value);
    return `${formatDate(start, 'YYYY年MM月DD日')} - ${formatDate(end, 'MM月DD日')} (第${weekNum}周)`;
  } else if (currentView.value === 'month') {
    return formatDate(selectedDate.value, 'YYYY年MM月');
  } else {
    return formatDate(selectedDate.value, 'YYYY年');
  }
});

const weekDates = computed(() => {
  return getWeekDates(selectedDate.value);
});

// 方法
function selectCategory(categoryId: number) {
  selectedCategoryId.value = categoryId;
}

function toggleCategoryVisibility(categoryId: number) {
  const allCategories = [...scheduleCategories.value, ...otherSchedules.value];
  const category = allCategories.find(c => c.id === categoryId);
  if (category) {
    category.visible = !category.visible;
  }
}

function handleAddCalendar() {
  ElMessage.info('添加日历功能开发中...');
}

function goToPrevious() {
  if (currentView.value === 'week') {
    selectedDate.value = addWeeks(selectedDate.value, -1);
  } else if (currentView.value === 'month') {
    selectedDate.value = addMonths(selectedDate.value, -1);
  } else {
    selectedDate.value = addYears(selectedDate.value, -1);
  }
}

function goToNext() {
  if (currentView.value === 'week') {
    selectedDate.value = addWeeks(selectedDate.value, 1);
  } else if (currentView.value === 'month') {
    selectedDate.value = addMonths(selectedDate.value, 1);
  } else {
    selectedDate.value = addYears(selectedDate.value, 1);
  }
}

function switchView(view: string) {
  currentView.value = view as 'week' | 'month' | 'year';
}

function formatHour(hour: number) {
  return `${hour.toString().padStart(2, '0')}:00`;
}

function isCurrentHour(date: Date, hour: number) {
  const now = new Date();
  return isSameDay(date, now) && hour === now.getHours();
}

function handleTimeSlotClick(date: Date, hour: number) {
  const startTime = new Date(date);
  startTime.setHours(hour, 0, 0, 0);
  const endTime = new Date(date);
  endTime.setHours(hour + 1, 0, 0, 0);
  
  scheduleForm.value = {
    title: '',
    startTime: formatDate(startTime, 'YYYY-MM-DD HH:mm'),
    endTime: formatDate(endTime, 'YYYY-MM-DD HH:mm'),
    categoryId: 1,
    description: '',
  };
  
  isEditMode.value = false;
  showScheduleDialog.value = true;
}

function getEventsForTimeSlot(date: Date, hour: number) {
  return scheduleEvents.value.filter(event => {
    const eventStart = new Date(event.startTime);
    const eventEnd = new Date(event.endTime);
    const slotStart = new Date(date);
    slotStart.setHours(hour, 0, 0, 0);
    const slotEnd = new Date(date);
    slotEnd.setHours(hour + 1, 0, 0, 0);
    
    return eventStart < slotEnd && eventEnd > slotStart && 
           isSameDay(eventStart, date);
  });
}

function getEventStyle(event: any) {
  const category = scheduleCategories.value.find(c => c.id === event.categoryId);
  return {
    backgroundColor: category?.color || '#409EFF',
    opacity: category?.visible ? 1 : 0.3,
  };
}

function selectEvent(event: any) {
  selectedEvent.value = event;
}

function closeDetailPanel() {
  selectedEvent.value = null;
}

function formatEventTime(event: any) {
  const start = new Date(event.startTime);
  const end = new Date(event.endTime);
  return `${formatDate(start, 'HH:mm')}-${formatDate(end, 'HH:mm')}`;
}

function formatEventFullTime(event: any) {
  const start = new Date(event.startTime);
  const end = new Date(event.endTime);
  return `${formatDate(start, 'YYYY年MM月DD日 HH:mm')} - ${formatDate(end, 'HH:mm')}`;
}

function handleCreateSchedule() {
  scheduleForm.value = {
    title: '',
    startTime: '',
    endTime: '',
    categoryId: 1,
    description: '',
  };
  isEditMode.value = false;
  showScheduleDialog.value = true;
}

function editEvent() {
  if (!selectedEvent.value) return;
  
  scheduleForm.value = {
    title: selectedEvent.value.title,
    startTime: selectedEvent.value.startTime,
    endTime: selectedEvent.value.endTime,
    categoryId: selectedEvent.value.categoryId,
    description: selectedEvent.value.description || '',
  };
  
  isEditMode.value = true;
  showScheduleDialog.value = true;
}

function deleteEvent() {
  if (!selectedEvent.value) return;
  
  ElMessageBox.confirm('确定要删除这个日程吗？', '确认删除', {
    type: 'warning',
  }).then(() => {
    const index = scheduleEvents.value.findIndex(e => e.id === selectedEvent.value.id);
    if (index > -1) {
      scheduleEvents.value.splice(index, 1);
      selectedEvent.value = null;
      ElMessage.success('删除成功');
    }
  });
}

async function saveSchedule() {
  if (!scheduleFormRef.value) return;
  
  try {
    await scheduleFormRef.value.validate();
    
    saving.value = true;
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (isEditMode.value && selectedEvent.value) {
      // 更新现有日程
      const index = scheduleEvents.value.findIndex(e => e.id === selectedEvent.value.id);
      if (index > -1) {
        scheduleEvents.value[index] = {
          ...scheduleEvents.value[index],
          ...scheduleForm.value,
        };
      }
      ElMessage.success('更新成功');
    } else {
      // 创建新日程
      const newEvent = {
        id: Date.now(),
        ...scheduleForm.value,
      };
      scheduleEvents.value.push(newEvent);
      ElMessage.success('创建成功');
    }
    
    showScheduleDialog.value = false;
    selectedEvent.value = null;
  } catch (error) {
    console.error('保存失败:', error);
  } finally {
    saving.value = false;
  }
}

function getDayEvents(date: string) {
  return scheduleEvents.value.filter(event => {
    return formatDate(new Date(event.startTime), 'YYYY-MM-DD') === date;
  });
}

function handleDateClick(data: any) {
  selectedDate.value = new Date(data.day);
  currentView.value = 'week';
}

function selectMonth(month: number) {
  const newDate = new Date(selectedDate.value);
  newDate.setMonth(month - 1);
  selectedDate.value = newDate;
  currentView.value = 'month';
}

// 生命周期
onMounted(() => {
  // 设置默认选中当前日期
  selectedDate.value = new Date();
  today.value = new Date();
});
</script>

<style scoped lang="scss">
.my-schedule {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
  
  // 左侧菜单栏
  .sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e8e8e8;
    padding: 16px;
    overflow-y: auto;
    
    .sidebar-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      
      .title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        
        .title-icon {
          margin-right: 8px;
          color: #666;
        }
      }
      
      .user-avatar {
        :deep(.el-avatar) {
          background: linear-gradient(135deg, #e787ff 0%, #8177f1 100%);
          color: white;
          font-size: 12px;
        }
      }
      
      .add-btn {
        padding: 4px 8px;
        height: 28px;
        
        .el-icon {
          font-size: 14px;
        }
      }
    }
    
    .search-section {
      margin-bottom: 24px;
      
      :deep(.el-input) {
        .el-input__wrapper {
          border-radius: 8px;
          background-color: #f5f5f5;
          border: none;
          box-shadow: none;
          
          &:hover, &.is-focus {
            background-color: #f0f0f0;
            box-shadow: none;
          }
        }
      }
    }
    
    .schedule-categories, .other-schedules {
      margin-bottom: 24px;
      
      .category-title {
        font-size: 14px;
        font-weight: 500;
        color: #666;
        margin-bottom: 12px;
      }
      
      .category-list {
        .category-item {
          display: flex;
          align-items: center;
          padding: 6px 8px;
          border-radius: 6px;
          cursor: pointer;
          margin-bottom: 4px;
          
          &:hover {
            background-color: #f5f5f5;
          }
          
          &.active {
            background-color: #e6f4ff;
            color: #1890ff;
          }
          
          .category-name {
            margin-left: 8px;
            font-size: 14px;
          }
        }
      }
      
      .add-calendar-btn {
        width: 100%;
        margin-top: 8px;
        color: #666;
        
        &:hover {
          color: #1890ff;
        }
      }
    }
  }
  
  // 主内容区域
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    
    .toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 24px;
      border-bottom: 1px solid #e8e8e8;
      background: white;
      
      .date-navigation {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .current-date {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          min-width: 200px;
        }
      }
      
      .view-controls {
        :deep(.el-button-group) {
          .el-button {
            padding: 6px 16px;
          }
        }
      }
    }
    
    .calendar-container {
      flex: 1;
      overflow: auto;
      
      // 周视图
      .week-view {
        display: flex;
        height: 100%;
        
        .time-axis {
          width: 80px;
          border-right: 1px solid #e8e8e8;
          background: #fafafa;
          
          .time-header {
            height: 60px;
            border-bottom: 1px solid #e8e8e8;
          }
          
          .time-slot {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            border-bottom: 1px solid #f0f0f0;
          }
        }
        
        .date-columns {
          flex: 1;
          
          .date-headers {
            display: flex;
            height: 60px;
            border-bottom: 1px solid #e8e8e8;
            background: #fafafa;
            
            .date-header {
              flex: 1;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              border-right: 1px solid #e8e8e8;
              
              &.today {
                background-color: #e6f4ff;
                color: #1890ff;
                
                .day-number {
                  background-color: #1890ff;
                  color: white;
                  border-radius: 50%;
                  width: 24px;
                  height: 24px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 12px;
                }
              }
              
              .day-name {
                font-size: 12px;
                color: #666;
                margin-bottom: 4px;
              }
              
              .day-number {
                font-size: 16px;
                font-weight: 500;
              }
            }
          }
          
          .schedule-grid {
            display: flex;
            
            .day-column {
              flex: 1;
              border-right: 1px solid #e8e8e8;
              
              .hour-slot {
                height: 60px;
                border-bottom: 1px solid #f0f0f0;
                position: relative;
                cursor: pointer;
                
                &:hover {
                  background-color: #f9f9f9;
                }
                
                &.current {
                  background-color: #fff7e6;
                }
                
                .schedule-event {
                  position: absolute;
                  left: 2px;
                  right: 2px;
                  padding: 4px 6px;
                  border-radius: 4px;
                  color: white;
                  font-size: 12px;
                  cursor: pointer;
                  border-left: 3px solid rgba(255, 255, 255, 0.8);
                  
                  .event-title {
                    font-weight: 500;
                    margin-bottom: 2px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }
                  
                  .event-time {
                    font-size: 11px;
                    opacity: 0.9;
                  }
                  
                  &:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                  }
                }
              }
            }
          }
        }
      }
      
      // 月视图
      .month-view {
        padding: 20px;
        
        :deep(.el-calendar) {
          border: none;
          
          .el-calendar__header {
            border-bottom: 1px solid #e8e8e8;
            padding: 16px 0;
          }
          
          .el-calendar__body {
            padding: 0;
          }
          
          .el-calendar-table {
            .el-calendar-day {
              padding: 8px;
              min-height: 120px;
              
              &:hover {
                background-color: #f9f9f9;
              }
            }
          }
        }
        
        .calendar-day {
          height: 100%;
          
          .day-number {
            font-weight: 500;
            margin-bottom: 4px;
          }
          
          .day-events {
            .mini-event {
              background-color: #1890ff;
              color: white;
              padding: 2px 6px;
              border-radius: 12px;
              font-size: 11px;
              margin-bottom: 2px;
              cursor: pointer;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              
              &.category-1 { background-color: #409EFF; }
              &.category-2 { background-color: #67C23A; }
              &.category-3 { background-color: #E6A23C; }
              &.category-4 { background-color: #F56C6C; }
              &.category-5 { background-color: #909399; }
            }
          }
        }
      }
      
      // 年视图
      .year-view {
        padding: 20px;
        
        .year-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 20px;
          
          .month-mini {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            
            &:hover {
              border-color: #1890ff;
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
            }
            
            .month-name {
              font-weight: 500;
              color: #333;
              text-align: center;
            }
          }
        }
      }
    }
  }
  
  // 右侧详情面板
  .detail-panel {
    width: 320px;
    background: white;
    border-left: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
    
    .panel-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      border-bottom: 1px solid #e8e8e8;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }
    
    .panel-content {
      flex: 1;
      padding: 20px;
      
      .event-info {
        margin-bottom: 24px;
        
        h4 {
          margin: 0 0 12px 0;
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }
        
        .event-time {
          color: #666;
          margin-bottom: 12px;
          font-size: 14px;
        }
        
        .event-description {
          color: #666;
          line-height: 1.5;
          font-size: 14px;
        }
      }
      
      .event-actions {
        display: flex;
        gap: 12px;
        
        .el-button {
          flex: 1;
        }
      }
    }
  }
  
  // 弹窗样式
  :deep(.el-dialog) {
    .el-dialog__body {
      padding: 20px 24px;
    }
  }
  
  // 表单样式
  :deep(.el-form) {
    .el-form-item {
      margin-bottom: 20px;
      
      .el-input, .el-select, .el-date-editor {
        width: 100%;
      }
      
      .el-textarea .el-textarea__inner {
        resize: none;
      }
    }
  }
  
  // 按钮组样式
  :deep(.el-button-group) {
    .el-button {
      &:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }
      
      &:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
    }
  }
  
  // 复选框样式
  :deep(.el-checkbox) {
    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #1890ff;
      border-color: #1890ff;
    }
  }
  
  // 头像样式
  :deep(.el-avatar) {
    font-weight: 500;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .my-schedule {
    .sidebar {
      width: 240px;
    }
    
    .detail-panel {
      width: 280px;
    }
  }
}

@media (max-width: 768px) {
  .my-schedule {
    .sidebar {
      position: absolute;
      z-index: 1000;
      height: 100%;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    }
    
    .detail-panel {
      display: none;
    }
  }
}
</style>
