import type { PageListResponse } from '@/pages/tasks/type.ts';

/**
 * 独立任务响应接口 - 基于 Swagger IndeTaskResponse
 * 用于表示独立任务列表中的单个任务项
 */
export interface IndependentTaskResponse {
  /** 租户ID */
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务父任务ID */
  parentIndeTaskId?: number;
  /** 独立任务名称 */
  indeTaskName?: string;
  /** 独立任务状态(1 未开始,2 进行中,3 已关闭) */
  indeTaskStatus?: number;
  /** 独立任务责任人 */
  indeTaskResponsible?: number;
  /** 独立任务责任部门 */
  indeTaskResponsibleDept?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType?: number;
  /** 专业/系统(1 产品策划,2 造型开发,3 平台开发,4 工程开发,5 整车工程/总布置,6 试验认证,7 生产准备,8 项目管理/系统工程,9 新兴技术开发) */
  major?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期天数 */
  overdueDays?: number;
  /** 独立任务验收标准 */
  indeTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 独立任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 独立任务说明 */
  indeTaskDesc?: string;
  /** 指派人 */
  assigneer?: number;
  /** 指派时间 */
  assigneeTime?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 责任人姓名 */
  responsibleName?: string;
  /** 独立任务责任人姓名 */
  indeTaskResponsibleName?: string;
  /** 参与人姓名列表，用逗号分隔 */
  participantNames?: string;
  /** 参与人ID列表 */
  participants?: IndeTaskParticipantChangeRequest[];
  /** 过程文档列表 */
  processDocs?: IndeTaskProcessDocAddRequest[];
  /** 任务层级，用于前端显示 */
  level?: number;
  /** 任务索引，用于排序 */
  index?: number;
  /** 层级序号，如 1, 1.1, 1.2, 2, 2.1 等 */
  taskNumber?: string;
  /** 是否有子任务 */
  hasChildren?: boolean;
  /** 子任务列表，嵌套的任务结构 */
  children?: IndependentTaskResponse[];
}

/**
 * 兼容性类型别名 - 保持向后兼容
 */
export type IndependentTaskItem = IndependentTaskResponse;

/**
 * 标签页项接口 - 基于 Swagger TabType 定义
 * 用于表示独立任务列表上方的标签页配置
 */
export interface TabItem {
  /** 标签页名称，用于标识 */
  name: string;
  /** 标签页显示标签 */
  label: string;
  /** 标签页值，对应 TabType */
  value: TabType;
}

/**
 * 标签页类型 - 基于 Swagger queryFlag 定义
 * 定义独立任务列表支持的标签页类型
 */
export type TabType =
  /** 全部任务 */
  | 1
  /** 我负责的任务 */
  | 2
  /** 我参与的任务 */
  | 3
  /** 我指派的任务 */
  | 4
  /** 我待办的任务 */
  | 5;

/**
 * 获取独立任务列表参数 - 基于Swagger IndeTaskQueryRequest
 * 用于查询独立任务列表的请求参数
 */
export interface GetIndependentTasksParams {
  /** 标签页类型，决定显示哪类任务 */
  tabType: TabType;
  /** 关键词，可选，用于模糊搜索任务名称 */
  keyword?: string;
  /** 页码，可选，从1开始的页码 */
  page?: number;
  /** 每页大小，可选，每页返回的记录数 */
  pageSize?: number;
  /** 租户ID，可选 */
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务父任务ID */
  parentIndeTaskId?: number;
  /** 独立任务名称 */
  indeTaskName?: string;
  /** 独立任务状态(1 未开始,2 进行中,3 已关闭) */
  indeTaskStatus?: number;
  /** 独立任务责任人 */
  indeTaskResponsible?: number;
  /** 独立任务责任部门 */
  indeTaskResponsibleDept?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType?: number;
  /** 专业/系统(1 产品策划,2 造型开发,3 平台开发,4 工程开发,5 整车工程/总布置,6 试验认证,7 生产准备,8 项目管理/系统工程,9 新兴技术开发) */
  major?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 独立任务验收标准 */
  indeTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 独立任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 独立任务说明 */
  indeTaskDesc?: string;
  /** 查询标识(1全部;2我负责的;3我参与的;4我指派的;5我代办的) */
  queryFlag?: number;
  /** 标签ID */
  taskTagId?: number;
  /** 排序条件 */
  sorts?: SortRequest[];
  /** 关注标识(1已关注;2未关注) */
  followFlag?: number;
  /** 责任人姓名 */
  responsibleName?: string;
  /** 导出列 */
  exportColumns?: string;
  /** 实际开始日期范围 - 开始 */
  beginActualStartDate?: string;
  /** 实际开始日期范围 - 结束 */
  endActualStartDate?: string;
  /** 计划开始日期范围 - 开始 */
  beginPlanStartDate?: string;
  /** 计划开始日期范围 - 结束 */
  endPlanStartDate?: string;
  /** 计划完成日期范围 - 开始 */
  beginPlanFinishDate?: string;
  /** 计划完成日期范围 - 结束 */
  endPlanFinishDate?: string;
}

/**
 * 排序请求
 */
export interface SortRequest {
  /** 排序字段 */
  field: string;
  /** 排序类型(asc, desc) */
  type: string;
}

/**
 * 获取独立任务列表响应
 * 分页查询独立任务列表的响应数据
 */
export type GetIndependentTasksResponse = PageListResponse<IndependentTaskItem>;

/**
 * 筛选参数接口
 * 用于独立任务列表的筛选条件
 */
export interface FilterParams {
  /** 标签页类型，决定显示哪类任务 */
  tabType: TabType;
  /** 关键词，可选，用于模糊搜索任务名称 */
  keyword?: string;
  /** 页码，可选，从1开始的页码 */
  page?: number;
  /** 每页大小，可选，每页返回的记录数 */
  pageSize?: number;
}

/**
 * 独立任务列表组件属性
 * 定义独立任务列表组件的输入属性
 */
export interface IndependentTaskListProps {
  /** 任务列表数据 */
  tasks: IndependentTaskItem[];
  /** 加载状态，是否正在加载数据 */
  loading: boolean;
}

/**
 * 独立任务标签页组件属性
 * 定义独立任务标签页组件的输入属性
 */
export interface IndependentTaskTabsProps {
  /** 当前激活的标签页类型 */
  activeTab: TabType;
}

/**
 * 独立任务标签页组件事件
 * 定义独立任务标签页组件的事件类型
 */
export interface IndependentTaskTabsEmits {
  /** 标签页切换事件 */
  (e: 'change', tab: TabType): void;
}

/**
 * 独立任务状态枚举 - 基于 Swagger indeTaskStatus 定义
 * 定义独立任务的所有可能状态
 */
export enum IndependentTaskStatus {
  /** 未开始 */
  NOT_STARTED = 1,
  /** 进行中 */
  IN_PROGRESS = 2,
  /** 已关闭 */
  CLOSED = 3,
}

/**
 * 独立任务状态标签映射
 */
export const IndependentTaskStatusLabels = {
  [IndependentTaskStatus.NOT_STARTED]: '未开始',
  [IndependentTaskStatus.IN_PROGRESS]: '进行中',
  [IndependentTaskStatus.CLOSED]: '已关闭',
} as const;

/**
 * 独立任务状态样式映射
 */
export const IndependentTaskStatusMap = {
  [IndependentTaskStatus.NOT_STARTED]: 'info',
  [IndependentTaskStatus.IN_PROGRESS]: 'warning',
  [IndependentTaskStatus.CLOSED]: 'success',
} as const;

// 新增独立任务表单数据接口
export interface AddIndependentTaskForm {
  taskName: string;
  taskDesc: string;
  responsible: string;
  taskType: string;
  participants: number[];
  startDate: string;
  endDate: string;
  approvalProcess: string;
  autoProgress: number;
}

/**
 * API 响应基础类型
 * 统一的 API 响应格式，所有接口都遵循此结构
 */
export interface ApiResult<T = any> {
  /** 响应状态码，200表示成功 */
  code: number;
  /** 响应消息，描述请求结果 */
  message: string;
  /** 响应时间戳，ISO 8601 格式 */
  timestamp: string;
  /** 响应数据，泛型类型，根据具体接口而定 */
  data: T;
}

/**
 * 兼容性 API 响应类型
 * 扩展 ApiResult，保持向后兼容
 */
export interface ApiResponse<T = any> extends ApiResult<T> {
  /** 请求是否成功，可选字段，用于兼容旧版本 */
  success?: boolean;
}

/**
 * 独立任务查询请求 - 基于 Swagger IndeTaskQueryRequest
 */
export interface IndeTaskQueryRequest {
  /** 租户ID，可选，用于多租户环境下的数据隔离 */
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务父任务ID */
  parentIndeTaskId?: number;
  /** 独立任务名称 */
  indeTaskName?: string;
  /** 独立任务状态(1 未开始,2 进行中,3 已关闭) */
  indeTaskStatus?: number;
  /** 独立任务责任人 */
  indeTaskResponsible?: number;
  /** 独立任务责任部门 */
  indeTaskResponsibleDept?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 独立任务验收标准 */
  indeTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 独立任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 独立任务说明 */
  indeTaskDesc?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 关注人用户ID */
  follower?: number;
  /** 关注标识(1已关注;2未关注) */
  followFlag?: number;
  /** 查询标识(1全部;2我负责的;3我参与的;4我指派的;5我代办的) */
  queryFlag?: number;
  /** 标签ID */
  taskTagId?: number;
  /** 排序条件 */
  sorts?: SortRequest[];
  /** 责任人姓名 */
  responsibleName?: string;
  /** 导出列 */
  exportColumns?: string;
}

/**
 * 独立任务新增请求 - 基于 Swagger IndeTaskAddRequest
 */
export interface IndeTaskAddRequest {
  /** 租户ID，可选，用于多租户环境下的数据隔离 */
  tenantId?: number;
  /** 独立任务父任务ID */
  parentIndeTaskId?: number;
  /** 独立任务名称 */
  indeTaskName: string;
  /** 独立任务责任人 */
  indeTaskResponsible: number;
  /** 独立任务责任部门 */
  indeTaskResponsibleDept?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType?: number;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 独立任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 独立任务说明 */
  indeTaskDesc?: string;
  /** 参与人列表 */
  participants?: IndeTaskParticipantChangeRequest[];
  /** 过程文档列表 */
  processDocs?: IndeTaskProcessDocAddRequest[];
}

export type AddIndependentTaskData = IndeTaskAddRequest;

/**
 * 独立任务参与人变更请求
 */
export interface IndeTaskParticipantChangeRequest {
  /** 参与人ID */
  userId: number;
  /** 租户ID，可选 */
  tenantId?: number;
}

/**
 * 独立任务过程文档新增请求
 */
export interface IndeTaskProcessDocAddRequest {
  /** 租户ID，可选 */
  tenantId?: number;
  /** 独立任务过程文档ID */
  indeTaskProcessDocId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskProcessDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 独立任务编辑请求 - 基于 Swagger IndeTaskEditRequest
 */
export interface IndeTaskEditRequest {
  /** 租户ID，可选，用于多租户环境下的数据隔离 */
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 独立任务父任务ID */
  parentIndeTaskId?: number;
  /** 独立任务名称 */
  indeTaskName?: string;
  /** 独立任务状态(1 未开始,2 进行中,3 已关闭) */
  indeTaskStatus?: number;
  /** 独立任务责任人 */
  indeTaskResponsible?: number;
  /** 独立任务责任部门 */
  indeTaskResponsibleDept?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 独立任务验收标准 */
  indeTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 独立任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 独立任务说明 */
  indeTaskDesc?: string;
  /** 备注 */
  remark?: string;
  /** 任务标签列表 */
  tags?: ConfigIndeTaskTagEditRequest[];
  /** 参与人列表 */
  participants?: IndeTaskParticipantChangeRequest[];
  /** 过程文档列表 */
  processDocs?: IndeTaskProcessDocEditRequest[];
}

/**
 * 配置独立任务标签编辑请求
 */
export interface ConfigIndeTaskTagEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 任务标签ID */
  taskTagId: number;
  /** 父标签ID或者所属标签组ID */
  parentTaskTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag: number;
  /** 任务标签标题 */
  taskTagName: string;
  /** 任务标签描述 */
  taskTagDesc?: string;
  /** 任务标签颜色 */
  taskTagColor: string;
  /** 任务标签状态 (1 启用,0 禁用) */
  taskTagStatus: number;
  /** 备注 */
  remark?: string;
}

/**
 * 独立任务过程文档编辑请求
 */
export interface IndeTaskProcessDocEditRequest {
  /** 租户ID，可选 */
  tenantId?: number;
  /** 独立任务过程文档ID */
  indeTaskProcessDocId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentIndeTaskProcessDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 独立任务删除请求 - 基于 Swagger IndeTaskDeleteRequest
 */
export interface IndeTaskDeleteRequest {
  /** 租户ID，可选，用于多租户环境下的数据隔离 */
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId: number;
}

/**
 * 独立任务接受请求 - 基于 Swagger IndeTaskAcceptRequest
 */
export interface IndeTaskAcceptRequest {
  /** 租户ID，可选，用于多租户环境下的数据隔离 */
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 接受意见 */
  acceptOpinion?: string;
  /** 过程文档列表 */
  processDocs?: IndeTaskProcessDocAddRequest[];
}

/**
 * 独立任务驳回请求 - 基于 Swagger IndeTaskRejectRequest
 */
export interface IndeTaskRejectRequest {
  /** 租户ID，可选，用于多租户环境下的数据隔离 */
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 拒绝意见 */
  rejectOpinion?: string;
  /** 过程文档列表 */
  processDocs?: IndeTaskProcessDocAddRequest[];
}

/**
 * 独立任务变更请求 - 基于 Swagger IndeTaskChangeRequest
 */
export interface IndeTaskChangeRequest {
  /** 租户ID，可选，用于多租户环境下的数据隔离 */
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId: number;
  /** 独立任务名称 */
  indeTaskName: string;
  /** 独立任务责任人 */
  indeTaskResponsible: number;
  /** 独立任务责任部门 */
  indeTaskResponsibleDept?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 独立任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 参与人列表 */
  participants?: IndeTaskParticipantChangeRequest[];
  /** 变更原因 */
  changeReason?: string;
}
