import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import type {
  IndependentTaskItem,
  TabType,
  GetIndependentTasksParams,
} from './types';
import * as independentTasksService from './IndependentTasksService';

export const useIndependentTasksStore = defineStore(
  'independentTasksStore',
  () => {
    // 1. 响应式状态定义
    const tasks = ref<IndependentTaskItem[]>([]);
    const tasksLoading = ref(false);
    const searchKeyword = ref('');
    const addTaskVisible = ref(false);

    // 页签相关状态 - 基于 Swagger TabType 数字定义
    const activeTab = ref<TabType>(1);
    const tabs = ref<{ name: string; label: string; value: TabType }[]>([
      { name: 'all', label: '全部', value: 1 },
      { name: 'responsible', label: '我负责的', value: 2 },
      { name: 'participant', label: '我参与的', value: 3 },
      { name: 'assigned', label: '我指派的', value: 4 },
      { name: 'todo', label: '我待办的', value: 5 },
    ]);

    // 分页相关状态
    const page = ref(1);
    const pageSize = ref(20);
    const hasMore = ref(true);

    // 表格列筛选条件状态
    const tableFilterConditions = ref<Record<string, any>>({});

    // 列配置
    const columnList = ref([
      {
        field: 'indeTaskId',
        label: '任务ID',
        width: 120,
        align: 'left',
        show: false,
        filter: {
          type: 'input' as const,
          showOrder: false,
          placeholder: '请输入任务ID',
        },
        filterValue: '',
      },
      {
        field: 'indeTaskName',
        label: '独立任务名称',
        width: 144,
        align: 'left',
        show: true,
        filter: { type: 'input' as const, showOrder: false, showSearch: true },
        filterValue: '',
      },
      {
        field: 'indeTaskStatus',
        label: '状态',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox' as const,
          data: [
            { value: 1, label: '未开始' },
            { value: 2, label: '进行中' },
            { value: 3, label: '已关闭' },
          ],
        },
        filterValue: [],
      },
      {
        field: 'indeTaskResponsibleDept',
        label: '责任部门',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          showOrder: false,
          placeholder: '请输入责任部门',
        },
        filterValue: '',
      },
      {
        field: 'responsibleName',
        label: '责任人',
        width: 120,
        align: 'left',
        show: true,
        filter: { type: 'input' as const },
        filterValue: '',
      },
      {
        field: 'indeTaskType',
        label: '任务类型',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox' as const,
          data: [
            { value: 1, label: '里程碑作业' },
            { value: 2, label: '任务作业' },
            { value: 3, label: 'wbs作业' },
            { value: 4, label: '配合作业' },
          ],
        },
        filterValue: [],
      },
      {
        field: 'major',
        label: '专业/系统',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox' as const,
          data: [
            { value: 1, label: '产品策划' },
            { value: 2, label: '造型开发' },
            { value: 3, label: '平台开发' },
            { value: 4, label: '工程开发' },
            { value: 5, label: '整车工程/总布置' },
            { value: 6, label: '试验认证' },
            { value: 7, label: '生产准备' },
            { value: 8, label: '项目管理/系统工程' },
            { value: 9, label: '新兴技术开发' },
          ],
        },
        filterValue: [],
      },
      {
        field: 'planStartDate',
        label: '计划开始时间',
        width: 176,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          inputType: 'dateRange' as const,
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'actualStartDate',
        label: '实际开始时间',
        width: 176,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          inputType: 'dateRange' as const,
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'planFinishDate',
        label: '计划完成时间',
        width: 176,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          inputType: 'dateRange' as const,
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'overdueDays',
        label: '超期',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox' as const,
          data: [
            { value: 1, label: '是' },
            { value: 0, label: '否' },
          ],
        },
        filterValue: [],
      },
      {
        field: 'actualProgress',
        label: '进度',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          showOrder: true,
          placeholder: '请输入进度值',
        },
        filterValue: '',
        filterOrder: '',
      },
    ]);

    // 2. 计算属性
    const filterParams = computed<GetIndependentTasksParams>(() => {
      const params: GetIndependentTasksParams = {
        tabType: activeTab.value,
        keyword: searchKeyword.value,
        page: page.value,
        pageSize: pageSize.value,
      };

      // 合并表格列筛选条件
      Object.assign(params, tableFilterConditions.value);

      return params;
    });

    // 筛选任务（前端展示用，主要筛选逻辑在API层面）
    const filteredTasks = computed(() => {
      let filtered = tasks.value;

      // 根据关键字筛选（前端补充筛选）
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        filtered = filtered.filter(
          (task) =>
            (task.indeTaskName &&
              task.indeTaskName.toLowerCase().includes(keyword)) ||
            (task.responsibleName &&
              task.responsibleName.toLowerCase().includes(keyword)) ||
            (task.indeTaskType &&
              task.indeTaskType.toString().includes(keyword))
        );
      }

      return filtered;
    });

    // 甘特图数据转换 - 支持独立任务子任务展示
    const ganttData = computed(() => {
      // 递归函数：将嵌套的独立任务结构展平为甘特图需要的平级数据
      const flattenIndependentTasks = (tasks: IndependentTaskItem[]): any[] => {
        const result: any[] = [];

        const processTask = (task: IndependentTaskItem): any => {
          // 获取开始和结束日期，如果不存在则使用当前日期
          const currentDate = new Date().toISOString().split('T')[0];
          const startDate =
            task.actualStartDate || task.planStartDate || currentDate;
          const endDate = task.planFinishDate || currentDate;

          // 计算进度，使用实际进度或默认为0
          const progress = task.actualProgress || 0;

          return {
            id: task.indeTaskId,
            text: task.indeTaskName,
            start_date: startDate,
            end_date: endDate,
            progress: progress / 100, // 甘特图内部进度需要转换为0-1的小数
            parent: task.parentIndeTaskId || 0,
            type: task.hasChildren ? 'project' : 'task', // 有子任务的显示为项目类型
            open: false, // 默认不展开子任务，通过展开按钮控制
            // 甘特图显示字段
            status: task.indeTaskStatus,
            responsible_user: task.responsibleName,
            task_type: task.indeTaskType,
            actual_start_date: task.actualStartDate,
            // 进度列显示为百分比格式
            progress_display: `${progress}%`,
          };
        };

        // 递归处理每个任务及其子任务
        for (const task of tasks) {
          // 添加当前任务
          result.push(processTask(task));

          // 递归处理子任务
          if (task.children && task.children.length > 0) {
            const childTasks = flattenIndependentTasks(task.children);
            result.push(...childTasks);
          }
        }

        return result;
      };

      // DHTMLX Gantt 要求的数据格式：{ data: [], links: [] }
      const taskData = flattenIndependentTasks(filteredTasks.value);

      console.log('独立任务甘特图数据转换:', {
        原始任务数量: filteredTasks.value.length,
        展平后任务数量: taskData.length,
        任务数据示例: taskData.slice(0, 3),
      });

      return {
        data: taskData,
        links: [], // 独立任务暂时没有任务依赖关系
      };
    });

    // 3. 异步方法
    // 初始化方法
    async function init() {
      try {
        // 加载任务数据
        await loadTasks();
      } catch (error) {
        console.error('独立任务初始化失败:', error);
      }
    }

    async function loadTasks() {
      tasksLoading.value = true;

      const params = filterParams.value;
      const result = await independentTasksService.getIndependentTasks(params);

      // Service 层已经处理了错误情况，这里直接使用返回的数据
      if (result) {
        // 如果是第一页，替换数据；否则追加数据
        if (page.value === 1) {
          setTasks(result.list);
        } else {
          setTasks([...tasks.value, ...result.list]);
        }

        // 更新分页信息
        hasMore.value = result.hasMore;
      }

      tasksLoading.value = false;
    }

    async function loadMoreTasks() {
      if (tasksLoading.value || !hasMore.value) {
        return;
      }

      page.value += 1;
      await loadTasks();
    }

    async function addTask(task: IndependentTaskItem) {
      const response = await independentTasksService.addIndependentTask(task);

      // 检查接口返回：判断新增操作是否成功
      if (response !== undefined && response !== null) {
        ElMessage.success('新增独立任务成功');
      }
    }

    async function deleteTask(taskIds: { id: number; tenantId?: number }[]) {
      // 转换为正确的删除请求格式
      const deleteRequests = taskIds.map((item) => ({
        indeTaskId: Number(item.id),
      }));

      const response = await independentTasksService.deleteIndependentTask(
        deleteRequests[0] // 假设一次只删除一个任务
      );

      // 检查接口返回：判断删除操作是否成功
      if (response !== undefined && response !== null) {
        // 从列表中移除删除的任务
        const idsToDelete = taskIds.map((item) => item.id);
        tasks.value = tasks.value.filter(
          (task) => !task.indeTaskId || !idsToDelete.includes(task.indeTaskId)
        );
        ElMessage.success('删除独立任务成功');
      } else {
        throw new Error('删除独立任务失败');
      }
    }

    // 4. 同步方法
    function resetData() {
      tasks.value = [];
      page.value = 1;
      hasMore.value = true;
      searchKeyword.value = '';
    }

    function setTasks(newTasks: IndependentTaskItem[], append = false) {
      if (append) {
        tasks.value = [...tasks.value, ...newTasks];
      } else {
        tasks.value = newTasks;
      }
    }

    function updateTask(taskId: number, updates: Partial<IndependentTaskItem>) {
      const index = tasks.value.findIndex((task) => task.indeTaskId === taskId);
      if (index !== -1) {
        tasks.value[index] = { ...tasks.value[index], ...updates };
      }
    }

    function removeTask(taskId: number) {
      const index = tasks.value.findIndex((task) => task.indeTaskId === taskId);
      if (index !== -1) {
        tasks.value.splice(index, 1);
      }
    }

    function setActiveTab(tab: TabType) {
      activeTab.value = tab;
      // 切换页签时重置分页
      page.value = 1;
      hasMore.value = true;
    }

    function setKeyword(keyword: string) {
      searchKeyword.value = keyword;
    }

    function resetSearch() {
      page.value = 1;
      hasMore.value = true;
      loadTasks();
    }

    // 表格筛选相关方法
    function setTableFilterConditions(conditions: Record<string, any>) {
      tableFilterConditions.value = { ...conditions };
    }

    function clearTableFilterConditions() {
      tableFilterConditions.value = {};
    }

    async function exportIndependentTask() {
      // 传递当前的筛选参数到导出接口
      await independentTasksService.exportIndependentTask(filterParams.value);
      // 导出函数已经处理了文件下载，无需检查返回值
    }

    async function importIndependentTask(file: FormData) {
      const response = await independentTasksService.importIndependentTask(
        file
      );
      // 检查接口返回：导入操作是否成功
      if (response === undefined || response === null) {
        throw new Error('导入独立任务失败');
      }
      return response;
    }

    async function exportIndependentTaskTemplate() {
      await independentTasksService.exportIndependentTaskTemplate();
      // 导出函数已经处理了文件下载，无需检查返回值
    }

    return {
      // 状态
      tasks,
      tasksLoading,
      page,
      pageSize,
      hasMore,
      searchKeyword,
      activeTab,
      tabs,
      columnList,
      addTaskVisible,

      // 计算属性
      filterParams,
      filteredTasks,
      ganttData,

      // 方法
      init,
      loadTasks,
      loadMoreTasks,
      addTask,
      deleteTask,
      resetData,
      setTasks,
      updateTask,
      removeTask,
      setActiveTab,
      setKeyword,
      resetSearch,
      setTableFilterConditions,
      clearTableFilterConditions,
      exportIndependentTask,
      importIndependentTask,
      exportIndependentTaskTemplate,
    };
  },
  {
    // 5. 持久化配置 - 只持久化必要数据
    persist: {
      pick: ['columnList'],
      key: 'independentTasks_columnList',
    },
  }
);
