<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  useTemplateRef,
  nextTick,
} from 'vue';
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { ElMessageBox, ElMessage } from 'element-plus';
import { tableConfig } from '@/app-config.ts';
import { useIndependentTasksStore } from './IndependentTasksStore';
import type { IndependentTaskItem, TabType } from './types';
import { MoreFilled, Loading } from '@element-plus/icons-vue';
import BtColumn from '@/components/non_biz/bt_column/BtColumn.vue';
import BtSearch from '@/components/non_biz/bt_search/BtSearch.vue';
import BtThFilter from '@/components/non_biz/bt_th_filter/BtThFilter.vue';
import TaskTabsWithToolbar from '@/pages/tasks/components/task_tabs_with_toolbar/TaskTabsWithToolbar.vue';
import AcceptTask from './components/accept_task/AcceptTask.vue';
import RejectTask from './components/reject_task/RejectTask.vue';
import AddTask from './components/add_task/AddTask.vue';
import type { UploadFile } from 'element-plus';
import { useLayoutStore } from '@/components/non_biz/layout/LayoutStore.ts';

const layoutStore = useLayoutStore();
layoutStore.setAddAction(handleAdd);

// Store使用
const store = useIndependentTasksStore();
const { tasksLoading, columnList } = storeToRefs(store);

// 路由
const router = useRouter();

// 初始化
function init() {
  store.tasksLoading = true;
  store.loadTasks();
}

function handleTabChange(tab: TabType) {
  store.setActiveTab(tab);
  store.loadTasks();
}

// 表格容器引用
const tableContainerRef = useTemplateRef<any>('tableContainerRef');

// 防抖函数
function debounce(func: Function, wait: number) {
  let timeout: number;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 滚动事件处理
const handleScroll = debounce(() => {
  const table = tableContainerRef.value;
  if (!table || store.tasksLoading || !store.hasMore) {
    return;
  }

  // 获取表格的body wrapper元素
  const bodyWrapper = table.$el?.querySelector('.el-table__body-wrapper');
  if (!bodyWrapper) {
    return;
  }

  const { scrollTop, scrollHeight, clientHeight } = bodyWrapper;
  const threshold = 100; // 距离底部100px时开始加载

  // 当滚动到接近底部时，加载更多数据
  if (scrollTop + clientHeight >= scrollHeight - threshold) {
    store.loadMoreTasks();
  }
}, 200);

// 监听滚动事件
onMounted(() => {
  nextTick(() => {
    const table = tableContainerRef.value;
    if (table && table.$el) {
      const bodyWrapper = table.$el.querySelector('.el-table__body-wrapper');
      if (bodyWrapper) {
        bodyWrapper.addEventListener('scroll', handleScroll);
      }
    }
  });
});

onUnmounted(() => {
  const table = tableContainerRef.value;
  if (table && table.$el) {
    const bodyWrapper = table.$el.querySelector('.el-table__body-wrapper');
    if (bodyWrapper) {
      bodyWrapper.removeEventListener('scroll', handleScroll);
    }
  }
});

// 生成层级序号的函数
function generateTaskNumbers(
  tasks: IndependentTaskItem[],
  parentNumber: string = ''
): IndependentTaskItem[] {
  return tasks.map((task, index) => {
    const currentNumber = parentNumber
      ? `${parentNumber}.${index + 1}`
      : `${index + 1}`;
    const updatedTask = {
      ...task,
      taskNumber: currentNumber,
    };

    if (task.children && task.children.length > 0) {
      updatedTask.children = generateTaskNumbers(task.children, currentNumber);
    }

    return updatedTask;
  });
}

// 表格列配置
const columnRef = useTemplateRef<any>('columnRef');

// 显示的列
const showColumn = computed(() => {
  return columnList.value.filter((v) => v.show);
});

// 表格筛选相关
function changeFilter(val: any, field: string) {
  console.log('筛选变更', val, field);

  // 更新对应列的筛选值
  const column = columnList.value.find((col) => col.field === field);
  if (column) {
    column.filterValue = val;
  }

  // 收集所有筛选条件并更新Store
  updateFilterConditions();

  // 重新加载任务列表
  store.loadTasks();
}

// 收集表格列筛选条件并更新Store
function updateFilterConditions() {
  const filterConditions: any = {};

  columnList.value.forEach((column) => {
    const value = column.filterValue;

    // 检查值是否有效（非空字符串、非空数组）
    const hasValue = Array.isArray(value) ? value.length > 0 : value;

    if (hasValue) {
      // 根据字段名映射到API参数
      switch (column.field) {
        case 'indeTaskName':
          filterConditions.indeTaskName = value;
          break;
        case 'indeTaskStatus':
          // 多选状态，传递数组
          filterConditions.indeTaskStatus = Array.isArray(value)
            ? value
            : [Number(value)];
          break;
        case 'indeTaskId':
          filterConditions.indeTaskId = value;
          break;
        case 'indeTaskResponsibleDept':
          filterConditions.indeTaskResponsibleDept = value;
          break;
        case 'actualProgress':
          filterConditions.actualProgress = Number(value);
          break;
        case 'responsibleName':
          filterConditions.responsibleName = value;
          break;
        case 'indeTaskType':
          // 多选任务类型，传递数组
          filterConditions.indeTaskType = Array.isArray(value)
            ? value
            : [Number(value)];
          break;
        case 'major':
          // 多选专业，传递数组
          filterConditions.major = Array.isArray(value)
            ? value
            : [Number(value)];
          break;
        case 'overdueDays':
          // 多选超期状态，传递数组
          filterConditions.overdueDays = Array.isArray(value)
            ? value
            : [Number(value)];
          break;
        case 'actualStartDate':
          // 日期范围处理
          if (Array.isArray(value) && value.length === 2) {
            filterConditions.beginActualStartDate = value[0];
            filterConditions.endActualStartDate = value[1];
          }
          break;
        case 'planStartDate':
          // 日期范围处理
          if (Array.isArray(value) && value.length === 2) {
            filterConditions.beginPlanStartDate = value[0];
            filterConditions.endPlanStartDate = value[1];
          }
          break;
        case 'planFinishDate':
          // 日期范围处理
          if (Array.isArray(value) && value.length === 2) {
            filterConditions.beginPlanFinishDate = value[0];
            filterConditions.endPlanFinishDate = value[1];
          }
          break;
      }
    }
  });

  // 收集排序参数
  const sorts: any[] = [];
  columnList.value.forEach((column) => {
    if (column.filterOrder) {
      sorts.push({
        field: column.field,
        direction: column.filterOrder,
      });
    }
  });

  // 添加排序参数到筛选条件
  if (sorts.length > 0) {
    filterConditions.sorts = sorts;
  }

  // 更新Store中的筛选条件
  store.setTableFilterConditions(filterConditions);
}

// 搜索相关
const keyword = ref('');

function toSearch() {
  // 更新 Store 中的搜索关键词并重新加载数据
  store.setKeyword(keyword.value);
  store.resetSearch();
}

// 获取状态类型 - 基于 Swagger IndependentTaskStatus 枚举
function getStatusType(status: number | undefined) {
  const statusMap: Record<number, string> = {
    1: 'info', // 未开始
    2: 'warning', // 进行中
    3: 'success', // 已关闭
  };
  return statusMap[status || 1] || 'info';
}

// 获取状态文本 - 基于 Swagger IndependentTaskStatus 枚举
function getStatusText(status: number | undefined) {
  const statusTextMap: Record<number, string> = {
    1: '未开始',
    2: '进行中',
    3: '已关闭',
  };
  return statusTextMap[status || 1] || '未开始';
}

// 获取任务类型文本 - 基于 Swagger IndependentTaskType 枚举
function getTaskTypeText(taskType: number | undefined) {
  const taskTypeTextMap: Record<number, string> = {
    1: '里程碑作业',
    2: '任务作业',
    3: 'wbs作业',
    4: '配合作业',
  };
  return taskTypeTextMap[taskType || 1] || '里程碑作业';
}

// 格式化进度
function formatProgress(val: number): string {
  return val + '%';
}

// 获取专业文本
function getMajorText(major: number | undefined): string {
  if (!major) return '';
  const majorMap = {
    1: '产品策划',
    2: '造型开发',
    3: '平台开发',
    4: '工程开发',
    5: '整车工程/总布置',
    6: '试验认证',
    7: '生产准备',
    8: '项目管理/系统工程',
    9: '新兴技术开发',
  };
  return majorMap[major as keyof typeof majorMap] || '';
}

// 格式化日期显示
function formatDate(dateStr: string | undefined): string {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  } catch {
    return dateStr;
  }
}

// 查看任务详情
function handleView(row: IndependentTaskItem) {
  // 跳转到独立任务详情页
  router.push({
    name: 'IndependentTaskDetail',
    params: { id: row.indeTaskId },
  });
}

// 接受任务弹窗相关
const acceptTaskVisible = ref(false);
const currentAcceptTask = ref<IndependentTaskItem | null>(null);
const acceptTaskRef = useTemplateRef<any>('acceptTaskRef');

function handleAccept(row: IndependentTaskItem) {
  console.log('🚀 ~ handleAccept ~ row:', row.indeTaskId);
  acceptTaskRef.value?.open(row.indeTaskId);
}

function onAcceptSuccess() {
  console.log('接受独立任务成功:', currentAcceptTask.value);
  // 重新加载任务列表
  store.loadTasks();
}

// 驳回任务弹窗相关
const rejectTaskVisible = ref(false);
const currentRejectTask = ref<IndependentTaskItem | null>(null);
const rejectTaskRef = useTemplateRef<any>('rejectTaskRef');

function handleReject(row: IndependentTaskItem) {
  rejectTaskRef.value?.open(row.indeTaskId);
}

function onRejectSuccess() {
  console.log('驳回独立任务成功:', currentRejectTask.value);
  // 重新加载任务列表
  store.loadTasks();
}

// 新增任务弹窗相关
const addTaskVisible = ref(false);
const addTaskRef = useTemplateRef<any>('addTaskRef');

function handleAdd() {
  addTaskRef.value?.open();
}

function onAddSuccess() {
  console.log('新增独立任务成功');
  // 重新加载任务列表以获取最新数据
  store.loadTasks();
}

// 获取子任务ID列表（递归）
function getChildTaskIds(children: IndependentTaskItem[]): { id: number }[] {
  const result: { id: number }[] = [];
  for (const task of children) {
    if (task.indeTaskId) {
      result.push({ id: task.indeTaskId });
    }
    if (task.children && task.children.length > 0) {
      result.push(...getChildTaskIds(task.children));
    }
  }
  return result;
}

// 删除任务
function handleDelete(row: IndependentTaskItem) {
  let message = '确定要删除该独立任务吗？';
  const deleteIds: { id: number }[] = row.indeTaskId
    ? [{ id: row.indeTaskId }]
    : [];

  // 检查是否有子任务
  if (row.children && row.children.length > 0) {
    const childIds = getChildTaskIds(row.children);
    message = `确定要删除该独立任务及其下 ${childIds.length} 个子任务吗？`;
    deleteIds.push(...childIds);
  }

  ElMessageBox({
    title: '提示',
    message: message,
    type: 'warning',
    showCancelButton: true,
    confirmButtonText: '删除',
    cancelButtonText: '取消',
  }).then(() => {
    // 调用删除接口
    store.deleteTask(deleteIds);
    console.log('删除独立任务:', deleteIds);
  });
}

// 处理下拉菜单命令
function handleCommand(command: string, row: IndependentTaskItem) {
  switch (command) {
    case 'view':
      handleView(row);
      break;
    case 'accept':
      handleAccept(row);
      break;
    case 'reject':
      handleReject(row);
      break;
    case 'delete':
      handleDelete(row);
      break;
    default:
      console.log('未知命令:', command);
  }
}

// 任务数据 - 添加序号
const tasks = computed(() => {
  return generateTaskNumbers(store.filteredTasks);
});

// 其他计算属性
const hasMore = computed(() => store.hasMore);

// 生命周期
onMounted(() => {
  init();
});

// Excel导出功能
function exportExcel() {
  store
    .exportIndependentTask()
    .then(() => {
      ElMessage.success('导出成功');
    })
    .catch((error) => {
      console.error('导出失败:', error);
      ElMessage.error('导出失败');
    });
}

// Excel导入相关
const uploadRef = useTemplateRef<any>('uploadRef');
function importExcel(file: UploadFile) {
  if (!file) return;
  const formData = new FormData();
  formData.append('file', file.raw as File);

  store
    .importIndependentTask(formData)
    .then(() => {
      ElMessage.success('导入成功');
      store.loadTasks();
    })
    .catch((error) => {
      console.error('导入失败:', error);
      ElMessage.error('导入失败');
    });
  return false; // 阻止自动上传
}

// 下载导入模板
function downloadTemplate() {
  store
    .exportIndependentTaskTemplate()
    .then(() => {
      ElMessage.success('模板下载成功');
    })
    .catch((error) => {
      console.error('模板下载失败:', error);
      ElMessage.error('模板下载失败');
    });
}
</script>
<template>
  <div class="independent-task-list">
    <div class="container">
      <div class="header">
        <TaskTabsWithToolbar
          :tabs="store.tabs"
          :activeTab="store.activeTab"
          @change="handleTabChange"
          class="task-tabs"
        >
          <template #toolbar>
            <div class="right-opt flex flex-center">
              <el-button class="btn" @click="handleAdd">
                新增独立任务
              </el-button>
              <el-dropdown placement="bottom-end" class="item">
                <el-button class="btn">Excel</el-button>
                <template #dropdown>
                  <el-dropdown-menu style="width: 215px">
                    <el-dropdown-item>
                      <el-upload
                        ref="uploadRef"
                        action="#"
                        :auto-upload="false"
                        :show-file-list="false"
                        :on-change="importExcel"
                        :limit="1"
                        accept=".xlsx,.xls"
                      >
                        导入列表（Excel）
                      </el-upload>
                    </el-dropdown-item>
                    <el-dropdown-item @click="exportExcel"
                      >导出列表（Excel）</el-dropdown-item
                    >
                    <el-dropdown-item @click="downloadTemplate"
                      >下载模板（Excel）</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <BtSearch
                class="item"
                v-model="keyword"
                placeholder="请输入任务名称搜索"
                @search="toSearch"
                @clear="toSearch"
              ></BtSearch>
              <BtColumn ref="columnRef" v-model="columnList"></BtColumn>
            </div>
          </template>
        </TaskTabsWithToolbar>
      </div>
      <!-- 列表视图 -->

      <div class="cont">
        <el-table
          ref="tableContainerRef"
          v-loading="tasksLoading && tasks.length === 0"
          class="table"
          :data="tasks"
          row-key="indeTaskId"
          default-expand-all
          @row-dblclick="handleView"
        >
          <el-table-column type="selection" width="33" fixed="left" />

          <!-- 序号列 -->
          <el-table-column label="#" width="32" align="left" type="">
            <template #default="{ row }">
              <span class="task-number">{{ row.taskNumber }}</span>
            </template>
          </el-table-column>

          <!-- 各列单独处理，确保筛选图标与列对应 -->
          <template v-for="column in showColumn" :key="column.field">
            <!-- 独立任务名称列 -->
            <el-table-column
              v-if="column.field === 'indeTaskName'"
              :property="column.field"
              :label="column.label"
              :width="column.width"
              :indent="20"
              show-overflow-tooltip
            >
              <template #header>
                <BtThFilter
                  v-if="column.filter"
                  :title="column.label"
                  :type="column.filter.type"
                  :inputType="column.filter.inputType"
                  :showOrder="column.filter.showOrder"
                  :placeholder="column.filter.placeholder"
                  :options="column.filter.data"
                  v-model="column.filterValue"
                  v-model:order="column.filterOrder"
                  @change="changeFilter($event, column.field)"
                ></BtThFilter>
                <span v-else>{{ column.label }}</span>
              </template>
              <template #default="{ row }">
                <span class="task-name" @click="handleView(row)">{{
                  row.indeTaskName
                }}</span>
              </template>
            </el-table-column>

            <!-- 状态列 -->
            <el-table-column
              v-else-if="column.field === 'indeTaskStatus'"
              :property="column.field"
              :label="column.label"
              :width="column.width"
            >
              <template #header>
                <BtThFilter
                  v-if="column.filter"
                  :title="column.label"
                  :type="column.filter.type"
                  :inputType="column.filter.inputType"
                  :showOrder="column.filter.showOrder"
                  :placeholder="column.filter.placeholder"
                  :options="column.filter.data"
                  v-model="column.filterValue"
                  v-model:order="column.filterOrder"
                  @change="changeFilter($event, column.field)"
                ></BtThFilter>
                <span v-else>{{ column.label }}</span>
              </template>
              <template #default="{ row }">
                {{ getStatusText(row.indeTaskStatus) }}
              </template>
            </el-table-column>

            <!-- 进度列 -->
            <el-table-column
              v-else-if="column.field === 'actualProgress'"
              :property="column.field"
              :label="column.label"
              :width="column.width"
            >
              <template #header>
                <BtThFilter
                  v-if="column.filter"
                  :title="column.label"
                  :type="column.filter.type"
                  :inputType="column.filter.inputType"
                  :showOrder="column.filter.showOrder"
                  :placeholder="column.filter.placeholder"
                  :options="column.filter.data"
                  v-model="column.filterValue"
                  v-model:order="column.filterOrder"
                  @change="changeFilter($event, column.field)"
                ></BtThFilter>
                <span v-else>{{ column.label }}</span>
              </template>
              <template #default="{ row }">
                <el-progress
                  class="progress"
                  color="#52C41A"
                  :stroke-width="12"
                  :percentage="row.actualProgress || 0"
                  :format="formatProgress"
                />
              </template>
            </el-table-column>

            <!-- 任务类型列 -->
            <el-table-column
              v-else-if="column.field === 'indeTaskType'"
              :property="column.field"
              :label="column.label"
              :width="column.width"
            >
              <template #header>
                <BtThFilter
                  v-if="column.filter"
                  :title="column.label"
                  :type="column.filter.type"
                  :inputType="column.filter.inputType"
                  :showOrder="column.filter.showOrder"
                  :placeholder="column.filter.placeholder"
                  :options="column.filter.data"
                  v-model="column.filterValue"
                  v-model:order="column.filterOrder"
                  @change="changeFilter($event, column.field)"
                ></BtThFilter>
                <span v-else>{{ column.label }}</span>
              </template>
              <template #default="{ row }">
                {{ getTaskTypeText(row.indeTaskType) }}
              </template>
            </el-table-column>

            <!-- 专业列 -->
            <el-table-column
              v-else-if="column.field === 'major'"
              :property="column.field"
              :label="column.label"
              :width="column.width"
            >
              <template #header>
                <BtThFilter
                  v-if="column.filter"
                  :title="column.label"
                  :type="column.filter.type"
                  :inputType="column.filter.inputType"
                  :showOrder="column.filter.showOrder"
                  :placeholder="column.filter.placeholder"
                  :options="column.filter.data"
                  v-model="column.filterValue"
                  v-model:order="column.filterOrder"
                  @change="changeFilter($event, column.field)"
                ></BtThFilter>
                <span v-else>{{ column.label }}</span>
              </template>
              <template #default="{ row }">
                {{ getMajorText(row.major) }}
              </template>
            </el-table-column>

            <!-- 实际开始时间列 -->
            <el-table-column
              v-else-if="column.field === 'actualStartDate'"
              :property="column.field"
              :label="column.label"
              :width="column.width"
            >
              <template #header>
                <BtThFilter
                  v-if="column.filter"
                  :title="column.label"
                  :type="column.filter.type"
                  :inputType="column.filter.inputType"
                  :showOrder="column.filter.showOrder"
                  :placeholder="column.filter.placeholder"
                  :options="column.filter.data"
                  v-model="column.filterValue"
                  v-model:order="column.filterOrder"
                  @change="changeFilter($event, column.field)"
                ></BtThFilter>
                <span v-else>{{ column.label }}</span>
              </template>
              <template #default="{ row }">
                {{ formatDate(row.actualStartDate) }}
              </template>
            </el-table-column>

            <!-- 超期列 -->
            <el-table-column
              v-else-if="column.field === 'overdueDays'"
              :property="column.field"
              :label="column.label"
              :width="column.width"
            >
              <template #header>
                <BtThFilter
                  v-if="column.filter"
                  :title="column.label"
                  :type="column.filter.type"
                  :inputType="column.filter.inputType"
                  :showOrder="column.filter.showOrder"
                  :placeholder="column.filter.placeholder"
                  :options="column.filter.data"
                  v-model="column.filterValue"
                  v-model:order="column.filterOrder"
                  @change="changeFilter($event, column.field)"
                ></BtThFilter>
                <span v-else>{{ column.label }}</span>
              </template>
              <template #default="{ row }">
                <el-tag v-if="row.overdueDays > 0" type="danger">是</el-tag>
                <el-tag v-else type="success">否</el-tag>
              </template>
            </el-table-column>

            <!-- 其他列 -->
            <el-table-column
              v-else
              :property="column.field"
              :label="column.label"
              :width="column.width"
              show-overflow-tooltip
            >
              <template #header>
                <BtThFilter
                  v-if="column.filter"
                  :title="column.label"
                  :type="column.filter.type"
                  :inputType="column.filter.inputType"
                  :showOrder="column.filter.showOrder"
                  :placeholder="column.filter.placeholder"
                  :options="column.filter.data"
                  v-model="column.filterValue"
                  v-model:order="column.filterOrder"
                  @change="changeFilter($event, column.field)"
                ></BtThFilter>
                <span v-else>{{ column.label }}</span>
              </template>
              <template #default="{ row }">
                {{ row[column.field] }}
              </template>
            </el-table-column>
          </template>

          <!-- 操作列 -->
          <el-table-column
            label="操作"
            v-bind="tableConfig.optColumnAttr"
            fixed="right"
            :width="tableConfig.optColumnAttr.width"
          >
            <template #default="{ row }">
              <el-dropdown
                placement="bottom-end"
                @command="(command:string) => handleCommand(command, row)"
              >
                <div class="more-opt">
                  <el-icon class="icon"><MoreFilled /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu class="border drop-opt">
                    <el-dropdown-item command="accept">
                      接受任务
                    </el-dropdown-item>
                    <el-dropdown-item command="reject">
                      驳回任务
                    </el-dropdown-item>
                    <el-dropdown-item command="delete"> 删除 </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>

          <!-- 表格底部插槽 - 加载状态提示 -->
          <template #append>
            <!-- 加载更多提示 -->
            <div
              v-if="tasksLoading && tasks.length > 0"
              class="table-loading-more"
            >
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>正在加载更多...</span>
            </div>

            <!-- 没有更多数据提示 -->
            <div v-else-if="!hasMore && tasks.length > 0" class="table-no-more">
              <span>已加载全部数据</span>
            </div>
          </template>
        </el-table>
      </div>
    </div>

    <!-- 接受任务弹窗 -->
    <AcceptTask
      v-model="acceptTaskVisible"
      @success="onAcceptSuccess"
      ref="acceptTaskRef"
    />

    <!-- 驳回任务弹窗 -->
    <RejectTask
      v-model="rejectTaskVisible"
      @success="onRejectSuccess"
      ref="rejectTaskRef"
    />

    <!-- 新增独立任务弹窗 -->
    <AddTask
      v-model="addTaskVisible"
      @success="onAddSuccess"
      ref="addTaskRef"
    />
  </div>
</template>

<style scoped lang="scss">
.independent-task-list {
  height: 100%;
  display: flex;

  .container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .cont {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px;
    overflow: hidden;

    .task-tabs {
      flex-shrink: 0;

      .right-opt {
        .item {
          margin-left: var(--base-margin);
        }
      }
    }

    .table-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .table-loading-more,
    .table-no-more {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px 20px;
      color: var(--el-text-color-secondary);
      font-size: 13px;
      transition: all 0.3s ease;

      .el-icon {
        margin-right: 6px;
        font-size: 14px;
      }
    }

    .table-no-more {
      color: var(--el-text-color-placeholder);
    }

    // 鼠标悬停效果
    .table-loading-more:hover {
      background-color: var(--el-fill-color-light);
    }

    .table {
      flex: 1;

      // 表格底部插槽样式
      :deep(.el-table__append-wrapper) {
        border-top: none;
      }

      :deep(.el-table__indent) {
        padding-left: 12px;
      }

      .task-name {
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        color: var(--el-color-primary);

        &:hover {
          text-decoration: underline;
        }
      }

      .task-number {
        color: var(--el-text-color-primary);
      }

      .progress {
        :deep(.el-progress__text) {
          font-size: 12px !important;
        }
      }
    }
  }

  .footer {
    padding: 10px 20px;
    text-align: right;
  }
}

:deep(.el-tabs) {
  .el-tabs__header {
    margin: 0 0 16px 0;
  }

  .el-tabs__nav-wrap::after {
    background-color: var(--border-color);
  }

  .el-tabs__item {
    color: var(--color-text-secondary);
    font-weight: 500;

    &.is-active {
      color: var(--color-primary);
    }

    &:hover {
      color: var(--color-primary);
    }
  }

  .el-tabs__active-bar {
    background-color: var(--color-primary);
  }
}

.drop-opt {
  width: 140px;
}

.more-opt {
  cursor: pointer;
  .icon {
    font-size: 16px;
    color: var(--el-text-color-regular);
  }
}
</style>
