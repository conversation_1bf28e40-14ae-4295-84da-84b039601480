import { send } from '@/libs/request';
import { appConfig } from '@/app-config.ts';
import axios from 'axios';
import type {
  IndependentTaskItem,
  GetIndependentTasksParams,
  GetIndependentTasksResponse,
  IndeTaskDeleteRequest,
  IndeTaskAcceptRequest,
  IndeTaskRejectRequest,
  IndeTaskChangeRequest,
} from './types';
import {
  independentTasksList,
  addIndependentTask as addIndependentTaskUrl,
  deleteIndependentTask as deleteIndependentTaskUrl,
  acceptIndependentTask,
  rejectIndependentTask,
  changeIndependentTask as changeIndependentTaskUrl,
  followIndependentTask,
  addIndependentTaskTag,
  exportIndependentTaskTemplate as exportIndependentTaskTemplateUrl,
  importIndependentTask as importIndependentTaskUrl,
  exportIndependentTask as exportIndependentTaskUrl,
  getIndependentTaskDetail as getIndependentTaskDetailUrl,
} from '@/service_url/tasks';

/**
 * 下载文件的工具函数（GET请求）
 * @param url 下载地址
 * @param filename 文件名
 */
async function downloadFile(url: string, filename: string): Promise<void> {
  const baseURL = `${appConfig.protocol}://${appConfig.apiBaseUrl}${appConfig.apiPath}`;
  const fullUrl = `${baseURL}${url}`;

  // 创建一个隐藏的 a 标签来触发下载
  const link = document.createElement('a');
  link.href = fullUrl;
  link.download = filename;
  link.style.display = 'none';

  // 添加到 DOM，点击，然后移除
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * 通过POST请求下载文件的工具函数
 * @param url 下载地址
 * @param filename 文件名
 * @param data 请求参数
 */
async function downloadFileWithPost(
  url: string,
  filename: string,
  data: any
): Promise<void> {
  try {
    const baseURL = `${appConfig.protocol}://${appConfig.apiBaseUrl}${appConfig.apiPath}`;
    const fullUrl = `${baseURL}${url}`;

    // 使用axios直接处理blob响应
    const response = await axios({
      method: 'POST',
      url: fullUrl,
      data,
      responseType: 'blob',
      withCredentials: true, // 保持与项目配置一致
      timeout: 300000, // 保持与项目配置一致
    });

    // 创建blob URL
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const blobUrl = window.URL.createObjectURL(blob);

    // 创建下载链接
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = filename;
    link.style.display = 'none';

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理blob URL
    window.URL.revokeObjectURL(blobUrl);
  } catch (error) {
    console.error('文件下载失败:', error);
    throw new Error('文件下载失败');
  }
}

// 获取独立任务列表
export async function getIndependentTasks(
  params: GetIndependentTasksParams
): Promise<
  Omit<GetIndependentTasksResponse, 'pages'> & {
    hasMore: boolean;
  }
> {
  const response = await send({
    method: 'POST',
    url: independentTasksList(params.pageSize || 20, params.page || 1),
    data: {
      queryFlag: params.tabType, // 使用 TabType 作为 queryFlag
      indeTaskName: params.keyword || params.indeTaskName, // 关键词搜索任务名称，支持表格筛选
      tenantId: params.tenantId,
      indeTaskId: params.indeTaskId,
      parentIndeTaskId: params.parentIndeTaskId,
      // 直接使用List格式的参数名
      indeTaskStatusList: params.indeTaskStatus,
      indeTaskResponsible: params.indeTaskResponsible,
      indeTaskResponsibleDept: params.indeTaskResponsibleDept,
      indeTaskTypeList: params.indeTaskType,
      majorList: params.major,
      taskTagId: params.taskTagId,
      sorts: params.sorts,
      followFlag: params.followFlag,
      responsibleName: params.responsibleName,
      // 表格筛选支持的时间字段
      actualStartDate: params.actualStartDate,
      planStartDate: params.planStartDate,
      planFinishDate: params.planFinishDate,
      // 日期范围筛选字段
      beginActualStartDate: params.beginActualStartDate,
      endActualStartDate: params.endActualStartDate,
      beginPlanStartDate: params.beginPlanStartDate,
      endPlanStartDate: params.endPlanStartDate,
      beginPlanFinishDate: params.beginPlanFinishDate,
      endPlanFinishDate: params.endPlanFinishDate,
      // 表格筛选支持的进度字段
      actualProgress: params.actualProgress,
      planProgress: params.planProgress,
      // 其他可能的筛选字段
      overdueDaysList: params.overdueDays,
      indeTaskAcceptCriteria: params.indeTaskAcceptCriteria,
      duration: params.duration,
      workflowId: params.workflowId,
      autoProgressFlag: params.autoProgressFlag,
      actualFinishDate: params.actualFinishDate,
      indeTaskDesc: params.indeTaskDesc,
      exportColumns: params.exportColumns,
    },
  });

  // 检查关键数据是否存在
  if (!response || !Array.isArray(response.list)) {
    return {
      list: [],
      total: 0,
      pageNum: params.page || 1,
      pageSize: params.pageSize || 20,
      hasMore: false,
    };
  }

  return {
    list: response.list,
    total: response.total || 0,
    pageNum: response.page || params.page || 1,
    pageSize: response.pageSize || params.pageSize || 20,
    hasMore: (response.page || 1) < (response.pages || 1),
  };
}

// 新增独立任务
export async function addIndependentTask(
  task: IndependentTaskItem
): Promise<IndependentTaskItem> {
  const response = await send({
    method: 'POST',
    url: addIndependentTaskUrl,
    data: task,
  });

  return response;
}

// 删除独立任务
export async function deleteIndependentTask(
  params: IndeTaskDeleteRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: deleteIndependentTaskUrl,
    data: params,
  });
}

// 接受独立任务
export async function acceptIndependentTaskById(
  params: IndeTaskAcceptRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: acceptIndependentTask,
    data: params,
  });
}

// 驳回独立任务
export async function rejectIndependentTaskById(
  params: IndeTaskRejectRequest
): Promise<any> {
  const response = await send({
    method: 'POST',
    url: rejectIndependentTask,
    data: params,
  });

  return {
    success: response !== undefined && response !== null,
  };
}

// 变更独立任务
export async function changeIndependentTask(
  params: IndeTaskChangeRequest
): Promise<{ success: boolean }> {
  const response = await send({
    method: 'POST',
    url: changeIndependentTaskUrl,
    data: params,
  });

  // 检查接口返回：send 函数即使处理了失败也会走到这里，需要判断返回数据
  return {
    success: response !== undefined && response !== null,
  };
}

// 关注/取消关注独立任务
export async function followIndependentTaskById(
  indeTaskId: number,
  status: number
): Promise<{ success: boolean }> {
  const response = await send({
    method: 'POST',
    url: followIndependentTask,
    data: {
      indeTaskId,
      status,
    },
  });

  // 检查接口返回：send 函数即使处理了失败也会走到这里，需要判断返回数据
  return {
    success: response !== undefined && response !== null,
  };
}

// 添加独立任务标签
export async function addTagToIndependentTask(
  indeTaskId: number,
  tags: Array<{ taskTagId: number }>
): Promise<{ success: boolean }> {
  const response = await send({
    method: 'POST',
    url: addIndependentTaskTag,
    data: {
      indeTaskId,
      tags,
    },
  });

  // 检查接口返回：send 函数即使处理了失败也会走到这里，需要判断返回数据
  return {
    success: response !== undefined && response !== null,
  };
}

// 导出独立任务模板
export async function exportIndependentTaskTemplate(): Promise<void> {
  const filename = `独立任务导入模板_${new Date()
    .toISOString()
    .slice(0, 10)}.xlsx`;
  await downloadFile(exportIndependentTaskTemplateUrl, filename);
}

// 导入独立任务
export async function importIndependentTask(formData: FormData): Promise<any> {
  return send({
    method: 'POST',
    url: importIndependentTaskUrl,
    data: formData,
  });
}

// 导出独立任务
export async function exportIndependentTask(
  params?: GetIndependentTasksParams
): Promise<void> {
  const filename = `独立任务列表_${new Date().toISOString().slice(0, 10)}.xlsx`;

  if (params) {
    // 如果有筛选参数，使用POST请求导出
    const exportData = {
      queryFlag: params.tabType,
      indeTaskName: params.keyword || params.indeTaskName,
      tenantId: params.tenantId,
      indeTaskId: params.indeTaskId,
      parentIndeTaskId: params.parentIndeTaskId,
      // 直接使用List格式的参数名
      indeTaskStatusList: params.indeTaskStatus,
      indeTaskResponsible: params.indeTaskResponsible,
      indeTaskResponsibleDept: params.indeTaskResponsibleDept,
      indeTaskTypeList: params.indeTaskType,
      majorList: params.major,
      taskTagId: params.taskTagId,
      sorts: params.sorts,
      followFlag: params.followFlag,
      responsibleName: params.responsibleName,
      actualStartDate: params.actualStartDate,
      planStartDate: params.planStartDate,
      planFinishDate: params.planFinishDate,
      actualProgress: params.actualProgress,
      planProgress: params.planProgress,
      overdueDaysList: params.overdueDays,
      indeTaskAcceptCriteria: params.indeTaskAcceptCriteria,
      duration: params.duration,
      workflowId: params.workflowId,
      autoProgressFlag: params.autoProgressFlag,
      actualFinishDate: params.actualFinishDate,
      indeTaskDesc: params.indeTaskDesc,
      exportColumns: params.exportColumns,
    };

    await downloadFileWithPost(exportIndependentTaskUrl, filename, exportData);
  } else {
    // 如果没有筛选参数，使用GET请求导出全部
    await downloadFile(exportIndependentTaskUrl, filename);
  }
}

// 获取独立任务详情
export async function getIndependentTaskDetail(
  taskId: string | number
): Promise<IndependentTaskItem> {
  return send({
    method: 'GET',
    url: getIndependentTaskDetailUrl(taskId),
  });
}
