import { ref, reactive } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import type { AcceptTaskForm, AcceptTaskData, TaskItem } from './types';
import * as acceptTaskService from './AcceptTaskService';
import { useAddTaskStore } from '../add_task/AddTaskStore';

export const useAcceptTaskStore = defineStore(
  'independentAcceptTaskStore',
  () => {
    // 1. 响应式状态定义
    const loading = ref(false);

    // 表单数据
    const formData = reactive<AcceptTaskForm>({
      taskName: '',
      responsibleName: '',
      taskType: '',
      participants: [],
      startDate: '',
      endDate: '',
      attachments: [],
      acceptComment: '',
      newAttachments: [],
    });

    // 表单验证规则
    const formRules = reactive({
      acceptComment: [
        { required: true, message: '请输入接受任务意见', trigger: 'blur' },
        {
          min: 1,
          max: 300,
          message: '长度在 1 到 300 个字符',
          trigger: 'blur',
        },
      ],
    });

    // 当前任务数据
    const currentTask = ref<TaskItem | null>(null);

    // 用户列表
    const userList = useAddTaskStore().userList;

    // 2. 异步方法
    async function acceptTask() {
      if (!currentTask.value?.indeTaskId) {
        ElMessage.error('任务ID不能为空');
        return false;
      }

      loading.value = true;

      const data: AcceptTaskData = {
        indeTaskId: currentTask.value.indeTaskId,
        acceptOpinion: formData.acceptComment,
        processDocs: formData.newAttachments.map((file) => ({
          parentIndeTaskProcessDocId: file.parentIndeTaskProcessDocId,
          folderFlag: file.folderFlag || 0,
          docName: file.docName,
          docKey: file.docKey,
          docId: file.docId,
        })),
      };

      const response = await acceptTaskService.acceptTaskById(data);

      // 检查接口返回：判断接受操作是否成功
      const success = response !== undefined && response !== null;
      if (success) {
        ElMessage.success('接受任务成功');
      }

      loading.value = false;
      return success;
    }

    // 3. 同步方法
    function open(taskData?: TaskItem) {
      if (taskData) {
        currentTask.value = taskData;
        // 填充表单数据
        formData.taskName = taskData.indeTaskName || '';

        // 根据responsibleId从userList中查找对应的用户名
        const responsibleId = taskData.indeTaskResponsible;
        const responsibleUser = userList.find(
          (user) => user.userId === responsibleId
        );
        formData.responsibleName = responsibleUser?.userName || '';
        formData.taskType = getTaskTypeName(taskData.indeTaskType) || '';
        formData.participants =
          taskData.participants?.map((item) => item.userId) || [];
        formData.startDate = taskData.planStartDate || '';
        formData.endDate = taskData.planFinishDate || '';
        //@ts-ignore
        formData.attachments =
          taskData.processDocs?.map((doc) => ({
            fileName: doc.docName || '',
            fileKey: doc.docKey || '',
            fileId: doc.docId || '',
          })) || [];
      }
    }

    function close() {
      resetForm();
      currentTask.value = null;
    }

    function resetForm() {
      formData.taskName = '';
      formData.responsibleName = '';
      formData.taskType = '';
      formData.participants = [];
      formData.startDate = '';
      formData.endDate = '';
      formData.attachments = [];
      formData.acceptComment = '';
      formData.newAttachments = [];
    }

    // 获取任务类型名称
    function getTaskTypeName(type?: number): string {
      const typeMap: Record<number, string> = {
        1: '里程碑作业',
        2: '任务作业',
        3: 'WBS作业',
        4: '配合作业',
      };
      return type ? typeMap[type] || '未知类型' : '';
    }

    return {
      // 状态
      loading,
      formData,
      formRules,
      currentTask,
      userList,

      // 方法
      acceptTask,
      open,
      close,
      resetForm,
    };
  }
);
