import type { IndependentTaskItem } from '../../types';
import type { FileItem as UploadFileItem } from '@/components/biz/file_upload/types';

// 组件属性类型
export interface PropsType {
  modelValue: boolean;
  title?: string;
}

// 组件事件类型
export interface EventsType {
  'update:modelValue': [value: boolean];
  change: [value: boolean];
  success: [];
}

// 文件项类型
export interface FileItem {
  name: string;
  path?: string;
  size?: number;
  type?: string;
}

// 用户选项类型
export interface UserOption {
  label: string;
  value: number;
}

// 表单数据类型
export interface AcceptTaskForm {
  taskName: string;
  responsibleName: string;
  taskType: string;
  participants: number[];
  startDate: string;
  endDate: string;
  attachments: { name: string }[];
  acceptComment: string;
  newAttachments: (UploadFileItem &
    NonNullable<AcceptTaskData['processDocs']>[number])[];
}

// 提交数据类型 - 基于 Swagger IndeTaskAcceptRequest
export interface AcceptTaskData {
  /** 租户ID，可选，用于多租户环境下的数据隔离 */
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId?: number;
  /** 接受意见 */
  acceptOpinion?: string;
  /** 过程文档列表 */
  processDocs?: {
    /** 租户ID，可选 */
    tenantId?: number;
    /** 独立任务ID */
    indeTaskId?: number;
    /** 父文档ID,或文件夹ID */
    parentIndeTaskProcessDocId?: number;
    /** 是否文件夹(0 否,1 是) */
    folderFlag?: number;
    /** 附件名 */
    docName?: string;
    /** 附件唯一标识 */
    docKey?: string;
    /** 附件id(关联附件表) */
    docId?: number;
  }[];
}

// 任务项类型别名
export type TaskItem = IndependentTaskItem;
