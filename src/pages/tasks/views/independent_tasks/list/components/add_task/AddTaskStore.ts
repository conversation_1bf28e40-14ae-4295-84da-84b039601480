import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import type {
  AddIndependentTaskForm,
  UserOption,
  TaskTypeOption,
} from './types';
import * as addTaskService from './AddTaskService';
import type { AddIndependentTaskData } from '../../types';

export const useAddTaskStore = defineStore(
  'addTaskStore',
  () => {
    // 1. 响应式状态定义
    const visible = ref(false);
    const loading = ref(false);

    // 表单数据
    const formData = ref<AddIndependentTaskForm>({
      taskName: '',
      taskDesc: '',
      responsible: '',
      taskType: '',
      participants: [],
      startDate: '',
      endDate: '',
      approvalProcess: '',
      autoProgress: 0,
      attachments: [],
    });

    // 表单验证规则
    const formRules = ref({
      taskName: [
        { required: true, message: '请输入任务名称', trigger: 'blur' },
      ],
      responsible: [
        { required: true, message: '请选择负责人', trigger: 'change' },
      ],
      // taskType: [
      //   { required: true, message: '请选择任务类型', trigger: 'change' },
      // ],
      // startDate: [
      //   { required: true, message: '请选择开始时间', trigger: 'change' },
      // ],
      // endDate: [
      //   { required: true, message: '请选择结束时间', trigger: 'change' },
      // ],
    });

    // 选项数据
    const userList = ref<UserOption[]>([]);

    const taskTypeOptions = ref<TaskTypeOption[]>([
      { label: '里程碑作业', value: 1 },
      { label: '任务作业', value: 2 },
      { label: 'wbs作业', value: 3 },
      { label: '配合作业', value: 4 },
    ]);

    // 2. 计算属性
    const isFormValid = computed(() => {
      return !!(
        formData.value.taskName &&
        formData.value.responsible &&
        formData.value.taskType &&
        formData.value.startDate &&
        formData.value.endDate
      );
    });

    // 转换为提交数据格式
    const submitData = computed<AddIndependentTaskData>(() => {
      return {
        indeTaskName: formData.value.taskName,
        indeTaskDesc: formData.value.taskDesc,
        indeTaskResponsible: parseInt(formData.value.responsible),
        indeTaskType: parseInt(formData.value.taskType),
        planStartDate: formData.value.startDate,
        planFinishDate: formData.value.endDate,
        autoProgressFlag: formData.value.autoProgress,
        participants: formData.value.participants.map((v) => ({
          userId: v,
        })),
        processDocs: formData.value.attachments.map((file) => ({
          parentIndeTaskProcessDocId: file.parentIndeTaskProcessDocId,
          docName: file.docName,
          docKey: file.docKey,
          docId: file.docId, // 新建任务时，docId 通常为空
          folderFlag: file.folderFlag || 0,
        })),
      };
    });

    // 3. 异步方法
    async function submitTask(): Promise<boolean> {
      loading.value = true;

      const response = await addTaskService.addIndependentTask(
        submitData.value
      );

      // 检查接口返回：判断是否有有效的返回数据
      if (response !== undefined && response !== null) {
        ElMessage.success('新增任务成功');
        resetForm();
        loading.value = false;
        return true;
      } else {
        ElMessage.error('新增任务失败');
        loading.value = false;
        return false;
      }
    }

    // 4. 同步方法
    function open() {
      visible.value = true;
    }

    function close() {
      visible.value = false;
    }

    function resetForm() {
      formData.value = {
        taskName: '',
        taskDesc: '',
        responsible: '',
        taskType: '',
        participants: [],
        startDate: '',
        endDate: '',
        approvalProcess: '',
        autoProgress: 0,
        attachments: [],
      };
    }

    function setFormData(data: Partial<AddIndependentTaskForm>) {
      formData.value = { ...formData.value, ...data };
    }

    // 加载用户列表
    async function loadUsers() {
      const users = await addTaskService.getUserList();
      // 检查接口返回：判断是否返回了用户数组
      if (Array.isArray(users)) {
        userList.value = users;
      }
    }

    // 加载任务类型列表
    async function loadTaskTypes() {
      const types = await addTaskService.getTaskTypeList();
      // 检查接口返回：判断是否返回了类型数组
      if (Array.isArray(types)) {
        taskTypeOptions.value = types;
      }
    }

    return {
      // 状态
      visible,
      loading,
      formData,
      formRules,
      userList,
      taskTypeOptions,

      // 计算属性
      isFormValid,
      submitData,

      // 方法
      open,
      close,
      resetForm,
      setFormData,
      submitTask,
      loadUsers,
      loadTaskTypes,
    };
  },
  {
    // 5. 持久化配置 - 不持久化表单数据和状态
    persist: {
      pick: ['userList'],
    },
  }
);
