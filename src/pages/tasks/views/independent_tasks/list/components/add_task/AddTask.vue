<script setup lang="ts">
import { computed, onMounted, useTemplateRef } from 'vue';
import { storeToRefs } from 'pinia';
import { useAddTaskStore } from './AddTaskStore';
import type { PropsType, EventsType } from './types';
import { Search } from '@element-plus/icons-vue';
import BtPicker from '@/components/non_biz/bt_picker/BtPicker.vue';
import BtSelect from '@/components/non_biz/bt_select/BtSelect.vue';
import FileUpload from '@/components/biz/file_upload/FileUpload.vue';
import type { ElForm } from 'element-plus';

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: false,
  title: '新增独立任务',
});

const emits = defineEmits<EventsType>();

// Store使用
const store = useAddTaskStore();
const {
  // visible,
  // loading,
  formData,
  formRules,
  userList,
  taskTypeOptions,
} = storeToRefs(store);

// 表单引用
const ruleFormRef = useTemplateRef<InstanceType<typeof ElForm>>('ruleFormRef');

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => {
    emits('update:modelValue', val);
    emits('change', val);
  },
});

// 方法定义
function open() {
  store.open();
  dialogVisible.value = true;
}

function close() {
  store.close();
  dialogVisible.value = false;
}

function handleClosed() {
  ruleFormRef.value?.resetFields();
  store.resetForm();
}

async function submitForm() {
  try {
    await ruleFormRef.value?.validate();
    const success = await store.submitTask();
    if (success) {
      emits('success');
      close();
    }
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 生命周期
onMounted(() => {
  store.loadUsers();
  store.loadTaskTypes();
});

defineExpose({ open, close });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800px"
    :before-close="close"
    @closed="handleClosed"
  >
    <el-form
      ref="ruleFormRef"
      class="form"
      :model="formData"
      :rules="formRules"
      label-width="136px"
    >
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="formData.taskName"
          placeholder="请输入任务名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="任务说明" prop="taskDesc">
        <el-input
          v-model="formData.taskDesc"
          type="textarea"
          :rows="3"
          maxlength="300"
          show-word-limit
          placeholder="请输入任务说明"
        />
      </el-form-item>

      <div class="row">
        <el-form-item label="负责人" prop="responsible">
          <BtPicker
            class="select"
            title="负责人"
            :list="
              userList.map((item) => ({
                label: item.userName,
                value: item.userId,
              }))
            "
            showSearch
            v-model="formData.responsible"
          />
        </el-form-item>

        <el-form-item label="任务类型" prop="taskType">
          <BtSelect
            class="select"
            :list="taskTypeOptions"
            v-model="formData.taskType"
            clearable
          />
        </el-form-item>
      </div>

      <el-form-item label="参与人" prop="participants">
        <BtPicker
          class="w-100"
          title="参与人"
          :list="
            userList.map((item) => ({
              label: item.userName,
              value: item.userId,
            }))
          "
          showSearch
          multiple
          v-model="formData.participants"
          :maxCollapseTags="4"
        />
      </el-form-item>

      <div class="row">
        <el-form-item label="开始时间" prop="startDate">
          <el-date-picker
            v-model="formData.startDate"
            type="date"
            placeholder="请选择开始时间"
            class="date-select"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="结束时间" prop="endDate">
          <el-date-picker
            v-model="formData.endDate"
            type="date"
            placeholder="请选择结束时间"
            class="date-select"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </div>

      <div class="row">
        <el-form-item label="审批流程" prop="approvalProcess">
          <el-input
            v-model="formData.approvalProcess"
            placeholder="任务审批流程（可选）"
            :suffix-icon="Search"
          />
        </el-form-item>
        <el-form-item label="自动进度" prop="autoProgress">
          <el-switch
            v-model="formData.autoProgress"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
      </div>

      <el-form-item label="附件" prop="attachments">
        <FileUpload
          v-model="formData.attachments"
          class="upload-file"
          drag
          multiple
        >
          <div class="title">选择文件</div>
          <div class="tip">
            支持：.rar .zip .doc .docx .pdf .jpg，且单个文件不能超过5M
          </div>
        </FileUpload>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.form {
  padding-right: 32px;
  margin: 0 -24px;

  .row {
    display: flex;
    justify-content: space-between;
  }

  .select {
    width: 216px;
  }

  .date-select {
    width: 216px;
  }

  .w-100 {
    width: 100%;
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__content) {
    width: 216px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
