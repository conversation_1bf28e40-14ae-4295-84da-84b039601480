import type { FileItem } from '@/components/biz/file_upload/types';
import type { IndeTaskProcessDocAddRequest } from '../../types.ts';

// 组件 Props 类型
export interface PropsType {
  modelValue: boolean;
  title?: string;
}

// 组件事件类型
export interface EventsType {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'change', value: boolean): void;
  (e: 'success'): void;
}

// 表单数据类型
export interface AddIndependentTaskForm {
  taskName: string;
  taskDesc: string;
  responsible: string;
  taskType: string;
  participants: number[];
  startDate: string;
  endDate: string;
  approvalProcess: string;
  autoProgress: number;
  attachments: (FileItem & IndeTaskProcessDocAddRequest)[];
}

// 提交数据类型
export interface AddIndependentTaskData {
  taskName: string;
  taskDesc: string;
  responsible: number;
  taskType: number;
  participants: { userId: number }[];
  startDate: string;
  endDate: string;
  approvalProcess: string;
  autoProgress: number;
  attachments: {
    attaKey: string;
    attaName: string;
    attaPath: string;
    attaSize: number;
    attaType: string;
  }[];
}

// 用户选项类型
export interface UserOption {
  /**
   * 访问类型(1 普通用户 2 管理员 3 外部用户)
   */
  accessType?: number;
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 创建者
   */
  createUser?: number;
  /**
   * 部门ID
   */
  deptId?: number;
  /**
   * 管理员备注
   */
  remark?: string;
  /**
   * 性别
   */
  sex?: number;
  /**
   * 电话
   */
  telephone?: string;
  /**
   * 租户id
   */
  tenantId?: number;
  /**
   * 更新时间
   */
  updateTime?: Date;
  /**
   * 更新者
   */
  updateUser?: number;
  /**
   * 用户账号
   */
  userAccount?: string;
  /**
   * 备注
   */
  userDesc?: string;
  /**
   * email
   */
  userEmail?: string;
  /**
   * 用户ID
   */
  userId?: number;
  /**
   * 用户名
   */
  userName?: string;
  /**
   * 密码
   */
  userPassword?: string;
}

// 任务类型选项类型
export interface TaskTypeOption {
  label: string;
  value: number;
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success?: boolean;
}
