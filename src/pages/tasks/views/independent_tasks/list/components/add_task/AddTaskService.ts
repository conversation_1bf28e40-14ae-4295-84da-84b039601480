import { send } from '@/libs/request';
import type { UserOption, TaskTypeOption } from './types';
import type {
  AddIndependentTaskData,
  IndependentTaskItem,
} from '../../types.ts';
import { addIndependentTask as addIndependentTaskUrl } from '@/service_url/tasks';
import { userList } from '@/service_url/shared.ts';

// 新增独立任务
export async function addIndependentTask(
  task: AddIndependentTaskData
): Promise<IndependentTaskItem> {
  const response = await send({
    method: 'POST',
    url: addIndependentTaskUrl,
    data: task,
  });

  return response;
}

/**
 * 获取用户列表
 */
export async function getUserList(): Promise<UserOption[]> {
  // 实际项目中应该调用真实API
  const result = await send({
    method: 'POST',
    url: userList,
    data: {},
  });
  return result;
}

/**
 * 获取任务类型列表
 */
export async function getTaskTypeList(): Promise<TaskTypeOption[]> {
  // 实际项目中应该调用真实API
  // const result = await send({
  //   url: '/api/task-types/list'
  // });
  // return result.data;

  // 目前返回 mock 数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([
        { label: '里程碑作业', value: 1 },
        { label: '任务作业', value: 2 },
        { label: 'wbs作业', value: 3 },
        { label: '配合作业', value: 4 },
      ]);
    }, 200);
  });
}

/**
 * 验证任务名称是否重复
 */
export async function validateTaskName(taskName: string): Promise<boolean> {
  return send({
    method: 'post',
    url: '/api/independent-tasks/validate-name',
    data: { taskName },
  });
}

/**
 * 获取审批流程列表
 */
export async function getApprovalProcessList(): Promise<
  { label: string; value: string }[]
> {
  // 实际项目中应该调用真实API
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([
        { label: '标准审批流程', value: 'standard' },
        { label: '快速审批流程', value: 'fast' },
        { label: '严格审批流程', value: 'strict' },
      ]);
    }, 200);
  });
}
