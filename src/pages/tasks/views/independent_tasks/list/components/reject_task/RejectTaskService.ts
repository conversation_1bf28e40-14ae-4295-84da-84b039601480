import { send } from '@/libs/request';
import { rejectIndependentTask } from '@/service_url/tasks';
import type { RejectTaskData } from './types';

/**
 * 拒绝独立任务
 */
export async function rejectTaskById(data: RejectTaskData): Promise<any> {
  const response = await send({
    method: 'POST',
    url: rejectIndependentTask,
    data,
  });

  return {
    success: response !== undefined && response !== null,
  };
}
