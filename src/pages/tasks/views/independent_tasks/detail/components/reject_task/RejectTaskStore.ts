import { ref, reactive } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import type { RejectTaskForm, RejectTaskData, TaskItem } from './types';
import * as rejectTaskService from './RejectTaskService';

export const useRejectTaskStore = defineStore(
  'independentDetailRejectTaskStore',
  () => {
    // 1. 响应式状态定义
    const loading = ref(false);

    // 表单数据
    const formData = reactive<RejectTaskForm>({
      taskName: '',
      responsible: '',
      taskType: '',
      participants: [],
      startDate: '',
      endDate: '',
      attachments: [],
      rejectComment: '',
      newAttachments: [],
    });

    // 表单验证规则
    const formRules = reactive({
      rejectComment: [
        { required: true, message: '请输入驳回意见', trigger: 'blur' },
        {
          min: 1,
          max: 300,
          message: '长度在 1 到 300 个字符',
          trigger: 'blur',
        },
      ],
    });

    // 驳回原因选项
    const rejectReasons = ref([
      { label: '任务描述不清晰', value: '任务描述不清晰' },
      { label: '时间安排不合理', value: '时间安排不合理' },
      { label: '资源配置不足', value: '资源配置不足' },
      { label: '技术方案有问题', value: '技术方案有问题' },
      { label: '其他原因', value: '其他原因' },
    ]);

    // 当前任务数据
    const currentTask = ref<TaskItem | null>(null);

    // 用户列表 - 简化版本，详情页面不需要复杂的用户管理
    const userList = ref<any[]>([]);

    // 2. 异步方法
    async function rejectTask() {
      if (!currentTask.value?.indeTaskId) {
        ElMessage.error('任务ID不能为空');
        return false;
      }

      loading.value = true;

      const data: RejectTaskData = {
        indeTaskId: currentTask.value.indeTaskId,
        rejectOpinion: formData.rejectComment,
        processDocs: formData.newAttachments.map((file: any) => ({
          parentIndeTaskProcessDocId: file.parentIndeTaskProcessDocId,
          folderFlag: 0,
          docName: file.docName,
          docKey: file.docKey,
          docId: file.docId,
        })),
      };

      const response = await rejectTaskService.rejectTaskById(data);

      // 检查接口返回：判断驳回操作是否成功
      const success = response !== undefined && response !== null;
      if (success) {
        ElMessage.success('驳回任务成功');
      }

      loading.value = false;
      return success;
    }

    // 获取责任人姓名
    function getResponsibleName(taskData: TaskItem, allUsers?: any[]): string {
      // 优先使用传递的responsibleName
      if (taskData.responsibleName) {
        return taskData.responsibleName;
      }

      // 如果没有responsibleName，通过用户列表查找
      if (taskData.indeTaskResponsible && allUsers && allUsers.length > 0) {
        const user = allUsers.find(
          (u) => u.userId === taskData.indeTaskResponsible
        );
        return user?.userName || '';
      }

      return '';
    }

    // 3. 同步方法
    function open(taskData?: TaskItem, allUsers?: any[]) {
      if (taskData) {
        currentTask.value = taskData;
        // 填充表单数据
        formData.taskName = taskData.indeTaskName || '';
        formData.responsible = getResponsibleName(taskData, allUsers);
        formData.taskType = getTaskTypeName(taskData.indeTaskType) || '';

        // 设置用户列表 - 优先使用传入的完整用户列表
        if (allUsers && allUsers.length > 0) {
          userList.value = allUsers;
        } else if (taskData.participants && taskData.participants.length > 0) {
          // 如果没有传入完整用户列表，则使用任务的参与人数据
          userList.value = taskData.participants.map((item: any) => ({
            userId: item.userId,
            userName: item.userName,
          }));
        }

        // 设置选中的参与人
        formData.participants =
          taskData.participants?.map((item: any) => item.userId) || [];

        formData.startDate = taskData.planStartDate || '';
        formData.endDate = taskData.planFinishDate || '';
        // 处理附件数据 - 适配详情页面的数据结构
        formData.attachments =
          taskData.processDocs?.map((doc: any) => ({
            name: doc.docName || '',
          })) || [];
      }
    }

    function close() {
      resetForm();
      currentTask.value = null;
    }

    function resetForm() {
      formData.taskName = '';
      formData.responsible = '';
      formData.taskType = '';
      formData.participants = [];
      formData.startDate = '';
      formData.endDate = '';
      formData.attachments = [];
      formData.rejectComment = '';
      formData.newAttachments = [];
    }

    // 获取任务类型名称
    function getTaskTypeName(type?: number): string {
      const typeMap: Record<number, string> = {
        1: '里程碑作业',
        2: '任务作业',
        3: 'WBS作业',
        4: '配合作业',
      };
      return type ? typeMap[type] || '未知类型' : '';
    }

    return {
      // 状态
      loading,
      formData,
      formRules,
      rejectReasons,
      currentTask,
      userList,

      // 方法
      rejectTask,
      open,
      close,
      resetForm,
    };
  }
);
