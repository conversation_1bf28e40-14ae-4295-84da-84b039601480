<script setup lang="ts">
interface Events {
  (e: 'add-subtask'): void;
}

const emits = defineEmits<Events>();

// 处理新增子任务
function handleAddSubtask() {
  emits('add-subtask');
}
</script>

<template>
  <div class="subtask-tab-actions">
    <!-- 操作按钮 -->
    <div class="actions-toolbar">
      <el-button @click="handleAddSubtask"> 新增子任务 </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.subtask-tab-actions {
  :deep(.el-dialog__header) {
    text-align: left;
  }
  .actions-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
</style>
