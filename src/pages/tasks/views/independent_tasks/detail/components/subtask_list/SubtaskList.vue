<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { MoreFilled } from '@element-plus/icons-vue';
// 移除搜索和筛选组件的导入
import AddSubtask from './components/AddSubtask.vue';
import EditSubtask from './components/EditSubtask.vue';
import {
  getSubtaskPage,
  deleteSubtask,
  type SubtaskResponse,
} from '../../services/SubtaskService';
import { useIndependentTaskDetailStore } from '../../IndependentTaskDetailStore';

// 子任务数据类型
interface SubtaskItem {
  id: string | number;
  name: string;
  status: string;
  progress: number;
  indeTaskResponsibleName: string;
  startDate: string;
  endDate: string;
  actualStartDate: string;
  actualEndDate: string;
  priority: string;
  description: string;
  parentTaskId: string | number;
  createdAt: string;
  updatedAt: string;
}

// 获取store实例
const store = useIndependentTaskDetailStore();

// 响应式数据
const loading = ref(false);
const subtasks = ref<SubtaskItem[]>([]);

// 弹窗状态
const addDialogVisible = ref(false);
const editDialogVisible = ref(false);
const currentEditSubtask = ref<SubtaskItem | null>(null);

// 表格列配置（按UI规范调整）
const columns = [
  {
    field: 'name',
    label: '子任务名称',
    width: '144',
    align: 'left',
  },
  {
    field: 'status',
    label: '状态',
    width: '144',
    align: 'left',
  },
  {
    field: 'progress',
    label: '进度',
    width: '144',
    align: 'left',
  },
  {
    field: 'indeTaskResponsibleName',
    label: '责任人',
    width: '120',
    align: 'left',
  },
  {
    field: 'priority',
    label: '优先级',
    width: '144',
    align: 'left',
  },
  {
    field: 'startDate',
    label: '计划开始时间',
    width: '176',
    align: 'left',
  },
  {
    field: 'endDate',
    label: '计划完成时间',
    width: '176',
    align: 'left',
  },
];

// 获取状态类型
function getStatusType(status: string) {
  const statusMap: Record<string, string> = {
    未开始: 'info',
    进行中: 'primary',
    已完成: 'success',
    已关闭: '',
  };
  return statusMap[status] || 'info';
}

// 获取优先级类型
function getPriorityType(priority: string) {
  const priorityMap: Record<string, string> = {
    高: 'danger',
    中: 'warning',
    低: 'info',
  };
  return priorityMap[priority] || 'info';
}

// 格式化进度
function formatProgress(val: number): string {
  return val + '%';
}

// 加载子任务列表
async function loadSubtasks() {
  loading.value = true;
  try {
    const taskId = store.formData.indeTaskId;
    if (!taskId) {
      console.warn('任务ID为空，跳过加载子任务列表');
      return;
    }

    const response = await getSubtaskPage(1, 1000, {
      parentIndeTaskId: Number(taskId),
    });

    // 转换API数据格式
    subtasks.value = response.list.map((item: SubtaskResponse) => ({
      id: item.indeTaskId || 0,
      name: item.indeTaskName || '',
      status: getTaskStatusText(item.indeTaskStatus || 1),
      progress: item.actualProgress || 0,
      indeTaskResponsibleName: item.responsibleName || '',
      startDate: item.planStartDate || '',
      endDate: item.planFinishDate || '',
      actualStartDate: item.actualStartDate || '',
      actualEndDate: item.actualFinishDate || '',
      priority: getTaskPriority(item.indeTaskType || 2),
      description: item.indeTaskDesc || '',
      parentTaskId: item.parentIndeTaskId || 0,
      createdAt: item.createTime || '',
      updatedAt: item.updateTime || '',
    }));

    // 移除total相关逻辑
  } catch (error) {
    console.error('加载子任务列表失败:', error);
    ElMessage.error('加载子任务列表失败');
  } finally {
    loading.value = false;
  }
}

// 获取任务状态文本
function getTaskStatusText(status: number): string {
  const statusMap: Record<number, string> = {
    1: '未开始',
    2: '进行中',
    3: '已完成',
    4: '已关闭',
  };
  return statusMap[status] || '未开始';
}

// 获取任务优先级（基于任务类型映射）
function getTaskPriority(taskType: number): string {
  const priorityMap: Record<number, string> = {
    1: '高', // 里程碑作业
    2: '中', // 任务作业
    3: '中', // WBS作业
    4: '低', // 配合作业
  };
  return priorityMap[taskType] || '中';
}

// 添加子任务
function handleAdd() {
  addDialogVisible.value = true;
}

// 编辑子任务
function handleEdit(row: SubtaskItem) {
  currentEditSubtask.value = { ...row };
  editDialogVisible.value = true;
}

// 删除子任务
function handleDelete(row: SubtaskItem) {
  ElMessageBox.confirm(`确定要删除子任务"${row.name}"吗？`, '提示', {
    confirmButtonText: '删除',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteSubtask({
        indeTaskId: Number(row.id),
      });
      ElMessage.success('删除成功');
      loadSubtasks();
    } catch (error) {
      console.error('删除子任务失败:', error);
      ElMessage.error('删除失败');
    }
  });
}

// 处理下拉菜单命令
function handleCommand(command: string, row: SubtaskItem) {
  switch (command) {
    case 'edit':
      handleEdit(row);
      break;
    case 'delete':
      handleDelete(row);
      break;
    default:
      console.log('未知命令:', command);
  }
}

// 添加成功后的回调
function onAddSuccess() {
  addDialogVisible.value = false;
  loadSubtasks();
  ElMessage.success('添加子任务成功');
}

// 编辑成功后的回调
function onEditSuccess() {
  editDialogVisible.value = false;
  currentEditSubtask.value = null;
  loadSubtasks();
  ElMessage.success('编辑子任务成功');
}

// 暴露给父组件的方法
function openAdd() {
  handleAdd();
}

// 暴露方法
defineExpose({
  openAdd,
  refreshData: loadSubtasks,
});

// 初始化
onMounted(() => {
  loadSubtasks();
});
</script>

<template>
  <div class="subtask-list">
    <!-- 子任务表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="subtasks"
        class="table"
        row-key="id"
        stripe
      >
        <!-- 选择列 -->
        <el-table-column type="selection" width="50" fixed="left" />

        <!-- 序号列 -->
        <el-table-column type="index" label="#" width="32" align="center" />

        <!-- 动态列 -->
        <template v-for="column in columns" :key="column.field">
          <!-- 子任务名称列 -->
          <el-table-column
            v-if="column.field === 'name'"
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span class="task-name" :title="row.name">{{ row.name }}</span>
            </template>
          </el-table-column>

          <!-- 状态列 -->
          <el-table-column
            v-else-if="column.field === 'status'"
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
          >
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 进度列 -->
          <el-table-column
            v-else-if="column.field === 'progress'"
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
          >
            <template #default="{ row }">
              <el-progress
                :percentage="row.progress || 0"
                :format="formatProgress"
                :stroke-width="8"
                color="#52C41A"
              />
            </template>
          </el-table-column>

          <!-- 优先级列 -->
          <el-table-column
            v-else-if="column.field === 'priority'"
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
          >
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)" size="small">
                {{ row.priority }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 其他列 -->
          <el-table-column
            v-else
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row[column.field] }}
            </template>
          </el-table-column>
        </template>

        <!-- 操作列 -->
        <el-table-column label="操作" width="48" align="center" fixed="right">
          <template #default="{ row }">
            <el-dropdown
              placement="bottom-end"
              @command="(command: string) => handleCommand(command, row)"
              hide-on-click
              :show-timeout="50"
            >
              <div class="more-opt">
                <el-icon class="icon"><MoreFilled /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="border drop-opt">
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="delete">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加子任务弹窗 -->
    <AddSubtask v-model="addDialogVisible" @success="onAddSuccess" />

    <!-- 编辑子任务弹窗 -->
    <EditSubtask
      v-model="editDialogVisible"
      :subtask="currentEditSubtask"
      @success="onEditSuccess"
    />
  </div>
</template>

<style scoped lang="scss">
.subtask-list {
  height: 100%;
  display: flex;
  flex-direction: column;

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .left {
      display: flex;
      align-items: center;
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-box {
        width: 240px;
      }
    }
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    border-radius: 6px;

    .subtask-table {
      height: 100%;

      .task-name {
        cursor: pointer;
        color: var(--el-color-primary);

        &:hover {
          text-decoration: underline;
        }
      }

      .more-opt {
        cursor: pointer;
        .icon {
          font-size: 16px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }

  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}

.dropdown-menu {
  min-width: 100px;
}
</style>
