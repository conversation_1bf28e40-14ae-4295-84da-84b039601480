<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';

// 子任务数据类型
interface SubtaskItem {
  id: string | number;
  name: string;
  status: string;
  progress: number;
  indeTaskResponsibleName: string;
  startDate: string;
  endDate: string;
  actualStartDate: string;
  actualEndDate: string;
  priority: string;
  description: string;
  parentTaskId: string | number;
  createdAt: string;
  updatedAt: string;
}

// Props
interface Props {
  modelValue: boolean;
  subtask: SubtaskItem | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  success: [];
}>();

// 弹窗可见性
const dialogVisible = ref(false);

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  description: '',
  indeTaskResponsibleName: '',
  priority: 'medium',
  startDate: '',
  endDate: '',
  actualStartDate: '',
  actualEndDate: '',
  status: '未开始',
  progress: 0,
});

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入子任务名称', trigger: 'blur' },
    { max: 100, message: '子任务名称不能超过100个字符', trigger: 'blur' },
  ],
  indeTaskResponsibleName: [
    { required: true, message: '请选择责任人', trigger: 'change' },
  ],
  startDate: [
    { required: true, message: '请选择计划开始时间', trigger: 'change' },
  ],
  endDate: [
    { required: true, message: '请选择计划完成时间', trigger: 'change' },
  ],
  progress: [
    { required: true, message: '请输入进度', trigger: 'blur' },
    {
      type: 'number',
      min: 0,
      max: 100,
      message: '进度必须在0-100之间',
      trigger: 'blur',
    },
  ],
};

// 表单引用
const formRef = ref();

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val;
    if (val && props.subtask) {
      // 填充表单数据
      Object.assign(formData, {
        id: props.subtask.id,
        name: props.subtask.name,
        description: props.subtask.description,
        indeTaskResponsibleName: props.subtask.indeTaskResponsibleName,
        priority: props.subtask.priority,
        startDate: props.subtask.startDate,
        endDate: props.subtask.endDate,
        actualStartDate: props.subtask.actualStartDate,
        actualEndDate: props.subtask.actualEndDate,
        status: props.subtask.status,
        progress: props.subtask.progress,
      });
    }
  }
);

watch(dialogVisible, (val) => {
  emit('update:modelValue', val);
});

// 重置表单
function resetForm() {
  Object.assign(formData, {
    id: '',
    name: '',
    description: '',
    indeTaskResponsibleName: '',
    priority: 'medium',
    startDate: '',
    endDate: '',
    actualStartDate: '',
    actualEndDate: '',
    status: '未开始',
    progress: 0,
  });
  formRef.value?.clearValidate();
}

// 关闭弹窗
function close() {
  dialogVisible.value = false;
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate();

    // TODO: 调用API更新子任务
    // const response = await updateSubtask(formData.id, formData);
    console.log('更新子任务:', formData);

    ElMessage.success('更新子任务成功');
    emit('success');
    close();
  } catch (error) {
    console.error('更新子任务失败:', error);
    ElMessage.error('更新子任务失败');
  }
}

// 弹窗关闭时重置表单
function handleClosed() {
  resetForm();
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑子任务"
    width="800px"
    :before-close="close"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="136px"
      class="edit-subtask-form"
    >
      <el-form-item label="任务名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入子任务名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="任务描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入任务描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="责任人" prop="indeTaskResponsibleName">
            <el-input
              v-model="formData.indeTaskResponsibleName"
              placeholder="请输入责任人"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select
              v-model="formData.priority"
              placeholder="请选择优先级"
              class="w-100"
            >
              <el-option label="高" value="高" />
              <el-option label="中" value="中" />
              <el-option label="低" value="低" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startDate">
            <el-date-picker
              v-model="formData.startDate"
              type="date"
              placeholder="请选择开始时间"
              class="w-100"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="完成时间" prop="endDate">
            <el-date-picker
              v-model="formData.endDate"
              type="date"
              placeholder="请选择完成时间"
              class="w-100"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="实际开始" prop="actualStartDate">
            <el-date-picker
              v-model="formData.actualStartDate"
              type="date"
              placeholder="请选择实际开始时间"
              class="w-100"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际完成" prop="actualEndDate">
            <el-date-picker
              v-model="formData.actualEndDate"
              type="date"
              placeholder="请选择实际完成时间"
              class="w-100"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务状态" prop="status">
            <el-select
              v-model="formData.status"
              placeholder="请选择任务状态"
              class="w-100"
            >
              <el-option label="未开始" value="未开始" />
              <el-option label="进行中" value="进行中" />
              <el-option label="已完成" value="已完成" />
              <el-option label="已关闭" value="已关闭" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="进度(%)" prop="progress">
            <el-input-number
              v-model="formData.progress"
              :min="0"
              :max="100"
              placeholder="请输入进度"
              class="w-100"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.edit-subtask-form {
  padding-right: 32px;
  margin: 0 -24px;

  .w-100 {
    width: 100%;
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__content) {
    width: 216px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
