<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { addSubtask } from '../../../services/SubtaskService';
import { useIndependentTaskDetailStore } from '../../../IndependentTaskDetailStore';

// Props
interface Props {
  modelValue: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  success: [];
}>();

// 获取store实例
const store = useIndependentTaskDetailStore();

// 弹窗可见性
const dialogVisible = ref(false);

// 表单数据
const formData = reactive({
  indeTaskName: '',
  indeTaskDesc: '',
  indeTaskResponsible: 0,
  indeTaskType: 2, // 任务作业
  planStartDate: '',
  planFinishDate: '',
  autoProgressFlag: 0,
});

// 表单验证规则
const formRules = {
  indeTaskName: [
    { required: true, message: '请输入子任务名称', trigger: 'blur' },
    { max: 100, message: '子任务名称不能超过100个字符', trigger: 'blur' },
  ],
  indeTaskResponsible: [
    { required: true, message: '请选择责任人', trigger: 'change' },
  ],
  planStartDate: [
    { required: true, message: '请选择计划开始时间', trigger: 'change' },
  ],
  planFinishDate: [
    { required: true, message: '请选择计划完成时间', trigger: 'change' },
  ],
};

// 表单引用
const formRef = ref();

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val;
  }
);

watch(dialogVisible, (val) => {
  emit('update:modelValue', val);
});

// 重置表单
function resetForm() {
  Object.assign(formData, {
    indeTaskName: '',
    indeTaskDesc: '',
    indeTaskResponsible: 0,
    indeTaskType: 2,
    planStartDate: '',
    planFinishDate: '',
    autoProgressFlag: 0,
  });
  formRef.value?.clearValidate();
}

// 关闭弹窗
function close() {
  dialogVisible.value = false;
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate();

    const parentTaskId = store.formData.indeTaskId;
    if (!parentTaskId) {
      ElMessage.error('父任务ID获取失败');
      return;
    }

    await addSubtask({
      parentIndeTaskId: Number(parentTaskId),
      indeTaskName: formData.indeTaskName,
      indeTaskResponsible: formData.indeTaskResponsible,
      indeTaskType: formData.indeTaskType,
      planStartDate: formData.planStartDate,
      planFinishDate: formData.planFinishDate,
      autoProgressFlag: formData.autoProgressFlag,
      indeTaskDesc: formData.indeTaskDesc,
    });

    ElMessage.success('创建子任务成功');
    emit('success');
    close();
  } catch (error) {
    console.error('创建子任务失败:', error);
    ElMessage.error('创建子任务失败');
  }
}

// 弹窗关闭时重置表单
function handleClosed() {
  resetForm();
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="新增子任务"
    width="800px"
    :before-close="close"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="136px"
      class="add-subtask-form"
    >
      <el-form-item label="任务名称" prop="indeTaskName">
        <el-input
          v-model="formData.indeTaskName"
          placeholder="请输入子任务名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="任务描述" prop="indeTaskDesc">
        <el-input
          v-model="formData.indeTaskDesc"
          type="textarea"
          :rows="3"
          placeholder="请输入任务描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="责任人" prop="indeTaskResponsible">
        <el-input-number
          v-model="formData.indeTaskResponsible"
          placeholder="请输入责任人ID"
          :min="1"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="任务类型" prop="indeTaskType">
        <el-select
          v-model="formData.indeTaskType"
          placeholder="请选择任务类型"
          style="width: 100%"
        >
          <el-option label="里程碑作业" :value="1" />
          <el-option label="任务作业" :value="2" />
          <el-option label="WBS作业" :value="3" />
          <el-option label="配合作业" :value="4" />
        </el-select>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="planStartDate">
            <el-date-picker
              v-model="formData.planStartDate"
              type="date"
              placeholder="请选择开始时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-100"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="完成时间" prop="planFinishDate">
            <el-date-picker
              v-model="formData.planFinishDate"
              type="date"
              placeholder="请选择完成时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-100"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="自动进度">
        <el-radio-group v-model="formData.autoProgressFlag">
          <el-radio :value="1">是</el-radio>
          <el-radio :value="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.add-subtask-form {
  padding-right: 32px;
  margin: 0 -24px;

  .w-100 {
    width: 100%;
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__content) {
    width: 216px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
