import { send } from '@/libs/request';
import { changeIndependentTask } from '@/service_url/tasks';
import type {
  IndeTaskChangeRequest,
  UserOption,
  TaskTypeOption,
  WorkflowDefinitionResponse,
  TaskTag,
} from './types';

/**
 * 提交独立任务变更
 * @param changeData 变更数据
 * @returns 变更结果
 */
export async function submitTaskChange(
  changeData: IndeTaskChangeRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: changeIndependentTask,
    data: changeData,
  });
}

/**
 * 获取用户列表
 * @returns 用户列表
 */
export async function getUserList(): Promise<UserOption[]> {
  const response = await send({
    method: 'GET',
    url: '/system/user/list',
  });

  // 检查接口返回：判断是否返回了用户数组
  if (Array.isArray(response)) {
    return response;
  }

  // 如果response是对象且包含list字段
  if (response && Array.isArray(response.list)) {
    return response.list;
  }

  return [];
}

/**
 * 获取任务类型列表
 * @returns 任务类型列表
 */
export async function getTaskTypeList(): Promise<TaskTypeOption[]> {
  // 返回固定的任务类型选项
  return [
    { label: '里程碑作业', value: 1 },
    { label: '任务作业', value: 2 },
    { label: 'wbs作业', value: 3 },
    { label: '配合作业', value: 4 },
  ];
}

/**
 * 获取工作流定义列表
 * @returns 工作流定义列表
 */
export async function getWorkflowDefinitionList(): Promise<
  WorkflowDefinitionResponse[]
> {
  // const response = await send({
  //   method: 'GET',
  //   url: '/process/definition/list',
  // });

  // // 检查接口返回：判断是否返回了工作流数组
  // if (Array.isArray(response)) {
  //   return response;
  // }

  // // 如果response是对象且包含list字段
  // if (response && Array.isArray(response.list)) {
  //   return response.list;
  // }

  return [];
}

/**
 * 获取可用任务标签列表
 * @returns 任务标签列表
 */
export async function getAvailableTaskTags(): Promise<TaskTag[]> {
  const response = await send({
    method: 'GET',
    url: '/projectmanage/config-task-tag/list',
  });

  // 检查接口返回：判断是否返回了标签数组
  if (Array.isArray(response)) {
    return response;
  }

  // 如果response是对象且包含list字段
  if (response && Array.isArray(response.list)) {
    return response.list;
  }

  return [];
}

/**
 * 添加任务变更记录
 * @param taskId 任务ID
 * @param changeReason 变更原因
 * @param changeContent 变更内容
 * @returns 添加结果
 */
export async function addTaskChangeLog(
  taskId: number,
  changeReason: string,
  changeContent: string
): Promise<any> {
  return send({
    method: 'POST',
    url: '/projectmanage/task-change-log/add',
    data: {
      taskId,
      projTaskFlag: 0, // 0表示独立任务
      changeReason,
      changeContent,
    },
  });
}

/**
 * 生成变更内容描述
 * @param changes 变更记录数组
 * @returns 变更内容字符串
 */
export function generateChangeContent(
  changes: Array<{
    fieldLabel: string;
    oldValue: any;
    newValue: any;
  }>
): string {
  return changes
    .map((change) => {
      const oldVal = change.oldValue || '空';
      const newVal = change.newValue || '空';
      return `${change.fieldLabel}: ${oldVal} → ${newVal}`;
    })
    .join('; ');
}

/**
 * 格式化用户显示名称
 * @param users 用户列表
 * @param userId 用户ID
 * @returns 用户名称
 */
export function formatUserName(users: UserOption[], userId: number): string {
  const user = users.find((u) => u.userId === userId);
  return user ? user.userName || user.userAccount || '未知用户' : '未知用户';
}

/**
 * 格式化任务类型显示名称
 * @param taskType 任务类型值
 * @returns 任务类型名称
 */
export function formatTaskTypeName(taskType: number): string {
  const typeMap: Record<number, string> = {
    1: '里程碑作业',
    2: '任务作业',
    3: 'wbs作业',
    4: '配合作业',
  };
  return typeMap[taskType] || '未知类型';
}

/**
 * 格式化工作流显示名称
 * @param workflows 工作流列表
 * @param workflowId 工作流ID
 * @returns 工作流名称
 */
export function formatWorkflowName(
  workflows: WorkflowDefinitionResponse[],
  workflowId: string
): string {
  const workflow = workflows.find((w) => w.id === workflowId);
  return workflow ? `${workflow.name} v${workflow.version}` : '未知流程';
}

/**
 * 格式化参与人显示名称
 * @param participants 参与人列表
 * @returns 参与人名称字符串
 */
export function formatParticipantsName(
  participants: Array<{ userName: string }>
): string {
  return participants.map((p) => p.userName).join(', ') || '无';
}

/**
 * 格式化标签显示名称
 * @param tags 标签列表
 * @returns 标签名称字符串
 */
export function formatTagsName(tags: Array<{ taskTagName: string }>): string {
  return tags.map((t) => t.taskTagName).join(', ') || '无';
}

/**
 * 格式化文档显示名称
 * @param docs 文档列表
 * @returns 文档名称字符串
 */
export function formatDocsName(docs: Array<{ docName: string }>): string {
  return docs.map((d) => d.docName).join(', ') || '无';
}
