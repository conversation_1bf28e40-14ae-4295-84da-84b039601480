/**
 * 独立任务变更入参对象
 * @source POST /projectmanage/inde-task/change [REQUEST]
 */
export interface IndeTaskChangeRequest {
  tenantId?: number;
  /** 独立任务ID */
  indeTaskId: number;
  /** 独立任务名称 */
  indeTaskName: string;
  /** 独立任务责任人 */
  indeTaskResponsible: number;
  /** 独立任务责任部门 */
  indeTaskResponsibleDept?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 独立任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 参与人列表 */
  participants?: IndeTaskParticipantChangeRequest[];
  /** 变更原因 */
  changeReason?: string;
}

/**
 * 独立任务变更参与人入参对象
 */
export interface IndeTaskParticipantChangeRequest {
  tenantId?: number;
  /** 主键ID */
  holderId?: number;
  /** 用户ID(关联用户表) */
  userId: number;
}

/**
 * 任务变更记录
 * @source POST /projectmanage/task-change-log/add [REQUEST]
 */
export interface TaskChangeLogEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  taskChangeLogId?: number;
  /** 项目任务风险ID,风险回答 */
  taskId?: number;
  /** 是否为风险回答(1是,0 否) */
  projTaskFlag?: number;
  /** 变更原因 */
  changeReason?: string;
  /** 变更内容 */
  changeContent?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 用户选项类型
 */
export interface UserOption {
  /** 用户ID */
  userId?: number;
  /** 用户名 */
  userName?: string;
  /** 用户账号 */
  userAccount?: string;
  /** 部门ID */
  deptId?: number;
  /** 电话 */
  telephone?: string;
  /** email */
  userEmail?: string;
}

/**
 * 任务类型选项类型
 */
export interface TaskTypeOption {
  label: string;
  value: number;
}

/**
 * 工作流定义响应类型
 */
export interface WorkflowDefinitionResponse {
  id: string;
  name: string;
  version: number;
  key: string;
  description?: string;
}

/**
 * 任务标签类型
 */
export interface TaskTag {
  taskTagId: number;
  taskTagName: string;
  taskTagColor: string;
  taskTagDesc?: string;
}

/**
 * 任务参与人类型
 */
export interface TaskParticipant {
  userId: number;
  userName: string;
  holderId?: number;
}

/**
 * 过程文档类型
 */
export interface TaskAttachment {
  docId: number;
  docKey: string;
  docName: string;
  docType: string;
  docSize?: number;
  folderFlag?: number;
}

// ==================== 组件相关类型定义 ====================

/**
 * 组件Props类型
 */
export interface TaskChangeDialogProps {
  modelValue: boolean;
  taskId: string | number;
  loading?: boolean;
}

/**
 * 组件事件类型
 */
export interface TaskChangeDialogEvents {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
  (e: 'cancel'): void;
}

/**
 * 任务变更表单数据类型
 */
export interface TaskChangeFormData {
  // 变更原因（必填）
  changeReason: string;

  // 14个可变更字段（移除过程文档和标签）
  indeTaskName: string; // 任务名称
  indeTaskResponsible: number; // 责任人
  indeTaskType: number; // 任务类型
  participants: TaskParticipant[]; // 参与人
  planStartDate: string; // 计划开始时间
  planFinishDate: string; // 计划完成时间
  actualStartDate: string; // 实际开始时间
  workflowId: string; // 审批流程
  actualProgress: number; // 任务进度
  duration: number; // 工期
  autoProgressFlag: number; // 自动进度
  indeTaskDesc: string; // 任务说明
  indeTaskAcceptCriteria: string; // 验收标准
}

/**
 * 变更记录项类型
 */
export interface ChangeRecord {
  fieldName: string;
  fieldLabel: string;
  oldValue: any;
  newValue: any;
  changeTime: string;
  changeUser: number;
}

/**
 * API响应类型
 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success?: boolean;
}
