<script setup lang="ts">
import { computed, onMounted, useTemplateRef, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useTaskChangeDialogStore } from './TaskChangeDialogStore';
import type { TaskChangeDialogProps, TaskChangeDialogEvents } from './types';
import BtPicker from '@/components/non_biz/bt_picker/BtPicker.vue';
import BtSelect from '@/components/non_biz/bt_select/BtSelect.vue';
import type { ElForm } from 'element-plus';

const props = withDefaults(defineProps<TaskChangeDialogProps>(), {
  modelValue: false,
  loading: false,
});

const emits = defineEmits<TaskChangeDialogEvents>();

// Store使用
const store = useTaskChangeDialogStore();
const {
  loading,
  formData,
  formRules,
  userList,
  taskTypeOptions,
  workflowDefinitions,
} = storeToRefs(store);

// 表单引用
const ruleFormRef = useTemplateRef<InstanceType<typeof ElForm>>('ruleFormRef');

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => {
    emits('update:modelValue', val);
  },
});

// 监听props变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      store.loadOptions();
    }
  }
);

// 方法定义
function close() {
  store.close();
  dialogVisible.value = false;
  emits('cancel');
}

function handleClosed() {
  ruleFormRef.value?.resetFields();
  store.resetForm();
}

async function submitForm() {
  try {
    await ruleFormRef.value?.validate();
    const success = await store.submitTaskChange();
    if (success) {
      emits('success');
      close();
    }
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 生命周期
onMounted(() => {
  store.loadOptions();
});

defineExpose({
  open: (taskId: string | number, initialData: any) =>
    store.open(taskId, initialData),
  close,
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="独立任务变更"
    width="800px"
    :before-close="close"
    @closed="handleClosed"
  >
    <el-form
      ref="ruleFormRef"
      class="form"
      :model="formData"
      :rules="formRules"
      label-width="136px"
    >
      <!-- 变更原因 -->
      <el-form-item label="变更原因" prop="changeReason">
        <el-input
          v-model="formData.changeReason"
          type="textarea"
          :rows="3"
          maxlength="500"
          show-word-limit
          placeholder="请输入任务变更原因"
        />
      </el-form-item>

      <!-- 任务名称 -->
      <el-form-item label="任务名称" prop="indeTaskName">
        <el-input
          v-model="formData.indeTaskName"
          placeholder="请输入任务名称"
          clearable
        />
      </el-form-item>

      <!-- 责任人和任务类型 -->
      <div class="row">
        <el-form-item label="责任人" prop="indeTaskResponsible">
          <BtPicker
            class="select"
            title="责任人"
            :list="
              userList.map((item) => ({
                label: item.userName,
                value: item.userId,
              }))
            "
            showSearch
            v-model="formData.indeTaskResponsible"
          />
        </el-form-item>

        <el-form-item label="任务类型" prop="indeTaskType">
          <BtSelect
            class="select"
            :list="taskTypeOptions"
            v-model="formData.indeTaskType"
            clearable
          />
        </el-form-item>
      </div>

      <!-- 参与人 -->
      <el-form-item label="参与人" prop="participants">
        <BtPicker
          class="w-100"
          title="参与人"
          :list="
            userList.map((item) => ({
              label: item.userName,
              value: item.userId,
            }))
          "
          showSearch
          multiple
          :model-value="formData.participants.map((p) => p.userId)"
          @update:model-value="
            (values: number[]) => {
              formData.participants = values.map((userId: number) => {
                const user = userList.find((u) => u.userId === userId);
                return {
                  userId,
                  userName: user?.userName || '',
                  holderId: formData.participants.find(
                    (p) => p.userId === userId
                  )?.holderId,
                };
              });
            }
          "
          :maxCollapseTags="4"
        />
      </el-form-item>

      <!-- 计划时间 -->
      <div class="row">
        <el-form-item label="计划开始时间" prop="planStartDate">
          <el-date-picker
            v-model="formData.planStartDate"
            type="datetime"
            placeholder="请选择计划开始时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="date-select w-100"
          />
        </el-form-item>

        <el-form-item label="计划完成时间" prop="planFinishDate">
          <el-date-picker
            v-model="formData.planFinishDate"
            type="datetime"
            placeholder="请选择计划完成时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="date-select w-100"
          />
        </el-form-item>
      </div>

      <!-- 实际开始时间和工期 -->
      <div class="row">
        <el-form-item label="实际开始时间" prop="actualStartDate">
          <el-date-picker
            v-model="formData.actualStartDate"
            type="datetime"
            placeholder="请选择实际开始时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="date-select w-100"
          />
        </el-form-item>

        <el-form-item label="工期（天）" prop="duration">
          <el-input-number
            v-model="formData.duration"
            class="w-100"
            :min="0"
            placeholder="请输入工期"
          />
        </el-form-item>
      </div>

      <!-- 审批流程和任务进度 -->
      <div class="row">
        <el-form-item label="审批流程" prop="workflowId">
          <el-select
            v-model="formData.workflowId"
            placeholder="请选择审批流程"
            filterable
            clearable
            class="w-100"
          >
            <el-option
              v-for="workflow in workflowDefinitions"
              :key="workflow.id"
              :label="`${workflow.name} v${workflow.version}`"
              :value="workflow.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="任务进度" prop="actualProgress">
          <el-input-number
            v-model="formData.actualProgress"
            class="w-100"
            :min="0"
            :max="100"
            placeholder="请输入任务进度"
          />
        </el-form-item>
      </div>

      <!-- 自动进度 -->
      <el-form-item label="自动进度" prop="autoProgressFlag">
        <el-switch
          v-model="formData.autoProgressFlag"
          :active-value="1"
          :inactive-value="0"
        />
      </el-form-item>

      <!-- 任务说明 -->
      <el-form-item label="任务说明" prop="indeTaskDesc">
        <el-input
          v-model="formData.indeTaskDesc"
          type="textarea"
          :rows="3"
          maxlength="500"
          show-word-limit
          placeholder="请输入任务说明"
        />
      </el-form-item>

      <!-- 验收标准 -->
      <el-form-item label="验收标准" prop="indeTaskAcceptCriteria">
        <el-input
          v-model="formData.indeTaskAcceptCriteria"
          type="textarea"
          :rows="3"
          maxlength="500"
          show-word-limit
          placeholder="请输入验收标准"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.form {
  padding-right: 32px;
  margin: 0 -24px;

  .row {
    display: flex;
    justify-content: space-between;
  }

  .select {
    width: 216px;
  }

  .w-100 {
    width: 216px;
  }

  .date-select {
    width: 216px;
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__content) {
    width: 216px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-dialog__header) {
  text-align: left;
}

// 覆盖Element Plus样式
:deep(.el-divider__text) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-switch) {
  height: 32px;
  line-height: 32px;
}
</style>
