import { ref, reactive, computed } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import type {
  TaskChangeFormData,
  TaskTypeOption,
  WorkflowDefinitionResponse,
  IndeTaskChangeRequest,
} from './types';
import * as taskChangeService from './TaskChangeDialogService';

export const useTaskChangeDialogStore = defineStore(
  'taskChangeDialog',
  () => {
    // 1. 响应式状态定义
    const visible = ref(false);
    const loading = ref(false);
    const taskId = ref<string | number>('');

    // 外部传入的用户列表（从主Store获取）
    const externalUserList = ref<any[]>([]);

    // 表单数据
    const formData = reactive<TaskChangeFormData>({
      changeReason: '',
      indeTaskName: '',
      indeTaskResponsible: 0,
      indeTaskType: 1,
      participants: [],
      planStartDate: '',
      planFinishDate: '',
      actualStartDate: '',
      workflowId: '',
      actualProgress: 0,
      duration: 0,
      autoProgressFlag: 0,
      indeTaskDesc: '',
      indeTaskAcceptCriteria: '',
    });

    // 原始数据（用于变更对比）
    const originalData = reactive<TaskChangeFormData>({
      changeReason: '',
      indeTaskName: '',
      indeTaskResponsible: 0,
      indeTaskType: 1,
      participants: [],
      planStartDate: '',
      planFinishDate: '',
      actualStartDate: '',
      workflowId: '',
      actualProgress: 0,
      duration: 0,
      autoProgressFlag: 0,
      indeTaskDesc: '',
      indeTaskAcceptCriteria: '',
    });

    // 表单验证规则
    const formRules = ref({
      changeReason: [
        { required: true, message: '请输入变更原因', trigger: 'blur' },
        { min: 5, message: '变更原因至少5个字符', trigger: 'blur' },
      ],
      indeTaskName: [
        { required: true, message: '请输入任务名称', trigger: 'blur' },
      ],
      indeTaskResponsible: [
        { required: true, message: '请选择责任人', trigger: 'change' },
      ],
      indeTaskType: [
        { required: true, message: '请选择任务类型', trigger: 'change' },
      ],
      // planStartDate: [
      //   { required: true, message: '请选择计划开始时间', trigger: 'change' },
      // ],
      // planFinishDate: [
      //   { required: true, message: '请选择计划完成时间', trigger: 'change' },
      // ],
    });

    // 选项数据（用户列表从主Store获取，不在这里维护）
    const taskTypeOptions = ref<TaskTypeOption[]>([]);
    const workflowDefinitions = ref<WorkflowDefinitionResponse[]>([]);

    // 2. 计算属性
    const isFormValid = computed(() => {
      return (
        formData.changeReason.length >= 5 &&
        formData.indeTaskName.trim() !== '' &&
        formData.indeTaskResponsible > 0 &&
        formData.indeTaskType > 0 &&
        formData.planStartDate !== '' &&
        formData.planFinishDate !== ''
      );
    });

    // 转换为提交数据格式
    const submitData = computed<IndeTaskChangeRequest>(() => {
      return {
        indeTaskId: Number(taskId.value),
        indeTaskName: formData.indeTaskName,
        indeTaskResponsible: formData.indeTaskResponsible,
        indeTaskType: formData.indeTaskType,
        planStartDate: formData.planStartDate,
        planFinishDate: formData.planFinishDate,
        workflowId: formData.workflowId,
        actualProgress: formData.actualProgress,
        duration: formData.duration,
        autoProgressFlag: formData.autoProgressFlag,
        participants: formData.participants.map((p) => ({
          userId: p.userId,
          holderId: p.holderId,
        })),
        changeReason: formData.changeReason,
      };
    });

    // 3. 异步方法
    async function submitTaskChange(): Promise<boolean> {
      loading.value = true;

      try {
        // 提交任务变更
        const response = await taskChangeService.submitTaskChange(
          submitData.value
        );

        // 检查接口返回：判断是否有有效的返回数据
        if (response !== undefined && response !== null) {
          // 记录变更日志
          await taskChangeService.addTaskChangeLog(
            Number(taskId.value),
            formData.changeReason,
            '任务变更'
          );

          ElMessage.success('任务变更成功');
          resetForm();
          loading.value = false;
          return true;
        } else {
          ElMessage.error('任务变更失败');
          loading.value = false;
          return false;
        }
      } catch (error) {
        console.error('任务变更失败:', error);
        ElMessage.error('任务变更失败');
        loading.value = false;
        return false;
      }
    }

    // 4. 同步方法
    function open(
      id: string | number,
      initialData: Partial<TaskChangeFormData>
    ) {
      taskId.value = id;
      visible.value = true;

      // 设置表单数据和原始数据
      Object.assign(formData, {
        changeReason: '',
        ...initialData,
      });
      Object.assign(originalData, initialData);
    }

    function close() {
      visible.value = false;
    }

    function resetForm() {
      Object.assign(formData, {
        changeReason: '',
        indeTaskName: '',
        indeTaskResponsible: 0,
        indeTaskType: 1,
        participants: [],
        planStartDate: '',
        planFinishDate: '',
        actualStartDate: '',
        workflowId: '',
        actualProgress: 0,
        duration: 0,
        autoProgressFlag: 0,
        indeTaskDesc: '',
        indeTaskAcceptCriteria: '',
      });
      Object.assign(originalData, formData);
    }

    // 加载选项数据（不包括用户列表，用户列表从外部传入）
    async function loadOptions() {
      try {
        const [taskTypes, workflows] = await Promise.all([
          taskChangeService.getTaskTypeList(),
          taskChangeService.getWorkflowDefinitionList(),
        ]);

        taskTypeOptions.value = taskTypes;
        workflowDefinitions.value = workflows;
      } catch (error) {
        console.error('加载选项数据失败:', error);
      }
    }

    // 设置外部用户列表
    function setUserList(users: any[]) {
      externalUserList.value = users;
    }

    return {
      // 状态
      visible,
      loading,
      taskId,
      formData,
      originalData,
      formRules,
      userList: externalUserList, // 别名，保持组件接口一致
      taskTypeOptions,
      workflowDefinitions,

      // 计算属性
      isFormValid,
      submitData,

      // 方法
      open,
      close,
      resetForm,
      submitTaskChange,
      loadOptions,
      setUserList,
    };
  },
  {
    // 5. 持久化配置 - 不持久化表单数据和状态
    persist: {
      pick: ['taskTypeOptions', 'workflowDefinitions'],
    },
  }
);
