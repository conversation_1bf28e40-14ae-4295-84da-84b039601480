// 文件类型图标映射
export const FILE_TYPE_ICONS: Record<string, string> = {
  'pdf': '📄',
  'doc': '📝',
  'docx': '📝',
  'xls': '📊',
  'xlsx': '📊',
  'ppt': '📈',
  'pptx': '📈',
  'txt': '📄',
  'zip': '📦',
  'rar': '📦',
  'jpg': '🖼️',
  'jpeg': '🖼️',
  'png': '🖼️',
  'gif': '🖼️',
  'mp4': '🎥',
  'avi': '🎥',
  'mp3': '🎵',
  'wav': '🎵',
  'folder': '📁',
};

// 文件大小格式化
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
}

// 获取文件图标
export function getFileIcon(fileName: string, isFolder: boolean = false): string {
  if (isFolder) {
    return FILE_TYPE_ICONS.folder;
  }
  
  const extension = fileName.split('.').pop()?.toLowerCase() || '';
  return FILE_TYPE_ICONS[extension] || '📄';
}

// 获取文件扩展名
export function getFileExtension(fileName: string): string {
  return fileName.split('.').pop()?.toLowerCase() || '';
}

// 文档类型样式映射
export const DOCUMENT_TYPE_STYLES: Record<string, string> = {
  '管理类': '#409EFF',
  '技术类': '#67C23A',
  '合规类': '#E6A23C',
  '其他': '#909399',
};

// 获取文档类型样式
export function getDocumentTypeStyle(type: string): string {
  return DOCUMENT_TYPE_STYLES[type] || DOCUMENT_TYPE_STYLES['其他'];
} 