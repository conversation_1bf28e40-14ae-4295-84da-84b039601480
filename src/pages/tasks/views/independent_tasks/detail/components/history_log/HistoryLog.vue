<script setup lang="ts">
import { ref, onMounted, defineExpose, nextTick, useTemplateRef } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import { MoreFilled, Folder, Document } from '@element-plus/icons-vue';
import FileUpload from '@/components/biz/file_upload/FileUpload.vue';
import DocumentUploadDialog from '../file_list/DocumentUploadDialog.vue';
import { useHistoryLogStore, type DeliveryDocItem } from './HistoryLogStore';
import { useIndependentTaskDetailStore } from '../../IndependentTaskDetailStore';
import { formatFileSize } from './types';

// Store 实例
const historyLogStore = useHistoryLogStore();
const detailStore = useIndependentTaskDetailStore();
const route = useRoute();

// 响应式引用
const folderNameInput = useTemplateRef<any>('folderNameInput');
const fileUploadRef = useTemplateRef<any>('fileUploadRef');
const documentUploadDialogRef = useTemplateRef<any>('documentUploadDialogRef');
const showUploadDialog = ref(false);

// 表格列配置（按UI规范调整）
const columns = [
  {
    field: 'docName',
    label: '文件名称',
    width: '144',
    align: 'left',
  },
  {
    field: 'docType',
    label: '文件类型',
    width: '144',
    align: 'left',
  },
  {
    field: 'docSize',
    label: '大小',
    width: '144',
    align: 'left',
  },
  {
    field: 'createUserName',
    label: '创建人',
    width: '120',
    align: 'left',
  },
  {
    field: 'updateUserName',
    label: '更新人',
    width: '120',
    align: 'left',
  },
];

// 移除搜索、筛选、分页相关函数

// 点击文件名处理
function handleFileClick(row: DeliveryDocItem) {
  if (row.folderFlag === 1) {
    // 如果是文件夹，进入该文件夹
    historyLogStore.enterFolder(row);
  } else {
    // 如果是文件，可以在这里添加预览或下载逻辑
    console.log('点击文件:', row.docName);
  }
}

// 双击进入编辑模式（仅对新建的文件夹）
function handleDoubleClick(row: DeliveryDocItem) {
  if (row.indeTaskDeliveryDocId === -1 && row.folderFlag === 1) {
    // 新建的文件夹，进入编辑模式
    nextTick(() => {
      folderNameInput.value?.focus();
    });
  }
}

// 文件夹名称输入处理
function handleFolderNameInput(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    event.preventDefault(); // 阻止默认行为
    const input = event.target as HTMLInputElement;
    const folderName = input.value.trim();

    if (folderName) {
      // 先禁用输入框防止重复提交
      input.disabled = true;
      historyLogStore.confirmAddFolder(folderName);
    } else {
      historyLogStore.cancelAddFolder();
    }
  } else if (event.key === 'Escape') {
    historyLogStore.cancelAddFolder();
  }
}

// 文件夹名称失去焦点
function handleFolderNameBlur(event: FocusEvent) {
  const input = event.target as HTMLInputElement;
  // 如果输入框已被禁用，说明已经处理过，直接返回
  if (input.disabled) return;

  const folderName = input.value.trim();

  if (folderName) {
    historyLogStore.confirmAddFolder(folderName);
  } else {
    historyLogStore.cancelAddFolder();
  }
}

// 新增文件夹
function handleAddFolder() {
  historyLogStore.startAddFolder();
  nextTick(() => {
    folderNameInput.value?.focus();
  });
}

// 上传文档
function handleUpload() {
  documentUploadDialogRef.value?.open();
}

// 文件上传成功回调
function handleUploadSuccess(uploadData: any) {
  if (uploadData && uploadData.length > 0) {
    const fileData = uploadData[0];
    historyLogStore.addDocument({
      docName: fileData.fileName || fileData.docName,
      docKey: fileData.fileKey || fileData.docKey,
      docId: fileData.fileId || fileData.docId,
      folderFlag: 0,
    });
  }
}

// 文档上传成功回调
function handleDocumentUploadSuccess() {
  // 刷新交付文档列表
  historyLogStore.refresh();
}

// 删除文件或文件夹
function handleDelete(row: DeliveryDocItem) {
  if (!row.indeTaskDeliveryDocId || row.indeTaskDeliveryDocId === -1) {
    // 如果是临时新建的文件夹，直接取消
    historyLogStore.cancelAddFolder();
    return;
  }

  const itemType = row.folderFlag === 1 ? '文件夹' : '文件';
  ElMessageBox.confirm(`确定要删除该${itemType}"${row.docName}"吗？`, '提示', {
    confirmButtonText: '删除',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    historyLogStore.deleteFile(row.indeTaskDeliveryDocId!);
  });
}

// 处理下拉菜单命令
function handleCommand(command: string, row: DeliveryDocItem) {
  switch (command) {
    case 'delete':
      handleDelete(row);
      break;
    default:
      console.log('未知命令:', command);
  }
}



// 暴露给父组件的方法
function openUpload() {
  documentUploadDialogRef.value?.open();
}

function refreshData() {
  historyLogStore.refresh();
}

// 处理回退
function handleGoBack() {
  historyLogStore.goBack();
}

// 暴露方法
defineExpose({
  openUpload,
  refreshData,
  handleUpload,
  handleAddFolder,
  handleGoBack,
  historyLogStore,
});

// 初始化
onMounted(() => {
  const taskId = Number(route.params.id || detailStore.formData.indeTaskId);
  if (taskId) {
    historyLogStore.initialize(taskId);
  }
});
</script>

<template>
  <div class="history-log">
    <!-- 移除工具栏 -->

    <!-- 文档表格 -->
    <div class="table-container">
      <el-table
        v-loading="historyLogStore.loading"
        :data="historyLogStore.deliveryDocList"
        class="table document-table"
        row-key="indeTaskDeliveryDocId"
        stripe
        @row-dblclick="handleDoubleClick"
      >
        <!-- 选择列 -->
        <el-table-column type="selection" width="50" />

        <!-- 序号列 -->
        <el-table-column type="index" label="#" width="32" align="center" />

        <!-- 动态列 -->
        <template v-for="column in columns" :key="column.field">
          <!-- 文件名称列 -->
          <el-table-column
            v-if="column.field === 'docName'"
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div class="file-name-cell">
                <el-icon class="file-icon">
                  <Folder v-if="row.folderFlag === 1" />
                  <Document v-else />
                </el-icon>

                <!-- 正常显示模式 -->
                <span
                  v-if="!row.editing"
                  class="file-name"
                  :title="row.docName"
                  @click="handleFileClick(row)"
                >
                  {{ row.docName }}
                </span>

                <!-- 编辑模式（新建文件夹） -->
                <input
                  v-else
                  ref="folderNameInput"
                  type="text"
                  class="folder-name-input"
                  placeholder="请输入文件夹名称"
                  @keydown="handleFolderNameInput($event)"
                  @blur="handleFolderNameBlur($event)"
                />
              </div>
            </template>
          </el-table-column>

          <!-- 其他列 -->
          <el-table-column
            v-else
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <template v-if="column.field === 'docType'">
                <el-tag v-if="row.folderFlag === 1" size="small" type="info"
                  >文件夹</el-tag
                >
                <el-tag v-else-if="row.docType" size="small">{{
                  row.docType
                }}</el-tag>
              </template>
              <template v-else-if="column.field === 'docSize'">
                <span v-if="row.folderFlag === 1"></span>
                <span v-else>{{ formatFileSize(row.docSize || 0) }}</span>
              </template>
              <template v-else>
                {{ row[column.field] || '' }}
              </template>
            </template>
          </el-table-column>
        </template>

        <!-- 操作列 -->
        <el-table-column label="操作" width="48" align="center" fixed="right">
          <template #default="{ row }">
            <el-dropdown
              placement="bottom-end"
              @command="(command: string) => handleCommand(command, row)"
            >
              <div class="more-opt">
                <el-icon class="icon"><MoreFilled /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="dropdown-menu">
                  <el-dropdown-item command="delete">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 移除分页组件 -->

    <!-- 文件上传组件 -->
    <FileUpload ref="fileUploadRef" @success="handleUploadSuccess" />

    <!-- 文档上传弹窗 -->
    <DocumentUploadDialog
      ref="documentUploadDialogRef"
      v-model="showUploadDialog"
      :task-id="Number(route.params.id || detailStore.formData.indeTaskId)"
      doc-type="delivery"
      :parent-folder-id="historyLogStore.currentFolderId!"
      @success="handleDocumentUploadSuccess"
    />
  </div>
</template>

<style scoped lang="scss">
.history-log {
  height: 100%;
  display: flex;
  flex-direction: column;



  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 16px;

    .left {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-box {
        width: 240px;
      }
    }
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    border-radius: 6px;

    .document-table {
      height: 100%;

      .file-name-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        .file-icon {
          font-size: 18px;
          flex-shrink: 0;
          color: var(--el-color-primary);
        }

        .file-name {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
          color: var(--el-color-primary);

          &:hover {
            text-decoration: underline;
          }
        }

        .folder-name-input {
          flex: 1;
          border: 1px solid var(--el-border-color);
          border-radius: 4px;
          padding: 4px 8px;
          font-size: 14px;
          outline: none;

          &:focus {
            border-color: var(--el-color-primary);
            box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
          }
        }
      }

      .more-opt {
        cursor: pointer;
        .icon {
          font-size: 16px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }

  .pagination {
    margin-top: 16px;
    padding: 0 16px;
    display: flex;
    justify-content: flex-end;
  }
}

.dropdown-menu {
  min-width: 120px;
}
</style>
