<script setup lang="ts">
import { Back, Right } from '@element-plus/icons-vue';

interface Props {
  canGoBack?: boolean;
}

interface Events {
  (e: 'upload'): void;
  (e: 'add-folder'): void;
  (e: 'go-back'): void;
}

const props = withDefaults(defineProps<Props>(), {
  canGoBack: false,
});

const emits = defineEmits<Events>();

// 处理回退
function handleGoBack() {
  emits('go-back');
}

// 处理上传文档
function handleUpload() {
  emits('upload');
}

// 处理新建文件夹
function handleAddFolder() {
  emits('add-folder');
}
</script>

<template>
  <div class="delivery-doc-tab-actions">
    <!-- 操作按钮 -->
    <div class="actions-toolbar">
      <!-- 导航图标 -->
      <!-- 功能按钮 -->
      <el-button @click="handleUpload"> 上传文档 </el-button>
      <el-button @click="handleAddFolder"> 新建文件夹 </el-button>
      <div class="nav-icons">
        <el-icon
          :class="['nav-icon', { disabled: !props.canGoBack }]"
          @click="props.canGoBack && handleGoBack()"
        >
          <Back />
        </el-icon>
        <el-icon class="nav-icon disabled">
          <Right />
        </el-icon>
      </div>

    </div>
  </div>
</template>

<style scoped lang="scss">
.delivery-doc-tab-actions {
  .actions-toolbar {
    display: flex;
    align-items: center;

    .nav-icons {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-left: 16px;

      .nav-icon {
        font-size: 16px;
        cursor: pointer;
        color: #606266;
        transition: color 0.3s;

        &:hover:not(.disabled) {
          color: #409eff;
        }

        &.disabled {
          color: #c0c4cc;
          cursor: not-allowed;
        }
      }
    }

    .el-divider {
      margin: 0 8px;
      height: 24px;
    }
  }
}
</style>
