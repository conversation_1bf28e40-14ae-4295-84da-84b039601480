import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import {
  getIndeTaskDeliveryDocPage,
  addIndeTaskDeliveryDoc,
  deleteIndeTaskDeliveryDoc,
  type IndeTaskDeliveryDocResponse,
  type IndeTaskDeliveryDocAddRequest,
  type IndeTaskDeliveryDocQueryRequest,
  type IndeTaskDeliveryDocDeleteRequest,
} from '../../services/DeliveryDocService';
import type { PageResponse } from '../../types';

// 文件项类型定义
export interface DeliveryDocItem extends IndeTaskDeliveryDocResponse {
  // 扩展属性
  editing?: boolean; // 是否处于编辑状态
}

// 导航路径项
export interface BreadcrumbItem {
  id: number | null; // null 表示根目录
  name: string;
  parentId?: number | null;
}

export const useHistoryLogStore = defineStore('history-log-store', () => {
  // 1. 响应式状态定义
  const loading = ref(false);
  const deliveryDocList = ref<DeliveryDocItem[]>([]);
  const searchKeyword = ref('');
  
  // 分页状态
  const currentPage = ref(1);
  const pageSize = ref(20);
  const total = ref(0);
  
  // 当前目录ID（null表示根目录）
  const currentFolderId = ref<number | null>(null);
  
  // 当前任务ID
  const currentTaskId = ref<number | null>(null);
  
  // 导航路径
  const breadcrumbs = ref<BreadcrumbItem[]>([
    { id: null, name: '根目录' }
  ]);
  
  // 临时新增的文件夹项
  const newFolderItem = ref<DeliveryDocItem | null>(null);

  // 2. 计算属性
  const hasMore = computed(() => {
    return currentPage.value * pageSize.value < total.value;
  });

  const canGoBack = computed(() => {
    return breadcrumbs.value.length > 0;
  });

  // 3. 异步方法
  
  /**
   * 初始化 - 设置当前任务ID
   */
  function initialize(taskId: number) {
    currentTaskId.value = taskId;
    resetToRoot();
    loadDeliveryDocList();
  }

  /**
   * 加载交付文档列表
   */
  async function loadDeliveryDocList() {
    if (!currentTaskId.value) return;
    
    loading.value = true;
    try {
      const queryParams: IndeTaskDeliveryDocQueryRequest = {
        indeTaskId: currentTaskId.value,
        parentIndeTaskDeliveryDocId: currentFolderId.value ?? undefined,
        ...(searchKeyword.value && { docName: searchKeyword.value }),
      };

      const response: PageResponse<IndeTaskDeliveryDocResponse> = await getIndeTaskDeliveryDocPage(
        currentPage.value,
        pageSize.value,
        queryParams
      );

      // 如果是第一页，替换数据；否则追加数据
      if (currentPage.value === 1) {
        deliveryDocList.value = response.list.map(item => ({ ...item }));
      } else {
        deliveryDocList.value = [...deliveryDocList.value, ...response.list.map(item => ({ ...item }))];
      }
      
      total.value = response.total || 0;
    } catch (error) {
      console.error('加载交付文档列表失败:', error);
      ElMessage.error('加载交付文档列表失败');
    } finally {
      loading.value = false;
    }
  }

  /**
   * 加载更多数据
   */
  async function loadMore() {
    if (loading.value || !hasMore.value) return;
    
    currentPage.value += 1;
    await loadDeliveryDocList();
  }

  /**
   * 搜索文件
   */
  function searchFiles(keyword: string) {
    searchKeyword.value = keyword;
    currentPage.value = 1;
    loadDeliveryDocList();
  }

  /**
   * 进入文件夹
   */
  function enterFolder(folder: DeliveryDocItem) {
    if (folder.folderFlag !== 1 || !folder.indeTaskDeliveryDocId) return;
    
    currentFolderId.value = folder.indeTaskDeliveryDocId;
    
    // 添加到导航路径
    breadcrumbs.value.push({
      id: folder.indeTaskDeliveryDocId,
      name: folder.docName || '未命名文件夹',
      parentId: currentFolderId.value,
    });
    
    // 重置分页并加载新目录的内容
    currentPage.value = 1;
    searchKeyword.value = '';
    loadDeliveryDocList();
  }

  /**
   * 回退到上级目录
   */
  function goBack() {
    if (!canGoBack.value) return;
    
    // 移除当前路径
    breadcrumbs.value.pop();
    
    // 设置新的当前目录ID
    if (breadcrumbs.value.length > 0) {
      const newCurrent = breadcrumbs.value[breadcrumbs.value.length - 1];
      currentFolderId.value = newCurrent.id;
    } else {
      // 回到根目录
      currentFolderId.value = null;
    }
    
    // 重置分页并加载新目录的内容
    currentPage.value = 1;
    searchKeyword.value = '';
    loadDeliveryDocList();
  }

  /**
   * 导航到指定路径
   */
  function navigateTo(breadcrumbIndex: number) {
    if (breadcrumbIndex < 0 || breadcrumbIndex >= breadcrumbs.value.length) return;
    
    // 截取导航路径
    breadcrumbs.value = breadcrumbs.value.slice(0, breadcrumbIndex + 1);
    
    // 设置当前目录ID
    const target = breadcrumbs.value[breadcrumbIndex];
    currentFolderId.value = target.id;
    
    // 重置分页并加载新目录的内容
    currentPage.value = 1;
    searchKeyword.value = '';
    loadDeliveryDocList();
  }

  /**
   * 重置到根目录
   */
  function resetToRoot() {
    currentFolderId.value = null;
    breadcrumbs.value = [];
    currentPage.value = 1;
    searchKeyword.value = '';
  }

  /**
   * 开始新增文件夹
   */
  function startAddFolder() {
    // 如果已经有正在编辑的新文件夹，先移除
    if (newFolderItem.value) {
      cancelAddFolder();
    }
    
    // 创建新的文件夹项
    newFolderItem.value = {
      indeTaskDeliveryDocId: -1, // 临时ID
      docName: '',
      folderFlag: 1,
      parentIndeTaskDeliveryDocId: currentFolderId.value ?? undefined,
      indeTaskId: currentTaskId.value ?? undefined,
      editing: true,
    };
    
    // 添加到列表顶部
    if (newFolderItem.value) {
      deliveryDocList.value.unshift(newFolderItem.value);
    }
  }

  /**
   * 确认新增文件夹
   */
  // 标记是否正在提交
  const isSubmitting = ref(false);

  async function confirmAddFolder(folderName: string) {
    // 如果正在提交或没有文件夹名称或没有任务ID，直接返回
    if (isSubmitting.value || !folderName.trim() || !currentTaskId.value) {
      cancelAddFolder();
      return;
    }
    
    // 标记为正在提交，防止重复请求
    isSubmitting.value = true;
    
    try {
      const addRequest: IndeTaskDeliveryDocAddRequest = {
        docName: folderName.trim(),
        folderFlag: 1,
        parentIndeTaskDeliveryDocId: currentFolderId.value ?? undefined,
        docId: 0, // 文件夹没有文件ID
        docKey: '', // 文件夹没有文件Key
      };
      
      // 使用正确的接口签名
      await addIndeTaskDeliveryDoc(currentTaskId.value, addRequest as any);
      
      ElMessage.success('创建文件夹成功');
      
      // 移除临时项
      if (newFolderItem.value) {
        const index = deliveryDocList.value.findIndex(item => item.indeTaskDeliveryDocId === -1);
        if (index !== -1) {
          deliveryDocList.value.splice(index, 1);
        }
        newFolderItem.value = null;
      }
      
      // 重新加载列表
      currentPage.value = 1;
      loadDeliveryDocList();
    } catch (error) {
      console.error('创建文件夹失败:', error);
      ElMessage.error('创建文件夹失败');
      cancelAddFolder();
    } finally {
      // 无论成功还是失败，都重置提交状态
      isSubmitting.value = false;
    }
  }

  /**
   * 取消新增文件夹
   */
  function cancelAddFolder() {
    if (newFolderItem.value) {
      const index = deliveryDocList.value.findIndex(item => item.indeTaskDeliveryDocId === -1);
      if (index !== -1) {
        deliveryDocList.value.splice(index, 1);
      }
      newFolderItem.value = null;
    }
  }

  /**
   * 新增文档
   */
  async function addDocument(docData: IndeTaskDeliveryDocAddRequest) {
    if (!currentTaskId.value) return;
    
    try {
      await addIndeTaskDeliveryDoc(currentTaskId.value, {
        ...docData,
        parentIndeTaskDeliveryDocId: currentFolderId.value ?? undefined,
      } as any);
      
      ElMessage.success('添加文档成功');
      
      // 重新加载列表
      currentPage.value = 1;
      loadDeliveryDocList();
    } catch (error) {
      console.error('添加文档失败:', error);
      ElMessage.error('添加文档失败');
      throw error;
    }
  }

  /**
   * 删除文件或文件夹
   */
  async function deleteFile(fileId: number) {
    try {
      const deleteRequest: IndeTaskDeliveryDocDeleteRequest = {
        indeTaskDeliveryDocId: fileId,
      };
      
      await deleteIndeTaskDeliveryDoc(deleteRequest);
      
      ElMessage.success('删除成功');
      
      // 从列表中移除
      const index = deliveryDocList.value.findIndex(item => item.indeTaskDeliveryDocId === fileId);
      if (index !== -1) {
        deliveryDocList.value.splice(index, 1);
        total.value = Math.max(0, total.value - 1);
      }
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
      throw error;
    }
  }

  /**
   * 刷新当前目录
   */
  function refresh() {
    currentPage.value = 1;
    loadDeliveryDocList();
  }

  return {
    // 状态
    loading,
    deliveryDocList,
    searchKeyword,
    currentPage,
    pageSize,
    total,
    currentFolderId,
    currentTaskId,
    breadcrumbs,
    newFolderItem,
    isSubmitting,
    
    // 计算属性
    hasMore,
    canGoBack,
    
    // 方法
    initialize,
    loadDeliveryDocList,
    loadMore,
    searchFiles,
    enterFolder,
    goBack,
    navigateTo,
    resetToRoot,
    startAddFolder,
    confirmAddFolder,
    cancelAddFolder,
    addDocument,
    deleteFile,
    refresh,
  };
});