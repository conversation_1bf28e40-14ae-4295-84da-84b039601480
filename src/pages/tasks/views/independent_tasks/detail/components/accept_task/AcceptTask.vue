<script setup lang="ts">
import { computed, useTemplateRef } from 'vue';
import { storeToRefs } from 'pinia';
import { useAcceptTaskStore } from './AcceptTaskStore';
import type { PropsType, EventsType } from './types';
import FileUpload from '@/components/biz/file_upload/FileUpload.vue';
import BtInput from '@/components/non_biz/bt_input/BtInput.vue';
import { Document } from '@element-plus/icons-vue';
import type { ElForm } from 'element-plus';

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: false,
  title: '接受独立任务',
});

const emits = defineEmits<EventsType>();

// Store使用
const store = useAcceptTaskStore();
const { loading, formData, formRules } = storeToRefs(store);

// 表单引用
const ruleFormRef = useTemplateRef<InstanceType<typeof ElForm>>('ruleFormRef');

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => {
    emits('update:modelValue', val);
    emits('change', val);
  },
});

// 方法定义
function open(taskData: any, userList?: any[]) {
  store.open(taskData, userList);
  dialogVisible.value = true;
}

function close() {
  store.close();
  dialogVisible.value = false;
}

function handleClosed() {
  ruleFormRef.value?.resetFields();
  store.resetForm();
}

async function submitForm() {
  try {
    await ruleFormRef.value?.validate();
    const success = await store.acceptTask();
    if (success) {
      emits('success');
      close();
    }
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

defineExpose({ open, close });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800px"
    :before-close="close"
    @closed="handleClosed"
  >
    <el-form
      ref="ruleFormRef"
      class="form"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="任务名称">
        <el-input
          v-model="formData.taskName"
          placeholder=""
          disabled
        ></el-input>
      </el-form-item>

      <div class="row">
        <el-form-item label="负责人">
          <el-input
            v-model="formData.responsibleName"
            placeholder=""
            disabled
            class="select"
          ></el-input>
        </el-form-item>
        <el-form-item label="任务类型">
          <el-input
            v-model="formData.taskType"
            placeholder=""
            disabled
            class="select"
          ></el-input>
        </el-form-item>
      </div>

      <el-form-item label="参与人">
        <el-select
          v-model="formData.participants"
          multiple
          placeholder=""
          disabled
          style="width: 100%"
          collapse-tags
          collapse-tags-tooltip
        >
          <el-option
            v-for="user in store.userList"
            :key="user.userId"
            :label="user.userName"
            :value="user.userId"
          />
        </el-select>
      </el-form-item>

      <div class="row">
        <el-form-item label="计划开始时间">
          <el-input
            v-model="formData.startDate"
            placeholder=""
            disabled
            class="date-select"
          ></el-input>
        </el-form-item>
        <el-form-item label="计划完成时间">
          <el-input
            v-model="formData.endDate"
            placeholder=""
            disabled
            class="date-select"
          ></el-input>
        </el-form-item>
      </div>

      <el-form-item label="附件" v-if="formData.attachments.length > 0">
        <div class="attachment-display">
          <div
            v-for="(file, index) in formData.attachments"
            :key="index"
            class="attachment-item"
          >
            <el-icon><Document /></el-icon>
            <span>{{ file.name }}</span>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="接受任务意见" prop="acceptComment">
        <BtInput
          v-model="formData.acceptComment"
          type="textarea"
          :rows="3"
          maxlength="300"
          show-word-limit
          placeholder="请输入"
        />
      </el-form-item>

      <el-form-item label="附件">
        <FileUpload
          v-model="formData.newAttachments"
          class="upload-file"
          drag
          multiple
        >
          <div class="title">选择文件</div>
          <div class="tip">
            支持：.rar .zip .doc .docx .pdf .jpg，且单个文件不能超过5M
          </div>
        </FileUpload>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submitForm"
          >接受</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.form {
  .row {
    display: flex;
    justify-content: space-between;
  }
  .select {
    width: 216px;
  }
  .date-select {
    width: 216px;
  }
  .attachment-display {
    .attachment-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 8px;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;

      .el-icon {
        margin-right: 8px;
        color: var(--el-color-primary);
      }
    }
  }
}
</style>
