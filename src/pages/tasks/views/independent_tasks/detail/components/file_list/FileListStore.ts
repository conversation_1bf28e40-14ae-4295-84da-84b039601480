import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import {
  getIndeTaskProcessDocPage,
  addIndeTaskProcessDoc,
  deleteIndeTaskProcessDoc,
  type IndeTaskProcessDocResponse,
  type IndeTaskProcessDocAddRequest,
  type IndeTaskProcessDocQueryRequest,
  type IndeTaskProcessDocDeleteRequest,
} from '../../services/ProcessDocService';
import type { PageResponse } from '../../types';

// 文件项类型定义
export interface FileItem extends IndeTaskProcessDocResponse {
  // 扩展属性
  editing?: boolean; // 是否处于编辑状态
}

// 导航路径项
export interface BreadcrumbItem {
  id: number | null; // null 表示根目录
  name: string;
  parentId?: number | null;
}

export const useFileListStore = defineStore('file-list-store', () => {
  // 1. 响应式状态定义
  const loading = ref(false);
  const fileList = ref<FileItem[]>([]);
  const searchKeyword = ref('');

  // 移除分页状态，一次性加载所有数据

  // 当前目录ID（null表示根目录）
  const currentFolderId = ref<number | null>(null);

  // 当前任务ID
  const currentTaskId = ref<number | null>(null);

  // 导航路径
  const breadcrumbs = ref<BreadcrumbItem[]>([{ id: null, name: '根目录' }]);

  // 临时新增的文件夹项
  const newFolderItem = ref<FileItem | null>(null);

  // 2. 计算属性
  const canGoBack = computed(() => {
    return breadcrumbs.value.length > 0;
  });

  // 3. 异步方法

  /**
   * 初始化 - 设置当前任务ID
   */
  function initialize(taskId: number) {
    currentTaskId.value = taskId;
    resetToRoot();
    loadFileList();
  }

  /**
   * 加载文件列表
   */
  async function loadFileList() {
    if (!currentTaskId.value) return;

    loading.value = true;
    try {
      const queryParams: IndeTaskProcessDocQueryRequest = {
        indeTaskId: currentTaskId.value,
        parentIndeTaskProcessDocId: currentFolderId.value ?? undefined,
        ...(searchKeyword.value && { docName: searchKeyword.value }),
      };

      const response: PageResponse<IndeTaskProcessDocResponse> =
        await getIndeTaskProcessDocPage(1, 1000, queryParams);

      // 一次性加载所有数据
      fileList.value = response.list.map((item) => ({ ...item }));
    } catch (error) {
      console.error('加载文件列表失败:', error);
      ElMessage.error('加载文件列表失败');
    } finally {
      loading.value = false;
    }
  }

  /**
   * 搜索文件
   */
  function searchFiles(keyword: string) {
    searchKeyword.value = keyword;
    loadFileList();
  }

  /**
   * 进入文件夹
   */
  function enterFolder(folder: FileItem) {
    if (folder.folderFlag !== 1 || !folder.indeTaskProcessDocId) return;

    currentFolderId.value = folder.indeTaskProcessDocId;

    // 添加到导航路径
    breadcrumbs.value.push({
      id: folder.indeTaskProcessDocId,
      name: folder.docName || '未命名文件夹',
      parentId: currentFolderId.value,
    });

    // 重置搜索并加载新目录的内容
    searchKeyword.value = '';
    loadFileList();
  }

  /**
   * 回退到上级目录
   */
  function goBack() {
    if (!canGoBack.value) return;

    // 移除当前路径
    breadcrumbs.value.pop();

    // 设置新的当前目录ID
    if (breadcrumbs.value.length > 0) {
      const newCurrent = breadcrumbs.value[breadcrumbs.value.length - 1];
      currentFolderId.value = newCurrent.id;
    } else {
      // 回到根目录
      currentFolderId.value = null;
    }

    // 重置搜索并加载新目录的内容
    searchKeyword.value = '';
    loadFileList();
  }

  /**
   * 导航到指定路径
   */
  function navigateTo(breadcrumbIndex: number) {
    if (breadcrumbIndex < 0 || breadcrumbIndex >= breadcrumbs.value.length)
      return;

    // 截取导航路径
    breadcrumbs.value = breadcrumbs.value.slice(0, breadcrumbIndex + 1);

    // 设置当前目录ID
    const target = breadcrumbs.value[breadcrumbIndex];
    currentFolderId.value = target.id;

    // 重置搜索并加载新目录的内容
    searchKeyword.value = '';
    loadFileList();
  }

  /**
   * 重置到根目录
   */
  function resetToRoot() {
    currentFolderId.value = null;
    breadcrumbs.value = [];
    searchKeyword.value = '';
  }

  /**
   * 开始新增文件夹
   */
  function startAddFolder() {
    // 如果已经有正在编辑的新文件夹，先移除
    if (newFolderItem.value) {
      cancelAddFolder();
    }

    // 创建新的文件夹项
    newFolderItem.value = {
      indeTaskProcessDocId: -1, // 临时ID
      docName: '',
      folderFlag: 1,
      parentIndeTaskProcessDocId: currentFolderId.value ?? undefined,
      indeTaskId: currentTaskId.value ?? undefined,
      editing: true,
    };

    // 添加到列表顶部
    if (newFolderItem.value) {
      fileList.value.unshift(newFolderItem.value);
    }
  }

  /**
   * 确认新增文件夹
   */
  // 标记是否正在提交
  const isSubmitting = ref(false);

  async function confirmAddFolder(folderName: string) {
    // 如果正在提交或没有文件夹名称或没有任务ID，直接返回
    if (isSubmitting.value || !folderName.trim() || !currentTaskId.value) {
      cancelAddFolder();
      return;
    }

    // 标记为正在提交，防止重复请求
    isSubmitting.value = true;

    try {
      const addRequest: IndeTaskProcessDocAddRequest = {
        docName: folderName.trim(),
        folderFlag: 1,
        parentIndeTaskProcessDocId: currentFolderId.value ?? undefined,
        docId: 0, // 文件夹没有文件ID
        docKey: '', // 文件夹没有文件Key
      };

      // 使用正确的接口签名
      await addIndeTaskProcessDoc(currentTaskId.value, addRequest as any);

      ElMessage.success('创建文件夹成功');

      // 移除临时项
      if (newFolderItem.value) {
        const index = fileList.value.findIndex(
          (item) => item.indeTaskProcessDocId === -1
        );
        if (index !== -1) {
          fileList.value.splice(index, 1);
        }
        newFolderItem.value = null;
      }

      loadFileList();
    } catch (error) {
      console.error('创建文件夹失败:', error);
      ElMessage.error('创建文件夹失败');
      cancelAddFolder();
    } finally {
      // 无论成功还是失败，都重置提交状态
      isSubmitting.value = false;
    }
  }

  /**
   * 取消新增文件夹
   */
  function cancelAddFolder() {
    if (newFolderItem.value) {
      const index = fileList.value.findIndex(
        (item) => item.indeTaskProcessDocId === -1
      );
      if (index !== -1) {
        fileList.value.splice(index, 1);
      }
      newFolderItem.value = null;
    }
  }

  /**
   * 新增文档
   */
  async function addDocument(docData: IndeTaskProcessDocAddRequest) {
    if (!currentTaskId.value) return;

    try {
      await addIndeTaskProcessDoc(currentTaskId.value, {
        ...docData,
        parentIndeTaskProcessDocId: currentFolderId.value ?? undefined,
      } as any);

      ElMessage.success('添加文档成功');

      // 重新加载列表
      loadFileList();
    } catch (error) {
      console.error('添加文档失败:', error);
      ElMessage.error('添加文档失败');
      throw error;
    }
  }

  /**
   * 删除文件或文件夹
   */
  async function deleteFile(fileId: number) {
    try {
      const deleteRequest: IndeTaskProcessDocDeleteRequest = {
        indeTaskProcessDocId: fileId,
      };

      await deleteIndeTaskProcessDoc(deleteRequest);

      ElMessage.success('删除成功');

      // 从列表中移除
      const index = fileList.value.findIndex(
        (item) => item.indeTaskProcessDocId === fileId
      );
      if (index !== -1) {
        fileList.value.splice(index, 1);
      }
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
      throw error;
    }
  }

  /**
   * 刷新当前目录
   */
  function refresh() {
    loadFileList();
  }

  return {
    // 状态
    loading,
    fileList,
    searchKeyword,
    currentFolderId,
    currentTaskId,
    breadcrumbs,
    newFolderItem,
    isSubmitting,

    // 计算属性
    canGoBack,

    // 方法
    initialize,
    loadFileList,
    searchFiles,
    enterFolder,
    goBack,
    navigateTo,
    resetToRoot,
    startAddFolder,
    confirmAddFolder,
    cancelAddFolder,
    addDocument,
    deleteFile,
    refresh,
  };
});
