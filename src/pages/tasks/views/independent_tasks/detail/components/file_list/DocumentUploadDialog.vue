<script setup lang="ts">
import { ref, computed, useTemplateRef } from 'vue';
import { ElMessage } from 'element-plus';
import { Upload } from '@element-plus/icons-vue';
import FileUpload from '@/components/biz/file_upload/FileUpload.vue';
import type { FileItem } from '@/components/biz/file_upload/types';
import {
  addIndeTaskProcessDoc,
  type IndeTaskProcessDocAddRequest,
} from '../../services/ProcessDocService';
import {
  addIndeTaskDeliveryDoc,
  type IndeTaskDeliveryDocAddRequest,
} from '../../services/DeliveryDocService';

// 扩展文件项类型，包含上传后的文档ID
interface UploadedFileItem extends FileItem {
  docId?: number;
  docName?: string;
  docKey?: string;
}

interface Props {
  modelValue: boolean;
  taskId: number;
  docType: 'process' | 'delivery'; // 文档类型：过程文档或交付文档
  parentFolderId?: number; // 父文件夹ID
}

interface Events {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void; // 上传成功事件
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  parentFolderId: undefined,
});

const emits = defineEmits<Events>();

// 响应式状态
const uploadedFiles = ref<UploadedFileItem[]>([]);
const uploading = ref(false);

// 文件上传组件引用
const fileUploadRef = useTemplateRef<any>('fileUploadRef');

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => {
    emits('update:modelValue', val);
  },
});

const dialogTitle = computed(() => {
  return props.docType === 'process' ? '上传过程文档' : '上传交付文档';
});

// 方法
function handleClose() {
  if (uploading.value) {
    ElMessage.warning('文档正在上传中，请等待完成');
    return;
  }
  dialogVisible.value = false;
}

function handleClosed() {
  // 重置状态
  uploadedFiles.value = [];
  uploading.value = false;
}

// 文件上传成功回调
function handleFileUploadChange(files: FileItem[]) {
  // 将FileItem转换为UploadedFileItem，处理字段映射
  uploadedFiles.value = files.map((file, index) => {
    // 根据实际API返回的数据结构进行映射
    // API返回的数据可能直接就包含了所需字段
    const apiData = file as any;

    const mappedFile: UploadedFileItem = {
      // 保持原始FileItem字段
      etag: file.etag || '',
      fileKey: file.fileKey || apiData.docKey || '',
      fileName: file.fileName || apiData.docName || `文件${index + 1}`,
      filePath: file.filePath || apiData.docPath || '',
      fileSize: file.fileSize || apiData.fileSize || 0,
      fileType: file.fileType || apiData.docType || '',

      // 扩展字段
      docId: apiData.docId || 0,
      docName: apiData.docName || file.fileName || `文件${index + 1}`,
      docKey: apiData.docKey || file.fileKey || '',
    };

    return mappedFile;
  });
}

// 确认上传
async function confirmUpload() {
  if (uploadedFiles.value.length === 0) {
    ElMessage.warning('请先选择要上传的文件');
    return;
  }

  uploading.value = true;

  try {
    // 遍历上传的文件，调用对应的接口
    for (const file of uploadedFiles.value) {
      if (props.docType === 'process') {
        // 上传过程文档
        const processDocData: IndeTaskProcessDocAddRequest = {
          docId: file.docId || 0,
          docKey: file.docKey || file.fileKey || '',
          docName: file.docName || file.fileName || '',
          folderFlag: 0, // 文件，不是文件夹
          parentIndeTaskProcessDocId: props.parentFolderId,
        };

        await addIndeTaskProcessDoc(props.taskId, processDocData);
      } else {
        // 上传交付文档
        const deliveryDocData: IndeTaskDeliveryDocAddRequest = {
          docId: file.docId || 0,
          docKey: file.docKey || file.fileKey || '',
          docName: file.docName || file.fileName || '',
          folderFlag: 0, // 文件，不是文件夹
          parentIndeTaskDeliveryDocId: props.parentFolderId,
        };

        await addIndeTaskDeliveryDoc(props.taskId, deliveryDocData);
      }
    }

    ElMessage.success(`${dialogTitle.value}成功`);
    emits('success');
    dialogVisible.value = false;
  } catch (error) {
    console.error('上传文档失败:', error);
    ElMessage.error(`${dialogTitle.value}失败`);
  } finally {
    uploading.value = false;
  }
}

// 格式化文件大小的工具函数
function formatFileSize(bytes: number | undefined | null): string {
  // 处理空值和无效值
  if (!bytes || bytes === 0 || isNaN(bytes)) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  // 确保 i 在有效范围内
  const sizeIndex = Math.min(i, sizes.length - 1);
  const size = parseFloat((bytes / Math.pow(k, sizeIndex)).toFixed(2));

  return `${size} ${sizes[sizeIndex]}`;
}

// 暴露方法给父组件
function open() {
  dialogVisible.value = true;
}

defineExpose({
  open,
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :before-close="handleClose"
    @closed="handleClosed"
  >
    <div class="upload-dialog-content">
      <div class="upload-section">
        <div class="upload-header">
          <el-icon class="upload-icon"><Upload /></el-icon>
          <h3>选择文件</h3>
          <p>
            支持：.rar .zip .doc .docx .pdf .jpg .png 等格式，单个文件不超过5M
          </p>
        </div>

        <FileUpload
          ref="fileUploadRef"
          v-model="uploadedFiles"
          @change="handleFileUploadChange"
          class="upload-area"
          drag
          multiple
        >
          <div class="title">选择文件</div>
          <div class="tip">
            支持：.rar .zip .doc .docx .pdf .jpg .png 等格式，单个文件不超过5M
          </div>
        </FileUpload>
      </div>

      <!-- 已选择的文件列表 -->
      <div v-if="uploadedFiles.length > 0" class="file-list-section">
        <h4>已选择的文件 ({{ uploadedFiles.length }})</h4>
        <div class="file-list">
          <div
            v-for="(file, index) in uploadedFiles"
            :key="index"
            class="file-item"
          >
            <div class="file-info">
              <span class="file-name">{{
                file.docName || file.fileName || '未知文件'
              }}</span>
              <span class="file-size">{{
                formatFileSize(file.fileSize || 0)
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="uploading">取消</el-button>
        <el-button
          type="primary"
          @click="confirmUpload"
          :loading="uploading"
          :disabled="uploadedFiles.length === 0"
        >
          {{ uploading ? '上传中...' : '确认上传' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.upload-dialog-content {
  padding-right: 32px;
  margin: 0 -24px;
  .upload-section {
    .upload-header {
      text-align: center;
      margin-bottom: 20px;

      .upload-icon {
        font-size: 48px;
        color: var(--el-color-primary);
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 500;
      }

      p {
        margin: 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }

    .upload-area {
      .title {
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        text-align: center;
        margin-bottom: 8px;
      }

      .tip {
        font-size: 14px;
        color: var(--el-text-color-regular);
        text-align: center;
      }
    }
  }

  .file-list-section {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color-light);

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 500;
    }

    .file-list {
      max-height: 200px;
      overflow-y: auto;

      .file-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border: 1px solid var(--el-border-color);
        border-radius: 4px;
        margin-bottom: 8px;
        background-color: var(--el-fill-color-light);

        .file-info {
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .file-name {
            font-size: 14px;
            color: var(--el-text-color-primary);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 300px;
          }

          .file-size {
            font-size: 12px;
            color: var(--el-text-color-regular);
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
