<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { useIndependentTaskDetailStore } from '../../../IndependentTaskDetailStore';
import { addIndeTaskRisk } from '../../../services/RiskService';
import FileUpload from '@/components/biz/file_upload/FileUpload.vue';
import BtPicker from '@/components/non_biz/bt_picker/BtPicker.vue';
import { useRiskStore } from '../RiskStore';
import { storeToRefs } from 'pinia';

// 定义 props
interface Props {
  modelValue: boolean;
}

// 定义 emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 获取store实例
const store = useIndependentTaskDetailStore();
const riskStore = useRiskStore();

// 从store中获取用户列表和风险类型
const { userList, riskTypeOptions } = storeToRefs(riskStore);

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive({
  indeTaskRiskName: '',
  remark: '',
  indeTaskResponsible: undefined as number | undefined,
  closingTime: '',
  indeTaskRiskType: undefined as string | number | undefined,
  indeTaskRiskLevel: 3,
  disCloseFlag: 0,
  publicReplyFlag: 0,
  riskDocs: [] as any[],
});

// 文件列表（用于显示）
const fileList = ref<any[]>([]);

// 表单验证规则
const rules: FormRules = {
  indeTaskRiskName: [
    { required: true, message: '请输入风险名称', trigger: 'blur' },
  ],
  indeTaskResponsible: [
    { required: true, message: '请选择负责人', trigger: 'change' },
  ],
  closingTime: [
    { required: true, message: '请选择截止日期', trigger: 'change' },
  ],
  indeTaskRiskType: [
    { required: true, message: '请选择风险类型', trigger: 'change' },
  ],
  indeTaskRiskLevel: [
    { required: true, message: '请选择风险级别', trigger: 'change' },
  ],
};

// 风险级别选项
const riskLevelOptions = [
  { label: '高', value: 1 },
  { label: '中', value: 2 },
  { label: '低', value: 3 },
];

// 提交状态
const submitting = ref(false);

// 显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

// 重置表单
function resetForm() {
  formRef.value?.resetFields();
  Object.assign(formData, {
    indeTaskRiskName: '',
    remark: '',
    indeTaskResponsible: undefined,
    closingTime: '',
    indeTaskRiskType: undefined,
    indeTaskRiskLevel: 3,
    disCloseFlag: 0,
    publicReplyFlag: 0,
    riskDocs: [],
  });
  fileList.value = [];
}

// 处理文件上传
function handleFileUpload(files: any[]) {
  fileList.value = files;
  formData.riskDocs = files.map((file: any) => ({
    docId: file.docId || 0, // 如果后端上传响应没有docId，使用0作为默认值
    docKey: file.fileKey || '',
    docName: file.fileName || '',
    folderFlag: 0,
    parentIndeTaskRiskDocId: undefined,
  }));
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    submitting.value = true;

    // 构造请求数据
    const requestData = {
      indeTaskId: Number(store.formData.indeTaskId),
      indeTaskRiskName: formData.indeTaskRiskName,
      remark: formData.remark,
      indeTaskResponsible: formData.indeTaskResponsible,
      closingTime: formData.closingTime,
      indeTaskRiskType: formData.indeTaskRiskType!,
      indeTaskRiskLevel: formData.indeTaskRiskLevel,
      disCloseFlag: formData.disCloseFlag,
      publicReplyFlag: formData.publicReplyFlag,
      riskDocs: formData.riskDocs,
    };

    await addIndeTaskRisk(requestData);

    ElMessage.success('新建风险成功');
    emit('success');
    resetForm();
  } catch (error) {
    console.error('新建风险失败:', error);
    ElMessage.error('新建风险失败');
  } finally {
    submitting.value = false;
  }
}

// 取消操作
function handleCancel() {
  resetForm();
  emit('update:modelValue', false);
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化数据
  riskStore.initialize();
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="新建项目风险"
    width="800px"
    :before-close="handleCancel"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="136px"
      label-position="right"
      class="form"
    >
      <!-- 风险名称 -->
      <el-form-item label="风险名称" prop="indeTaskRiskName">
        <el-input
          v-model="formData.indeTaskRiskName"
          placeholder="请输入"
          maxlength="100"
          show-word-limit
          style="width: 100%"
        />
      </el-form-item>

      <!-- 风险说明 -->
      <el-form-item label="风险说明" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="4"
          placeholder="请输入"
          maxlength="500"
          show-word-limit
          style="width: 100%"
        />
      </el-form-item>

      <!-- 负责人和截止日期 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人" prop="indeTaskResponsible">
            <BtPicker
              class="select"
              title="负责人"
              :list="
                userList.map((item) => ({
                  label: item.userName,
                  value: item.userId,
                }))
              "
              showSearch
              v-model="formData.indeTaskResponsible"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="截止日期" prop="closingTime">
            <el-date-picker
              v-model="formData.closingTime"
              type="datetime"
              placeholder="请选择"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 风险类型和风险级别 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="风险类型" prop="indeTaskRiskType">
            <el-select
              v-model="formData.indeTaskRiskType"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="option in riskTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="风险级别" prop="indeTaskRiskLevel">
            <el-select
              v-model="formData.indeTaskRiskLevel"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="option in riskLevelOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 是否公开 -->
      <el-form-item label="是否公开" prop="disCloseFlag">
        <div class="switch-container">
          <el-switch
            v-model="formData.disCloseFlag"
            :active-value="1"
            :inactive-value="0"
            active-text="是"
            inactive-text="否"
          />
          <span class="switch-tip"
            >注：公开问题所有项目成员课件；私有问题仅项目经理和负责人可见。</span
          >
        </div>
      </el-form-item>

      <!-- 公开回复 -->
      <el-form-item label="公开回复" prop="publicReplyFlag">
        <div class="switch-container">
          <el-switch
            v-model="formData.publicReplyFlag"
            :active-value="1"
            :inactive-value="0"
            active-text="是"
            inactive-text="否"
          />
          <span class="switch-tip">注：开启后所有项目成员都可以回复问题</span>
        </div>
      </el-form-item>

      <!-- 附件 -->
      <el-form-item label="附件">
        <div class="upload-container">
          <FileUpload
            v-model="fileList"
            @change="handleFileUpload"
            accept=".rar,.zip,.doc,.docx,.pdf,.jpg"
            :max-size="5"
            :max-count="10"
            multiple
          />
          <div class="upload-tips">
            支持：.rar .zip .doc .docx .pdf .jpg，且单个文件不能超过5M
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.form {
  padding-right: 32px;
  margin: 0 -24px;

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__content) {
    width: 216px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.upload-container {
  width: 100%;

  .upload-tips {
    font-size: 12px;
    color: #666;
    margin-top: 8px;
  }
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 12px;

  .switch-tip {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
  }
}

.select {
  width: 100%;
}
</style>
