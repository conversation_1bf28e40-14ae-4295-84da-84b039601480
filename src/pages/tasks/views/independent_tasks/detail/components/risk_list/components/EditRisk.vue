<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import BtPicker from '@/components/non_biz/bt_picker/BtPicker.vue';
import { editIndeTaskRisk } from '../../../services/RiskService';
import type { IndeTaskRiskResponse } from '../../../services/RiskService';
import { useRiskStore } from '../RiskStore';
import { storeToRefs } from 'pinia';

// 定义 props
interface Props {
  modelValue: boolean;
  risk: IndeTaskRiskResponse | null;
  indeTaskId: number;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 获取store实例
const riskStore = useRiskStore();

// 从store中获取用户列表和风险类型
const { userList, riskTypeOptions } = storeToRefs(riskStore);

// 定义 emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive({
  indeTaskRiskId: 0,
  indeTaskId: props.indeTaskId || 0,
  indeTaskRiskName: '',
  remark: '',
  indeTaskRiskLevel: 3,
  indeTaskRiskType: undefined as string | number | undefined,
  indeTaskRiskStatus: 1,
  indeTaskResponsible: undefined as number | undefined,
  indeTaskResponsibleName: '',
  // Other properties may be here
});

// 表单验证规则
const rules: FormRules = {
  indeTaskRiskName: [
    { required: true, message: '请输入风险名称', trigger: 'blur' },
  ],
  indeTaskRiskLevel: [
    { required: true, message: '请选择风险级别', trigger: 'change' },
  ],
  indeTaskRiskType: [
    { required: true, message: '请选择风险类型', trigger: 'change' },
  ],
  indeTaskResponsible: [
    { required: true, message: '请选择责任人', trigger: 'change' },
  ],
};

// 风险级别选项
const riskLevelOptions = [
  { label: '高', value: 1 },
  { label: '中', value: 2 },
  { label: '低', value: 3 },
];

// 风险状态选项
const riskStatusOptions = [
  { label: '未处理', value: 1 },
  { label: '处理中', value: 2 },
  { label: '已处理', value: 3 },
  { label: '已关闭', value: 4 },
];

// 提交状态
const submitting = ref(false);

// 显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

// 监听风险数据变化，更新表单
watch(
  () => props.risk,
  (newRisk) => {
    if (newRisk) {
      Object.assign(formData, {
        indeTaskRiskId: newRisk.indeTaskRiskId,
        indeTaskRiskName: newRisk.indeTaskRiskName,
        remark: newRisk.remark || '',
        indeTaskRiskLevel: newRisk.indeTaskRiskLevel,
        indeTaskRiskType: newRisk.indeTaskRiskType,
        indeTaskRiskStatus: newRisk.indeTaskRiskStatus,
        indeTaskResponsible: newRisk.indeTaskResponsible,
        indeTaskResponsibleName: newRisk.indeTaskResponsibleName || '',
      });
    }
  },
  { immediate: true }
);

// 重置表单
function resetForm() {
  formRef.value?.resetFields();
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    submitting.value = true;

    // 构造更新数据
    const updateData = {
      indeTaskRiskId: Number(formData.indeTaskRiskId),
      indeTaskRiskName: formData.indeTaskRiskName,
      remark: formData.remark,
      indeTaskRiskLevel: formData.indeTaskRiskLevel,
      indeTaskRiskType: formData.indeTaskRiskType,
      indeTaskRiskStatus: formData.indeTaskRiskStatus,
      indeTaskResponsible: formData.indeTaskResponsible,
    };

    await editIndeTaskRisk(updateData);

    ElMessage.success('更新风险成功');
    emit('success');
    emit('update:modelValue', false);
  } catch (error) {
    console.error('更新风险失败:', error);
    ElMessage.error('更新风险失败');
  } finally {
    submitting.value = false;
  }
}

// 取消操作
function handleCancel() {
  resetForm();
  emit('update:modelValue', false);
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化数据
  riskStore.initialize();
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑风险"
    width="800px"
    :before-close="handleCancel"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="136px"
      label-position="right"
      class="form"
    >
      <el-form-item label="风险名称" prop="indeTaskRiskName">
        <el-input
          v-model="formData.indeTaskRiskName"
          placeholder="请输入风险名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="风险状态" prop="indeTaskRiskStatus">
        <el-select
          v-model="formData.indeTaskRiskStatus"
          placeholder="请选择风险状态"
          style="width: 100%"
        >
          <el-option
            v-for="option in riskStatusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="风险类型" prop="indeTaskRiskType">
        <el-select
          v-model="formData.indeTaskRiskType"
          placeholder="请选择风险类型"
          style="width: 100%"
        >
          <el-option
            v-for="option in riskTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="风险级别" prop="indeTaskRiskLevel">
        <el-select
          v-model="formData.indeTaskRiskLevel"
          placeholder="请选择风险级别"
          style="width: 100%"
        >
          <el-option
            v-for="option in riskLevelOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="责任人" prop="indeTaskResponsible">
        <BtPicker
          class="select"
          title="责任人"
          :list="
            userList.map((item) => ({
              label: item.userName,
              value: item.userId,
            }))
          "
          showSearch
          v-model="formData.indeTaskResponsible"
        />
      </el-form-item>

      <el-form-item label="风险说明">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入风险说明"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.form {
  padding-right: 32px;
  margin: 0 -24px;

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__content) {
    width: 216px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.select {
  width: 100%;
}
</style>
