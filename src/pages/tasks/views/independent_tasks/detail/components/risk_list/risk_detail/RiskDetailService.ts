import { ElMessage } from 'element-plus';
import { send } from '@/libs/request';
import type { IndeTaskRiskDetailResponse, IndeTaskRiskAnswerAddRequest, IndeTaskRiskAnswerEditRequest } from './types';
import { dictDataList } from '@/service_url/shared';

// 获取独立任务风险详情
export async function getIndeTaskRiskDetail(riskId: string | number): Promise<IndeTaskRiskDetailResponse> {
  try {
    const response: any = await send({
      url: `/projectmanage/inde-task-risk/${riskId}`,
      method: 'get'
    });
    if (response) {
      return response;
    } else {
      throw new Error(response.msg || '获取风险详情失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取风险详情失败');
    throw error;
  }
}

// 关闭独立任务风险
export async function closeIndeTaskRisk(riskId: number): Promise<void> {
  try {
    const response = await send({
      url: `/projectmanage/inde-task-risk/close/${riskId}`,
      method: 'post'
    });
    
    if (response.code !== 200) {
      throw new Error(response.msg || '关闭风险失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '关闭风险失败');
    throw error;
  }
}

// 获取风险类型选项
export async function getRiskTypeOptions(): Promise<any[]> {
  const response: any = await send({
    method: 'POST',
    url: dictDataList,
    data: {
      dictType: 'inde_task_risk_type'
    }
  });
  
  if (response && Array.isArray(response)) {
    return response.map((item: any) => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  }
  return [];
}

// 获取风险回答列表
export async function getRiskAnswerList(riskId: number): Promise<any[]> {
  try {
    const response: any = await send({
      url: '/projectmanage/inde-task-risk-answer/list',
      method: 'post',
      data: {
        indeTaskRiskId: riskId
      }
    });
    
    if (response && Array.isArray(response)) {
      return response;
    } else {
      return [];
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取风险回答列表失败');
    return [];
  }
}

// 添加风险回答
export async function addRiskAnswer(data: IndeTaskRiskAnswerAddRequest): Promise<boolean> {
  try {
    const response: any = await send({
      url: '/projectmanage/inde-task-risk-answer/add',
      method: 'post',
      data: {
        indeTaskRiskId: data.indeTaskRiskId,
        indeTaskRiskAnswer: data.indeTaskRiskAnswer,
        indeTaskRiskDisscution: data.indeTaskRiskDisscution,
        remark: data.remark || ''
      }
    });
    
    if (response) {
      return true;
    } else {
      throw new Error(response.msg || '添加风险回答失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '添加风险回答失败');
    throw error;
  }
}

// 编辑风险回答
export async function editRiskAnswer(data: IndeTaskRiskAnswerEditRequest): Promise<boolean> {
  try {
    const response: any = await send({
      url: '/projectmanage/inde-task-risk-answer/edit',
      method: 'post',
      data: {
        indeTaskRiskId: data.indeTaskRiskId,
        indeTaskRiskAnswer: data.indeTaskRiskAnswer,
        indeTaskRiskAnswerId: data.indeTaskRiskAnswerId,
        indeTaskRiskDisscution: data.indeTaskRiskDisscution,
        remark: data.remark || ''
      }
    });
    
    if (response) {
      return true;
    } else {
      throw new Error(response.msg || '编辑风险回答失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '编辑风险回答失败');
    throw error;
  }
}

// 删除风险回答
export async function deleteRiskAnswer(answerId: number): Promise<boolean> {
  try {
    const response: any = await send({
      url: '/projectmanage/inde-task-risk-answer/delete',
      method: 'post',
      data: {
        indeTaskRiskAnswerId: answerId
      }
    });
    
    if (response) {
      return true;
    } else {
      throw new Error(response.msg || '删除风险回答失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '删除风险回答失败');
    throw error;
  }
} 