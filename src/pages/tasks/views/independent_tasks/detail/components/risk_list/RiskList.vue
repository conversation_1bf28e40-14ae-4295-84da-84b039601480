<script setup lang="ts">
import { ref, onMounted, defineExpose } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { MoreFilled } from '@element-plus/icons-vue';
// 移除搜索和筛选组件的导入
import AddRisk from './components/AddRisk.vue';
import EditRisk from './components/EditRisk.vue';
import {
  getIndeTaskRiskPage,
  deleteIndeTaskRisk,
  closeIndeTaskRisk,
  convertRiskToIssue,
  assignIndeTaskRisk,
  type IndeTaskRiskResponse,
  type IndeTaskRiskQueryRequest,
  type IndeTaskRiskDeleteRequest,
} from '../../services/RiskService';
import { useRiskStore } from './RiskStore';
import { storeToRefs } from 'pinia';

const router = useRouter();
const route = useRoute();
const taskId = ref<number | undefined>(undefined);
const tableLoading = ref(false);
const riskList = ref<IndeTaskRiskResponse[]>([]);

// 添加风险对话框
const addRiskVisible = ref(false);

// 添加EditRisk组件导入
const editDialogVisible = ref(false);
const currentEditRisk = ref<IndeTaskRiskResponse | null>(null);

// 获取store实例
const riskStore = useRiskStore();

// 从store中获取风险类型和用户列表
const { riskTypeOptions } = storeToRefs(riskStore);

// 风险级别选项
const riskLevelOptions = [
  { value: 1, label: '严重', type: 'danger' },
  { value: 2, label: '高', type: 'warning' },
  { value: 3, label: '中', type: 'info' },
  { value: 4, label: '低', type: '' },
];

// 风险状态选项
const riskStatusOptions = [
  { value: 1, label: '未处理', type: 'danger' },
  { value: 2, label: '处理中', type: 'warning' },
  { value: 3, label: '已处理', type: 'success' },
  { value: 4, label: '已关闭', type: 'info' },
];

// 表格列配置（按UI规范调整）
const columns = [
  {
    field: 'indeTaskRiskName',
    label: '风险名称',
    width: '144',
    align: 'left',
  },
  {
    field: 'indeTaskRiskType',
    label: '风险类型',
    width: '144',
    align: 'left',
  },
  {
    field: 'indeTaskRiskLevel',
    label: '风险级别',
    width: '144',
    align: 'left',
  },
  {
    field: 'indeTaskRiskStatus',
    label: '状态',
    width: '144',
    align: 'left',
  },
  {
    field: 'indeTaskResponsibleName',
    label: '责任人',
    width: '120',
    align: 'left',
  },
  {
    field: 'createTime',
    label: '创建时间',
    width: '176',
    align: 'left',
  },
];

// 移除showColumn相关逻辑

// 初始化页面
onMounted(() => {
  const id = route.params.id;
  if (id) {
    taskId.value = Number(id);
    // 初始化风险Store
    riskStore.initialize().then(() => {
      loadRiskList();
    });
  }
});

// 加载风险列表
const loadRiskList = async () => {
  if (!taskId.value) return;

  tableLoading.value = true;
  try {
    const queryParams: IndeTaskRiskQueryRequest = {
      indeTaskId: taskId.value,
    };

    const { list } = await getIndeTaskRiskPage(1, 1000, queryParams);

    riskList.value = list;
  } catch (error) {
    console.error('加载风险列表失败:', error);
    ElMessage.error('加载风险列表失败');
  } finally {
    tableLoading.value = false;
  }
};

// 移除搜索和筛选相关函数

// 打开添加风险对话框
const openAdd = () => {
  addRiskVisible.value = true;
};

// 添加风险成功回调
const onAddSuccess = () => {
  addRiskVisible.value = false;
  loadRiskList();
  ElMessage.success('新建风险成功');
};

// 删除风险
const handleDelete = (row: IndeTaskRiskResponse) => {
  ElMessageBox.confirm('确认删除此风险?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const data: IndeTaskRiskDeleteRequest = {
          indeTaskRiskId: row.indeTaskRiskId!,
        };

        await deleteIndeTaskRisk(data);
        ElMessage.success('删除成功');
        loadRiskList(); // 重新加载列表
      } catch (error) {
        console.error('删除风险失败:', error);
        ElMessage.error('删除风险失败');
      }
    })
    .catch(() => {});
};

// 关闭风险
const handleClose = (row: IndeTaskRiskResponse) => {
  ElMessageBox.confirm('确认关闭此风险?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const data: IndeTaskRiskDeleteRequest = {
          indeTaskRiskId: row.indeTaskRiskId!,
        };

        await closeIndeTaskRisk(data);
        ElMessage.success('关闭风险成功');
        loadRiskList(); // 重新加载列表
      } catch (error) {
        console.error('关闭风险失败:', error);
        ElMessage.error('关闭风险失败');
      }
    })
    .catch(() => {});
};

// 转换为问题
const handleConvert = (row: IndeTaskRiskResponse) => {
  ElMessageBox.confirm('确认将此风险转换为问题?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const data: IndeTaskRiskDeleteRequest = {
          indeTaskRiskId: row.indeTaskRiskId!,
        };

        await convertRiskToIssue(data);
        ElMessage.success('转换为问题成功');
        loadRiskList(); // 重新加载列表
      } catch (error) {
        console.error('转换为问题失败:', error);
        ElMessage.error('转换为问题失败');
      }
    })
    .catch(() => {});
};

// 重新指派风险
const reassignDialogVisible = ref(false);
const selectedUserId = ref<number | undefined>(undefined);
const currentReassignRow = ref<IndeTaskRiskResponse | null>(null);

function handleReassign(row: IndeTaskRiskResponse) {
  reassignDialogVisible.value = true;
  selectedUserId.value = undefined;
  currentReassignRow.value = row;
}

async function submitReassign() {
  if (!selectedUserId.value || !currentReassignRow.value) {
    ElMessage.warning('请选择负责人');
    return;
  }

  try {
    await assignIndeTaskRisk({
      indeTaskRiskId: currentReassignRow.value.indeTaskRiskId!,
      indeTaskResponsible: selectedUserId.value,
    });

    ElMessage.success('重新指派成功');
    reassignDialogVisible.value = false;
    selectedUserId.value = undefined;
    currentReassignRow.value = null;
    loadRiskList();
  } catch (error) {
    console.error('重新指派失败:', error);
    ElMessage.error('重新指派失败');
  }
}

// 添加编辑函数
function handleEdit(row: IndeTaskRiskResponse) {
  currentEditRisk.value = { ...row };
  editDialogVisible.value = true;
}

// 处理查看详情
function handleViewDetail(row: IndeTaskRiskResponse) {
  const taskId = route.params.id;
  router.push(`/independent-tasks/detail/${taskId}/risk/${row.indeTaskRiskId}`);
}

// 处理下拉菜单命令
function handleCommand(command: string, row: IndeTaskRiskResponse) {
  switch (command) {
    case 'view':
      handleViewDetail(row);
      break;
    case 'edit':
      handleEdit(row);
      break;
    case 'close':
      handleClose(row);
      break;
    case 'convert':
      handleConvert(row);
      break;
    case 'reassign':
      handleReassign(row);
      break;
    case 'delete':
      handleDelete(row);
      break;
    default:
      console.log('未知命令:', command);
  }
}

// 添加编辑成功回调
function onEditSuccess() {
  editDialogVisible.value = false;
  currentEditRisk.value = null;
  loadRiskList();
  ElMessage.success('编辑风险成功');
}

// 移除分页相关函数

// 获取风险类型标签文本
const getRiskTypeName = (typeValue: string | number): string => {
  console.log(riskTypeOptions);
  if (!typeValue) return '未知类型';
  const option = riskTypeOptions.value.find((opt) => opt.value == typeValue);
  return option ? option.label : '未知类型';
};

// 获取风险级别标签样式和文本
const getRiskLevelInfo = (level: number) => {
  const option = riskLevelOptions.find((item) => item.value === level);
  return option
    ? { type: option.type, label: option.label }
    : { type: '', label: '未知' };
};

// 获取风险状态标签样式和文本
const getRiskStatusInfo = (status: number) => {
  const option = riskStatusOptions.find((item) => item.value === status);
  return option
    ? { type: option.type, label: option.label }
    : { type: '', label: '未知' };
};

// 暴露方法给父组件
defineExpose({
  openAdd,
  refreshData: loadRiskList,
});
</script>

<template>
  <div class="risk-list">
    <!-- 风险表格 -->
    <div class="table-container">
      <el-table
        v-loading="tableLoading"
        :data="riskList"
        class="table"
        row-key="indeTaskRiskId"
        stripe
      >
        <!-- 选择列 -->
        <el-table-column type="selection" width="50" fixed="left" />

        <!-- 序号列 -->
        <el-table-column type="index" label="#" width="32" align="center" />

        <!-- 动态列 -->
        <template v-for="column in columns" :key="column.field">
          <!-- 风险名称列 -->
          <el-table-column
            v-if="column.field === 'indeTaskRiskName'"
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span
                class="risk-name"
                :title="row.indeTaskRiskName"
                @click="handleViewDetail(row)"
                >{{ row.indeTaskRiskName }}</span
              >
            </template>
          </el-table-column>

          <!-- 其他列 -->
          <el-table-column
            v-else
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <template v-if="column.field === 'indeTaskRiskType'">
                <el-tag size="small">{{
                  getRiskTypeName(row.indeTaskRiskType)
                }}</el-tag>
              </template>
              <template v-else-if="column.field === 'indeTaskRiskLevel'">
                <el-tag size="small">
                  {{ getRiskLevelInfo(row.indeTaskRiskLevel).label }}
                </el-tag>
              </template>
              <template v-else-if="column.field === 'indeTaskRiskStatus'">
                {{ getRiskStatusInfo(row.indeTaskRiskStatus).label }}
              </template>
              <template v-else>
                {{ row[column.field] }}
              </template>
            </template>
          </el-table-column>
        </template>

        <!-- 操作列 -->
        <el-table-column label="操作" width="48" align="center" fixed="right">
          <template #default="{ row }">
            <el-dropdown
              placement="bottom-end"
              @command="(command: string) => handleCommand(command, row)"
              hide-on-click
              :show-timeout="50"
            >
              <div class="more-opt">
                <el-icon class="icon"><MoreFilled /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="border drop-opt">
                  <el-dropdown-item command="view">查看详情</el-dropdown-item>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="close">关闭风险</el-dropdown-item>
                  <el-dropdown-item command="convert"
                    >转为问题</el-dropdown-item
                  >
                  <el-dropdown-item command="reassign"
                    >重新指派</el-dropdown-item
                  >
                  <el-dropdown-item command="delete">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 移除分页组件 -->

    <!-- 添加风险对话框 -->
    <AddRisk v-model="addRiskVisible" @success="onAddSuccess" />

    <!-- 编辑风险对话框 -->
    <EditRisk
      v-model="editDialogVisible"
      :risk="currentEditRisk"
      :indeTaskId="taskId!"
      @success="onEditSuccess"
    />

    <!-- 重新指派对话框 -->
    <el-dialog
      v-model="reassignDialogVisible"
      title="重新指派"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form label-width="80px">
        <el-form-item label="负责人">
          <el-select
            v-model="selectedUserId"
            placeholder="请选择负责人"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="user in riskStore.userList"
              :key="user.userId"
              :label="user.userName"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="reassignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReassign">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.risk-list {
  height: 100%;
  display: flex;
  flex-direction: column;

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .left {
      display: flex;
      align-items: center;
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-box {
        width: 240px;
      }
    }
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    border-radius: 6px;

    .risk-table {
      height: 100%;

      .risk-name {
        cursor: pointer;
        color: var(--el-color-primary);

        &:hover {
          text-decoration: underline;
        }
      }

      .more-opt {
        cursor: pointer;
        .icon {
          font-size: 16px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }

  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}

.dropdown-menu {
  min-width: 100px;
}
</style>
