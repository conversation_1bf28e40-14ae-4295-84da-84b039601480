import { ref, reactive } from 'vue';
import { defineStore } from 'pinia';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { 
  getIndeTaskRiskDetail, 
  closeIndeTaskRisk, 
  getRiskTypeOptions,
  getRiskAnswerList,
  addRiskAnswer,
  editRiskAnswer,
  deleteRiskAnswer
} from './RiskDetailService';
import type { 
  IndeTaskRiskDetailResponse, 
  IndeTaskRiskDocResponse, 
  IndeTaskDeliveryDocResponse,
  IndeTaskRiskAnswerResponse,
  IndeTaskRiskAnswerAddRequest,
  IndeTaskRiskAnswerEditRequest
} from './types';
import { assignIndeTaskRisk } from '../../../services/RiskService';
import { getUserList as getUserListService } from '@/pages/tasks/views/independent_tasks/list/components/add_task/AddTaskService';
import type { UserOption } from '@/pages/tasks/views/independent_tasks/list/components/add_task/types';

// 类型定义
interface TypeOption {
  label: string;
  value: string | number;
}

export const useRiskDetailStore = defineStore('risk-detail', () => {
  const router = useRouter();
  
  // 加载状态
  const loading = ref(false);
  const loadingAnswers = ref(false);
  
  // 原始API数据
  const apiData = ref<IndeTaskRiskDetailResponse | null>(null);
  
  // 风险详情数据
  const riskDetail = reactive({
    indeTaskRiskId: 0,
    indeTaskId: 0,
    indeTaskRiskName: '',
    indeTaskRiskStatus: 0,
    indeTaskRiskType: 0,
    indeTaskRiskLevel: 0,
    closingTime: '',
    completionDate: '',
    indeTaskResponsible: 0,
    indeTaskResponsibleName: '',
    createTime: '',
    updateTime: '',
    createUser: 0,
    createUserName: '',
    updateUser: 0,
    remark: '',
    tenantId: 0,
    disCloseFlag: 0,
    publicReplyFlag: 0,
    riskDocs: [] as IndeTaskRiskDocResponse[],
    deliveryDocs: [] as IndeTaskDeliveryDocResponse[]
  });

  // 风险回答列表
  const riskAnswers = ref<IndeTaskRiskAnswerResponse[]>([]);
  
  // 风险类型选项
  const riskTypeOptions = ref<TypeOption[]>([]);
  
  // 用户列表
  const userList = ref<UserOption[]>([]);
  const loadingUsers = ref(false);
  
  // 加载用户列表
  async function loadUserList() {
    if (loadingUsers.value) return;
    
    loadingUsers.value = true;
    try {
      // 使用相同的方法获取用户列表
      const result = await getUserListService();
      userList.value = result || [];
    } catch (error) {
      console.error('加载用户列表失败:', error);
    } finally {
      loadingUsers.value = false;
    }
  }
  
  // 加载风险类型
  async function loadRiskTypes() {
    try {
      riskTypeOptions.value = await getRiskTypeOptions();
    } catch (error) {
      console.error('加载风险类型失败:', error);
      ElMessage.error('加载风险类型失败');
    }
  }
  
  // 获取风险类型名称
  function getRiskTypeName(typeValue: string | number): string {
    const option = riskTypeOptions.value.find(opt => {
      // 尝试多种比较方式确保类型匹配
      return String(opt.value) === String(typeValue) || 
             opt.value == typeValue ||
             Number(opt.value) === Number(typeValue);
    });
    return option ? option.label : '未知类型';
  }
  
  // 初始化 - 加载所有必要数据
  async function initialize() {
    await Promise.all([
      loadUserList(),
      loadRiskTypes()
    ]);
  }
  
  // 加载风险详情
  async function loadRiskDetail(riskId: string | number) {
    loading.value = true;
    
    try {
      await loadRiskTypes(); // 先加载风险类型
      
      const response = await getIndeTaskRiskDetail(riskId);
      apiData.value = response;
      
      // 将API返回的数据映射到本地状态
      Object.assign(riskDetail, {
        indeTaskRiskId: response.indeTaskRiskId || 0,
        indeTaskId: response.indeTaskId || 0,
        indeTaskRiskName: response.indeTaskRiskName || '',
        indeTaskRiskStatus: response.indeTaskRiskStatus || 0,
        indeTaskRiskType: response.indeTaskRiskType || 0,
        indeTaskRiskLevel: response.indeTaskRiskLevel || 0,
        closingTime: response.closingTime || '',
        completionDate: response.completionDate || '',
        indeTaskResponsible: response.indeTaskResponsible || 0,
        indeTaskResponsibleName: response.indeTaskResponsibleName || '',
        createTime: response.createTime || '',
        updateTime: response.updateTime || '',
        createUser: response.createUser || 0,
        createUserName: response.createUserName || '',
        updateUser: response.updateUser || 0,
        remark: response.remark || '',
        tenantId: response.tenantId || 0,
        disCloseFlag: response.disCloseFlag || 0,
        publicReplyFlag: response.publicReplyFlag || 0,
        riskDocs: response.riskDocs || [],
        deliveryDocs: response.deliveryDocs || []
      });
      
      // 加载风险回答列表
      await loadRiskAnswers(Number(riskId));
      
      return true;
    } catch (error) {
      console.error('加载风险详情失败:', error);
      ElMessage.error('加载风险详情失败');
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  // 加载风险回答列表
  async function loadRiskAnswers(riskId: number) {
    loadingAnswers.value = true;
    try {
      const response = await getRiskAnswerList(riskId);
      
      // Add backward compatibility fields if needed
      response.forEach(answer => {
        if (answer.projTaskRiskAnswer && !answer.indeTaskRiskAnswer) {
          answer.indeTaskRiskAnswer = answer.projTaskRiskAnswer;
        }
        if (answer.projTaskRiskAnswerId && !answer.indeTaskRiskAnswerId) {
          answer.indeTaskRiskAnswerId = answer.projTaskRiskAnswerId;
        }
        if (answer.projTaskRiskId && !answer.indeTaskRiskId) {
          answer.indeTaskRiskId = answer.projTaskRiskId;
        }
      });
      
      riskAnswers.value = response;
    } catch (error) {
      console.error('加载风险回答列表失败:', error);
    } finally {
      loadingAnswers.value = false;
    }
  }
  
  // 添加风险回答
  async function addRiskAnswerAction(data: IndeTaskRiskAnswerAddRequest) {
    try {
      await addRiskAnswer(data);
      ElMessage.success('添加回答成功');
      await loadRiskAnswers(data.indeTaskRiskId);
      return true;
    } catch (error) {
      console.error('添加风险回答失败:', error);
      return false;
    }
  }
  
  // 编辑风险回答
  async function editRiskAnswerAction(data: IndeTaskRiskAnswerEditRequest) {
    try {
      await editRiskAnswer(data);
      ElMessage.success('编辑回答成功');
      if (data.indeTaskRiskId) {
        await loadRiskAnswers(data.indeTaskRiskId);
      }
      return true;
    } catch (error) {
      console.error('编辑风险回答失败:', error);
      return false;
    }
  }
  
  // 删除风险回答
  async function deleteRiskAnswerAction(answerId: number) {
    try {
      await deleteRiskAnswer(answerId);
      ElMessage.success('删除回答成功');
      await loadRiskAnswers(riskDetail.indeTaskRiskId);
      return true;
    } catch (error) {
      console.error('删除风险回答失败:', error);
      return false;
    }
  }
  
  // 重新指派风险
  async function assignRisk(userId: number) {
    loading.value = true;
    
    try {
      await assignIndeTaskRisk({
        indeTaskRiskId: riskDetail.indeTaskRiskId,
        indeTaskResponsible: userId
      });
      
      ElMessage.success('重新指派成功');
      
      // 重新加载风险详情
      await loadRiskDetail(riskDetail.indeTaskRiskId);
      return true;
    } catch (error) {
      console.error('重新指派失败:', error);
      ElMessage.error('重新指派失败');
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  // 关闭风险
  async function closeRisk() {
    loading.value = true;
    
    try {
      await closeIndeTaskRisk(riskDetail.indeTaskRiskId);
      ElMessage.success('风险关闭成功');
      
      // 重新加载风险详情
      await loadRiskDetail(riskDetail.indeTaskRiskId);
      return true;
    } catch (error) {
      console.error('关闭风险失败:', error);
      ElMessage.error('关闭风险失败');
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  // 返回任务详情页
  function backToTaskDetail() {
    const taskId = riskDetail.indeTaskId;
    router.push(`/independent-tasks/detail/${taskId}`);
  }
  
  // 获取风险状态文本
  function getRiskStatusText(status: number): string {
    const statusMap: Record<number, string> = {
      1: '未开始',
      2: '进行中',
      3: '已完成',
      4: '已关闭',
    };
    return statusMap[status] || '未知状态';
  }
  
  // 获取风险级别文本
  function getRiskLevelText(level: number): string {
    const levelMap: Record<number, string> = {
      1: '高',
      2: '中',
      3: '低',
    };
    return levelMap[level] || '未知级别';
  }
  
  // 获取风险状态类型（用于标签颜色）
  function getRiskStatusType(status: number): string {
    const statusMap: Record<number, string> = {
      1: 'info',
      2: 'warning',
      3: 'success',
      4: 'danger',
    };
    return statusMap[status] || '';
  }
  
  // 获取风险级别类型（用于标签颜色）
  function getRiskLevelType(level: number): string {
    const levelMap: Record<number, string> = {
      1: 'danger',
      2: 'warning',
      3: 'info',
    };
    return levelMap[level] || '';
  }
  
  // 格式化文件大小
  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  // 格式化时间
  function formatDate(dateStr: string): string {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;
    
    return date.toLocaleString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }
  
  return {
    loading,
    loadingAnswers,
    riskDetail,
    apiData,
    riskTypeOptions,
    userList,
    riskAnswers,
    loadUserList,
    loadRiskTypes,
    initialize,
    loadRiskDetail,
    loadRiskAnswers,
    addRiskAnswerAction,
    editRiskAnswerAction,
    deleteRiskAnswerAction,
    assignRisk,
    closeRisk,
    backToTaskDetail,
    getRiskStatusText,
    getRiskLevelText,
    getRiskStatusType,
    getRiskLevelType,
    getRiskTypeName,
    formatFileSize,
    formatDate
  };
}); 