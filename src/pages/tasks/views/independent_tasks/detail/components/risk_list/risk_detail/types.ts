// 风险附件文件
export interface IndeTaskRiskDocResponse {
  /**
   * 文档ID
   */
  docId?: number;
  /**
   * 文档名称
   */
  docName?: string;
  /**
   * 文档大小
   */
  docSize?: number;
  /**
   * 风险文档ID
   */
  indeTaskRiskDocId?: number;
  /**
   * 风险ID
   */
  indeTaskRiskId?: number;
}

// 交付文档
export interface IndeTaskDeliveryDocResponse {
  /**
   * 文档ID
   */
  docId?: number;
  /**
   * 文档名称
   */
  docName?: string;
  /**
   * 文档大小
   */
  docSize?: number;
  /**
   * 交付文档ID
   */
  indeTaskDeliveryDocId?: number;
}

// 风险详情响应
export interface IndeTaskRiskDetailResponse {
  /**
   * 独立任务风险ID
   */
  indeTaskRiskId?: number;
  /**
   * 独立任务ID
   */
  indeTaskId?: number;
  /**
   * 独立任务风险名称
   */
  indeTaskRiskName?: string;
  /**
   * 独立任务风险状态(1未开始, 2进行中,3已完成,4已关闭)
   */
  indeTaskRiskStatus?: number;
  /**
   * 风险分类
   */
  indeTaskRiskType?: number;
  /**
   * 风险级别 (1高2中3低)
   */
  indeTaskRiskLevel?: number;
  /**
   * 截止时间
   */
  closingTime?: string;
  /**
   * 完成日期
   */
  completionDate?: string;
  /**
   * 独立任务风险负责人ID
   */
  indeTaskResponsible?: number;
  /**
   * 独立任务风险负责人姓名
   */
  indeTaskResponsibleName?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 创建者ID
   */
  createUser?: number;
  /**
   * 独立任务风险提出人姓名
   */
  createUserName?: string;
  /**
   * 更新者ID
   */
  updateUser?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 是否公开 (1是0否)
   */
  disCloseFlag?: number;
  /**
   * 公开回复 (1是0否)
   */
  publicReplyFlag?: number;
  /**
   * 风险文档列表
   */
  riskDocs?: IndeTaskRiskDocResponse[];
  /**
   * 交付文档列表
   */
  deliveryDocs?: IndeTaskDeliveryDocResponse[];
  
  // Keep these fields from the previous interface to avoid breaking existing code
  id?: number;
  taskId?: number;
  riskType?: number;
  riskContent?: string;
  riskStatus?: number;
  createdBy?: string;
  createdTime?: string;
  updatedBy?: string;
  updatedTime?: string;
  riskTypeName?: string;
  riskStatusName?: string;
}

export interface IndeTaskRiskDocResponse {
  indeTaskRiskDocId?: number;
  indeTaskRiskId?: number;
  docName?: string;
  docType?: string;
  docId?: number;
  docKey?: string;
  docPath?: string;
  docSize?: number;
  folderFlag?: number;
  parentIndeTaskRiskDocId?: number;
  answerFlag?: number;
  createUserName?: string;
  updateUserName?: string;
  tenantId?: number;
}

export interface IndeTaskDeliveryDocResponse {
  indeTaskDeliveryDocId?: number;
  indeTaskId?: number;
  docName?: string;
  docType?: string;
  docId?: number;
  docKey?: string;
  docPath?: string;
  docSize?: number;
  folderFlag?: number;
  parentIndeTaskDeliveryDocId?: number;
  createUserName?: string;
  updateUserName?: string;
  tenantId?: number;
}

export interface IndeTaskRiskAnswerResponse {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建用户
   */
  createUser?: number;
  /**
   * 创建用户姓名
   */
  createUserName?: string;
  /**
   * 项目任务风险回答内容
   */
  projTaskRiskAnswer?: string;
  /**
   * 项目任务风险回答ID
   */
  projTaskRiskAnswerId?: number;
  /**
   * 项目任务风险回答时间
   */
  projTaskRiskAnswerTime?: string;
  /**
   * 项目任务风险讨论
   */
  projTaskRiskDisscution?: string;
  /**
   * 项目任务风险ID
   */
  projTaskRiskId?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 修改时间
   */
  updateTime?: string;
  /**
   * 修改用户
   */
  updateUser?: number;
  /**
   * 风险文档列表
   */
  riskDocs?: IndeTaskRiskDocResponse[];
  
  // Backward compatibility fields
  indeTaskRiskAnswer?: string;
  indeTaskRiskAnswerId?: number;
  indeTaskRiskId?: number;
}

export interface IndeTaskRiskAnswerAddRequest {
  /**
   * 独立任务风险回答内容
   */
  indeTaskRiskAnswer?: string;
  /**
   * 独立任务风险讨论
   */
  indeTaskRiskDisscution?: string;
  /**
   * 独立任务风险ID
   */
  indeTaskRiskId: number;
  /**
   * 文档列表
   */
  issueDocs?: IndeTaskRiskDocEditRequest[];
  /**
   * 备注
   */
  remark?: string;
  
  // For backward compatibility
  projTaskRiskId?: number;
  projTaskRiskAnswer?: string;
  projTaskRiskDisscution?: string;
}

export interface IndeTaskRiskAnswerEditRequest {
  /**
   * 独立任务风险回答内容
   */
  indeTaskRiskAnswer?: string;
  /**
   * 独立任务风险回答ID
   */
  indeTaskRiskAnswerId?: number;
  /**
   * 独立任务风险回答时间
   */
  indeTaskRiskAnswerTime?: string;
  /**
   * 独立任务风险讨论
   */
  indeTaskRiskDisscution?: string;
  /**
   * 独立任务风险ID
   */
  indeTaskRiskId?: number;
  /**
   * 文档列表
   */
  issueDocs?: IndeTaskRiskDocEditRequest[];
  /**
   * 备注
   */
  remark?: string;
  
  // Backward compatibility fields
  projTaskRiskAnswer?: string;
  projTaskRiskAnswerId?: number;
  projTaskRiskId?: number;
}

export interface IndeTaskRiskDocEditRequest {
  /**
   * 是否为风险回答(1是,0 否)
   */
  answerFlag?: number;
  /**
   * 附件id(关联附件表)
   */
  docId?: number;
  /**
   * 附件唯一标识
   */
  docKey?: string;
  /**
   * 附件名
   */
  docName?: string;
  /**
   * 是否文件夹(0 否,1 是)
   */
  folderFlag?: number;
  /**
   * 自增主键
   */
  indeTaskRiskDocId?: number;
  /**
   * 独立任务风险ID,风险回答
   */
  indeTaskRiskId?: number;
  /**
   * 父文档ID,或文件夹ID
   */
  parentIndeTaskRiskDocId?: number;
} 