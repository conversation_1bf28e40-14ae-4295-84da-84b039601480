<script setup lang="ts">
interface Events {
  (e: 'add-risk'): void;
}

const emits = defineEmits<Events>();

// 处理新增风险
function handleAddRisk() {
  emits('add-risk');
}
</script>

<template>
  <div class="risk-tab-actions">
    <!-- 操作按钮 -->
    <div class="actions-toolbar">
      <el-button @click="handleAddRisk">
        新增风险
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.risk-tab-actions {
  :deep(.el-dialog__header) {
    text-align: left;
  }
  .actions-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
</style> 