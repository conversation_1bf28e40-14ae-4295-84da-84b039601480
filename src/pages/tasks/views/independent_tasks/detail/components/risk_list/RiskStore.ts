import { ref } from 'vue';
import { defineStore } from 'pinia';
import { send } from '@/libs/request';
import { dictDataList } from '@/service_url/shared';
import { getUserList as getUserListService } from '@/pages/tasks/views/independent_tasks/list/components/add_task/AddTaskService';
import type { UserOption } from '@/pages/tasks/views/independent_tasks/list/components/add_task/types';

// 类型定义
interface RiskTypeOption {
  label: string;
  value: string | number;
}

// 定义Risk Store
export const useRiskStore = defineStore('risk-store', () => {
  // 用户列表
  const userList = ref<UserOption[]>([]);
  const loadingUsers = ref(false);

  // 风险类型选项
  const riskTypeOptions = ref<RiskTypeOption[]>([]);
  const loadingRiskTypes = ref(false);

  // 加载用户列表
  async function loadUserList() {
    if (loadingUsers.value) return;
    
    loadingUsers.value = true;
    try {
      // 使用与AddTask相同的方法获取用户列表
      const result = await getUserListService();
      userList.value = result || [];
    } catch (error) {
      console.error('加载用户列表失败:', error);
    } finally {
      loadingUsers.value = false;
    }
  }

  // 加载风险类型列表
  async function loadRiskTypes() {
    if (loadingRiskTypes.value) return;
    
    loadingRiskTypes.value = true;
    try {
      const response:any = await send({
        method: 'POST',
        url: dictDataList,
        data: {
          dictType: 'inde_task_risk_type'
        }
      });
      
      if (response && Array.isArray(response)) {
        riskTypeOptions.value = response.map((item: any) => ({
          label: item.dictLabel,
          value: item.dictValue
        }));
      }
    } catch (error) {
      console.error('加载风险类型列表失败:', error);
    } finally {
      loadingRiskTypes.value = false;
    }
  }

  // 初始化 - 加载所有必要数据
  async function initialize() {
    await Promise.all([
      loadUserList(),
      loadRiskTypes()
    ]);
  }

  return {
    userList,
    loadingUsers,
    riskTypeOptions,
    loadingRiskTypes,
    loadUserList,
    loadRiskTypes,
    initialize
  };
}); 