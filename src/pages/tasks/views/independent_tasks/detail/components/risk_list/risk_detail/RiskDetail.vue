<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useRiskDetailStore } from './RiskDetailStore';
import { ElMessageBox, ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';
import { Document, ArrowLeft } from '@element-plus/icons-vue';
import BtPicker from '@/components/non_biz/bt_picker/BtPicker.vue';
import { editIndeTaskRisk } from '../../../services/RiskService';
import { storeToRefs } from 'pinia';
import type { IndeTaskRiskAnswerResponse } from './types';

// Simple user info hook implementation
const useUserInfo = () => {
  // In a real implementation, this would get the current user from a store or API
  return {
    userId: 1,  // Mocked user ID for demo
    userName: '当前用户' // Mocked username
  };
};

const route = useRoute();
const store = useRiskDetailStore();
const { userList, riskTypeOptions } = storeToRefs(store);

// 获取当前用户信息
const userInfo = useUserInfo();
const currentUserId = userInfo.userId;


// 表单引用
const formRef = ref<FormInstance>();
const answerFormRef = ref<FormInstance>();
const uploadRef = ref();

// 表单数据
const formData = reactive({
  indeTaskRiskId: 0,
  indeTaskRiskName: '',
  remark: '',
  indeTaskRiskLevel: 3,
  indeTaskRiskType: undefined as string | number | undefined,
  indeTaskRiskStatus: 1,
  indeTaskResponsible: undefined as number | undefined,
  indeTaskResponsibleName: '',
  closingTime: '',
  disCloseFlag: 0,
  publicReplyFlag: 0
});

// 回答表单数据
const answerForm = reactive({
  content: '',
  fileList: [] as any[],
});

// 提交状态
const submitting = ref(false);
const submittingAnswer = ref(false);
const loading = ref(false);

// 编辑回答状态
const isEditingAnswer = ref(false);
const editingAnswerId = ref<number | null>(null);

// 风险级别选项
const riskLevelOptions = [
  { label: '严重', value: 1 },
  { label: '高', value: 2 },
  { label: '中', value: 3 },
  { label: '低', value: 4 },
];

// 风险状态选项
const riskStatusOptions = [
  { label: '未处理', value: 1 },
  { label: '处理中', value: 2 },
  { label: '已处理', value: 3 },
  { label: '已关闭', value: 4 },
];

// 表单验证规则
const rules = {
  indeTaskRiskName: [
    { required: true, message: '请输入风险名称', trigger: 'blur' },
  ],
  indeTaskRiskLevel: [
    { required: true, message: '请选择风险级别', trigger: 'change' },
  ],
  indeTaskRiskType: [
    { required: true, message: '请选择风险类型', trigger: 'change' },
  ],
  indeTaskResponsible: [
    { required: true, message: '请选择责任人', trigger: 'change' },
  ],
};

// 处理文件下载
function downloadFile(file: { docId?: number; docName?: string }) {
  if (!file.docId) return;
  
  const downloadUrl = `/system/oss/download?fileId=${file.docId}`;
  
  // 创建一个隐藏的a标签并点击它来触发下载
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.target = '_blank';
  link.download = file.docName || '文件下载';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// 处理文件上传改变
function handleFileChange(file: any) {
  console.log('文件上传改变:', file);
}

// 处理文件移除
function handleFileRemove(file: any) {
  console.log('文件移除:', file);
  answerForm.fileList = answerForm.fileList.filter((f: any) => f.uid !== file.uid);
}

// 提交回答
async function handleSubmitAnswer() {
  if (!answerForm.content.trim()) {
    ElMessage.warning('请输入回答内容');
    return;
  }
  
  submittingAnswer.value = true;
  try {
    // 上传文件并获取文件ID (这里需要实现文件上传逻辑)
    // 此处假设我们已经上传了文件并获取了文件ID
    const fileIds = await uploadAnswerFiles();
    
    if (isEditingAnswer.value && editingAnswerId.value) {
      // 编辑回答
      await store.editRiskAnswerAction({
        indeTaskRiskAnswerId: editingAnswerId.value,
        indeTaskRiskId: formData.indeTaskRiskId,
        indeTaskRiskAnswer: answerForm.content,
        indeTaskRiskDisscution: '',
        remark: '',
        issueDocs: fileIds.map(item => ({
          docId: item.docId,
          docName: item.fileName,
          answerFlag: 1
        }))
      });
    } else {
      // 添加回答
      await store.addRiskAnswerAction({
        indeTaskRiskId: formData.indeTaskRiskId,
        indeTaskRiskAnswer: answerForm.content,
        indeTaskRiskDisscution: '',
        remark: '',
        issueDocs: fileIds.map(item => ({
          docId: item.docId,
          docName: item.fileName,
          answerFlag: 1
        }))
      });
    }
    
    // 清空表单
    answerForm.content = '';
    answerForm.fileList = [];
    isEditingAnswer.value = false;
    editingAnswerId.value = null;
    
  } catch (error) {
    console.error('提交回答失败:', error);
    ElMessage.error('提交回答失败');
  } finally {
    submittingAnswer.value = false;
  }
}

// 上传回答文件 (这里需要实现实际的文件上传逻辑)
async function uploadAnswerFiles() {
  // 这里是一个模拟的实现，实际项目中需要调用文件上传API
  // 返回格式为 { docId: number, fileName: string }[]
  const uploadedFiles = answerForm.fileList.map(file => ({
    docId: Math.floor(Math.random() * 1000000),  // 模拟文件ID
    fileName: file.name
  }));
  
  return uploadedFiles;
}

// 处理编辑回答
function handleEditAnswer(answer: IndeTaskRiskAnswerResponse) {
  isEditingAnswer.value = true;
  editingAnswerId.value = answer.projTaskRiskAnswerId || answer.indeTaskRiskAnswerId || null;
  answerForm.content = answer.projTaskRiskAnswer || answer.indeTaskRiskAnswer || '';
  
  // 如果有附件，需要处理附件列表
  if (answer.riskDocs && answer.riskDocs.length > 0) {
    answerForm.fileList = answer.riskDocs.map((doc) => ({
      name: doc.docName,
      docId: doc.docId,
      indeTaskRiskDocId: doc.indeTaskRiskDocId
    }));
  }
  
  // 滚动到回答表单
  setTimeout(() => {
    document.querySelector('.add-answer-form')?.scrollIntoView({ behavior: 'smooth' });
  }, 100);
}

// 处理删除回答
function handleDeleteAnswer(answer: IndeTaskRiskAnswerResponse) {
  const answerId = answer.projTaskRiskAnswerId || answer.indeTaskRiskAnswerId;
  if (!answerId) return;
  
  ElMessageBox.confirm(
    '确定要删除此回答吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    await store.deleteRiskAnswerAction(answerId);
  }).catch(() => {});
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    submitting.value = true;
    
    // 构造更新数据
    const updateData = {
      indeTaskRiskId: Number(formData.indeTaskRiskId),
      indeTaskRiskName: formData.indeTaskRiskName,
      remark: formData.remark,
      indeTaskRiskLevel: formData.indeTaskRiskLevel,
      indeTaskRiskType: formData.indeTaskRiskType,
      indeTaskRiskStatus: formData.indeTaskRiskStatus,
      indeTaskResponsible: formData.indeTaskResponsible,
      closingTime: formData.closingTime,
      disCloseFlag: formData.disCloseFlag,
      publicReplyFlag: formData.publicReplyFlag
    };
    
    await editIndeTaskRisk(updateData);
    
    ElMessage.success('更新风险成功');
    await store.loadRiskDetail(String(formData.indeTaskRiskId));
  } catch (error) {
    console.error('更新风险失败:', error);
    ElMessage.error('更新风险失败');
  } finally {
    submitting.value = false;
  }
}

// 关闭风险
function handleCloseRisk() {
  ElMessageBox.confirm(
    '确定要关闭此风险吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    await store.closeRisk();
    loadRiskDetail();
  });
}

// 重新指派风险
const reassignDialogVisible = ref(false);
const selectedUserId = ref<number | undefined>(undefined);

function handleReassign() {
  reassignDialogVisible.value = true;
  selectedUserId.value = undefined;
}

async function submitReassign() {
  if (!selectedUserId.value) {
    ElMessage.warning('请选择负责人');
    return;
  }
  
  const success = await store.assignRisk(selectedUserId.value);
  if (success) {
    reassignDialogVisible.value = false;
    selectedUserId.value = undefined;
  }
}

// 返回任务详情页
function goBack() {
  store.backToTaskDetail();
}

// 加载风险详情
async function loadRiskDetail() {
  loading.value = true;
  try {
    const riskId = route.params.riskId as string;
    await store.loadRiskDetail(riskId);
    
    // 加载成功后更新表单数据
    Object.assign(formData, {
      indeTaskRiskId: store.riskDetail.indeTaskRiskId,
      indeTaskRiskName: store.riskDetail.indeTaskRiskName,
      remark: store.riskDetail.remark || '',
      indeTaskRiskLevel: store.riskDetail.indeTaskRiskLevel,
      indeTaskRiskType: store.riskDetail.indeTaskRiskType.toString(),
      indeTaskRiskStatus: store.riskDetail.indeTaskRiskStatus,
      indeTaskResponsible: store.riskDetail.indeTaskResponsible,
      indeTaskResponsibleName: store.riskDetail.indeTaskResponsibleName || '',
      closingTime: store.riskDetail.closingTime || '',
      disCloseFlag: store.riskDetail.disCloseFlag || 0,
      publicReplyFlag: store.riskDetail.publicReplyFlag || 0
    });
    
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  // 初始化数据
  await store.initialize();
  loadRiskDetail();
});
</script>

<template>
  <div class="risk-detail">
    <!-- 顶部栏 -->
    <div class="header">
      <div class="back">
        <el-button type="text" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
      <div class="title">风险详情</div>
      <div class="actions">
        <el-button @click="goBack">返回</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">保存</el-button>
        <el-button 
          type="warning" 
          @click="handleCloseRisk"
          :disabled="formData.indeTaskRiskStatus === 4"
        >
          关闭风险
        </el-button>
        <el-button 
          type="info" 
          @click="handleReassign"
        >
          重新指派
        </el-button>
      </div>
    </div>

    <div v-loading="loading" class="risk-content">
      <!-- 表单内容 -->
      <el-form class="detail-form" label-width="120px" :model="formData" :rules="rules" ref="formRef">
        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="风险名称" prop="indeTaskRiskName">
                <el-input 
                  v-model="formData.indeTaskRiskName" 
                  placeholder="请输入风险名称"
                  maxlength="100"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="风险状态" prop="indeTaskRiskStatus">
                <el-select
                  v-model="formData.indeTaskRiskStatus"
                  placeholder="请选择风险状态"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in riskStatusOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="风险级别" prop="indeTaskRiskLevel">
                <el-select
                  v-model="formData.indeTaskRiskLevel"
                  placeholder="请选择风险级别"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in riskLevelOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="风险类型" prop="indeTaskRiskType">
                <el-select
                  v-model="formData.indeTaskRiskType"
                  placeholder="请选择风险类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in riskTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="责任人" prop="indeTaskResponsible">
                <BtPicker
                  class="select"
                  title="责任人"
                  :list="
                    userList.map((item) => ({
                      label: item.userName,
                      value: item.userId,
                    }))
                  "
                  showSearch
                  v-model="formData.indeTaskResponsible"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="提出人">
                <el-input v-model="store.riskDetail.createUserName" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="截止时间">
                <el-date-picker
                  v-model="formData.closingTime"
                  type="date"
                  placeholder="选择截止时间"
                  style="width: 100%"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="提出时间">
                <el-input v-model="store.riskDetail.createTime" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="完成时间">
                <el-input v-model="store.riskDetail.completionDate" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="更新时间">
                <el-input v-model="store.riskDetail.updateTime" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否公开">
                <el-switch
                  v-model="formData.disCloseFlag"
                  :active-value="1"
                  :inactive-value="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="公开回复">
                <el-switch
                  v-model="formData.publicReplyFlag"
                  :active-value="1"
                  :inactive-value="0"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row>
            <el-col :span="24">
              <el-form-item label="风险描述">
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  :rows="5"
                  placeholder="请输入风险描述"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        
        <!-- 风险回答列表 -->
        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>讨论记录</span>
            </div>
          </template>
          
          <div v-loading="store.loadingAnswers" class="risk-answers">
            <!-- 回答列表 -->
            <div v-if="store.riskAnswers.length > 0" class="answer-list">
              <div v-for="answer in store.riskAnswers" :key="answer.indeTaskRiskAnswerId" class="answer-item">
                <div class="answer-header">
                  <div class="answer-user">
                    <span class="user-name">{{ answer.createUserName }}</span>
                    <span class="answer-time">{{ store.formatDate(answer.createTime || '') }}</span>
                  </div>
                  <div class="answer-actions" v-if="answer.createUser === currentUserId">
                    <el-tooltip content="编辑" placement="top">
                      <el-button 
                        type="primary" 
                        link 
                        @click="handleEditAnswer(answer)"
                      >编辑</el-button>
                    </el-tooltip>
                    <el-tooltip content="删除" placement="top">
                      <el-button 
                        type="danger" 
                        link 
                        @click="handleDeleteAnswer(answer)"
                      >删除</el-button>
                    </el-tooltip>
                  </div>
                </div>
                <div class="answer-content">{{ answer.projTaskRiskAnswer || answer.indeTaskRiskAnswer }}</div>
                
                <!-- 回答附件 -->
                <div v-if="answer.riskDocs && answer.riskDocs.length > 0" class="answer-files">
                  <div class="files-title">附件：</div>
                  <div class="files-list">
                    <div v-for="file in answer.riskDocs" :key="file.indeTaskRiskDocId" class="file-item-small">
                      <el-button link type="primary" @click="downloadFile(file)">
                        <el-icon><Document /></el-icon>
                        {{ file.docName }}
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <el-empty v-else description="暂无讨论记录" :image-size="80"></el-empty>
            
            <!-- 添加回答表单 -->
            <div class="add-answer-form">
              <el-divider>添加回答</el-divider>
              <el-form :model="answerForm" ref="answerFormRef">
                <el-form-item prop="content">
                  <el-input
                    v-model="answerForm.content"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入回复内容"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <div class="upload-container">
                    <el-upload
                      ref="uploadRef"
                      :auto-upload="false"
                      :on-change="handleFileChange"
                      :on-remove="handleFileRemove"
                      :limit="5"
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt"
                      :file-list="answerForm.fileList"
                    >
                      <el-button type="primary">选择文件</el-button>
                      <template #tip>
                        <div class="el-upload__tip">
                          支持.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt格式，单个文件不超过10MB
                        </div>
                      </template>
                    </el-upload>
                  </div>
                </el-form-item>
                <el-form-item>
                  <div class="submit-container">
                    <el-button type="primary" @click="handleSubmitAnswer" :loading="submittingAnswer">提交回复</el-button>
                  </div>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-card>
      </el-form>
    </div>
    <!-- 重新指派对话框 -->
    <el-dialog
      v-model="reassignDialogVisible"
      title="重新指派"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form :model="{ selectedUserId }" label-width="80px">
        <el-form-item label="负责人">
          <BtPicker
            class="select"
            title="负责人"
            :list="
              userList.map((item) => ({
                label: item.userName,
                value: item.userId,
              }))
            "
            showSearch
            v-model="selectedUserId"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="reassignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReassign" :loading="loading">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.risk-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  
  .header {
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    
    .back {
      width: 80px;
    }
    
    .title {
      flex: 1;
      font-size: 18px;
      font-weight: 600;
      text-align: center;
    }
    
    .actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
    }
  }
  
  .risk-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    
    .detail-form {
      .form-card {
        margin-bottom: 20px;
        
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 600;
          color: #303133;
        }
        
        .remark {
          white-space: pre-wrap;
          line-height: 1.6;
          padding: 8px;
          min-height: 80px;
        }
      }
    }
    
    .file-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      
      .file-item {
        width: calc(25% - 12px);
        min-width: 200px;
        
        .file-card {
          display: flex;
          flex-direction: column;
          height: 100%;
          
          .file-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            
            .file-icon {
              color: #409eff;
              font-size: 20px;
            }
            
            .file-name {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-size: 14px;
            }
            
            .file-size {
              font-size: 12px;
              color: #909399;
            }
          }
          
          .file-actions {
            display: flex;
            justify-content: flex-end;
          }
        }
      }
    }
    
    .risk-answers {
      padding: 0;
      
      .answer-list {
        margin-bottom: 20px;
        
        .answer-item {
          padding: 16px;
          border-bottom: 1px solid var(--el-border-color-lighter);
          
          &:last-child {
            border-bottom: none;
          }
          
          .answer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            
            .answer-user {
              display: flex;
              align-items: center;
              gap: 8px;
              
              .user-name {
                font-weight: 500;
                color: var(--el-color-primary);
              }
              
              .answer-time {
                font-size: 12px;
                color: var(--el-text-color-secondary);
              }
            }
            
            .answer-actions {
              display: flex;
              gap: 8px;
            }
          }
          
          .answer-content {
            margin: 10px 0;
            white-space: pre-wrap;
            line-height: 1.6;
          }
          
          .answer-files {
            margin-top: 8px;
            background-color: var(--el-fill-color-lighter);
            padding: 8px;
            border-radius: 4px;
            
            .files-title {
              font-size: 13px;
              font-weight: 500;
              margin-bottom: 4px;
            }
            
            .files-list {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
              
              .file-item-small {
                font-size: 13px;
              }
            }
          }
        }
      }
      
      .add-answer-form {
        margin-top: 24px;
        
        .submit-container {
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }
}

.select {
  width: 100%;
}

@media screen and (max-width: 768px) {
  .risk-detail {
    .risk-content {
      .file-list {
        .file-item {
          width: calc(50% - 8px);
        }
      }
    }
  }
}

@media screen and (max-width: 480px) {
  .risk-detail {
    .risk-content {
      .file-list {
        .file-item {
          width: 100%;
        }
      }
    }
  }
}
</style> 