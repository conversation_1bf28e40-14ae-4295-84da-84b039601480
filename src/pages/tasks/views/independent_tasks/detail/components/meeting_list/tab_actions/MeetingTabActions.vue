<script setup lang="ts">
interface Events {
  (e: 'add-meeting'): void;
  (e: 'schedule-feishu'): void;
}

const emits = defineEmits<Events>();

// 处理新增会议
function handleAddMeeting() {
  emits('add-meeting');
}

// 处理预定飞书会议
function handleScheduleFeiShu() {
  emits('schedule-feishu');
}
</script>

<template>
  <div class="meeting-tab-actions">
    <!-- 操作按钮 -->
    <div class="actions-toolbar">
      <el-button @click="handleAddMeeting"> 新增会议 </el-button>
      <el-button @click="handleScheduleFeiShu"> 预定飞书会议 </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.meeting-tab-actions {
  :deep(.el-dialog__header) {
    text-align: left;
  }
  .actions-toolbar {
    display: flex;
    align-items: center;
  }
}
</style>
