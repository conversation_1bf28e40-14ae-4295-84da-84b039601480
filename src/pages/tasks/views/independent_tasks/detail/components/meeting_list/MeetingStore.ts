import { ref } from 'vue';
import { defineStore } from 'pinia';
import { getUserList as getUserListService } from '@/pages/tasks/views/independent_tasks/list/components/add_task/AddTaskService';
import type { UserOption } from '@/pages/tasks/views/independent_tasks/list/components/add_task/types';

// 类型定义
interface MeetingStatusOption {
  label: string;
  value: number;
  type: string;
}

// 定义Meeting Store
export const useMeetingStore = defineStore('meeting-store', () => {
  // 用户列表
  const userList = ref<UserOption[]>([]);
  const loadingUsers = ref(false);

  // 会议状态选项
  const meetingStatusOptions = ref<MeetingStatusOption[]>([
    { value: 1, label: '未开始', type: 'warning' },
    { value: 2, label: '进行中', type: 'primary' },
    { value: 3, label: '已完成', type: 'success' },
    { value: 4, label: '已取消', type: 'danger' }
  ]);

  // 加载用户列表
  async function loadUserList() {
    if (loadingUsers.value) return;
    
    loadingUsers.value = true;
    try {
      // 获取用户列表
      const result = await getUserListService();
      userList.value = result || [];
    } catch (error) {
      console.error('加载用户列表失败:', error);
    } finally {
      loadingUsers.value = false;
    }
  }

  // 初始化 - 加载所有必要数据
  async function initialize() {
    await Promise.all([
      loadUserList()
    ]);
  }

  // 获取会议状态信息
  function getMeetingStatusInfo(status: number) {
    const option = meetingStatusOptions.value.find(item => item.value === status);
    return option ? { type: option.type, label: option.label } : { type: '', label: '未知' };
  }

  return {
    userList,
    loadingUsers,
    meetingStatusOptions,
    loadUserList,
    initialize,
    getMeetingStatusInfo
  };
}); 