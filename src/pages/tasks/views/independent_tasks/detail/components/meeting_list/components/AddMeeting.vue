<script setup lang="ts">
import { ref, computed, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useMeetingStore } from '../MeetingStore';
import { useIndependentTaskDetailStore } from '../../../IndependentTaskDetailStore';
import { addIndeTaskMeeting, type IndeTaskMeetingEditRequest } from '../../../services/MeetingService';

interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

const taskDetailStore = useIndependentTaskDetailStore();
const meetingStore = useMeetingStore();

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive<IndeTaskMeetingEditRequest>({
  indeTaskId: Number(taskDetailStore.formData.indeTaskId),
  indeTaskSubject: '',
  indeTaskMeetingStatus: 1,
  indeTaskMeetingTime: '',
  indeTaskLocation: '',
  indeTaskInitiator: undefined, // 不设置默认值
  indeTaskMeetingUrl: '',
  remark: '',
  participants: []
});

// 表单校验规则
const rules = {
  indeTaskSubject: [{ required: true, message: '请输入会议主题', trigger: 'blur' }],
  indeTaskMeetingTime: [{ required: true, message: '请选择会议时间', trigger: 'change' }],
  indeTaskLocation: [{ required: true, message: '请输入会议地点', trigger: 'blur' }],
  indeTaskInitiator: [{ required: true, message: '请选择会议发起人', trigger: 'change' }]
};

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return false;
    }
    
    try {
      await addIndeTaskMeeting(formData);
      ElMessage.success('添加会议成功');
      resetForm();
      emit('success');
    } catch (error) {
      console.error('添加会议失败:', error);
      ElMessage.error('添加会议失败');
    }
  });
}

// 重置表单
function resetForm() {
  if (!formRef.value) return;
  
  formRef.value.resetFields();
  formData.indeTaskSubject = '';
  formData.indeTaskMeetingStatus = 1;
  formData.indeTaskMeetingTime = '';
  formData.indeTaskLocation = '';
  formData.indeTaskMeetingUrl = '';
  formData.remark = '';
  formData.participants = [];
}

// 处理取消
function handleCancel() {
  resetForm();
  emit('update:modelValue', false);
}

// 添加参与人
function addParticipant(user: { userId: number; userName: string }) {
  if (!formData.participants) {
    formData.participants = [];
  }
  
  // 检查是否已存在该参与人
  const exists = formData.participants.some(p => p.userId === user.userId);
  if (!exists) {
    formData.participants.push({
      userId: user.userId,
      userName: user.userName
    });
  }
}

// 移除参与人
function removeParticipant(userId: number) {
  if (formData.participants) {
    formData.participants = formData.participants.filter(p => p.userId !== userId);
  }
}
</script>

<template>
  <el-dialog 
    v-model="dialogVisible" 
    title="新建会议" 
    width="600px"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <el-form 
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <!-- 会议主题 -->
      <el-form-item label="会议主题" prop="indeTaskSubject">
        <el-input v-model="formData.indeTaskSubject" placeholder="请输入会议主题"></el-input>
      </el-form-item>
      
      <!-- 会议状态 -->
      <el-form-item label="会议状态" prop="indeTaskMeetingStatus">
        <el-select v-model="formData.indeTaskMeetingStatus" placeholder="请选择会议状态" class="w-100">
          <el-option
            v-for="option in meetingStore.meetingStatusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      
      <!-- 会议时间 -->
      <el-form-item label="会议时间" prop="indeTaskMeetingTime">
        <el-date-picker
          v-model="formData.indeTaskMeetingTime"
          type="datetime"
          placeholder="选择会议时间"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="w-100"
        />
      </el-form-item>
      
      <!-- 会议地点 -->
      <el-form-item label="会议地点" prop="indeTaskLocation">
        <el-input v-model="formData.indeTaskLocation" placeholder="请输入会议地点"></el-input>
      </el-form-item>
      
      <!-- 会议链接 -->
      <el-form-item label="会议链接" prop="indeTaskMeetingUrl">
        <el-input v-model="formData.indeTaskMeetingUrl" placeholder="请输入会议链接（可选）"></el-input>
      </el-form-item>
      
      <!-- 会议发起人 -->
      <el-form-item label="发起人" prop="indeTaskInitiator">
        <el-select 
          v-model="formData.indeTaskInitiator" 
          placeholder="请选择发起人"
          filterable
          class="w-100"
        >
          <el-option
            v-for="user in meetingStore.userList"
            :key="user.userId"
            :label="user.userName"
            :value="user.userId"
          />
        </el-select>
      </el-form-item>
      
      <!-- 参会人员 -->
      <el-form-item label="参会人员" prop="participants">
        <div class="participants-container">
          <el-select 
            placeholder="请选择参会人员"
            filterable
            @change="addParticipant"
            class="w-100"
          >
            <el-option
              v-for="user in meetingStore.userList"
              :key="user.userId"
              :label="user.userName"
              :value="user"
            />
          </el-select>
          
          <div class="tag-container" v-if="formData.participants && formData.participants.length > 0">
            <el-tag 
              v-for="(participant, index) in formData.participants" 
              :key="index"
              closable
              @close="removeParticipant(participant.userId!)"
              class="participant-tag"
            >
              {{ participant.userName }}
            </el-tag>
          </div>
        </div>
      </el-form-item>
      
      <!-- 会议纪要 -->
      <el-form-item label="会议备注" prop="remark">
        <el-input 
          v-model="formData.remark" 
          type="textarea" 
          :rows="3" 
          placeholder="请输入会议备注（可选）"
        ></el-input>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.w-100 {
  width: 100%;
}

.participants-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.participant-tag {
  margin-right: 4px;
}
</style> 