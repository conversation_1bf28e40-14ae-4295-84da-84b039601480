<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import {
  getIndeTaskMeetingPage,
  deleteIndeTaskMeeting,
  type IndeTaskMeetingResponse,
  type IndeTaskMeetingQueryRequest,
} from '../../services/MeetingService';
import { useIndependentTaskDetailStore } from '../../IndependentTaskDetailStore';

// 获取store实例
const taskStore = useIndependentTaskDetailStore();

// 会议数据类型
type MeetingItem = IndeTaskMeetingResponse;

// 响应式数据
const loading = ref(false);
const meetings = ref<MeetingItem[]>([]);

// 弹窗状态
const addDialogVisible = ref(false);
const editDialogVisible = ref(false);
const currentEditMeeting = ref<MeetingItem | null>(null);

// 表格列配置（按UI规范调整）
const columns = [
  {
    field: 'indeTaskSubject',
    label: '会议主题',
    width: '144',
    align: 'left',
  },
  {
    field: 'indeTaskMeetingStatus',
    label: '状态',
    width: '144',
    align: 'left',
  },
  {
    field: 'indeTaskMeetingTime',
    label: '会议时间',
    width: '176',
    align: 'left',
  },
  {
    field: 'indeTaskLocation',
    label: '会议地点',
    width: '144',
    align: 'left',
  },
  {
    field: 'indeTaskInitiator',
    label: '发起人',
    width: '120',
    align: 'left',
  },
  {
    field: 'indeTaskMeetingUrl',
    label: '会议链接',
    width: '144',
    align: 'left',
  },
  {
    field: 'remark',
    label: '会议纪要',
    width: '144',
    align: 'left',
  },
  {
    field: 'docs',
    label: '文档',
    width: '144',
    align: 'left',
  },
];

// 移除showColumn相关逻辑

// 获取会议状态信息
function getMeetingStatusInfo(status: number) {
  const statusMap: Record<number, { label: string; type: string }> = {
    1: { label: '待开始', type: 'info' },
    2: { label: '进行中', type: 'warning' },
    3: { label: '已结束', type: 'success' },
    4: { label: '已取消', type: 'danger' },
  };
  return statusMap[status] || { label: '未知', type: 'info' };
}

// 加载会议列表
async function loadMeetings() {
  loading.value = true;
  try {
    const taskId = taskStore.formData.indeTaskId;
    if (!taskId) {
      console.warn('任务ID为空，跳过加载会议列表');
      return;
    }

    const params: IndeTaskMeetingQueryRequest = {
      indeTaskId: Number(taskId),
    };

    const response = await getIndeTaskMeetingPage(1000, 1, params);

    // 转换API数据格式
    meetings.value = response.list.map((item: IndeTaskMeetingResponse) => ({
      indeTaskMeetingId: item.indeTaskMeetingId || 0,
      indeTaskSubject: item.indeTaskSubject || '',
      indeTaskMeetingStatus: item.indeTaskMeetingStatus || 1,
      indeTaskMeetingTime: item.indeTaskMeetingTime || '',
      indeTaskLocation: item.indeTaskLocation || '',
      indeTaskInitiator: item.indeTaskInitiator || 0,
      indeTaskMeetingUrl: item.indeTaskMeetingUrl || '',
      remark: item.remark || '',
      docs: item.docs || [],
      createTime: item.createTime || '',
      updateTime: item.updateTime || '',
    }));

    // 移除total相关逻辑
  } catch (error) {
    console.error('加载会议列表失败:', error);
    ElMessage.error('加载会议列表失败');
  } finally {
    loading.value = false;
  }
}

// 移除搜索、筛选、分页相关函数

// 预定飞书会议 (已迁移到tab actions)
// 为了保留API兼容性，保留此方法，但移除了实现细节
function handleScheduleFeiShu() {
  // 实现已移动到tab actions
}

// 添加会议
function handleAdd() {
  addDialogVisible.value = true;
}

// 编辑会议
function handleEdit(row: MeetingItem) {
  currentEditMeeting.value = { ...row };
  editDialogVisible.value = true;
}

// 删除会议
function handleDelete(row: MeetingItem) {
  ElMessageBox.confirm(`确定要删除会议"${row.indeTaskSubject}"吗？`, '提示', {
    confirmButtonText: '删除',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      if (!row.indeTaskMeetingId) {
        ElMessage.error('会议ID不存在');
        return;
      }

      await deleteIndeTaskMeeting(row.indeTaskMeetingId);
      ElMessage.success('删除成功');
      loadMeetings();
    } catch (error) {
      console.error('删除会议失败:', error);
      ElMessage.error('删除失败');
    }
  });
}

// 处理下拉菜单命令
function handleCommand(command: string, row: MeetingItem) {
  switch (command) {
    case 'edit':
      handleEdit(row);
      break;
    case 'delete':
      handleDelete(row);
      break;
    default:
      console.log('未知命令:', command);
  }
}

// 添加成功后的回调
function onAddSuccess() {
  addDialogVisible.value = false;
  loadMeetings();
  ElMessage.success('添加会议成功');
}

// 编辑成功后的回调
function onEditSuccess() {
  editDialogVisible.value = false;
  currentEditMeeting.value = null;
  loadMeetings();
  ElMessage.success('编辑会议成功');
}

// 暴露给父组件的方法
function openAdd() {
  handleAdd();
}

// 暴露方法
defineExpose({
  openAdd,
  refreshData: loadMeetings,
  handleScheduleFeiShu,
});

// 初始化
onMounted(() => {
  loadMeetings();
});
</script>

<template>
  <div class="meeting-list">
    <!-- 会议表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="meetings"
        class="table"
        row-key="id"
        stripe
      >
        <!-- 选择列 -->
        <el-table-column type="selection" width="50" fixed="left" />

        <!-- 序号列 -->
        <el-table-column type="index" label="#" width="32" align="left" />

        <!-- 动态列 -->
        <template v-for="column in columns" :key="column.field">
          <!-- 会议主题列 -->
          <el-table-column
            v-if="column.field === 'indeTaskSubject'"
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span class="meeting-title" :title="row.indeTaskSubject">{{
                row.indeTaskSubject
              }}</span>
            </template>
          </el-table-column>

          <!-- 其他列 -->
          <el-table-column
            v-else
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <template v-if="column.field === 'indeTaskMeetingStatus'">
                <el-tag
                  :type="getMeetingStatusInfo(row.indeTaskMeetingStatus).type"
                >
                  {{ getMeetingStatusInfo(row.indeTaskMeetingStatus).label }}
                </el-tag>
              </template>
              <template v-else-if="column.field === 'indeTaskMeetingTime'">
                <div class="time-range">
                  <div>{{ row.indeTaskMeetingTime }}</div>
                </div>
              </template>
              <template v-else>
                {{ row[column.field] }}
              </template>
            </template>
          </el-table-column>
        </template>

        <!-- 操作列 -->
        <el-table-column label="操作" width="48" align="center" fixed="right">
          <template #default="{ row }">
            <el-dropdown
              placement="bottom-end"
              @command="(command: string) => handleCommand(command, row)"
              hide-on-click
              :show-timeout="50"
            >
              <div class="more-opt">
                <el-icon class="icon"><MoreFilled /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="border drop-opt">
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="delete">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加会议弹窗 -->
    <AddMeeting v-model="addDialogVisible" @success="onAddSuccess" />

    <!-- 编辑会议弹窗 -->
    <EditMeeting
      v-model="editDialogVisible"
      :meeting="currentEditMeeting"
      @success="onEditSuccess"
    />
  </div>
</template>

<style scoped lang="scss">
.meeting-list {
  height: 100%;
  display: flex;
  flex-direction: column;

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .left {
      display: flex;
      align-items: center;
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-box {
        width: 240px;
      }
    }
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    border-radius: 6px;

    .meeting-table {
      height: 100%;

      .meeting-title {
        cursor: pointer;
        color: var(--el-color-primary);

        &:hover {
          text-decoration: underline;
        }
      }

      .time-range {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 12px;

        .time-separator {
          margin: 2px 0;
          color: var(--el-text-color-secondary);
        }
      }

      .document-types {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        justify-content: center;

        .type-tag {
          color: white;
          border: none;
        }
      }

      .summary-content {
        display: flex;
        align-items: center;
        gap: 4px;

        .summary-icon {
          font-size: 14px;
        }

        span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 150px;
        }
      }

      .more-opt {
        cursor: pointer;
        .icon {
          font-size: 16px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }

  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}

.dropdown-menu {
  min-width: 100px;
}
</style>
