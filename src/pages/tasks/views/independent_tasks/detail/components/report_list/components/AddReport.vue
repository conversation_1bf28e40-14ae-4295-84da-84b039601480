<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import {
  addIndeTaskReport,
  type IndeTaskReportDocEditRequest,
} from '../../../services/ReportService';
import { useIndependentTaskDetailStore } from '../../../IndependentTaskDetailStore';
import FileUpload from '@/components/biz/file_upload/FileUpload.vue';
import type { FileItem } from '@/components/biz/file_upload/types';

interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 获取store实例
const store = useIndependentTaskDetailStore();

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive({
  progress: 0,
  indeTaskDesc: '',
  reportTime: '',
  dataType: 'report',
  indeTaskReportStatus: 1, // 1: 已汇报
  workflowId: '',
  attachments: [] as (FileItem & IndeTaskReportDocEditRequest)[], // 替换remark为attachments
  reportDocs: [],
});

// 表单验证规则
const rules: FormRules = {
  progress: [
    { required: true, message: '请输入任务进度', trigger: 'blur' },
    {
      type: 'number',
      min: 0,
      max: 100,
      message: '进度范围为0-100',
      trigger: 'blur',
    },
  ],
  indeTaskDesc: [
    { required: true, message: '请输入汇报内容', trigger: 'blur' },
  ],
  reportTime: [
    { required: true, message: '请选择汇报时间', trigger: 'change' },
  ],
};

// 显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

// 重置表单
function resetForm() {
  formRef.value?.resetFields();
  Object.assign(formData, {
    progress: 0,
    indeTaskDesc: '',
    reportTime: '',
    dataType: 'report',
    indeTaskReportStatus: 1,
    workflowId: '',
    attachments: [],
    reportDocs: [],
  });
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const taskId = store.formData.indeTaskId;
    if (!taskId) {
      ElMessage.error('任务ID获取失败');
      return;
    }

    await addIndeTaskReport({
      indeTaskId: Number(taskId),
      progress: formData.progress,
      indeTaskDesc: formData.indeTaskDesc,
      reportTime: formData.reportTime,
      indeTaskReportStatus: formData.indeTaskReportStatus,
      workflowId: formData.workflowId,
      reportDocs: formData.attachments.map((file) => ({
        parentIndeTaskReportDocId: file.parentIndeTaskReportDocId,
        docName: file.docName,
        docKey: file.docKey,
        docId: file.docId, // 新建汇报时，docId 通常为空
        folderFlag: file.folderFlag || 0,
      })),
    });

    ElMessage.success('提交汇报成功');
    emit('success');
    resetForm();
  } catch (error) {
    console.error('提交汇报失败:', error);
    ElMessage.error('提交汇报失败');
  }
}

// 取消操作
function handleCancel() {
  resetForm();
  emit('update:modelValue', false);
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="独立任务汇报"
    width="796"
    :before-close="handleCancel"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="136px"
      label-position="right"
      class="form"
    >
      <el-form-item label="汇报内容" prop="indeTaskDesc">
        <el-input
          v-model="formData.indeTaskDesc"
          type="textarea"
          :rows="4"
          placeholder="请输入汇报内容"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="任务进度" prop="progress">
        <el-input-number
          v-model="formData.progress"
          :min="0"
          :max="100"
          placeholder="请输入任务进度"
          style="width: 200px"
        />
        <span style="margin-left: 8px">%</span>
      </el-form-item>

      <el-form-item label="实际开始时间" prop="reportTime">
        <el-date-picker
          v-model="formData.reportTime"
          type="datetime"
          placeholder="请选择汇报时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="附件">
        <FileUpload
          v-model="formData.attachments"
          class="upload-file"
          drag
          multiple
        >
          <div class="title">选择文件</div>
          <div class="tip">
            支持：.rar .zip .doc .docx .pdf .jpg，且单个文件不能超过5M
          </div>
        </FileUpload>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.form {
  padding-right: 32px;
  margin: 0 -24px;

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__content) {
    width: 216px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
