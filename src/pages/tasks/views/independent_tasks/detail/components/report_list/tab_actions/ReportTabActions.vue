<script setup lang="ts">
import { computed, ref, useTemplateRef } from 'vue';
import { useReportTabActionsStore } from './ReportTabActionsStore';
import { useIndependentTaskDetailStore } from '../../../IndependentTaskDetailStore';
import { useTaskChangeDialogStore } from '../../task_change_dialog/TaskChangeDialogStore';
import AddReport from '../components/AddReport.vue';
import TaskChangeDialog from '../../task_change_dialog/TaskChangeDialog.vue';
import type { ReportTabActionsProps, ReportTabActionsEmits } from './types';

// Props定义
const props = defineProps<ReportTabActionsProps>();

// Emits定义
const emit = defineEmits<ReportTabActionsEmits>();

// Store实例
const store = useReportTabActionsStore();
const mainStore = useIndependentTaskDetailStore();
const taskChangeStore = useTaskChangeDialogStore();

// 任务变更对话框状态
const taskChangeVisible = ref(false);
const taskChangeRef = useTemplateRef<any>('taskChangeRef');

// 计算属性：任务变更按钮是否禁用
const isTaskChangeDisabled = computed(() => props.reportsCount === 0);

// 处理进度汇报
function handleProgressReport() {
  store.handleProgressReport();
}

// 添加汇报成功后的回调
function onAddSuccess() {
  store.onAddSuccess();
  // 通知父组件刷新汇报列表
  emit('refresh-reports');
}

// 打开任务变更对话框
function openTaskChangeDialog() {
  // 设置用户列表（从主Store获取）
  taskChangeStore.setUserList(mainStore.userList);

  // 构造符合弹窗组件期望的数据结构
  const taskData = {
    indeTaskName: mainStore.formData.indeTaskName,
    indeTaskResponsible: mainStore.formData.indeTaskResponsible,
    indeTaskType: mainStore.formData.indeTaskType,
    participants: mainStore.formData.participants,
    planStartDate: mainStore.formData.planStartDate,
    planFinishDate: mainStore.formData.planFinishDate,
    actualStartDate: mainStore.formData.actualStartDate,
    workflowId: mainStore.formData.workflowId,
    actualProgress: mainStore.formData.actualProgress,
    duration: mainStore.formData.duration,
    autoProgressFlag: mainStore.formData.autoProgressFlag,
    indeTaskDesc: mainStore.formData.indeTaskDesc,
    indeTaskAcceptCriteria: mainStore.formData.indeTaskAcceptCriteria,
  };

  taskChangeRef.value?.open(mainStore.formData.indeTaskId, taskData);
  taskChangeVisible.value = true;
}

// 任务变更成功后的回调
function handleTaskChangeSuccess() {
  taskChangeVisible.value = false;
  // 刷新任务详情
  mainStore.loadTaskDetail(mainStore.formData.indeTaskId.toString());
  // 通知父组件刷新汇报列表
  emit('refresh-reports');
}
</script>

<template>
  <div class="report-tab-actions">
    <!-- 操作按钮 -->
    <div class="actions-toolbar">
      <el-button @click="handleProgressReport"> 进度汇报 </el-button>
      <el-button @click="openTaskChangeDialog" :disabled="isTaskChangeDisabled">
        任务变更
      </el-button>
    </div>

    <!-- 添加汇报弹窗 -->
    <AddReport v-model="store.addDialogVisible" @success="onAddSuccess" />

    <!-- 任务变更弹窗 -->
    <TaskChangeDialog
      v-model="taskChangeVisible"
      :task-id="mainStore.formData.indeTaskId"
      @success="handleTaskChangeSuccess"
      ref="taskChangeRef"
    />
  </div>
</template>

<style scoped lang="scss">
.report-tab-actions {
  :deep(.el-dialog__header) {
    text-align: left;
  }
  .actions-toolbar {
    display: flex;
    align-items: center;
  }
}
</style>
