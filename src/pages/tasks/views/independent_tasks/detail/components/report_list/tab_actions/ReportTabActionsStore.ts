import { ref, reactive } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import { useIndependentTaskDetailStore } from '../../../IndependentTaskDetailStore';

import type { TaskChangeFormData } from './types';

export const useReportTabActionsStore = defineStore(
  'report-tab-actions',
  () => {
    // 进度汇报弹窗状态
    const addDialogVisible = ref(false);

    // 任务变更对话框状态
    const taskChangeDialogVisible = ref(false);
    const taskChangeLoading = ref(false);

    // 任务变更表单数据
    const taskChangeFormData = reactive<TaskChangeFormData>({
      changeReason: '',
      indeTaskName: '',
      indeTaskResponsible: '',
      planStartDate: '',
      planFinishDate: '',
      duration: 0,
      autoProgressFlag: 0,
      workflowId: '',
      indeTaskDesc: '',
      indeTaskAcceptCriteria: '',
    });

    // 获取主Store实例
    const mainStore = useIndependentTaskDetailStore();

    // 进度汇报
    function handleProgressReport() {
      addDialogVisible.value = true;
    }

    // 任务变更
    function handleTaskChange() {
      // 从详情表单中获取当前值并填入变更表单
      taskChangeFormData.changeReason = '';
      taskChangeFormData.indeTaskName = mainStore.formData.indeTaskName;
      taskChangeFormData.indeTaskResponsible = String(
        mainStore.formData.indeTaskResponsible
      );
      taskChangeFormData.planStartDate = mainStore.formData.planStartDate;
      taskChangeFormData.planFinishDate = mainStore.formData.planFinishDate;
      taskChangeFormData.duration = mainStore.formData.duration;
      taskChangeFormData.autoProgressFlag = mainStore.formData.autoProgressFlag;
      taskChangeFormData.workflowId = mainStore.formData.workflowId;
      taskChangeFormData.indeTaskDesc = mainStore.formData.indeTaskDesc;
      taskChangeFormData.indeTaskAcceptCriteria =
        mainStore.formData.indeTaskAcceptCriteria;

      taskChangeDialogVisible.value = true;
    }

    // 任务变更表单数据更新处理
    function handleTaskChangeFormUpdate(formData: Partial<TaskChangeFormData>) {
      Object.assign(taskChangeFormData, formData);
    }

    // 提交任务变更
    async function handleTaskChangeSubmit() {
      taskChangeLoading.value = true;
      try {
        // 更新主store中的任务变更数据
        mainStore.taskChangeDialogData.changeReason =
          taskChangeFormData.changeReason;
        Object.assign(mainStore.taskChangeDialogData.formData, {
          indeTaskName: taskChangeFormData.indeTaskName,
          indeTaskResponsible: taskChangeFormData.indeTaskResponsible,
          planStartDate: taskChangeFormData.planStartDate,
          planFinishDate: taskChangeFormData.planFinishDate,
          duration: taskChangeFormData.duration,
          autoProgressFlag: taskChangeFormData.autoProgressFlag,
          workflowId: taskChangeFormData.workflowId,
          indeTaskDesc: taskChangeFormData.indeTaskDesc,
          indeTaskAcceptCriteria: taskChangeFormData.indeTaskAcceptCriteria,
        });

        // 调用主store中的任务变更提交方法
        await mainStore.submitTaskChange();
        taskChangeDialogVisible.value = false;
        ElMessage.success('任务变更成功');
        return true;
      } catch (error) {
        console.error('任务变更失败:', error);
        ElMessage.error('任务变更失败');
        return false;
      } finally {
        taskChangeLoading.value = false;
      }
    }

    // 取消任务变更
    function handleTaskChangeCancel() {
      taskChangeDialogVisible.value = false;
      // 重置表单数据
      Object.assign(taskChangeFormData, {
        changeReason: '',
        indeTaskName: '',
        indeTaskResponsible: '',
        planStartDate: '',
        planFinishDate: '',
        duration: 0,
        autoProgressFlag: 0,
        workflowId: '',
        indeTaskDesc: '',
        indeTaskAcceptCriteria: '',
      });
    }

    // 添加汇报成功后的回调
    function onAddSuccess() {
      addDialogVisible.value = false;
      ElMessage.success('添加汇报成功');
    }

    return {
      // 状态
      addDialogVisible,
      taskChangeDialogVisible,
      taskChangeLoading,
      taskChangeFormData,

      // 方法
      handleProgressReport,
      handleTaskChange,
      handleTaskChangeFormUpdate,
      handleTaskChangeSubmit,
      handleTaskChangeCancel,
      onAddSuccess,
    };
  }
);
