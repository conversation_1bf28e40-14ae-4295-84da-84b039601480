<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MoreFilled } from '@element-plus/icons-vue';
import {
  getIndeTaskReportPage,
  deleteIndeTaskReport,
  type IndeTaskReportResponse,
  type IndeTaskReportDocResponse,
} from '../../services/ReportService';
import { addIndeTaskProcessDoc } from '../../services/ProcessDocService';
import { addIndeTaskDeliveryDoc } from '../../services/DeliveryDocService';
import { useIndependentTaskDetailStore } from '../../IndependentTaskDetailStore';
import { submitApproval } from '../../services/ApprovalService';
import { ElMessage, ElMessageBox } from 'element-plus';

// 获取store实例
const store = useIndependentTaskDetailStore();

// 响应式数据
const loading = ref(false);
const reports = ref<IndeTaskReportResponse[]>([]);

// 弹窗状态
// const addDialogVisible = ref(false);
// const editDialogVisible = ref(false);
// const currentEditReport = ref<IndeTaskReportResponse | null>(null);

// 表格列配置（按UI规范调整）
const columns = [
  {
    field: 'progress',
    label: '任务进度',
    width: '144',
    align: 'left',
  },
  {
    field: 'content',
    label: '汇报内容',
    minWidth: '144',
    align: 'left',
  },
  {
    field: 'attachments',
    label: '附件',
    minWidth: '144',
    align: 'left',
  },
  {
    field: 'documentTypes',
    label: '文档类型',
    width: '144',
    align: 'left',
  },
  {
    field: 'reporter',
    label: '汇报人',
    width: '120',
    align: 'left',
  },
  {
    field: 'reportTime',
    label: '汇报时间',
    width: '176',
    align: 'left',
  },
  {
    field: 'status',
    label: '状态',
    width: '144',
    align: 'left',
  },
];

// 获取状态类型
function getStatusType(status: string) {
  const statusMap: Record<string, string> = {
    已提交: 'warning',
    已接受: 'success',
    待清理: 'info',
  };
  return statusMap[status] || 'info';
}

// 获取文档类型样式
function getDocumentTypeStyle(type: string) {
  const typeMap: Record<string, string> = {
    管理类: '#409EFF',
    技术类: '#67C23A',
    合规类: '#E6A23C',
  };
  return typeMap[type] || '#909399';
}

// 格式化进度
function formatProgress(val: number): string {
  return val + '%';
}
// 加载汇报列表
async function loadReports() {
  loading.value = true;

  const taskId = store.formData.indeTaskId;
  if (!taskId) {
    console.warn('任务ID为空，跳过加载汇报列表');
    return;
  }

  const response = await getIndeTaskReportPage({
    indeTaskId: taskId,
  });

  // 转换API数据格式
  reports.value = response.map((item: IndeTaskReportResponse) => ({
    id: item.reportId || 0,
    progress: item.progress || 0,
    content: item.indeTaskDesc || '',
    attachments: item.reportDocs || [],
    documentTypes: item.reportDocs?.map((doc) => doc.docType || '') || [],
    reporter:
      store.userList.find((user) => user.userId === item.createUser)
        ?.userName || '', // API返回的用户信息需要进一步处理
    reportTime: item.reportTime || '',
    status: getReportStatusText(item.indeTaskReportStatus || 0),
    parentTaskId: item.indeTaskId || 0,
    createdAt: item.createTime || '',
    updatedAt: item.updateTime || '',
    reportDocs: item.reportDocs, // 将API返回的reportDocs添加到ReportItem中
    rawData: item,
  }));
  loading.value = false;
}

// 获取汇报状态文本
function getReportStatusText(status: number): string {
  const statusMap: Record<number, string> = {
    0: '待清理',
    1: '已汇报',
    2: '已接受',
  };
  return statusMap[status] || '未知状态';
}

// 添加过程文档
function handleAddProcessDoc(
  taskId: number | string,
  doc: IndeTaskReportDocResponse
) {
  if (!doc || !doc.docId) {
    ElMessage.error('文档ID为空，无法操作');
    return;
  }
  ElMessageBox({
    title: '添加到过程文档',
    message: `是否将《${doc.docName}》添加到过程文档？`,
    type: 'warning',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(async () => {
    // 转换类型，确保所有必需字段都有值
    const processDocData = {
      parentIndeTaskProcessDocId: doc.parentIndeTaskReportDocId,
      folderFlag: doc.folderFlag || 0, // 提供默认值0，防止undefined
      docName: doc.docName || '',
      docKey: doc.docKey || '',
      docId: doc.docId as number, // 我们已经在上面检查了docId不为空
    };
    const res = await addIndeTaskProcessDoc(taskId, processDocData);
    if (res) {
      ElMessage.success('添加成功');
      loadReports();
    }
  });
}

// 添加交付文档
function handleAddDeliveryDoc(
  taskId: number | string,
  doc: IndeTaskReportDocResponse
) {
  if (!doc || !doc.docId) {
    ElMessage.error('文档ID为空，无法操作');
    return;
  }
  ElMessageBox({
    title: '添加到交付文档',
    message: `是否将《${doc.docName}》添加到交付文档？`,
    type: 'warning',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(async () => {
    // 转换类型
    const deliveryDocData = {
      parentIndeTaskDeliveryDocId: doc.parentIndeTaskReportDocId,
      folderFlag: doc.folderFlag || 0,
      docName: doc.docName || '',
      docKey: doc.docKey || '',
      docId: doc.docId as number, // 我们已经在上面检查了docId不为空
      docType: '', // 如果需要可以添加适当的文档类型
    };
    const res = await addIndeTaskDeliveryDoc(taskId, deliveryDocData);
    if (res) {
      ElMessage.success('添加成功');
      loadReports();
    }
  });
}

// 删除文档
function handleDeleteDoc(
  reportId: number | string,
  doc: IndeTaskReportDocResponse
) {
  ElMessageBox({
    title: '添加到交付文档',
    message: `是否将《${doc.docName}》添加到交付文档？`,
    type: 'warning',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(async () => {
    const res = await deleteIndeTaskReport(reportId);
    if (res) {
      ElMessage.success('删除成功');
      loadReports();
    }
  });
}
// 提交评审
async function handleSubmitApproval(data: {
  reportId: number;
  indeTaskId: number;
}): Promise<any> {
  const res = await submitApproval(data);
  if (res) {
    ElMessage.success('提交成功');
    loadReports();
  } else {
    throw new Error('提交失败');
  }
}

// 处理下拉菜单命令（只读模式）
async function handleRowCommand(
  //'submitReview' | 'queryReview'
  command: string,
  row: IndeTaskReportResponse
) {
  if (command === 'submitReview') {
    await handleSubmitApproval({
      reportId: row.reportId!,
      indeTaskId: row.indeTaskId!,
    });
  }

  setTimeout(() => {
    store.switchToApprovalTab();
  }, 200);
}

function handleDocCommand(
  command: string,
  row: IndeTaskReportResponse,
  doc: {
    docId: number;
    docName: string;
    docKey: string;
  }
) {
  const taskId = row.indeTaskId!;
  switch (command) {
    case 'addToProcessDoc':
      handleAddProcessDoc(taskId, doc);
      break;
    case 'addToDeliveryDoc':
      handleAddDeliveryDoc(taskId, doc);
      break;
    case 'delete':
      if (!row.reportId) {
        return ElMessage.error('汇报ID为空，无法操作');
      }
      handleDeleteDoc(row.reportId, doc);
      break;
    default:
      console.log('未知命令:', command);
  }
}

// 暴露给父组件的方法
function openAdd() {
  console.log('打开添加汇报对话框');
}

// 暴露方法
defineExpose({
  openAdd,
  refreshData: loadReports,
  getReportsCount: () => reports.value.length,
});

// 初始化
onMounted(() => {
  loadReports();
});
</script>

<template>
  <div class="report-list">
    <!-- 汇报表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="reports"
        class="table"
        row-key="id"
        stripe
      >
        <el-table-column type="selection" width="50" fixed="left" />
        <!-- 序号列 -->
        <el-table-column type="index" label="#" width="32" align="center" />

        <!-- 动态列 -->
        <template v-for="column in columns" :key="column.field">
          <!-- 任务进度列 -->
          <el-table-column
            v-if="column.field === 'progress'"
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
          >
            <template #default="{ row }">
              <el-progress
                class="progress"
                color="#52C41A"
                :stroke-width="12"
                :percentage="row.progress || 0"
                :format="formatProgress"
              />
            </template>
          </el-table-column>

          <!-- 汇报内容列 -->
          <el-table-column
            v-else-if="column.field === 'content'"
            :property="column.field"
            :label="column.label"
            :min-width="column.minWidth"
            :align="column.align"
          >
            <template #default="{ row }">
              {{ row.content }}
            </template>
          </el-table-column>

          <!-- 附件列 -->
          <el-table-column
            v-else-if="column.field === 'attachments'"
            :property="column.field"
            :label="column.label"
            :min-width="column.minWidth"
            :align="column.align"
          >
            <template #default="{ row }">
              <div class="attachment-cell">
                <div v-if="row.attachments.length > 0" class="attachment-list">
                  <div
                    v-for="(attachment, index) in row.attachments"
                    :key="index"
                    class="attachment-item"
                  >
                    <el-icon class="attachment-icon">📄</el-icon>
                    <span class="attachment-name" :title="attachment">{{
                      attachment.docName
                    }}</span>
                    <el-dropdown
                      placement="bottom-end"
                      @command="(command: string) => handleDocCommand(command,row.rawData, attachment)"
                      hide-on-click
                      :show-timeout="50"
                    >
                      <div class="more-opt">
                        <el-icon class="icon"><MoreFilled /></el-icon>
                      </div>
                      <template #dropdown>
                        <el-dropdown-menu class="border drop-opt">
                          <el-dropdown-item command="addToProcessDoc">
                            添加到任务过程文档
                          </el-dropdown-item>
                          <el-dropdown-item command="addToDeliveryDoc">
                            添加到任务交付文档
                          </el-dropdown-item>
                          <el-dropdown-item command="delete"
                            >删除</el-dropdown-item
                          >
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 文档类型列 -->
          <el-table-column
            v-else-if="column.field === 'documentTypes'"
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
          >
            <template #default="{ row }">
              <div class="document-types">
                <el-tag
                  v-for="(type, index) in row.documentTypes"
                  :key="index"
                  size="medium"
                  class="type-tag"
                  closable
                >
                  {{ type }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <!-- 状态列 -->
          <el-table-column
            v-else-if="column.field === 'status'"
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
          >
            <template #default="{ row }">
              {{ row.status }}
            </template>
          </el-table-column>

          <!-- 其他列 -->
          <el-table-column
            v-else
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
          >
            <template #default="{ row }">
              {{ row[column.field] }}
            </template>
          </el-table-column>
        </template>

        <!-- 操作列 -->
        <el-table-column label="操作" width="48" align="center" fixed="right">
          <template #default="{ row }">
            <el-dropdown
              placement="bottom-end"
              @command="(command:string) => handleRowCommand(command, row)"
              hide-on-click
              :show-timeout="50"
            >
              <div class="more-opt">
                <el-icon class="icon"><MoreFilled /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="border drop-opt">
                  <el-dropdown-item
                    command="submitReview"
                    :disabled="
                      row.dataType === 'acceptTask' ||
                      row.dataType === 'rejectTask'
                    "
                  >
                    提交评审
                  </el-dropdown-item>
                  <el-dropdown-item command="queryReview">
                    查询评审
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<style scoped lang="scss">
.report-list {
  height: 100%;
  display: flex;
  flex-direction: column;

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .left {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-box {
        width: 240px;
      }
    }
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    border-radius: 6px;

    .table {
      height: 100%;

      .progress {
        :deep(.el-progress__text) {
          font-size: 12px !important;
        }
      }

      .content-input {
        :deep(.el-textarea__inner) {
          border: none;
          box-shadow: none;
          resize: none;
          padding: 4px;
          background: transparent;

          &:focus {
            border: 1px solid var(--el-color-primary);
            box-shadow: 0 0 0 1px var(--el-color-primary-light-7);
          }
        }
      }

      .attachment-cell {
        .attachment-list {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .attachment-item {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 2px 4px;
          border-radius: 4px;
          font-size: 12px;

          .attachment-icon {
            font-size: 14px;
          }

          .attachment-name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .document-types {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        justify-content: flex-start;

        .type-tag {
          background-color: #4c76c01a;
          color: #333333a5;
          border: none;
        }
      }

      .drop-opt {
        width: 140px;
      }

      .more-opt {
        cursor: pointer;
        .icon {
          font-size: 16px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}
</style>
