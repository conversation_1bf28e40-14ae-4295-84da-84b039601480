<script setup lang="ts">
import { computed } from 'vue';
import { ElMessage } from 'element-plus';

interface Props {
  modelValue: boolean;
  report: any;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

function handleSubmit() {
  ElMessage.success('编辑汇报成功');
  emit('success');
}

function handleCancel() {
  emit('update:modelValue', false);
}
</script>

<template>
  <el-dialog v-model="dialogVisible" title="编辑汇报" width="600px">
    <div>编辑汇报功能待完善</div>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template> 