// 任务变更表单数据类型
export interface TaskChangeFormData {
  changeReason: string;
  indeTaskName: string;
  indeTaskResponsible: string;
  planStartDate: string;
  planFinishDate: string;
  duration: number;
  autoProgressFlag: number;
  workflowId: string;
  indeTaskDesc: string;
  indeTaskAcceptCriteria: string;
}

// ReportTabActions组件的Props类型
export interface ReportTabActionsProps {
  reportsCount: number;
}

// ReportTabActions组件的Emits类型
export interface ReportTabActionsEmits {
  'refresh-reports': [];
}

// 工作流定义类型（从其他地方复用）
export interface WorkflowDefinition {
  id: string;
  name: string;
  version: string;
}
