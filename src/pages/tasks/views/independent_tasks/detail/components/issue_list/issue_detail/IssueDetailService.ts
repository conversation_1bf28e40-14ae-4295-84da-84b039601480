import { ElMessage } from 'element-plus';
import { send } from '@/libs/request';
import type { IndeTaskIssueDetailResponse } from './types';
import { dictDataList } from '@/service_url/shared';

// 获取独立任务问题详情
export async function getIndeTaskIssueDetail(issueId: string | number): Promise<IndeTaskIssueDetailResponse> {
  try {
    const response: any = await send({
      url: `/projectmanage/inde-task-issue/${issueId}`,
      method: 'get'
    });
    if (response) {
      // Ensure both field names are available for compatibility
      if (response.issueResponsibleName && !response.indeTaskResponsibleName) {
        response.indeTaskResponsibleName = response.issueResponsibleName;
      } else if (response.indeTaskResponsibleName && !response.issueResponsibleName) {
        response.issueResponsibleName = response.indeTaskResponsibleName;
      }
      return response;
    } else {
      throw new Error(response.msg || '获取问题详情失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取问题详情失败');
    throw error;
  }
}

// 关闭独立任务问题
export async function closeIndeTaskIssue(issueId: number): Promise<void> {
  try {
    const response = await send({
      url: `/projectmanage/inde-task-issue/close/${issueId}`,
      method: 'post'
    });
    
    if (response.code !== 200) {
      throw new Error(response.msg || '关闭问题失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '关闭问题失败');
    throw error;
  }
}

// 问题转换为风险
export async function convertIssueToRisk(issueId: number): Promise<void> {
  try {
    const response = await send({
      url: `/projectmanage/inde-task-issue/convert/risk`,
      method: 'post',
      data: {
        indeTaskIssueId: issueId
      }
    });
    
    if (response.code !== 200) {
      throw new Error(response.msg || '转换为风险失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '转换为风险失败');
    throw error;
  }
}

// 获取问题类型选项
export async function getIssueTypeOptions(): Promise<any[]> {
  const response: any = await send({
    method: 'POST',
    url: dictDataList,
    data: {
      dictType: 'inde_task_issue_type'
    }
  });
  
  if (response && Array.isArray(response)) {
    return response.map((item: any) => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  }
  return [];
}

// 获取问题回答列表
export async function getIssueCommentList(issueId: number): Promise<any[]> {
  try {
    const response: any = await send({
      url: '/projectmanage/inde-task-issue-answer/list',
      method: 'post',
      data: {
        indeTaskIssueId: issueId
      }
    });
    
    if (response && Array.isArray(response)) {
      return response;
    } else {
      return [];
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取问题回答列表失败');
    return [];
  }
}

// 添加问题回答
export async function addIssueComment(data: any): Promise<boolean> {
  try {
    const response: any = await send({
      url: '/projectmanage/inde-task-issue-answer/add',
      method: 'post',
      data: {
        indeTaskIssueId: data.indeTaskIssueId,
        indeTaskIssueAnswer: data.indeTaskIssueAnswer,
        issueDocs: data.issueDocs || [],
        remark: data.remark || ''
      }
    });
    
    if (response) {
      return true;
    } else {
      throw new Error(response.msg || '添加问题回答失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '添加问题回答失败');
    throw error;
  }
}

// 编辑问题回答
export async function editIssueComment(data: any): Promise<boolean> {
  try {
    const response: any = await send({
      url: '/projectmanage/inde-task-issue-answer/edit',
      method: 'post',
      data: {
        indeTaskIssueId: data.indeTaskIssueId,
        indeTaskIssueAnswer: data.content,
        indeTaskIssueAnswerId: data.indeTaskIssueAnswerId,
        issueDocs: data.issueDocs || [],
        remark: data.remark || ''
      }
    });
    
    if (response) {
      return true;
    } else {
      throw new Error(response.msg || '编辑问题回答失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '编辑问题回答失败');
    throw error;
  }
}

// 删除问题回答
export async function deleteIssueComment(commentId: number): Promise<boolean> {
  try {
    const response: any = await send({
      url: '/projectmanage/inde-task-issue-answer/delete',
      method: 'post',
      data: {
        indeTaskIssueAnswerId: commentId
      }
    });
    
    if (response) {
      return true;
    } else {
      throw new Error(response.msg || '删除问题回答失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '删除问题回答失败');
    throw error;
  }
} 