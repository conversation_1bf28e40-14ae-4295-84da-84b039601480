/**
 * IndeTaskIssueResponse，独立任务问题
 */
export interface IndeTaskIssueDetailResponse {
  completionDate?: string;
  createTime?: string;
  createUser?: number;
  createUserName?: string;
  deliveryDocs?: IndeTaskDeliveryDocResponse[];
  disCloseFlag?: number;
  estDuration?: number;
  indeTaskId?: number;
  indeTaskIssueDocs?: IndeTaskIssueDocResponse[];
  indeTaskIssueId?: number;
  indeTaskIssueLevel?: number;
  indeTaskIssueName?: string;
  indeTaskIssueStatus?: number;
  indeTaskIssueType?: string;
  indeTaskResponsible?: number;
  indeTaskResponsibleName?: string;
  issueResponsibleName?: string; // Add this field for compatibility
  issueStatusName?: string;
  issueTypeName?: string;
  levelName?: string;
  planEndTime?: string;
  planStartTime?: string;
  publicReplyFlag?: number;
  remark?: string;
  tenantId?: number;
  updateTime?: string;
  updateUser?: number;
  issueDocs?: IndeTaskIssueDocResponse[];
}

export interface IndeTaskIssueDocResponse {
  docId?: number;
  docKey?: string;
  docName?: string;
  docPath?: string;
  docSize?: number;
  docType?: string;
  folderFlag?: number;
  indeTaskIssueDocId?: number;
  indeTaskIssueId?: number;
  parentIndeTaskIssueDocId?: number;
  tenantId?: number;
}

export interface IndeTaskDeliveryDocResponse {
  docId?: number;
  docKey?: string;
  docName?: string;
  docPath?: string;
  docSize?: number;
  docType?: string;
  folderFlag?: number;
  indeTaskDeliveryDocId?: number;
  indeTaskId?: number;
  parentIndeTaskDeliveryDocId?: number;
  tenantId?: number;
}

export interface IndeTaskIssueCommentResponse {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人
   */
  createUser?: number;
  /**
   * 创建人姓名
   */
  createUserName?: string;
  /**
   * 项目任务问题回答内容
   */
  projTaskIssueAnswer?: string;
  /**
   * 项目任务问题回答ID
   */
  projTaskIssueAnswerId?: number;
  /**
   * 项目任务问题回答时间
   */
  projTaskIssueAnswerTime?: string;
  /**
   * 项目任务问题讨论
   */
  projTaskIssueDisscution?: string;
  /**
   * 项目任务问题ID
   */
  projTaskIssueId?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 文档列表
   */
  issueDocs?: IndeTaskIssueDocResponse[];
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 修改时间
   */
  updateTime?: string;
  /**
   * 修改人
   */
  updateUser?: number;
  
  // Backward compatibility fields
  indeTaskIssueComment?: string;
  indeTaskIssueCommentId?: number;
  indeTaskIssueId?: number;
  
  /**
   * 独立任务问题回答内容
   */
  indeTaskIssueAnswer?: string;
  /**
   * 独立任务问题回答ID
   */
  indeTaskIssueAnswerId?: number;
}

export interface IndeTaskIssueCommentAddRequest {
  /**
   * 项目任务问题回答内容
   */
  projTaskIssueAnswer?: string;
  /**
   * 项目任务问题讨论
   */
  projTaskIssueDisscution?: string;
  /**
   * 项目任务问题ID
   */
  projTaskIssueId?: number;
  /**
   * 文档列表
   */
  issueDocs?: IndeTaskIssueDocEditRequest[];
  /**
   * 备注
   */
  remark?: string;
  
  // Backward compatibility fields
  indeTaskIssueComment?: string;
  indeTaskIssueId: number;
  
  // 新API字段
  /**
   * 独立任务问题回答内容
   */
  indeTaskIssueAnswer?: string;
  /**
   * 独立任务问题讨论
   */
  indeTaskIssueDisscution?: string;
}

export interface IndeTaskIssueCommentEditRequest {
  /**
   * 项目任务问题回答内容
   */
  projTaskIssueAnswer?: string;
  /**
   * 项目任务问题回答ID
   */
  projTaskIssueAnswerId?: number;
  /**
   * 项目任务问题回答时间
   */
  projTaskIssueAnswerTime?: string;
  /**
   * 项目任务问题讨论
   */
  projTaskIssueDisscution?: string;
  /**
   * 项目任务问题ID
   */
  projTaskIssueId?: number;
  /**
   * 文档列表
   */
  issueDocs?: IndeTaskIssueDocEditRequest[];
  /**
   * 备注
   */
  remark?: string;
  
  // Backward compatibility fields
  indeTaskIssueComment?: string;
  indeTaskIssueCommentId?: number;
  indeTaskIssueId?: number;
  
  // 新API字段
  /**
   * 独立任务问题回答内容
   */
  indeTaskIssueAnswer?: string;
  /**
   * 独立任务问题回答ID
   */
  indeTaskIssueAnswerId?: number;
  /**
   * 独立任务问题回答时间
   */
  indeTaskIssueAnswerTime?: string;
  /**
   * 独立任务问题讨论
   */
  indeTaskIssueDisscution?: string;
}

export interface IndeTaskIssueDocEditRequest {
  /**
   * 是否为问题评论(1是,0 否)
   */
  commentFlag?: number;
  /**
   * 是否为问题回答(1是,0 否)
   */
  answerFlag?: number;
  /**
   * 附件id(关联附件表)
   */
  docId?: number;
  /**
   * 附件唯一标识
   */
  docKey?: string;
  /**
   * 附件名
   */
  docName?: string;
  /**
   * 是否文件夹(0 否,1 是)
   */
  folderFlag?: number;
  /**
   * 自增主键
   */
  indeTaskIssueDocId?: number;
  /**
   * 独立任务问题ID,问题评论
   */
  indeTaskIssueId?: number;
  /**
   * 父文档ID,或文件夹ID
   */
  parentIndeTaskIssueDocId?: number;
} 