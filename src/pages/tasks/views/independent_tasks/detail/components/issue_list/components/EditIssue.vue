<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import BtPicker from '@/components/non_biz/bt_picker/BtPicker.vue';
import { editIndeTaskIssue } from '../../../services/IssueService';
import { useIssueStore } from '../IssueStore';
import { storeToRefs } from 'pinia';

// 问题数据类型
interface IssueItem {
  id: string | number;
  name: string;
  status: string;
  issueStatus: string;
  level: string;
  proposer: string;
  responsibleUser: string;
  description: string;
  solution: string;
  issueType?: string | number;
}

// 定义 props
interface Props {
  modelValue: boolean;
  issue: IssueItem | null;
}

// 定义 emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 获取store实例
const issueStore = useIssueStore();

// 从store中获取用户列表和问题类型
const { userList, issueTypeOptions } = storeToRefs(issueStore);

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive({
  id: 0,
  name: '',
  description: '',
  level: '一般',
  proposer: '',
  responsibleUser: '',
  indeTaskResponsible: undefined as number | undefined,
  solution: '',
  status: '进行中',
  issueStatus: '待解决',
  indeTaskIssueType: undefined as string | number | undefined,
});

// 表单验证规则
const rules: FormRules = {
  name: [{ required: true, message: '请输入问题名称', trigger: 'blur' }],
  level: [{ required: true, message: '请选择问题级别', trigger: 'change' }],
  indeTaskResponsible: [
    { required: true, message: '请选择责任人', trigger: 'change' },
  ],
  indeTaskIssueType: [
    { required: true, message: '请选择问题类型', trigger: 'change' },
  ],
};

// 问题级别选项
const levelOptions = [
  { label: '严重', value: '严重' },
  { label: '重要', value: '重要' },
  { label: '一般', value: '一般' },
  { label: '轻微', value: '轻微' },
];

// 状态选项
const statusOptions = [
  { label: '进行中', value: '进行中' },
  { label: '已解决', value: '已解决' },
  { label: '已关闭', value: '已关闭' },
];

// 问题状态选项
const issueStatusOptions = [
  { label: '待解决', value: '待解决' },
  { label: '解决中', value: '解决中' },
  { label: '已解决', value: '已解决' },
  { label: '无法解决', value: '无法解决' },
];

// 提交状态
const submitting = ref(false);

// 显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

// 监听问题数据变化，更新表单
watch(
  () => props.issue,
  (newIssue) => {
    if (newIssue) {
      Object.assign(formData, {
        id: newIssue.id,
        name: newIssue.name,
        description: newIssue.description,
        level: newIssue.level,
        proposer: newIssue.proposer,
        responsibleUser: newIssue.responsibleUser,
        solution: newIssue.solution,
        status: newIssue.status,
        issueStatus: newIssue.issueStatus,
        indeTaskIssueType: newIssue.issueType,
      });

      // 如果已有问题数据，尝试匹配用户ID
      const responsibleUser = newIssue.responsibleUser;
      if (responsibleUser && userList.value.length > 0) {
        const user = userList.value.find((u) => u.userName === responsibleUser);
        if (user) {
          formData.indeTaskResponsible = user.userId;
        }
      }
    }
  },
  { immediate: true }
);

// 重置表单
function resetForm() {
  formRef.value?.resetFields();
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    submitting.value = true;

    // 构造更新数据
    const updateData = {
      indeTaskIssueId: Number(formData.id),
      indeTaskIssueName: formData.name,
      remark: formData.description,
      indeTaskIssueStatus: getIssueStatusValue(formData.issueStatus),
      indeTaskIssueLevel: getIssueLevelValue(formData.level),
      indeTaskResponsible: formData.indeTaskResponsible,
      indeTaskIssueType: formData.indeTaskIssueType,
    };

    await editIndeTaskIssue(updateData);

    ElMessage.success('更新问题成功');
    emit('success');
    emit('update:modelValue', false);
  } catch (error) {
    console.error('更新问题失败:', error);
    ElMessage.error('更新问题失败');
  } finally {
    submitting.value = false;
  }
}

// 获取问题状态值
function getIssueStatusValue(status: string): number {
  const statusMap: Record<string, number> = {
    未开始: 1,
    进行中: 2,
    已完成: 3,
    已关闭: 4,
    待解决: 1,
    解决中: 2,
    已解决: 3,
    无法解决: 4,
  };
  return statusMap[status] || 1;
}

// 获取问题级别值
function getIssueLevelValue(level: string): number {
  const levelMap: Record<string, number> = {
    严重: 1,
    重要: 2,
    一般: 3,
    轻微: 4,
  };
  return levelMap[level] || 3;
}

// 取消操作
function handleCancel() {
  resetForm();
  emit('update:modelValue', false);
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化数据
  issueStore.initialize();
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑问题"
    width="800px"
    :before-close="handleCancel"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="136px"
      label-position="right"
      class="form"
    >
      <el-form-item label="问题名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入问题名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="问题类型" prop="indeTaskIssueType">
        <el-select
          v-model="formData.indeTaskIssueType"
          placeholder="请选择问题类型"
          style="width: 100%"
        >
          <el-option
            v-for="option in issueTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select
          v-model="formData.status"
          placeholder="请选择状态"
          style="width: 100%"
        >
          <el-option
            v-for="option in statusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="问题状态" prop="issueStatus">
        <el-select
          v-model="formData.issueStatus"
          placeholder="请选择问题状态"
          style="width: 100%"
        >
          <el-option
            v-for="option in issueStatusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="问题级别" prop="level">
        <el-select
          v-model="formData.level"
          placeholder="请选择问题级别"
          style="width: 100%"
        >
          <el-option
            v-for="option in levelOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="提出人" prop="proposer">
        <el-input
          v-model="formData.proposer"
          placeholder="请输入提出人"
          disabled
        />
      </el-form-item>

      <el-form-item label="责任人" prop="indeTaskResponsible">
        <BtPicker
          class="select"
          title="责任人"
          :list="
            userList.map((item) => ({
              label: item.userName,
              value: item.userId,
            }))
          "
          showSearch
          v-model="formData.indeTaskResponsible"
        />
      </el-form-item>

      <el-form-item label="问题描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入问题描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="解决方案">
        <el-input
          v-model="formData.solution"
          type="textarea"
          :rows="3"
          placeholder="请输入解决方案"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.form {
  padding-right: 32px;
  margin: 0 -24px;

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__content) {
    width: 216px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.select {
  width: 100%;
}
</style>
