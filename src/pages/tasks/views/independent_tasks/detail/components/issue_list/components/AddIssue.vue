<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { QuestionFilled } from '@element-plus/icons-vue';
import { useIndependentTaskDetailStore } from '../../../IndependentTaskDetailStore';
import { addIndeTaskIssue } from '../../../services/IssueService';
import FileUpload from '@/components/biz/file_upload/FileUpload.vue';
import BtPicker from '@/components/non_biz/bt_picker/BtPicker.vue';
import { useIssueStore } from '../IssueStore';
import { storeToRefs } from 'pinia';

// 定义 props
interface Props {
  modelValue: boolean;
}

// 定义 emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 获取store实例
const store = useIndependentTaskDetailStore();
const issueStore = useIssueStore();

// 从store中获取用户列表和问题类型
const { userList, issueTypeOptions } = storeToRefs(issueStore);

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive({
  indeTaskIssueName: '',
  remark: '',
  indeTaskResponsible: undefined as number | undefined,
  closingTime: '',
  indeTaskIssueType: undefined as string | number | undefined,
  indeTaskIssueLevel: 3,
  disCloseFlag: 0,
  publicReplyFlag: 0,
  issueDocs: [] as any[],
});

// 表单验证规则
const rules: FormRules = {
  indeTaskIssueName: [
    { required: true, message: '请输入问题名称', trigger: 'blur' },
  ],
  indeTaskResponsible: [
    { required: true, message: '请选择负责人', trigger: 'change' },
  ],
  closingTime: [
    { required: true, message: '请选择截止日期', trigger: 'change' },
  ],
  indeTaskIssueType: [
    { required: true, message: '请选择问题类型', trigger: 'change' },
  ],
  indeTaskIssueLevel: [
    { required: true, message: '请选择问题级别', trigger: 'change' },
  ],
};

// 问题级别选项
const issueLevelOptions = [
  { label: '高', value: 1 },
  { label: '中', value: 2 },
  { label: '低', value: 3 },
];

// 提交状态
const submitting = ref(false);

// 显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

// 重置表单
function resetForm() {
  formRef.value?.resetFields();
  Object.assign(formData, {
    indeTaskIssueName: '',
    remark: '',
    indeTaskResponsible: undefined,
    closingTime: '',
    indeTaskIssueType: undefined,
    indeTaskIssueLevel: 3,
    disCloseFlag: 0,
    publicReplyFlag: 0,
    issueDocs: [],
  });
}

// 处理文件上传
function handleFileUpload(files: any[]) {
  formData.issueDocs = files.map((file) => ({
    docId: file.id,
    docKey: file.key,
    docName: file.name,
    folderFlag: 0,
    parentIndeTaskIssueDocId: undefined,
  }));
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    submitting.value = true;

    // 构造请求数据
    const requestData = {
      indeTaskId: Number(store.formData.indeTaskId),
      indeTaskIssueName: formData.indeTaskIssueName,
      remark: formData.remark,
      indeTaskResponsible: formData.indeTaskResponsible,
      closingTime: formData.closingTime,
      indeTaskIssueType: formData.indeTaskIssueType!,
      indeTaskIssueLevel: formData.indeTaskIssueLevel,
      disCloseFlag: formData.disCloseFlag,
      publicReplyFlag: formData.publicReplyFlag,
      issueDocs: formData.issueDocs,
    };

    await addIndeTaskIssue(requestData);

    ElMessage.success('新建问题成功');
    emit('success');
    resetForm();
  } catch (error) {
    console.error('新建问题失败:', error);
    ElMessage.error('新建问题失败');
  } finally {
    submitting.value = false;
  }
}

// 取消操作
function handleCancel() {
  resetForm();
  emit('update:modelValue', false);
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化数据
  issueStore.initialize();
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="新建任务问题"
    width="800px"
    :before-close="handleCancel"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="136px"
      label-position="right"
      class="form"
    >
      <!-- 问题名称 -->
      <el-form-item label="问题名称" prop="indeTaskIssueName">
        <el-input
          v-model="formData.indeTaskIssueName"
          placeholder="请输入"
          maxlength="100"
          show-word-limit
          style="width: 100%"
        />
      </el-form-item>

      <!-- 问题说明 -->
      <el-form-item label="问题说明" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="4"
          placeholder="请输入"
          maxlength="500"
          show-word-limit
          style="width: 100%"
        />
      </el-form-item>

      <!-- 负责人和截止日期 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人" prop="indeTaskResponsible">
            <BtPicker
              class="select"
              title="负责人"
              :list="
                userList.map((item) => ({
                  label: item.userName,
                  value: item.userId,
                }))
              "
              showSearch
              v-model="formData.indeTaskResponsible"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="截止日期" prop="closingTime">
            <el-date-picker
              v-model="formData.closingTime"
              type="datetime"
              placeholder="请选择"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 问题类型和问题级别 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="问题类型" prop="indeTaskIssueType">
            <el-select
              v-model="formData.indeTaskIssueType"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="option in issueTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="问题级别" prop="indeTaskIssueLevel">
            <el-select
              v-model="formData.indeTaskIssueLevel"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="option in issueLevelOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 是否公开和公开回复 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="disCloseFlag">
            <template #label>
              <span>是否公开</span>
              <el-tooltip
                content="注：公开问题所有项目成员课件；私有问题仅项目经理和负责人可见。"
                placement="top"
              >
                <el-icon class="tooltip-icon">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-switch
              v-model="formData.disCloseFlag"
              :active-value="1"
              :inactive-value="0"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="publicReplyFlag">
            <template #label>
              <span>公开回复</span>
              <el-tooltip
                content="注：开启后所有项目成员都可以回复问题"
                placement="top"
              >
                <el-icon class="tooltip-icon">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-switch
              v-model="formData.publicReplyFlag"
              :active-value="1"
              :inactive-value="0"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 附件 -->
      <el-form-item label="附件">
        <div class="upload-container">
          <FileUpload
            :file-list="formData.issueDocs"
            @change="handleFileUpload"
            accept=".rar,.zip,.doc,.docx,.pdf,.jpg"
            :max-size="5"
            :max-count="10"
            multiple
          />
          <div class="upload-tips">
            支持：.rar .zip .doc .docx .pdf .jpg，且单个文件不能超过5M
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.form {
  padding-right: 32px;
  margin: 0 -24px;

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__content) {
    width: 216px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-item-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

.upload-container {
  width: 100%;

  .upload-tips {
    font-size: 12px;
    color: #666;
    margin-top: 8px;
  }
}

.tooltip-icon {
  margin-left: 4px;
  font-size: 14px;
  color: #909399;
  cursor: help;
}

.select {
  width: 100%;
}
</style>
