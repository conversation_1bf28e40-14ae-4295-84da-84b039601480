import { ref } from 'vue';
import { defineStore } from 'pinia';
import { send } from '@/libs/request';
import { dictDataList } from '@/service_url/shared';
import { getUserList as getUserListService } from '@/pages/tasks/views/independent_tasks/list/components/add_task/AddTaskService';
import type { UserOption } from '@/pages/tasks/views/independent_tasks/list/components/add_task/types';

// 类型定义
interface IssueTypeOption {
  label: string;
  value: string | number;
}

// 定义Issue Store
export const useIssueStore = defineStore('issue-store', () => {
  // 用户列表
  const userList = ref<UserOption[]>([]);
  const loadingUsers = ref(false);

  // 问题类型选项
  const issueTypeOptions = ref<IssueTypeOption[]>([]);
  const loadingIssueTypes = ref(false);

  // 加载用户列表
  async function loadUserList() {
    if (loadingUsers.value) return;
    
    loadingUsers.value = true;
    try {
      // 使用与AddTask相同的方法获取用户列表
      const result = await getUserListService();
      userList.value = result || [];
    } catch (error) {
      console.error('加载用户列表失败:', error);
    } finally {
      loadingUsers.value = false;
    }
  }

  // 加载问题类型列表
  async function loadIssueTypes() {
    if (loadingIssueTypes.value) return;
    
    loadingIssueTypes.value = true;
    try {
      const response: any = await send({
        method: 'POST',
        url: dictDataList,
        data: {
          dictType: 'inde_task_issue_type'
        }
      });
      
      if (response && Array.isArray(response)) {
        issueTypeOptions.value = response.map((item: any) => ({
          label: item.dictLabel,
          value: item.dictValue
        }));
      }
    } catch (error) {
      console.error('加载问题类型列表失败:', error);
    } finally {
      loadingIssueTypes.value = false;
    }
  }

  // 初始化 - 加载所有必要数据
  async function initialize() {
    await Promise.all([
      loadUserList(),
      loadIssueTypes()
    ]);
  }

  return {
    userList,
    loadingUsers,
    issueTypeOptions,
    loadingIssueTypes,
    loadUserList,
    loadIssueTypes,
    initialize
  };
}); 