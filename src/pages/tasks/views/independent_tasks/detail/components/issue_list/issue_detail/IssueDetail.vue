<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useIssueDetailStore } from './IssueDetailStore';
import { ElMessageBox, ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';
import { Document, ArrowLeft } from '@element-plus/icons-vue';
import BtPicker from '@/components/non_biz/bt_picker/BtPicker.vue';
import { editIndeTaskIssue } from '../../../services/IssueService';
import { storeToRefs } from 'pinia';
import type { IndeTaskIssueCommentResponse, IndeTaskIssueDocResponse } from './types';

// Simple user info hook implementation
const useUserInfo = () => {
  // In a real implementation, this would get the current user from a store or API
  return {
    userId: 1,  // Mocked user ID for demo
    userName: '当前用户' // Mocked username
  };
};

const route = useRoute();
const store = useIssueDetailStore();
const { userList, issueTypeOptions } = storeToRefs(store);

// 获取当前用户信息
const userInfo = useUserInfo();
const currentUserId = userInfo.userId;


// 表单引用
const formRef = ref<FormInstance>();
const commentFormRef = ref<FormInstance>();
const uploadRef = ref();

// 表单数据
const formData = reactive({
  indeTaskIssueId: 0,
  indeTaskIssueName: '',
  remark: '',
  indeTaskIssueLevel: 3,
  indeTaskIssueType: undefined as string | number | undefined,
  indeTaskIssueStatus: 1,
  indeTaskResponsible: undefined as number | undefined,
  issueResponsibleName: '',
  disCloseFlag: 0,
  publicReplyFlag: 0,
  closingTime: ''
});

// 评论表单数据
const commentForm = reactive({
  content: '',
  fileList: [] as any[],
});

// 提交状态
const submitting = ref(false);
const submittingComment = ref(false);
const loading = ref(false);

// 编辑评论状态
const isEditingComment = ref(false);
const editingCommentId = ref<number | null>(null);

// 问题级别选项
const levelOptions = [
  { label: '严重', value: 1 },
  { label: '重要', value: 2 },
  { label: '一般', value: 3 },
  { label: '轻微', value: 4 },
];

// 问题状态选项
const statusOptions = [
  { label: '待提交', value: 1 },
  { label: '待解决', value: 2 },
  { label: '处理中', value: 3 },
  { label: '已处理', value: 4 },
  { label: '已解决', value: 5 },
  { label: '未解决', value: 6 },
];

// 表单验证规则
const rules = {
  indeTaskIssueName: [
    { required: true, message: '请输入问题名称', trigger: 'blur' },
  ],
  indeTaskIssueLevel: [
    { required: true, message: '请选择问题级别', trigger: 'change' },
  ],
  indeTaskIssueType: [
    { required: true, message: '请选择问题类型', trigger: 'change' },
  ],
  indeTaskResponsible: [
    { required: true, message: '请选择责任人', trigger: 'change' },
  ],
};

// 处理文件下载
function downloadFile(file: { docId?: number; docName?: string }) {
  if (!file.docId) return;
  
  const downloadUrl = `/system/oss/download?fileId=${file.docId}`;
  
  // 创建一个隐藏的a标签并点击它来触发下载
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.target = '_blank';
  link.download = file.docName || '文件下载';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// 处理文件上传改变
function handleFileChange(file: any) {
  console.log('文件上传改变:', file);
}

// 处理文件移除
function handleFileRemove(file: any) {
  console.log('文件移除:', file);
  commentForm.fileList = commentForm.fileList.filter((f: any) => f.uid !== file.uid);
}

// 提交评论
async function handleSubmitComment() {
  if (!commentForm.content.trim()) {
    ElMessage.warning('请输入评论内容');
    return;
  }
  submittingComment.value = true;
  try {
    // 上传文件并获取文件ID (这里需要实现文件上传逻辑)
    // 此处假设我们已经上传了文件并获取了文件ID
    const fileIds = await uploadCommentFiles();
    
    if (isEditingComment.value && editingCommentId.value) {
      // 编辑评论
      await store.editIssueCommentAction({
        indeTaskIssueAnswerId: editingCommentId.value,
        indeTaskIssueId: formData.indeTaskIssueId,
        indeTaskIssueAnswer: commentForm.content,
        indeTaskIssueDisscution: '',
        remark: '',
        issueDocs: fileIds.map(item => ({
          docId: item.docId,
          docName: item.fileName,
          answerFlag: 1
        }))
      });
    } else {
      // 添加评论
      await store.addIssueCommentAction({
        indeTaskIssueId: formData.indeTaskIssueId,
        indeTaskIssueAnswer: commentForm.content,
        indeTaskIssueDisscution: '',
        remark: '',
        issueDocs: fileIds.map(item => ({
          docId: item.docId,
          docName: item.fileName,
          answerFlag: 1
        }))
      });
    }
    
    // 清空表单
    commentForm.content = '';
    commentForm.fileList = [];
    isEditingComment.value = false;
    editingCommentId.value = null;
    
  } catch (error) {
    console.error('提交评论失败:', error);
    ElMessage.error('提交评论失败');
  } finally {
    submittingComment.value = false;
  }
}

// 上传评论文件 (这里需要实现实际的文件上传逻辑)
async function uploadCommentFiles() {
  // 这里是一个模拟的实现，实际项目中需要调用文件上传API
  // 返回格式为 { docId: number, fileName: string }[]
  const uploadedFiles = commentForm.fileList.map(file => ({
    docId: Math.floor(Math.random() * 1000000),  // 模拟文件ID
    fileName: file.name
  }));
  
  return uploadedFiles;
}

// 处理编辑评论
function handleEditComment(comment: IndeTaskIssueCommentResponse) {
  isEditingComment.value = true;
  editingCommentId.value = comment.projTaskIssueAnswerId || comment.indeTaskIssueCommentId || null;
  commentForm.content = comment.projTaskIssueAnswer || comment.indeTaskIssueComment || '';
  
  // 如果有附件，需要处理附件列表
  if (comment.issueDocs && comment.issueDocs.length > 0) {
    commentForm.fileList = comment.issueDocs.map((doc: IndeTaskIssueDocResponse) => ({
      name: doc.docName,
      docId: doc.docId,
      indeTaskIssueDocId: doc.indeTaskIssueDocId
    }));
  }
  
  // 滚动到评论表单
  setTimeout(() => {
    document.querySelector('.add-comment-form')?.scrollIntoView({ behavior: 'smooth' });
  }, 100);
}

// 处理删除评论
function handleDeleteComment(comment: IndeTaskIssueCommentResponse) {
  const commentId = comment.projTaskIssueAnswerId || comment.indeTaskIssueCommentId;
  if (!commentId) return;
  
  ElMessageBox.confirm(
    '确定要删除此评论吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    await store.deleteIssueCommentAction(commentId);
  }).catch(() => {});
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    submitting.value = true;
    
    // 构造更新数据
    const updateData = {
      indeTaskIssueId: Number(formData.indeTaskIssueId),
      indeTaskIssueName: formData.indeTaskIssueName,
      remark: formData.remark,
      indeTaskIssueLevel: formData.indeTaskIssueLevel,
      indeTaskIssueType: formData.indeTaskIssueType,
      indeTaskIssueStatus: formData.indeTaskIssueStatus,
      indeTaskResponsible: formData.indeTaskResponsible,
      closingTime: formData.closingTime,
      disCloseFlag: formData.disCloseFlag,
      publicReplyFlag: formData.publicReplyFlag
    };
    
    await editIndeTaskIssue(updateData);
    
    ElMessage.success('更新问题成功');
    await store.loadIssueDetail(String(formData.indeTaskIssueId));
  } catch (error) {
    console.error('更新问题失败:', error);
    ElMessage.error('更新问题失败');
  } finally {
    submitting.value = false;
  }
}

// 关闭问题
function handleCloseIssue() {
  ElMessageBox.confirm(
    '确定要关闭此问题吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    await store.closeIssue();
    loadIssueDetail();
  });
}

// 转为风险
function handleConvertToRisk() {
  ElMessageBox.confirm(
    '确定要将此问题转为风险吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    await store.convertToRisk();
    loadIssueDetail();
  });
}

// 重新指派问题
const reassignDialogVisible = ref(false);
const selectedUserId = ref<number | undefined>(undefined);

function handleReassign() {
  reassignDialogVisible.value = true;
  selectedUserId.value = undefined;
}

async function submitReassign() {
  if (!selectedUserId.value) {
    ElMessage.warning('请选择负责人');
    return;
  }
  
  const success = await store.assignIssue(selectedUserId.value);
  if (success) {
    reassignDialogVisible.value = false;
    selectedUserId.value = undefined;
  }
}

// 返回任务详情页
function goBack() {
  store.backToTaskDetail();
}

// 加载问题详情
async function loadIssueDetail() {
  loading.value = true;
  try {
    const issueId = route.params.issueId as string;
    await store.loadIssueDetail(issueId);
    
    // 加载成功后更新表单数据
    Object.assign(formData, {
      indeTaskIssueId: store.issueDetail.indeTaskIssueId,
      indeTaskIssueName: store.issueDetail.indeTaskIssueName,
      remark: store.issueDetail.remark || '',
      indeTaskIssueLevel: store.issueDetail.indeTaskIssueLevel,
      indeTaskIssueType: store.issueDetail.indeTaskIssueType,
      indeTaskIssueStatus: store.issueDetail.indeTaskIssueStatus,
      indeTaskResponsible: store.issueDetail.indeTaskResponsible,
      issueResponsibleName: store.issueDetail.issueResponsibleName || '',
      disCloseFlag: store.issueDetail.disCloseFlag || 0,
      publicReplyFlag: store.issueDetail.publicReplyFlag || 0,
      closingTime: store.issueDetail.completionDate || '' // Using completionDate as closingTime
    });
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  // 初始化数据
  await store.initialize();
  loadIssueDetail();
});
</script>

<template>
  <div class="issue-detail">
    <!-- 顶部栏 -->
    <div class="header">
      <div class="back">
        <el-button type="text" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
      <div class="title">问题详情</div>
      <div class="actions">
        <el-button @click="goBack">返回</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">保存</el-button>
        <el-button 
          type="warning" 
          @click="handleCloseIssue"
          :disabled="formData.indeTaskIssueStatus === 4"
        >
          关闭问题
        </el-button>
        <el-button 
          type="danger" 
          @click="handleConvertToRisk"
          :disabled="formData.indeTaskIssueStatus === 4"
        >
          转为风险
        </el-button>
        <el-button 
          type="info" 
          @click="handleReassign"
        >
          重新指派
        </el-button>
      </div>
    </div>

    <div v-loading="loading" class="issue-content">
      <!-- 表单内容 -->
      <el-form class="detail-form" label-width="120px" :model="formData" :rules="rules" ref="formRef">
        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="问题名称" prop="indeTaskIssueName">
                <el-input 
                  v-model="formData.indeTaskIssueName" 
                  placeholder="请输入问题名称"
                  maxlength="100"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="问题状态" prop="indeTaskIssueStatus">
                <el-select
                  v-model="formData.indeTaskIssueStatus"
                  placeholder="请选择问题状态"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in statusOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="问题级别" prop="indeTaskIssueLevel">
                <el-select
                  v-model="formData.indeTaskIssueLevel"
                  placeholder="请选择问题级别"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in levelOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="问题类型" prop="indeTaskIssueType">
                <el-select
                  v-model="formData.indeTaskIssueType"
                  placeholder="请选择问题类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in issueTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="责任人" prop="indeTaskResponsible">
                <BtPicker
                  class="select"
                  title="责任人"
                  :list="
                    userList.map((item) => ({
                      label: item.userName,
                      value: item.userId,
                    }))
                  "
                  showSearch
                  v-model="formData.indeTaskResponsible"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="提出人">
                <el-input v-model="store.issueDetail.createUserName" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="截止时间">
                <el-date-picker
                  v-model="formData.closingTime"
                  type="date"
                  placeholder="选择截止时间"
                  style="width: 100%"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="提出时间">
                <el-input v-model="store.issueDetail.createTime" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="完成时间">
                <el-input v-model="store.issueDetail.completionDate" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="更新时间">
                <el-input v-model="store.issueDetail.updateTime" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否公开">
                <el-switch
                  v-model="formData.disCloseFlag"
                  :active-value="1"
                  :inactive-value="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="公开回复">
                <el-switch
                  v-model="formData.publicReplyFlag"
                  :active-value="1"
                  :inactive-value="0"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row>
            <el-col :span="24">
              <el-form-item label="问题描述">
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  :rows="5"
                  placeholder="请输入问题描述"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        
        <!-- 问题评论列表 -->
        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>讨论记录</span>
            </div>
          </template>
          
          <div v-loading="store.loadingComments" class="issue-comments">
            <!-- 评论列表 -->
            <div v-if="store.issueComments && store.issueComments.length > 0" class="comment-list">
              <div v-for="comment in store.issueComments" :key="comment.indeTaskIssueCommentId" class="comment-item">
                <div class="comment-header">
                  <div class="comment-user">
                    <span class="user-name">{{ comment.createUserName }}</span>
                    <span class="comment-time">{{ store.formatDate(comment.createTime || '') }}</span>
                  </div>
                  <div class="comment-actions" v-if="comment.createUser === currentUserId">
                    <el-tooltip content="编辑" placement="top">
                      <el-button 
                        type="primary" 
                        link 
                        @click="handleEditComment(comment)"
                      >编辑</el-button>
                    </el-tooltip>
                    <el-tooltip content="删除" placement="top">
                      <el-button 
                        type="danger" 
                        link 
                        @click="handleDeleteComment(comment)"
                      >删除</el-button>
                    </el-tooltip>
                  </div>
                </div>
                <div class="comment-content">{{ comment.indeTaskIssueAnswer || comment.indeTaskIssueComment }}</div>
                
                <!-- 评论附件 -->
                <div v-if="comment.issueDocs && comment.issueDocs.length > 0" class="comment-files">
                  <div class="files-title">附件：</div>
                  <div class="files-list">
                    <div v-for="file in comment.issueDocs" :key="file.indeTaskIssueDocId" class="file-item-small">
                      <el-button link type="primary" @click="downloadFile(file)">
                        <el-icon><Document /></el-icon>
                        {{ file.docName }}
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <el-empty v-else description="暂无讨论记录" :image-size="80"></el-empty>
            
            <!-- 添加评论表单 -->
            <div class="add-comment-form">
              <el-divider>添加评论</el-divider>
              <el-form :model="commentForm" ref="commentFormRef">
                <el-form-item prop="content">
                  <el-input
                    v-model="commentForm.content"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入评论内容"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <div class="upload-container">
                    <el-upload
                      ref="uploadRef"
                      :auto-upload="false"
                      :on-change="handleFileChange"
                      :on-remove="handleFileRemove"
                      :limit="5"
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt"
                      :file-list="commentForm.fileList"
                    >
                      <el-button type="primary">选择文件</el-button>
                      <template #tip>
                        <div class="el-upload__tip">
                          支持.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt格式，单个文件不超过10MB
                        </div>
                      </template>
                    </el-upload>
                  </div>
                </el-form-item>
                <el-form-item>
                  <div class="submit-container">
                    <el-button type="primary" @click="handleSubmitComment" :loading="submittingComment">提交评论</el-button>
                  </div>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-card>
      </el-form>
    </div>

    <!-- 重新指派对话框 -->
    <el-dialog
      v-model="reassignDialogVisible"
      title="重新指派"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form :model="{ selectedUserId }" label-width="80px">
        <el-form-item label="负责人">
          <BtPicker
            class="select"
            title="负责人"
            :list="
              userList.map((item) => ({
                label: item.userName,
                value: item.userId,
              }))
            "
            showSearch
            v-model="selectedUserId"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="reassignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReassign" :loading="loading">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.issue-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  
  .header {
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    
    .back {
      width: 80px;
    }
    
    .title {
      flex: 1;
      font-size: 18px;
      font-weight: 600;
      text-align: center;
    }
    
    .actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
    }
  }
  
  .issue-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    
    .detail-form {
      .form-card {
        margin-bottom: 20px;
        
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 600;
          color: #303133;
        }
        
        .remark {
          white-space: pre-wrap;
          line-height: 1.6;
          padding: 8px;
          min-height: 80px;
        }
      }
    }
    
    .file-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      
      .file-item {
        width: calc(25% - 12px);
        min-width: 200px;
        
        .file-card {
          display: flex;
          flex-direction: column;
          height: 100%;
          
          .file-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            
            .file-icon {
              color: #409eff;
              font-size: 20px;
            }
            
            .file-name {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-size: 14px;
            }
            
            .file-size {
              font-size: 12px;
              color: #909399;
            }
          }
          
          .file-actions {
            display: flex;
            justify-content: flex-end;
          }
        }
      }
    }
    
    .issue-comments {
      padding: 0;
      
      .comment-list {
        margin-bottom: 20px;
        
        .comment-item {
          padding: 16px;
          border-bottom: 1px solid var(--el-border-color-lighter);
          
          &:last-child {
            border-bottom: none;
          }
          
          .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            
            .comment-user {
              display: flex;
              align-items: center;
              gap: 8px;
              
              .user-name {
                font-weight: 500;
                color: var(--el-color-primary);
              }
              
              .comment-time {
                font-size: 12px;
                color: var(--el-text-color-secondary);
              }
            }
            
            .comment-actions {
              display: flex;
              gap: 8px;
            }
          }
          
          .comment-content {
            margin: 10px 0;
            white-space: pre-wrap;
            line-height: 1.6;
          }
          
          .comment-files {
            margin-top: 8px;
            background-color: var(--el-fill-color-lighter);
            padding: 8px;
            border-radius: 4px;
            
            .files-title {
              font-size: 13px;
              font-weight: 500;
              margin-bottom: 4px;
            }
            
            .files-list {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
              
              .file-item-small {
                font-size: 13px;
              }
            }
          }
        }
      }
      
      .add-comment-form {
        margin-top: 24px;
        
        .submit-container {
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }
}

.select {
  width: 100%;
}

@media screen and (max-width: 768px) {
  .issue-detail {
    .issue-content {
      .file-list {
        .file-item {
          width: calc(50% - 8px);
        }
      }
    }
  }
}

@media screen and (max-width: 480px) {
  .issue-detail {
    .issue-content {
      .file-list {
        .file-item {
          width: 100%;
        }
      }
    }
  }
}
</style> 