<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { MoreFilled } from '@element-plus/icons-vue';
// 移除搜索和筛选组件的导入
import AddIssue from './components/AddIssue.vue';
import EditIssue from './components/EditIssue.vue';
import {
  getIndeTaskIssuePage,
  type IndeTaskIssueResponse,
  convertIssueToRisk,
  assignIndeTaskIssue,
} from '../../services/IssueService';
import { useIndependentTaskDetailStore } from '../../IndependentTaskDetailStore';
import { useRouter } from 'vue-router';

// 问题数据类型
interface IssueItem {
  id: string | number;
  name: string;
  status?: string;
  issueStatus: string;
  level: string;
  createUserName: string;
  proposeTime: string;
  issueResponsibleName: string;
  description: string;
  solution: string;
  parentTaskId: string | number;
  createdAt: string;
  updatedAt: string;
  issueType?: string | number;
}

// 获取store实例
const store = useIndependentTaskDetailStore();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const issues = ref<IssueItem[]>([]);

// 弹窗状态
const addDialogVisible = ref(false);
const editDialogVisible = ref(false);
// Using a more generic type to avoid the status field issue
const currentEditIssue = ref<any>(null);

// 表格列配置（按UI规范调整）
const columns = [
  {
    field: 'name',
    label: '问题名称',
    width: '144',
    align: 'left',
  },
  {
    field: 'issueType',
    label: '问题类型',
    width: '144',
    align: 'left',
  },
  {
    field: 'issueStatus',
    label: '问题状态',
    width: '144',
    align: 'left',
  },
  {
    field: 'level',
    label: '问题级别',
    width: '144',
    align: 'left',
  },
  {
    field: 'createUserName',
    label: '提出人',
    width: '120',
    align: 'left',
  },
  {
    field: 'proposeTime',
    label: '提出时间',
    width: '176',
    align: 'left',
  },
  {
    field: 'issueResponsibleName',
    label: '责任人',
    width: '120',
    align: 'left',
  },
];

// 获取问题类型文本
function getIssueTypeText(type: number | string): string {
  if (typeof type === 'string') {
    type = Number(type) || 0;
  }

  const typeMap: Record<number, string> = {
    1: '管理问题',
    2: '协同问题',
    3: '质量问题',
    4: '文档问题',
    5: '新员工问题',
  };
  return typeMap[type] || '未知类型';
}

// 获取级别标签类型
function getLevelType(level: string): string {
  const levelMap: Record<string, string> = {
    严重: 'danger',
    重要: 'warning',
    一般: 'info',
    轻微: 'success',
  };
  return levelMap[level] || 'info';
}

// 加载问题列表
async function loadIssues() {
  loading.value = true;
  try {
    const taskId = store.formData.indeTaskId;
    if (!taskId) {
      console.warn('任务ID为空，跳过加载问题列表');
      return;
    }

    const response = await getIndeTaskIssuePage(1, 1000, {
      indeTaskId: Number(taskId),
    });

    // 转换API数据格式
    issues.value = response.list.map((item: IndeTaskIssueResponse) => ({
      id: item.indeTaskIssueId || 0,
      name: item.indeTaskIssueName || '',
      issueStatus: getIssueStatusText(item.indeTaskIssueStatus || 1),
      level: getIssueLevelText(item.indeTaskIssueLevel || 3),
      createUserName: item.createUserName || '',
      proposeTime: item.createTime || '',
      issueResponsibleName: item.issueResponsibleName || '',
      description: item.remark || '',
      solution: '',
      parentTaskId: item.indeTaskId || 0,
      createdAt: item.createTime || '',
      updatedAt: item.updateTime || '',
      issueType: item.indeTaskIssueType || '',
    }));

    // 移除total相关逻辑
  } catch (error) {
    console.error('加载问题列表失败:', error);
    ElMessage.error('加载问题列表失败');
  } finally {
    loading.value = false;
  }
}

// 获取问题状态文本
function getIssueStatusText(status: number): string {
  const statusMap: Record<number, string> = {
    1: '待提交',
    2: '待解决',
    3: '处理中',
    4: '已处理',
    5: '已解决',
    6: '未解决',
  };
  return statusMap[status] || '未知状态';
}

// 获取问题级别文本
function getIssueLevelText(level: number): string {
  const levelMap: Record<number, string> = {
    1: '严重',
    2: '重要',
    3: '一般',
  };
  return levelMap[level] || '一般';
}

// 移除搜索、筛选、分页相关函数

// 添加问题
function handleAdd() {
  addDialogVisible.value = true;
}

// 编辑问题
function handleEdit(row: IssueItem) {
  // Add the status field back based on issueStatus for backward compatibility
  const rowWithStatus = {
    ...row,
    status: row.issueStatus,
  };
  currentEditIssue.value = rowWithStatus;
  editDialogVisible.value = true;
}

// 删除问题
function handleDelete(row: IssueItem) {
  ElMessageBox.confirm(`确定要删除问题"${row.name}"吗？`, '提示', {
    confirmButtonText: '删除',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await fetch('/projectmanage/inde-task-issue/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          indeTaskIssueId: Number(row.id),
        }),
      });
      ElMessage.success('删除成功');
      loadIssues();
    } catch (error) {
      console.error('删除问题失败:', error);
      ElMessage.error('删除失败');
    }
  });
}

// 转为风险
function handleConvertToRisk(row: IssueItem) {
  ElMessageBox.confirm(`确定要将问题"${row.name}"转为风险吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await convertIssueToRisk({
        indeTaskIssueId: Number(row.id),
      });
      ElMessage.success('转为风险成功');
      loadIssues();
    } catch (error) {
      console.error('转为风险失败:', error);
      ElMessage.error('转为风险失败');
    }
  });
}

// 重新指派问题
const reassignDialogVisible = ref(false);
const selectedUserId = ref<number | undefined>(undefined);
const currentReassignRow = ref<IssueItem | null>(null);

function handleReassign(row: IssueItem) {
  reassignDialogVisible.value = true;
  selectedUserId.value = undefined;
  currentReassignRow.value = row;
}

async function submitReassign() {
  if (!selectedUserId.value || !currentReassignRow.value) {
    ElMessage.warning('请选择负责人');
    return;
  }

  try {
    await assignIndeTaskIssue({
      indeTaskIssueId: Number(currentReassignRow.value.id),
      indeTaskResponsible: selectedUserId.value,
    });

    ElMessage.success('重新指派成功');
    reassignDialogVisible.value = false;
    selectedUserId.value = undefined;
    currentReassignRow.value = null;
    loadIssues();
  } catch (error) {
    console.error('重新指派失败:', error);
    ElMessage.error('重新指派失败');
  }
}

// 关闭问题
function handleCloseIssue(row: IssueItem) {
  ElMessageBox.confirm(`确定要关闭问题"${row.name}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await fetch('/projectmanage/inde-task-issue/close', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          indeTaskIssueId: Number(row.id),
        }),
      });
      ElMessage.success('关闭问题成功');
      loadIssues();
    } catch (error) {
      console.error('关闭问题失败:', error);
      ElMessage.error('关闭问题失败');
    }
  });
}

// 处理下拉菜单命令
function handleCommand(command: string, row: IssueItem) {
  switch (command) {
    case 'edit':
      handleEdit(row);
      break;
    case 'closeIssue':
      handleCloseIssue(row);
      break;
    case 'convertToRisk':
      handleConvertToRisk(row);
      break;
    case 'reassign':
      handleReassign(row);
      break;
    case 'delete':
      handleDelete(row);
      break;
    default:
      console.log('未知命令:', command);
  }
}

// 跳转到问题详情页
function goToIssueDetail(row: IssueItem) {
  const taskId = store.formData.indeTaskId;
  const issueId = row.id;
  router.push(`/independent-tasks/detail/${taskId}/issue/${issueId}`);
}

// 添加成功后的回调
function onAddSuccess() {
  addDialogVisible.value = false;
  loadIssues();
  ElMessage.success('添加问题成功');
}

// 编辑成功后的回调
function onEditSuccess() {
  editDialogVisible.value = false;
  currentEditIssue.value = null;
  loadIssues();
  ElMessage.success('编辑问题成功');
}

// 暴露给父组件的方法
function openAdd() {
  handleAdd();
}

// 暴露方法
defineExpose({
  openAdd,
  refreshData: loadIssues,
});

// 初始化
onMounted(() => {
  loadIssues();
});
</script>

<template>
  <div class="issue-list">
    <!-- 问题表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="issues"
        class="table"
        row-key="id"
        stripe
      >
        <!-- 选择列 -->
        <el-table-column type="selection" width="50" fixed="left" />

        <!-- 序号列 -->
        <el-table-column type="index" label="#" width="32" align="center" />

        <!-- 动态列 -->
        <template v-for="column in columns" :key="column.field">
          <!-- 问题名称列 -->
          <el-table-column
            v-if="column.field === 'name'"
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span
                class="issue-name"
                :title="row.name"
                @click="goToIssueDetail(row)"
                >{{ row.name }}</span
              >
            </template>
          </el-table-column>

          <!-- 其他列 -->
          <el-table-column
            v-else
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <template v-if="column.field === 'issueType'">
                <el-tag size="small">{{
                  getIssueTypeText(row.issueType)
                }}</el-tag>
              </template>
              <template v-else-if="column.field === 'issueStatus'">
                {{ row.issueStatus }}
              </template>
              <template v-else-if="column.field === 'level'">
                <el-tag :type="getLevelType(row.level)" size="small">{{
                  row.level
                }}</el-tag>
              </template>
              <template v-else>
                {{ row[column.field] }}
              </template>
            </template>
          </el-table-column>
        </template>

        <!-- 操作列 -->
        <el-table-column label="操作" width="48" align="center" fixed="right">
          <template #default="{ row }">
            <el-dropdown
              placement="bottom-end"
              @command="(command: string) => handleCommand(command, row)"
              hide-on-click
              :show-timeout="50"
            >
              <div class="more-opt">
                <el-icon class="icon"><MoreFilled /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="border drop-opt">
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="closeIssue"
                    >关闭问题</el-dropdown-item
                  >
                  <el-dropdown-item command="convertToRisk"
                    >转为风险</el-dropdown-item
                  >
                  <el-dropdown-item command="reassign"
                    >重新指派</el-dropdown-item
                  >
                  <el-dropdown-item command="delete">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 移除分页组件 -->

    <!-- 添加问题弹窗 -->
    <AddIssue v-model="addDialogVisible" @success="onAddSuccess" />

    <!-- 编辑问题弹窗 -->
    <EditIssue
      v-model="editDialogVisible"
      :issue="currentEditIssue"
      @success="onEditSuccess"
    />

    <!-- 重新指派对话框 -->
    <el-dialog
      v-model="reassignDialogVisible"
      title="重新指派"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form label-width="80px">
        <el-form-item label="负责人">
          <el-select
            v-model="selectedUserId"
            placeholder="请选择负责人"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="user in store.userList"
              :key="user.userId"
              :label="user.userName"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="reassignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReassign">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.issue-list {
  height: 100%;
  display: flex;
  flex-direction: column;

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .left {
      display: flex;
      align-items: center;
    }

    .right {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-box {
        width: 240px;
      }
    }
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    border-radius: 6px;

    .issue-table {
      height: 100%;

      .issue-name {
        cursor: pointer;
        color: var(--el-color-primary);

        &:hover {
          text-decoration: underline;
        }
      }

      .more-opt {
        cursor: pointer;
        .icon {
          font-size: 16px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }

  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}

.dropdown-menu {
  min-width: 100px;
}
</style>
