<script setup lang="ts">
interface Events {
  (e: 'add-issue'): void;
}

const emits = defineEmits<Events>();

// 处理新增问题
function handleAddIssue() {
  emits('add-issue');
}
</script>

<template>
  <div class="issue-tab-actions">
    <!-- 操作按钮 -->
    <div class="actions-toolbar">
      <el-button @click="handleAddIssue">
        新增问题
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.issue-tab-actions {
  :deep(.el-dialog__header) {
    text-align: left;
  }
  .actions-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
</style> 