import { ref, reactive } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import { 
  getIndeTaskIssueDetail, 
  closeIndeTaskIssue, 
  convertIssueToRisk, 
  getIssueTypeOptions,
  getIssueCommentList,
  addIssueComment,
  editIssueComment,
  deleteIssueComment
} from './IssueDetailService';
import type { 
  IndeTaskIssueDetailResponse, 
  IndeTaskIssueDocResponse, 
  IndeTaskDeliveryDocResponse,
  IndeTaskIssueCommentResponse,
  IndeTaskIssueCommentAddRequest,
  IndeTaskIssueCommentEditRequest
} from './types';
import { useRouter } from 'vue-router';
import { getUserList as getUserListService } from '@/pages/tasks/views/independent_tasks/list/components/add_task/AddTaskService';
import type { UserOption } from '@/pages/tasks/views/independent_tasks/list/components/add_task/types';
import { assignIndeTaskIssue } from '../../../services/IssueService';

// 类型定义
interface TypeOption {
  label: string;
  value: string | number;
}

export const useIssueDetailStore = defineStore('issue-detail', () => {
  const router = useRouter();
  
  // 加载状态
  const loading = ref(false);
  const loadingComments = ref(false);
  
  // 原始API数据
  const apiData = ref<IndeTaskIssueDetailResponse | null>(null);
  
  // 问题详情数据
  const issueDetail = reactive({
    indeTaskIssueId: 0,
    indeTaskId: 0,
    indeTaskIssueName: '',
    indeTaskIssueStatus: 0,
    indeTaskIssueType: '',
    indeTaskIssueLevel: 0,
    closingTime: '',
    completionDate: '',
    indeTaskResponsible: 0,
    issueResponsibleName: '',
    createTime: '',
    updateTime: '',
    createUser: 0,
    createUserName: '',
    updateUser: 0,
    remark: '',
    tenantId: 0,
    disCloseFlag: 0,
    publicReplyFlag: 0,
    issueDocs: [] as IndeTaskIssueDocResponse[],
    deliveryDocs: [] as IndeTaskDeliveryDocResponse[]
  });

  // 问题评论列表
  const issueComments = ref<IndeTaskIssueCommentResponse[]>([]);
  
  // 问题类型选项
  const issueTypeOptions = ref<TypeOption[]>([]);
  // 风险类型选项
  const riskTypeOptions = ref<TypeOption[]>([]);
  
  // 用户列表
  const userList = ref<UserOption[]>([]);
  const loadingUsers = ref(false);
  
  // 加载用户列表
  async function loadUserList() {
    if (loadingUsers.value) return;
    
    loadingUsers.value = true;
    try {
      // 使用相同的方法获取用户列表
      const result = await getUserListService();
      userList.value = result || [];
    } catch (error) {
      console.error('加载用户列表失败:', error);
    } finally {
      loadingUsers.value = false;
    }
  }
  
  // 加载问题类型
  async function loadIssueTypes() {
    try {
      issueTypeOptions.value = await getIssueTypeOptions();
    } catch (error) {
      console.error('加载问题类型失败:', error);
      ElMessage.error('加载问题类型失败');
    }
  }
  
  // 初始化 - 加载所有必要数据
  async function initialize() {
    await Promise.all([
      loadUserList(),
      loadIssueTypes()
    ]);
  }
  
  // 获取问题类型名称
  function getIssueTypeName(typeValue: string | number): string {
    const option = issueTypeOptions.value.find(opt => {
      // 尝试多种比较方式确保类型匹配
      return String(opt.value) == String(typeValue) || 
             opt.value == typeValue ||
             Number(opt.value) == Number(typeValue);
    });
    return option ? option.label : '未知类型';
  }
  
  // 加载问题详情
  async function loadIssueDetail(issueId: string | number) {
    loading.value = true;
    
    try {
      await loadIssueTypes(); // 先加载问题类型
      
      const response = await getIndeTaskIssueDetail(issueId);
      apiData.value = response;
      
      // 将API返回的数据映射到本地状态
      Object.assign(issueDetail, {
        indeTaskIssueId: response.indeTaskIssueId || 0,
        indeTaskId: response.indeTaskId || 0,
        indeTaskIssueName: response.indeTaskIssueName || '',
        indeTaskIssueStatus: response.indeTaskIssueStatus || 0,
        indeTaskIssueType: response.indeTaskIssueType || '',
        indeTaskIssueLevel: response.indeTaskIssueLevel || 0,
        completionDate: response.completionDate || '',
        indeTaskResponsible: response.indeTaskResponsible || 0,
        issueResponsibleName: response.issueResponsibleName || response.indeTaskResponsibleName || '',
        createTime: response.createTime || '',
        updateTime: response.updateTime || '',
        createUser: response.createUser || 0,
        createUserName: response.createUserName || '',
        updateUser: response.updateUser || 0,
        remark: response.remark || '',
        tenantId: response.tenantId || 0,
        disCloseFlag: response.disCloseFlag || 0,
        publicReplyFlag: response.publicReplyFlag || 0,
        issueDocs: response.issueDocs || [],
        deliveryDocs: response.deliveryDocs || []
      });
      
      // 加载问题评论列表
      await loadIssueComments(Number(issueId));
      
      return true;
    } catch (error) {
      console.error('加载问题详情失败:', error);
      ElMessage.error('加载问题详情失败');
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  // 加载问题评论列表
  async function loadIssueComments(issueId: number) {
    loadingComments.value = true;
    try {
      const response = await getIssueCommentList(issueId);
      
      // Add backward compatibility fields if needed
      response.forEach(comment => {
        if (comment.projTaskIssueAnswer && !comment.indeTaskIssueComment) {
          comment.indeTaskIssueComment = comment.projTaskIssueAnswer;
        }
        if (comment.projTaskIssueAnswerId && !comment.indeTaskIssueCommentId) {
          comment.indeTaskIssueCommentId = comment.projTaskIssueAnswerId;
        }
        if (comment.projTaskIssueId && !comment.indeTaskIssueId) {
          comment.indeTaskIssueId = comment.projTaskIssueId;
        }
      });
      
      issueComments.value = response;
    } catch (error) {
      console.error('加载问题评论列表失败:', error);
    } finally {
      loadingComments.value = false;
    }
  }
  
  // 添加问题评论
  async function addIssueCommentAction(data: IndeTaskIssueCommentAddRequest) {
    try {
      await addIssueComment(data);
      ElMessage.success('添加评论成功');
      await loadIssueComments(data.indeTaskIssueId);
      return true;
    } catch (error) {
      console.error('添加问题评论失败:', error);
      return false;
    }
  }
  
  // 编辑问题评论
  async function editIssueCommentAction(data: IndeTaskIssueCommentEditRequest) {
    try {
      await editIssueComment(data);
      ElMessage.success('编辑评论成功');
      if (data.indeTaskIssueId) {
        await loadIssueComments(data.indeTaskIssueId);
      }
      return true;
    } catch (error) {
      console.error('编辑问题评论失败:', error);
      return false;
    }
  }
  
  // 删除问题评论
  async function deleteIssueCommentAction(commentId: number) {
    try {
      await deleteIssueComment(commentId);
      ElMessage.success('删除评论成功');
      await loadIssueComments(issueDetail.indeTaskIssueId);
      return true;
    } catch (error) {
      console.error('删除问题评论失败:', error);
      return false;
    }
  }
  
  // 关闭问题
  async function closeIssue() {
    loading.value = true;
    
    try {
      await closeIndeTaskIssue(issueDetail.indeTaskIssueId);
      ElMessage.success('问题关闭成功');
      
      // 重新加载问题详情
      await loadIssueDetail(issueDetail.indeTaskIssueId);
      return true;
    } catch (error) {
      console.error('关闭问题失败:', error);
      ElMessage.error('关闭问题失败');
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  // 转换为风险
  async function convertToRisk() {
    loading.value = true;
    
    try {
      await convertIssueToRisk(issueDetail.indeTaskIssueId);
      ElMessage.success('问题已转换为风险');
      
      // 返回任务详情页
      backToTaskDetail();
      return true;
    } catch (error) {
      console.error('转换为风险失败:', error);
      ElMessage.error('转换为风险失败');
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  // 重新指派问题
  async function assignIssue(userId: number) {
    loading.value = true;
    
    try {
      await assignIndeTaskIssue({
        indeTaskIssueId: issueDetail.indeTaskIssueId,
        indeTaskResponsible: userId
      });
      
      ElMessage.success('重新指派成功');
      
      // 重新加载问题详情
      await loadIssueDetail(issueDetail.indeTaskIssueId);
      return true;
    } catch (error) {
      console.error('重新指派失败:', error);
      ElMessage.error('重新指派失败');
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  // 返回任务详情页
  function backToTaskDetail() {
    const taskId = issueDetail.indeTaskId;
    router.push(`/independent-tasks/detail/${taskId}`);
  }
  
  // 获取问题状态文本
  function getIssueStatusText(status: number): string {
    const statusMap: Record<number, string> = {
      1: '待提交',
      2: '待解决',
      3: '处理中',
      4: '已处理',
      5: '已解决',
      6: '未解决'
    };
    return statusMap[status] || '未知状态';
  }
  
  // 获取问题级别文本
  function getIssueLevelText(level: number): string {
    const levelMap: Record<number, string> = {
      1: '高',
      2: '中',
      3: '低',
    };
    return levelMap[level] || '未知级别';
  }
  
  // 获取问题状态类型（用于标签颜色）
  function getIssueStatusType(status: number): string {
    const statusTypeMap: Record<number, string> = {
      1: 'info',
      2: 'warning',
      3: 'primary',
      4: 'success',
      5: 'success',
      6: 'danger',
    };
    return statusTypeMap[status] || 'info';
  }
  
  // 获取问题级别类型（用于标签颜色）
  function getIssueLevelType(level: number): string {
    const levelMap: Record<number, string> = {
      1: 'danger',
      2: 'warning',
      3: 'info',
    };
    return levelMap[level] || 'info';
  }
  
  // 格式化文件大小
  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  // 格式化时间
  function formatDate(dateStr: string): string {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;
    
    return date.toLocaleString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }
  
  return {
    loading,
    loadingComments,
    issueDetail,
    apiData,
    issueTypeOptions,
    riskTypeOptions,
    userList,
    issueComments,
    loadUserList,
    loadIssueTypes,
    initialize,
    loadIssueDetail,
    loadIssueComments,
    addIssueCommentAction,
    editIssueCommentAction,
    deleteIssueCommentAction,
    closeIssue,
    convertToRisk,
    assignIssue,
    backToTaskDetail,
    getIssueStatusText,
    getIssueLevelText,
    getIssueStatusType,
    getIssueLevelType,
    getIssueTypeName,
    formatFileSize,
    formatDate
  };
}); 