<script setup lang="ts">
interface Events {
  (e: 'query-review'): void;
}

const emits = defineEmits<Events>();

// 处理查询评审
function handleQueryReview() {
  emits('query-review');
}
</script>

<template>
  <div class="approval-tab-actions">
    <!-- 操作按钮 -->
    <div class="actions-toolbar">
      <el-button @click="handleQueryReview">
        查询评审
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.approval-tab-actions {
  :deep(.el-dialog__header) {
    text-align: left;
  }
  .actions-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
</style> 