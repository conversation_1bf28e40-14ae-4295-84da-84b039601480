<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { MoreFilled } from '@element-plus/icons-vue';

// 评审记录数据类型
interface ReviewRecordItem {
  id: string | number;
  reviewProcessName: string;
  status: string;
  reviewer: string;
  startDate: string;
  endDate: string;
  reviewOpinion: string;
  parentTaskId: string | number;
}

// 响应式数据
const loading = ref(false);
const reviewRecords = ref<ReviewRecordItem[]>([]);

// 表格列配置（按UI规范调整）
const columns = [
  {
    field: 'reviewProcessName',
    label: '评审流程名称',
    width: '144',
    align: 'left',
  },
  {
    field: 'status',
    label: '状态',
    width: '144',
    align: 'left',
  },
  {
    field: 'reviewer',
    label: '评审人',
    width: '120',
    align: 'left',
  },
  {
    field: 'startDate',
    label: '开始日期',
    width: '144',
    align: 'left',
  },
  {
    field: 'endDate',
    label: '完成日期',
    width: '144',
    align: 'left',
  },
  {
    field: 'reviewOpinion',
    label: '评审意见',
    width: '144',
    align: 'left',
  },
];

// 加载评审记录列表
async function loadReviewRecords() {
  loading.value = true;
  try {
    // TODO: 调用API获取评审记录列表
    // const response = await getReviewRecordList(parentTaskId, {
    //   page: currentPage.value,
    //   pageSize: pageSize.value,
    //   keyword: searchKeyword.value,
    // });

    // 模拟数据
    reviewRecords.value = [
      {
        id: 1,
        reviewProcessName: 'XXXXXXXXXXXXXX',
        status: '进行中',
        reviewer: '张三',
        startDate: '2025-02-12 12:11',
        endDate: '2025-07-12 12:11',
        reviewOpinion: 'XXXXXXXXXXXXXX',
        parentTaskId: 1,
      },
    ];
  } catch (error) {
    console.error('加载评审记录列表失败:', error);
    ElMessage.error('加载评审记录列表失败');
  } finally {
    loading.value = false;
  }
}

// 移除搜索、筛选、分页相关函数

// 查询评审
function handleQueryReview() {
  ElMessage.info('查询评审功能待实现');
  console.log('查询评审');
  // TODO: 实现查询评审功能
}

// 删除评审记录
function handleDelete(row: ReviewRecordItem) {
  ElMessageBox.confirm(
    `确定要删除评审记录"${row.reviewProcessName}"吗？`,
    '提示',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      // TODO: 调用API删除评审记录
      console.log('删除评审记录:', row.id);
      ElMessage.success('删除成功');
      loadReviewRecords();
    } catch (error) {
      console.error('删除评审记录失败:', error);
      ElMessage.error('删除失败');
    }
  });
}

// 查看评审详情
function handleViewDetail(row: ReviewRecordItem) {
  ElMessage.info('查看评审详情功能待实现');
  console.log('查看评审详情:', row.id);
  // TODO: 实现查看评审详情功能
}

// 处理下拉菜单命令
function handleCommand(command: string, row: ReviewRecordItem) {
  switch (command) {
    case 'viewDetail':
      handleViewDetail(row);
      break;
    case 'delete':
      handleDelete(row);
      break;
    default:
      console.log('未知命令:', command);
  }
}

// 暴露给父组件的方法
function openQueryReview() {
  handleQueryReview();
}

// 暴露方法
defineExpose({
  openQueryReview,
  refreshData: loadReviewRecords,
});

// 初始化
onMounted(() => {
  loadReviewRecords();
});
</script>

<template>
  <div class="review-record-list">
    <!-- 评审记录表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="reviewRecords"
        class="review-table table"
        row-key="id"
        stripe
      >
        <!-- 选择列 -->
        <el-table-column type="selection" width="50" fixed="left" />

        <!-- 序号列 -->
        <el-table-column type="index" label="#" width="32" align="center" />

        <!-- 动态列 -->
        <template v-for="column in columns" :key="column.field">
          <!-- 评审流程名称列 -->
          <el-table-column
            v-if="column.field === 'reviewProcessName'"
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span class="process-name" :title="row.reviewProcessName">
                {{ row.reviewProcessName }}
              </span>
            </template>
          </el-table-column>

          <!-- 其他列 -->
          <el-table-column
            v-else
            :property="column.field"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <template v-if="column.field === 'status'">
                <el-tag size="small">
                  {{ row.status }}
                </el-tag>
              </template>
              <template v-else-if="column.field === 'reviewOpinion'">
                <span :title="row[column.field]" class="review-opinion">
                  {{ row[column.field] }}
                </span>
              </template>
              <template v-else>
                {{ row[column.field] }}
              </template>
            </template>
          </el-table-column>
        </template>

        <!-- 操作列 -->
        <el-table-column label="操作" width="48" align="center" fixed="right">
          <template #default="{ row }">
            <el-dropdown
              placement="bottom-end"
              @command="(command: string) => handleCommand(command, row)"
              hide-on-click
              :show-timeout="50"
            >
              <div class="more-opt">
                <el-icon class="icon"><MoreFilled /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="border drop-opt">
                  <el-dropdown-item command="viewDetail"
                    >查看详情</el-dropdown-item
                  >
                  <el-dropdown-item command="delete">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && reviewRecords.length === 0" class="empty-state">
      <div class="empty-icon">🔍</div>
      <div class="empty-text">未找到审批记录</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.review-record-list {
  height: 100%;
  display: flex;
  flex-direction: column;

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    // 移除空的样式规则

    .right {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;
      justify-content: space-between;

      .operation-info {
        font-size: 12px;
        color: var(--el-text-color-secondary);

        .label {
          color: var(--el-text-color-primary);
          font-weight: 500;
        }

        .content {
          color: var(--el-color-danger);
        }
      }

      .right-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .search-box {
          width: 240px;
        }
      }
    }
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    border-radius: 6px;

    .review-table {
      height: 100%;

      .process-name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
        color: var(--el-color-primary);

        &:hover {
          text-decoration: underline;
        }
      }

      .review-opinion {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 200px;
        display: inline-block;
      }

      .drop-opt {
        width: 140px;
      }

      .more-opt {
        cursor: pointer;
        .icon {
          font-size: 16px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-secondary);

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 14px;
    }
  }
}
</style>
