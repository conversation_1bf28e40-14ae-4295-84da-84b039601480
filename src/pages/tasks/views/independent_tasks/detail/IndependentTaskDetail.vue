<script setup lang="ts">
import { useIndependentTaskDetailStore } from './IndependentTaskDetailStore';
import { Plus, Document } from '@element-plus/icons-vue';
import { onMounted, ref, useTemplateRef } from 'vue';
import { useRoute } from 'vue-router';
import AcceptTask from './components/accept_task/AcceptTask.vue';
import RejectTask from './components/reject_task/RejectTask.vue';

const route = useRoute();
const store = useIndependentTaskDetailStore();

// 对话框控制
const acceptTaskVisible = ref(false);
const rejectTaskVisible = ref(false);
const acceptTaskRef = useTemplateRef<any>('acceptTaskRef');
const rejectTaskRef = useTemplateRef<any>('rejectTaskRef');

// 打开接受任务对话框
function openAcceptDialog() {
  // 构造符合弹窗组件期望的数据结构
  const taskData = {
    indeTaskId: store.formData.indeTaskId,
    indeTaskName: store.formData.indeTaskName,
    indeTaskResponsible: store.formData.indeTaskResponsible,
    responsibleName: store.formData.responsibleName,
    indeTaskType: store.formData.indeTaskType,
    participants: store.formData.participants,
    planStartDate: store.formData.planStartDate,
    planFinishDate: store.formData.planFinishDate,
    processDocs: store.formData.processDocs,
  };
  acceptTaskRef.value?.open(taskData, store.userList);
}

// 打开驳回任务对话框
function openRejectDialog() {
  // 构造符合弹窗组件期望的数据结构
  const taskData = {
    indeTaskId: store.formData.indeTaskId,
    indeTaskName: store.formData.indeTaskName,
    indeTaskResponsible: store.formData.indeTaskResponsible,
    responsibleName: store.formData.responsibleName,
    indeTaskType: store.formData.indeTaskType,
    participants: store.formData.participants,
    planStartDate: store.formData.planStartDate,
    planFinishDate: store.formData.planFinishDate,
    processDocs: store.formData.processDocs,
  };
  rejectTaskRef.value?.open(taskData, store.userList);
}

// 处理接受任务成功
function handleAcceptSuccess() {
  const taskId = String(store.formData.indeTaskId);
  store.loadTaskDetail(taskId);
}

// 处理驳回任务成功
function handleRejectSuccess() {
  const taskId = String(store.formData.indeTaskId);
  store.loadTaskDetail(taskId);
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 移除标签
const removeTag = (tagId: number) => {
  store.removeTaskTag(tagId);
};

// 处理添加标签（下拉菜单方式）
const handleAddTag = (tagId: number) => {
  store.addTaskTag(tagId);
};

// 获取可添加的标签列表（过滤已添加和禁用的标签）
const getAvailableTagsForAdd = () => {
  return store.availableTags.filter((tag) => {
    // 过滤掉已添加的标签
    const isAlreadyAdded = store.formData.tags.some(
      (addedTag) => addedTag.taskTagId === tag.taskTagId
    );
    // 过滤掉禁用的标签（只显示启用状态的标签）
    const isEnabled = tag.taskTagStatus === 1;

    return !isAlreadyAdded && isEnabled;
  });
};

// 获取汇报数量（用于传递给ReportTabActions）
const getReportsCount = (): number => {
  // 从当前激活的ReportList组件获取reports数量
  const currentTabIndex = store.tabList.findIndex(
    (tab) => tab.name === store.activeTab
  );
  if (currentTabIndex >= 0 && store.activeTab === 'report') {
    const reportComponent = store.tabComponents[currentTabIndex];
    if (
      reportComponent &&
      typeof reportComponent.getReportsCount === 'function'
    ) {
      return reportComponent.getReportsCount();
    }
  }
  return 0;
};

// 处理刷新汇报列表事件
const handleRefreshReports = () => {
  // 刷新当前激活tab的数据
  store.refreshCurrentTabData();
};

// 处理tab actions的上传事件
const handleTabUpload = () => {
  const currentTabIndex = store.tabList.findIndex(
    (tab) => tab.name === store.activeTab
  );
  if (currentTabIndex >= 0) {
    const component = store.tabComponents[currentTabIndex];
    if (component && typeof component.handleUpload === 'function') {
      component.handleUpload();
    }
  }
};

// 处理tab actions的新建文件夹事件
const handleTabAddFolder = () => {
  const currentTabIndex = store.tabList.findIndex(
    (tab) => tab.name === store.activeTab
  );
  if (currentTabIndex >= 0) {
    const component = store.tabComponents[currentTabIndex];
    if (component && typeof component.handleAddFolder === 'function') {
      component.handleAddFolder();
    }
  }
};

// 处理tab actions的回退事件
const handleTabGoBack = () => {
  const currentTabIndex = store.tabList.findIndex(
    (tab) => tab.name === store.activeTab
  );
  if (currentTabIndex >= 0) {
    const component = store.tabComponents[currentTabIndex];
    if (component && typeof component.handleGoBack === 'function') {
      component.handleGoBack();
    }
  }
};

// 获取是否可以回退
const getCanGoBack = (tabName: string) => {
  if (tabName === 'file') {
    const currentTabIndex = store.tabList.findIndex(
      (tab) => tab.name === tabName
    );
    if (currentTabIndex >= 0) {
      const component = store.tabComponents[currentTabIndex];
      if (component && component.fileListStore) {
        return component.fileListStore.canGoBack;
      }
    }
  } else if (tabName === 'delivery') {
    const currentTabIndex = store.tabList.findIndex(
      (tab) => tab.name === tabName
    );
    if (currentTabIndex >= 0) {
      const component = store.tabComponents[currentTabIndex];
      if (component && component.historyLogStore) {
        return component.historyLogStore.canGoBack;
      }
    }
  }
  return false;
};

// 处理新增子任务事件
const handleTabAddSubtask = () => {
  const currentTabIndex = store.tabList.findIndex(
    (tab) => tab.name === store.activeTab
  );
  if (currentTabIndex >= 0) {
    const component = store.tabComponents[currentTabIndex];
    if (component && typeof component.handleAdd === 'function') {
      component.handleAdd();
    } else if (component && typeof component.openAdd === 'function') {
      component.openAdd();
    }
  }
};

// 处理新增问题事件
const handleTabAddIssue = () => {
  const currentTabIndex = store.tabList.findIndex(
    (tab) => tab.name === store.activeTab
  );
  if (currentTabIndex >= 0) {
    const component = store.tabComponents[currentTabIndex];
    if (component && typeof component.handleAdd === 'function') {
      component.handleAdd();
    } else if (component && typeof component.openAdd === 'function') {
      component.openAdd();
    }
  }
};

// 处理新增风险事件
const handleTabAddRisk = () => {
  const currentTabIndex = store.tabList.findIndex(
    (tab) => tab.name === store.activeTab
  );
  if (currentTabIndex >= 0) {
    const component = store.tabComponents[currentTabIndex];
    if (component && typeof component.handleAdd === 'function') {
      component.handleAdd();
    } else if (component && typeof component.openAdd === 'function') {
      component.openAdd();
    }
  }
};

// 处理新增会议事件
const handleTabAddMeeting = () => {
  const currentTabIndex = store.tabList.findIndex(
    (tab) => tab.name === store.activeTab
  );
  if (currentTabIndex >= 0) {
    const component = store.tabComponents[currentTabIndex];
    if (component && typeof component.handleAdd === 'function') {
      component.handleAdd();
    } else if (component && typeof component.openAdd === 'function') {
      component.openAdd();
    }
  }
};

// 处理预定飞书会议事件
const handleTabScheduleFeiShu = () => {
  const currentTabIndex = store.tabList.findIndex(
    (tab) => tab.name === store.activeTab
  );
  if (currentTabIndex >= 0) {
    const component = store.tabComponents[currentTabIndex];
    if (component && typeof component.handleScheduleFeiShu === 'function') {
      component.handleScheduleFeiShu();
    }
  }
};

// 处理查询评审事件
const handleQueryReview = () => {
  const currentTabIndex = store.tabList.findIndex(
    (tab) => tab.name === store.activeTab
  );
  if (currentTabIndex >= 0) {
    const component = store.tabComponents[currentTabIndex];
    if (component && typeof component.openQueryReview === 'function') {
      component.openQueryReview();
    }
  }
};

// 初始化页面数据
onMounted(async () => {
  const taskId = route.params.id as string;
  await store.loadTaskDetail(taskId);
  // 任务详情加载完成后，刷新当前激活tab的数据
  setTimeout(() => {
    store.refreshCurrentTabData();
  }, 200);
});
</script>

<template>
  <div class="independent-task-detail">
    <div class="header">
      <div class="title">独立任务详情</div>
      <div class="right-opt flex flex-center">
        <template v-if="store.canAcceptOrReject()">
          <el-button @click="openAcceptDialog"> 接受 </el-button>
          <el-button @click="openRejectDialog"> 驳回 </el-button>
        </template>
        <el-button @click="store.backToList()"> 返回 </el-button>
      </div>
    </div>

    <!-- 任务基本信息展示 - 移出表单外 -->
    <div class="task-header-container">
      <div class="task-header">
        <!-- 头像区域 -->
        <div class="avatar-section">
          <el-avatar :size="64" class="task-avatar">
            <el-icon><User /></el-icon>
          </el-avatar>
        </div>

        <!-- 任务信息区域 -->
        <div class="task-info-section">
          <!-- 第一行：任务名称 + 标签 -->
          <div class="task-title-row">
            <span class="task-name">{{ store.formData.indeTaskName }}</span>
          </div>

          <!-- 第二行：责任人 + 参与人 -->
          <div class="task-details-row">
            <span class="detail-item">
              <span class="label">责任人：</span>
              <span class="value">{{
                store.formData.responsibleName || '暂无'
              }}</span>
            </span>
            <span class="detail-item">
              <span class="label">参与人：</span>
              <span class="value participants-tags">
                <el-tag
                  v-for="participant in store.formData.participants"
                  :key="participant.userId"
                  size="small"
                  type="info"
                  class="participant-tag"
                >
                  {{ participant.userName }}
                </el-tag>
                <span
                  v-if="store.formData.participants.length === 0"
                  class="no-participants"
                >
                  暂无
                </span>
              </span>
            </span>
          </div>
        </div>
        <div class="task-info-tag">
          <!-- 状态标签 -->
          <el-tag
            :type="store.getStatusType(store.formData.indeTaskStatus)"
            class="status-tag"
          >
            {{ store.getStatusText(store.formData.indeTaskStatus) }}
          </el-tag>

          <!-- 任务标签 -->
          <el-tag
            v-for="tag in store.formData.tags"
            :key="tag.taskTagId"
            :color="tag.taskTagColor"
            class="task-tag"
            closable
            @close="removeTag(tag.taskTagId)"
          >
            {{ tag.taskTagName }}
          </el-tag>

          <!-- 添加标签下拉菜单 -->
          <el-dropdown
            @command="handleAddTag"
            class="add-tag-dropdown"
            max-height="50vh"
          >
            <el-button size="small">
              <el-icon><Plus /></el-icon>
              添加标签
            </el-button>
            <template #dropdown>
              <el-dropdown-menu class="tag-dropdown-menu">
                <el-dropdown-item
                  v-for="tag in getAvailableTagsForAdd()"
                  :key="tag.taskTagId"
                  :command="tag.taskTagId"
                >
                  {{ tag.taskTagName }}
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="getAvailableTagsForAdd().length === 0"
                  disabled
                >
                  暂无可添加的标签
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <el-form
      ref="ruleFormRef"
      class="form"
      :model="store.formData"
      :rules="store.formRules"
      label-width="160px"
      disabled
    >
      <!-- 任务类型 -->
      <el-row class="row-bg">
        <el-form-item label="任务类型" prop="taskType">
          <el-input
            class="ipt readonly"
            :value="store.getTaskTypeText(store.formData.indeTaskType)"
            placeholder=""
            readonly
          ></el-input>
        </el-form-item>

        <el-form-item label="进度" prop="progress">
          <el-progress
            class="progress"
            color="#52C41A"
            :stroke-width="12"
            :percentage="store.formData.actualProgress || 0"
          />
        </el-form-item>

        <el-form-item label="超期" prop="overdue">
          <el-input
            class="ipt readonly"
            :value="`${store.formData.overdueDays || 0} 天`"
            placeholder=""
            readonly
          ></el-input>
        </el-form-item>
      </el-row>

      <!-- 任务说明 -->
      <el-row class="row-bg">
        <el-form-item label="任务说明" prop="description" class="full-row">
          <el-input
            class="textarea w-100"
            v-model="store.formData.indeTaskDesc"
            :rows="3"
            type="textarea"
            maxlength="500"
            show-word-limit
            placeholder=""
          />
        </el-form-item>
      </el-row>

      <!-- 验收标准 -->
      <el-row class="row-bg">
        <el-form-item
          label="验收标准"
          prop="indeTaskAcceptCriteria"
          class="full-row"
        >
          <el-input
            class="textarea w-100"
            v-model="store.formData.indeTaskAcceptCriteria"
            :rows="3"
            type="textarea"
            maxlength="500"
            show-word-limit
            placeholder=""
          />
        </el-form-item>
      </el-row>

      <!-- 过程文档显示 -->
      <el-row class="row-bg" v-if="store.formData.processDocs.length > 0">
        <el-form-item label="过程文档">
          <div class="attachments-container">
            <div
              v-for="attachment in store.formData.processDocs"
              :key="attachment.docId"
              class="attachment-item"
            >
              <el-icon class="attachment-icon"><Document /></el-icon>
              <span class="attachment-name">{{ attachment.docName }}</span>
              <span class="attachment-size"
                >({{ formatFileSize(attachment.docSize) }})</span
              >
            </div>
          </div>
        </el-form-item>
      </el-row>

      <!-- 时间相关字段 -->
      <el-row class="row-bg">
        <el-form-item label="计划开始时间" prop="planStartDate">
          <el-date-picker
            v-model="store.formData.planStartDate"
            type="datetime"
            placeholder=""
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="date-select w-100"
          />
        </el-form-item>

        <el-form-item label="实际开始时间" prop="actualStartDate">
          <el-date-picker
            v-model="store.formData.actualStartDate"
            type="datetime"
            placeholder=""
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="date-select w-100"
          />
        </el-form-item>
        <el-form-item label="计划完成时间" prop="planFinishDate">
          <el-date-picker
            v-model="store.formData.planFinishDate"
            type="datetime"
            placeholder=""
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="date-select w-100"
          />
        </el-form-item>
      </el-row>

      <el-row class="row-bg">
        <el-form-item label="工期（天）" prop="duration">
          <el-input-number
            v-model="store.formData.duration"
            style="width: 2160px"
            :min="0"
            placeholder=""
          />
        </el-form-item>
        <el-form-item label="任务评审流程" prop="workflowId">
          <div class="workflow-selector">
            <el-select
              class="ipt w-100"
              v-model="store.formData.workflowId"
              placeholder=""
              filterable
              clearable
            >
              <el-option
                v-for="workflow in store.workflowDefinitions"
                :key="workflow.id"
                :label="workflow.name"
                :value="workflow.id"
              >
                <div class="workflow-option">
                  <span>{{ workflow.name }}</span>
                  <span class="workflow-version">v{{ workflow.version }}</span>
                </div>
              </el-option>
            </el-select>
          </div>
        </el-form-item>

        <el-form-item label="自动进度" prop="autoProgressFlag">
          <el-switch
            v-model="store.formData.autoProgressFlag"
            :active-value="1"
            :inactive-value="0"
            class="switch-box"
          />
        </el-form-item>
      </el-row>

      <!-- 任务指派信息 -->
      <el-row class="row-bg">
        <el-form-item label="任务指派人" prop="assigneer">
          <el-input
            class="ipt readonly"
            :value="store.assigneerName"
            placeholder=""
            readonly
          ></el-input>
        </el-form-item>

        <el-form-item label="任务指派时间" prop="assigneeTime">
          <el-date-picker
            v-model="store.formData.assigneeTime"
            type="datetime"
            placeholder=""
            disabled
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="任务接受时间" prop="acceptTime">
          <el-date-picker
            v-model="store.formData.acceptTime"
            type="datetime"
            placeholder=""
            disabled
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-row>
    </el-form>

    <!-- 页签区域 -->
    <el-tabs
      v-model="store.activeTab"
      type="card"
      class="tabs"
      editable
      @edit="() => {}"
    >
      <!-- 页签操作组件 - 使用add-icon插槽 -->
      <template #add-icon>
        <template v-for="(item, index) in store.tabList" :key="index">
          <div
            v-if="item.actions && item.name === store.activeTab"
            class="tab-actions"
          >
            <component
              :is="item.actions"
              :reports-count="getReportsCount()"
              :can-go-back="getCanGoBack(item.name)"
              @refresh-reports="handleRefreshReports"
              @upload="handleTabUpload"
              @add-folder="handleTabAddFolder"
              @go-back="handleTabGoBack"
              @add-subtask="handleTabAddSubtask"
              @add-issue="handleTabAddIssue"
              @add-risk="handleTabAddRisk"
              @add-meeting="handleTabAddMeeting"
              @schedule-feishu="handleTabScheduleFeiShu"
              @query-review="handleQueryReview"
            />
          </div>
        </template>
      </template>
      <el-tab-pane
        v-for="(item, index) in store.tabList"
        :key="index"
        :label="item.label"
        :name="item.name"
        :closable="false"
      >
        <div class="tabs-content">
          <!-- 页签内容组件 -->
          <component
            :is="item.component"
            :ref="(el: any) => store.setTabComponent(index, el)"
          />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 接受任务弹窗 -->
    <AcceptTask
      v-model="acceptTaskVisible"
      @success="handleAcceptSuccess"
      ref="acceptTaskRef"
    />

    <!-- 驳回任务弹窗 -->
    <RejectTask
      v-model="rejectTaskVisible"
      @success="handleRejectSuccess"
      ref="rejectTaskRef"
    />
  </div>
</template>

<style scoped lang="scss">
.independent-task-detail {
  height: 100%;
  overflow-y: auto;

  // 任务头部信息容器样式
  .task-header-container {
    margin-top: 24px;
  }

  // 任务头部信息样式
  .task-header {
    display: flex;
    align-items: stretch;

    .avatar-section {
      flex-shrink: 0;
      width: 160px;
      text-align: right;
      padding-right: 12px;
      .task-avatar {
        background-color: #409eff;
        color: white;
      }
    }

    .task-info-section {
      min-width: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .task-title-row {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .task-name {
          font-size: 18px;
          font-weight: 600;
          height: 32px;
          color: #333;
          margin-right: 8px;
          display: inline-flex;
          align-items: center;
        }

        .status-tag {
          margin-right: 4px;
        }

        .task-tag {
          margin-right: 4px;
        }

        .add-tag-dropdown {
          margin-left: 4px;
        }
      }

      .task-details-row {
        display: flex;
        gap: 50px;
        color: #666;
        font-size: 14px;
        flex-wrap: wrap;

        .detail-item {
          display: flex;
          align-items: center;

          .label {
            font-weight: 500;
            margin-right: 4px;
          }

          .value {
            color: #333;

            &.participants-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
              align-items: center;

              .participant-tag {
                margin: 0;
              }

              .no-participants {
                color: #999;
                font-style: italic;
              }
            }
          }
        }
      }
    }

    .task-info-tag {
      margin-left: 24px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 48px;
    border-bottom: 1px solid var(--border-color);

    .title {
      font-size: var(--el-font-size-base);
      font-weight: 500;
      height: var(--el-tabs-header-height);
    }

    .right-opt {
      .item {
        margin-left: var(--base-margin);
      }

      .btn {
        margin-left: var(--base-margin);
        height: 32px;
        padding: 0 16px;
        font-size: 14px;
        border-radius: 4px;
      }

      :deep(.bt-search) {
        width: 240px;
      }

      :deep(.bt-column) {
        .el-button {
          height: 32px;
          padding: 0 12px;
          font-size: 14px;
        }
      }
    }
  }

  .form {
    padding-right: 64px;
    margin-top: 24px;

    :deep(.el-form-item) {
      margin-bottom: 24px;
    }
    :deep(.el-form-item__content) {
      width: 216px;
    }

    .full-row {
      :deep(.el-form-item__content) {
        width: 968px;
      }
    }

    .row {
      display: flex;
      justify-content: space-between;
    }

    .select,
    .ipt {
      width: 216px;
    }

    .textarea {
      width: 1016px;
    }

    .unit {
      color: rgba(85, 85, 85, 1);
    }

    :deep(.date-select) {
      min-width: 215px;
      width: 100%;
    }

    :deep(.el-row) {
      .el-col {
        max-width: 100%;
      }
    }

    .progress {
      width: 216px;
      :deep(.el-progress__text) {
        font-size: 14px !important;
      }
    }

    .switch-box {
      display: flex;
      align-items: center;
    }

    .readonly {
      background-color: var(--el-disabled-bg-color);
    }

    // 修复disabled状态下的颜色为#999999
    :deep(.el-input.is-disabled .el-input__inner) {
      color: #999999 !important;
      background-color: #f5f5f5;
    }

    :deep(.el-textarea.is-disabled .el-textarea__inner) {
      color: #999999 !important;
      background-color: #f5f5f5;
    }

    :deep(.el-date-editor.is-disabled .el-input__inner) {
      color: #999999 !important;
      background-color: #f5f5f5;
    }

    :deep(.el-input-number.is-disabled .el-input__inner) {
      color: #999999 !important;
      background-color: #f5f5f5;
    }

    :deep(.el-select.is-disabled .el-input__inner) {
      color: #999999 !important;
      background-color: #f5f5f5;
    }

    // 任务评审流程选择器背景色
    :deep(.workflow-select .el-input__inner) {
      background-color: #999999 !important;
    }

    .task-title-container {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .task-title-input {
        flex: 1;
        margin-right: 10px;
      }

      .tags-container {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-top: 8px;

        .tag-item {
          margin-right: 6px;
          margin-bottom: 4px;
        }

        .tag-input {
          width: 120px;
          margin-right: 6px;
          vertical-align: bottom;
        }

        .button-new-tag {
          font-size: 12px;
          padding: 4px 8px;
          height: 28px;
        }
      }
    }

    .workflow-selector {
      display: flex;
      align-items: center;

      .ipt {
        flex: 1;
      }

      .search-btn {
        margin-left: 8px;
      }
    }
  }

  .tabs {
    --el-tabs-header-height: 40px;
    padding-top: 8px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
      padding: 10px 16px 10px 16px;

      .el-tabs__new-tab {
        width: auto;
        border: none;
        margin: 0;
      }
      .el-tabs__nav {
        border-bottom: 1px solid transparent;
      }
      .el-tabs__item {
        margin: 0;
      }
    }

    .tabs-label {
      display: flex;
      align-items: center;

      .icon {
        font-size: 16px;
        margin-left: 8px;
        cursor: pointer;

        &:hover {
          color: var(--el-color-primary);
        }
      }
    }

    .tabs-content {
      padding: 16px;

      // 确保页签内容的表格100%自适应宽度
      :deep(.table-container) {
        width: 100%;

        .el-table {
          width: 100% !important;

          .el-table__header-wrapper,
          .el-table__body-wrapper {
            width: 100% !important;
          }

          // 让表格列自适应宽度
          .el-table__header,
          .el-table__body {
            width: 100% !important;
            table-layout: auto;
          }

          // 确保表格列能够自适应
          .el-table-column--selection {
            width: 50px !important;
          }

          .el-table__column {
            &:not(.el-table-column--selection) {
              min-width: 100px;
            }
          }
        }
      }
    }
  }
}

.attachments-container {
  display: flex;
  flex-wrap: wrap;

  .attachment-item {
    display: flex;
    align-items: center;
    margin-right: 12px;
    margin-bottom: 8px;
    padding: 6px 12px;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    background-color: #f5f5f5;

    .attachment-icon {
      margin-right: 6px;
      color: #666;
    }

    .attachment-name {
      margin-right: 4px;
      color: #333;
    }

    .attachment-size {
      color: #999;
      font-size: 12px;
    }
  }
}

.workflow-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .workflow-version {
    color: #909399;
    font-size: 12px;
  }
}

// 标签下拉菜单样式
:deep(.tag-dropdown-menu) {
  max-height: 160px;
  overflow-y: auto;

  .el-dropdown-menu__item {
    padding: 8px 16px;

    &:hover {
      background-color: #f5f7fa;
    }

    &.is-disabled {
      color: #c0c4cc;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
      }
    }
  }
}

// 隐藏 el-tabs 的关闭按钮并防止宽度变化
:deep(.el-tabs) {
  .el-icon.is-icon-close {
    display: none !important;
  }

  .el-tabs__item {
    // 强制保持一致的padding，防止hover时变化
    padding-left: 20px !important;
    padding-right: 20px !important;

    // 防止hover时宽度变化
    &:hover {
      padding-left: 20px !important;
      padding-right: 20px !important;

      .el-icon.is-icon-close {
        display: none !important;
      }
    }

    // 确保tab宽度稳定
    .el-tabs__item__close {
      display: none !important;
    }
  }

  // 隐藏所有可能的关闭相关元素
  .el-tabs__item__close,
  .el-tabs__item .el-icon-close,
  .is-icon-close {
    display: none !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}
</style>
