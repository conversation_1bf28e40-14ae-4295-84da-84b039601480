import { ref, reactive, markRaw, watch, computed } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import {
  getIndependentTaskDetail,
  saveIndependentTaskDetail,
  type IndeTaskResponse,
} from './IndependentTaskDetailService';
import {
  acceptIndeTask,
  rejectIndeTask,
  addTagToIndependentTask,
  removeTagFromIndependentTask,
  getWorkflowDefinitions,
  submitIndeTaskChange,
  getTaskTagList,
  type IndeTaskAcceptRequest,
  type IndeTaskRejectRequest,
  type WorkflowDefinitionResponse,
  type IndeTaskChangeRequest,
} from './services/TaskDetailService';
import { getUserList } from '../list/components/add_task/AddTaskService';
import type { IndependentTaskDetailForm, TabConfig } from './types';
import { useRouter } from 'vue-router';

// 页签组件导入
import SubtaskList from './components/subtask_list/SubtaskList.vue';
import ReportList from './components/report_list/ReportList.vue';
import ApprovalList from './components/approval_list/ApprovalList.vue';
import IssueList from './components/issue_list/IssueList.vue';
import RiskList from './components/risk_list/RiskList.vue';
import FileList from './components/file_list/FileList.vue';
import MeetingList from './components/meeting_list/MeetingList.vue';
// 注意：HistoryLog.vue 实际上是交付文档页签
import DeliveryDocumentList from './components/history_log/HistoryLog.vue';
// 页签操作组件导入
import ReportTabActions from './components/report_list/tab_actions/ReportTabActions.vue';
import FileListTabActions from './components/file_list/tab_actions/FileListTabActions.vue';
import DeliveryDocTabActions from './components/history_log/tab_actions/DeliveryDocTabActions.vue';
import SubtaskTabActions from './components/subtask_list/tab_actions/SubtaskTabActions.vue';
import IssueTabActions from './components/issue_list/tab_actions/IssueTabActions.vue';
import RiskTabActions from './components/risk_list/tab_actions/RiskTabActions.vue';
import MeetingTabActions from './components/meeting_list/tab_actions/MeetingTabActions.vue';
import ApprovalTabActions from './components/approval_list/tab_actions/ApprovalTabActions.vue';

// 任务状态常量
const TASK_STATUS = {
  PENDING_ACCEPT: 1, // 待接受
  IN_PROGRESS: 2, // 进行中
  COMPLETED: 3, // 已完成
  REJECTED: 4, // 已驳回
};

export const useIndependentTaskDetailStore = defineStore(
  'independent-task-detail',
  () => {
    const router = useRouter();

    // 原始API数据
    const apiData = ref<IndeTaskResponse | null>(null);

    // 表单数据
    const formData = reactive<IndependentTaskDetailForm>({
      indeTaskId: '',
      indeTaskName: '',
      indeTaskStatus: 0,
      indeTaskType: 0,
      indeTaskResponsible: 0,
      responsibleName: '',
      assigneer: 0,
      assigneeTime: '',
      acceptTime: '',
      participants: [],
      indeTaskDesc: '',
      indeTaskAcceptCriteria: '',
      planStartDate: '',
      actualStartDate: '',
      planFinishDate: '',
      actualFinishDate: '',
      actualProgress: 0,
      duration: 0,
      autoProgressFlag: 0,
      workflowId: '',
      overdueDays: 0,
      createTime: '',
      updateTime: '',
      createUser: 0,
      tags: [],
      processDocs: [],
    });

    // 工作流定义列表
    const workflowDefinitions = ref<WorkflowDefinitionResponse[]>([]);
    const loadingWorkflows = ref(false);

    // 可用标签列表
    const availableTags = ref<any[]>([]);
    const loadingTags = ref(false);

    // 用户列表
    const userList = ref<any[]>([]);
    const loadingUsers = ref(false);

    // 检查任务是否可接受/驳回（只有待接受状态的任务才能接受或驳回）
    function canAcceptOrReject(): boolean {
      return formData.indeTaskStatus === TASK_STATUS.PENDING_ACCEPT;
    }

    // 对话框数据
    const dialogData = reactive({
      acceptVisible: false,
      rejectVisible: false,
      acceptOpinion: '',
      rejectOpinion: '',
      acceptFiles: [] as any[],
      rejectFiles: [] as any[],
      loading: false,
    });

    // 任务变更对话框数据
    const taskChangeDialogData = reactive({
      visible: false,
      loading: false,
      changeReason: '',
      formData: {
        indeTaskName: '',
        indeTaskResponsible: '',
        participants: [] as any[],
        planStartDate: '',
        planFinishDate: '',
        duration: 0,
        autoProgressFlag: 0,
        workflowId: '',
        indeTaskDesc: '',
        indeTaskAcceptCriteria: '',
      },
    });

    // 表单验证规则
    const formRules = reactive({
      indeTaskName: [
        { required: true, message: '请输入任务名称', trigger: 'blur' },
      ],
      indeTaskResponsible: [
        { required: true, message: '请选择责任人', trigger: 'blur' },
      ],
      planStartDate: [
        { required: true, message: '请选择计划开始时间', trigger: 'change' },
      ],
      planFinishDate: [
        { required: true, message: '请选择计划完成时间', trigger: 'change' },
      ],
    });

    // 页签配置
    const tabList: TabConfig[] = [
      {
        label: '任务汇报',
        name: 'report',
        add: false,
        component: markRaw(ReportList),
        actions: markRaw(ReportTabActions),
      },
      {
        label: '评审记录',
        name: 'approval',
        add: false,
        component: markRaw(ApprovalList),
        actions: markRaw(ApprovalTabActions),
      },
      {
        label: '子任务',
        name: 'subtask',
        add: false,
        component: markRaw(SubtaskList),
        actions: markRaw(SubtaskTabActions),
      },
      {
        label: '问题',
        name: 'issue',
        add: false,
        component: markRaw(IssueList),
        actions: markRaw(IssueTabActions),
      },
      {
        label: '风险',
        name: 'risk',
        add: false,
        component: markRaw(RiskList),
        actions: markRaw(RiskTabActions),
      },
      {
        label: '会议',
        name: 'meeting',
        add: false,
        component: markRaw(MeetingList),
        actions: markRaw(MeetingTabActions),
      },
      {
        label: '过程文档',
        name: 'file',
        add: false,
        component: markRaw(FileList),
        actions: markRaw(FileListTabActions),
      },
      {
        label: '交付文档',
        name: 'delivery',
        add: false,
        component: markRaw(DeliveryDocumentList),
        actions: markRaw(DeliveryDocTabActions),
      },
    ];

    // 当前活跃页签
    const activeTab = ref('report');

    // 加载状态
    const loading = ref(false);

    // 页签组件引用
    const tabComponents = ref<any[]>([]);

    // 监听activeTab变化，切换时刷新当前tab数据
    watch(activeTab, (newTab, oldTab) => {
      if (newTab !== oldTab && formData.indeTaskId) {
        // 延迟一下确保组件已经渲染
        setTimeout(() => {
          refreshCurrentTabData();
        }, 100);
      }
    });

    // 刷新当前激活tab的数据
    function refreshCurrentTabData() {
      const currentTabIndex = tabList.findIndex(
        (tab) => tab.name === activeTab.value
      );
      if (currentTabIndex >= 0) {
        const component = tabComponents.value[currentTabIndex];
        if (component && typeof component.refreshData === 'function') {
          component.refreshData();
        } else if (component && typeof component.loadData === 'function') {
          component.loadData();
        } else {
          console.warn(
            `Tab组件 ${activeTab.value} 没有暴露refreshData或loadData方法`
          );
        }
      }
    }

    // 获取状态类型（用于显示状态标签的样式）
    function getStatusType(status: number): string {
      const statusMap: Record<number, string> = {
        1: 'info', // 未开始
        2: 'primary', // 进行中
        3: 'success', // 已完成
      };
      return statusMap[status] || 'info';
    }

    // 获取状态文本
    function getStatusText(status: number): string {
      const statusTextMap: Record<number, string> = {
        1: '未开始',
        2: '进行中',
        3: '已完成',
      };
      return statusTextMap[status] || '未开始';
    }

    // 获取任务类型文本
    function getTaskTypeText(taskType: number): string {
      const taskTypeTextMap: Record<number, string> = {
        1: '里程碑作业',
        2: '任务作业',
        3: 'wbs作业',
        4: '配合作业',
      };
      return taskTypeTextMap[taskType] || '';
    }

    // 设置页签组件引用
    function setTabComponent(index: number, component: any) {
      tabComponents.value[index] = component;
    }

    // 处理页签的添加按钮点击
    function handleTabAdd(index: number) {
      const component = tabComponents.value[index];
      if (component && component.openAdd) {
        component.openAdd();
      }
    }

    // 切换到评审记录页签
    function switchToApprovalTab() {
      activeTab.value = 'approval';
    }

    // 获取责任人姓名
    function getResponsibleName(data: IndeTaskResponse): string {
      // 优先使用API返回的responsibleName
      if (data.responsibleName) {
        return data.responsibleName;
      }

      // 如果没有responsibleName，通过indeTaskResponsible ID在用户列表中查找
      if (data.indeTaskResponsible && userList.value.length > 0) {
        const user = userList.value.find(
          (u) => u.userId === data.indeTaskResponsible
        );
        return user?.userName || '';
      }

      return '';
    }

    // 获取指派人姓名
    const assigneerName = computed(() => {
      if (!formData.assigneer || formData.assigneer === 0) {
        return '';
      }

      const user = userList.value.find((u) => u.userId === formData.assigneer);
      return user?.userName || formData.assigneer;
    });

    // 映射API数据到表单数据
    function mapApiDataToForm(data: IndeTaskResponse) {
      Object.assign(formData, {
        indeTaskId: data.indeTaskId || '',
        indeTaskName: data.indeTaskName || '',
        indeTaskStatus: data.indeTaskStatus || 0,
        indeTaskType: data.indeTaskType || 0,
        indeTaskResponsible: data.indeTaskResponsible || 0,
        responsibleName: getResponsibleName(data),
        assigneer: data.assigneer || 0,
        assigneeTime: data.assigneeTime || '',
        acceptTime: '', // acceptTime 在新接口中不存在，保持为空
        participants: data.participants || [],
        indeTaskDesc: data.indeTaskDesc || '',
        indeTaskAcceptCriteria: data.indeTaskAcceptCriteria || '',
        planStartDate: data.planStartDate || '',
        actualStartDate: data.actualStartDate || '',
        planFinishDate: data.planFinishDate || '',
        actualFinishDate: data.actualFinishDate || '',
        actualProgress: data.actualProgress || 0,
        duration: data.duration || 0,
        autoProgressFlag: data.autoProgressFlag || 0,
        workflowId: data.workflowId || '',
        overdueDays: data.overdueDays || 0,
        createTime: data.createTime || '',
        updateTime: data.updateTime || '',
        createUser: data.createUser || 0,
        tags: data.tags || [],
        processDocs: data.processDocs || [],
      });
    }

    // 加载工作流定义列表
    async function loadWorkflowDefinitions() {
      loadingWorkflows.value = true;
      try {
        const workflows = await getWorkflowDefinitions();
        workflowDefinitions.value = workflows;
      } catch (error) {
        console.error('加载工作流定义列表失败:', error);
      } finally {
        loadingWorkflows.value = false;
      }
    }

    // 加载可用标签列表
    async function loadAvailableTags() {
      loadingTags.value = true;
      try {
        // 添加状态筛选参数，只获取启用状态的标签
        const tags = await getTaskTagList({ taskTagStatus: 1 });
        availableTags.value = tags;
      } catch (error) {
        console.error('加载标签列表失败:', error);
      } finally {
        loadingTags.value = false;
      }
    }

    // 加载任务详情
    async function loadTaskDetail(taskId: string) {
      loading.value = true;

      // 并行加载工作流定义列表、标签列表和用户列表
      await Promise.all([
        loadWorkflowDefinitions(),
        loadAvailableTags(),
        loadUserList(),
      ]);

      const response = await getIndependentTaskDetail(taskId);
      console.log('API返回的任务详情数据:', response);
      console.log('responsibleName字段:', response.responsibleName);
      if (response) {
        apiData.value = response;
        mapApiDataToForm(response);
      }

      loading.value = false;
    }

    // 添加标签到任务
    async function addTaskTag(tagId: number) {
      if (!apiData.value?.indeTaskId) {
        ElMessage.error('任务ID不存在');
        return;
      }

      if (!tagId) {
        ElMessage.error('标签ID不能为空');
        return;
      }

      try {
        // 检查是否已经添加过该标签
        if (formData.tags.some((tag) => tag.taskTagId === tagId)) {
          ElMessage.warning('该标签已添加');
          return;
        }

        // 直接添加标签到任务
        const result = await addTagToIndependentTask(apiData.value.indeTaskId, [
          { taskTagId: tagId },
        ]);

        if (result.success) {
          // 刷新任务详情以获取最新标签
          await loadTaskDetail(apiData.value.indeTaskId.toString());
          ElMessage.success('成功添加标签');
        } else {
          ElMessage.error('添加标签失败');
        }
      } catch (error) {
        console.error('添加标签失败:', error);
        ElMessage.error('添加标签失败');
      }
    }

    // 从任务中移除标签
    async function removeTaskTag(tagId: number) {
      if (!apiData.value?.indeTaskId) {
        ElMessage.error('任务ID不存在');
        return;
      }

      try {
        // 查找要删除的标签
        const tagToRemove = formData.tags.find(
          (tag) => tag.taskTagId === tagId
        );
        if (!tagToRemove) {
          ElMessage.error('标签不存在');
          return;
        }

        // 调用API删除标签
        const result = await removeTagFromIndependentTask(
          apiData.value.indeTaskId,
          tagId
        );

        if (result.success) {
          // 重新加载任务详情以获取最新标签
          await loadTaskDetail(apiData.value.indeTaskId.toString());
          ElMessage.success(`成功移除标签 "${tagToRemove.taskTagName}"`);
        } else {
          ElMessage.error(`移除标签 "${tagToRemove.taskTagName}" 失败`);
        }
      } catch (error) {
        console.error('移除标签失败:', error);
        ElMessage.error('移除标签失败');
      }
    }

    // 保存任务详情
    async function saveTaskDetail() {
      if (!apiData.value) {
        ElMessage.error('没有可保存的数据');
        return;
      }

      try {
        const saveData = {
          indeTaskId: Number(formData.indeTaskId),
          indeTaskName: formData.indeTaskName,
          indeTaskDesc: formData.indeTaskDesc,
          indeTaskAcceptCriteria: formData.indeTaskAcceptCriteria,
          planStartDate: formData.planStartDate,
          actualStartDate: formData.actualStartDate,
          planFinishDate: formData.planFinishDate,
          duration: formData.duration,
          actualProgress: formData.actualProgress,
          autoProgressFlag: formData.autoProgressFlag,
          workflowId: formData.workflowId,
          remark: formData.indeTaskDesc,
        };

        await saveIndependentTaskDetail(saveData);
        ElMessage.success('保存任务详情成功');

        // 重新加载任务详情以获取最新数据
        await loadTaskDetail(formData.indeTaskId.toString());
      } catch (error) {
        console.error('保存任务详情失败:', error);
        ElMessage.error('保存任务详情失败');
      }
    }

    // 打开接受任务对话框
    function openAcceptDialog() {
      dialogData.acceptVisible = true;
      dialogData.acceptOpinion = '';
      dialogData.acceptFiles = [];
    }

    // 打开驳回任务对话框
    function openRejectDialog() {
      dialogData.rejectVisible = true;
      dialogData.rejectOpinion = '';
      dialogData.rejectFiles = [];
    }

    // 接受任务
    async function acceptTask() {
      if (!apiData.value?.indeTaskId) {
        ElMessage.error('任务ID不存在');
        return;
      }

      dialogData.loading = true;
      try {
        // 构建过程文档列表
        const processDocs = dialogData.acceptFiles.map((file) => ({
          docId: file.docId,
          docName: file.docName,
          docSize: file.docSize,
          docType: file.docType,
          docUrl: file.docUrl,
        }));

        const acceptData: IndeTaskAcceptRequest = {
          indeTaskId: apiData.value.indeTaskId,
          acceptOpinion: dialogData.acceptOpinion,
          processDocs,
        };

        await acceptIndeTask(acceptData);
        ElMessage.success('接受任务成功');
        dialogData.acceptVisible = false;

        // 重新加载任务详情
        await loadTaskDetail(apiData.value.indeTaskId.toString());
      } catch (error) {
        console.error('接受任务失败:', error);
        ElMessage.error('接受任务失败');
      } finally {
        dialogData.loading = false;
      }
    }

    // 驳回任务
    async function rejectTask() {
      if (!apiData.value?.indeTaskId) {
        ElMessage.error('任务ID不存在');
        return;
      }

      dialogData.loading = true;
      try {
        // 构建过程文档列表
        const processDocs = dialogData.rejectFiles.map((file) => ({
          docId: file.docId,
          docName: file.docName,
          docSize: file.docSize,
          docType: file.docType,
          docUrl: file.docUrl,
        }));

        const rejectData: IndeTaskRejectRequest = {
          indeTaskId: apiData.value.indeTaskId,
          rejectOpinion: dialogData.rejectOpinion,
          processDocs,
        };

        await rejectIndeTask(rejectData);
        ElMessage.success('驳回任务成功');
        dialogData.rejectVisible = false;

        // 重新加载任务详情
        await loadTaskDetail(apiData.value.indeTaskId.toString());
      } catch (error) {
        console.error('驳回任务失败:', error);
        ElMessage.error('驳回任务失败');
      } finally {
        dialogData.loading = false;
      }
    }

    // 返回任务列表
    function backToList() {
      router.push({
        path: '/independent-tasks/list',
      });
    }

    // 加载用户列表
    async function loadUserList() {
      if (loadingUsers.value) return;

      loadingUsers.value = true;
      try {
        const users = await getUserList();
        if (Array.isArray(users)) {
          userList.value = users;
        }
      } catch (error) {
        console.error('加载用户列表失败:', error);
      } finally {
        loadingUsers.value = false;
      }
    }

    // 打开任务变更对话框
    function openTaskChangeDialog() {
      if (!apiData.value) {
        ElMessage.error('任务数据不存在');
        return;
      }

      // 初始化变更表单数据
      taskChangeDialogData.changeReason = '';
      Object.assign(taskChangeDialogData.formData, {
        indeTaskName: formData.indeTaskName,
        indeTaskResponsible: formData.indeTaskResponsible,
        participants: formData.participants || [],
        planStartDate: formData.planStartDate,
        planFinishDate: formData.planFinishDate,
        duration: formData.duration,
        autoProgressFlag: formData.autoProgressFlag,
        workflowId: formData.workflowId,
        indeTaskDesc: formData.indeTaskDesc,
        indeTaskAcceptCriteria: formData.indeTaskAcceptCriteria,
      });

      taskChangeDialogData.visible = true;
    }

    // 提交任务变更
    async function submitTaskChange() {
      if (!apiData.value?.indeTaskId) {
        ElMessage.error('任务ID不存在');
        return;
      }

      if (!taskChangeDialogData.changeReason.trim()) {
        ElMessage.error('请填写变更原因');
        return;
      }

      taskChangeDialogData.loading = true;
      try {
        // 构建任务变更请求参数
        const changeRequest: IndeTaskChangeRequest = {
          tenantId: apiData.value.tenantId || 0,
          indeTaskId: apiData.value.indeTaskId,
          indeTaskName: taskChangeDialogData.formData.indeTaskName,
          indeTaskResponsible: Number(
            taskChangeDialogData.formData.indeTaskResponsible
          ),
          planStartDate: taskChangeDialogData.formData.planStartDate,
          planFinishDate: taskChangeDialogData.formData.planFinishDate,
          duration: taskChangeDialogData.formData.duration,
          autoProgressFlag: taskChangeDialogData.formData.autoProgressFlag,
          workflowId: taskChangeDialogData.formData.workflowId,
          participants: taskChangeDialogData.formData.participants.map((p) => ({
            userId: p.userId || 0,
            tenantId: apiData.value?.tenantId || 0,
          })),
          changeReason: taskChangeDialogData.changeReason,
        };

        // 调用任务变更API
        const result = await submitIndeTaskChange(changeRequest);

        if (result.success) {
          ElMessage.success('任务变更成功');
          taskChangeDialogData.visible = false;

          // 重新加载任务详情
          await loadTaskDetail(apiData.value.indeTaskId.toString());
        } else {
          ElMessage.error('任务变更失败');
        }
      } catch (error) {
        console.error('任务变更失败:', error);
        ElMessage.error('任务变更失败');
      } finally {
        taskChangeDialogData.loading = false;
      }
    }

    return {
      formData,
      formRules,
      dialogData,
      taskChangeDialogData,
      tabList,
      activeTab,
      loading,
      tabComponents,
      apiData,
      workflowDefinitions,
      loadingWorkflows,
      availableTags,
      loadingTags,
      getStatusType,
      getStatusText,
      getTaskTypeText,
      setTabComponent,
      handleTabAdd,
      switchToApprovalTab,
      loadTaskDetail,
      loadWorkflowDefinitions,
      loadAvailableTags,
      loadUserList,
      addTaskTag,
      removeTaskTag,
      saveTaskDetail,
      openAcceptDialog,
      openRejectDialog,
      acceptTask,
      rejectTask,
      backToList,
      canAcceptOrReject,
      TASK_STATUS,
      refreshCurrentTabData,
      openTaskChangeDialog,
      submitTaskChange,
      userList,
      loadingUsers,
      assigneerName,
    };
  }
);
