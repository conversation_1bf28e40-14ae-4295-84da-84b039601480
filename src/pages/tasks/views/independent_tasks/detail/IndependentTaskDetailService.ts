import { send } from '@/libs/request';

/**
 * IndeTaskResponse，独立任务详情
 */
export interface IndeTaskResponse {
  /**
   * 实际完成日期
   */
  actualFinishDate?: string;
  /**
   * 实际进度
   */
  actualProgress?: number;
  /**
   * 实际开始日期
   */
  actualStartDate?: string;
  /**
   * 指派人
   */
  assigneer?: number;
  /**
   * 指派时间
   */
  assigneeTime?: string;
  /**
   * 是否自动进度(1 是 0 否)
   */
  autoProgressFlag?: number;
  /**
   * 子独立任务列表
   */
  children?: IndeTaskResponse[];
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建者ID
   */
  createUser?: number;
  /**
   * 工期
   */
  duration?: number;
  /**
   * 独立任务验收标准
   */
  indeTaskAcceptCriteria?: string;
  /**
   * 独立任务说明
   */
  indeTaskDesc?: string;
  /**
   * 独立任务ID
   */
  indeTaskId?: number;
  /**
   * 独立任务名称
   */
  indeTaskName?: string;
  /**
   * 独立任务责任人
   */
  indeTaskResponsible?: number;
  /**
   * 独立任务责任部门
   */
  indeTaskResponsibleDept?: number;
  /**
   * 独立任务状态(1 未开始,2 进行中,3 已关闭)
   */
  indeTaskStatus?: number;
  /**
   * 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业)
   */
  indeTaskType?: number;
  /**
   * 超期
   */
  overdueDays?: number;
  /**
   * 独立任务父任务ID
   */
  parentIndeTaskId?: number;
  /**
   * 参与人列表
   */
  participants?: ParticipantResponse[];
  /**
   * 计划完成日期
   */
  planFinishDate?: string;
  /**
   * 计划进度
   */
  planProgress?: number;
  /**
   * 计划开始日期
   */
  planStartDate?: string;
  /**
   * 过程文档列表
   */
  processDocs?: IndeTaskProcessDocResponse[];
  /**
   * 备注
   */
  remark?: string;
  /**
   * 责任人姓名
   */
  responsibleName?: string;
  /**
   * 任务标签列表
   */
  tags?: ConfigTaskTagResponse[];
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 更新者ID
   */
  updateUser?: number;
  /**
   * 独立任务绑定的工作流定义ID
   */
  workflowId?: string;
}

/**
 * ParticipantResponse，参与人
 */
export interface ParticipantResponse {
  /**
   * 参与人ID
   */
  userId?: number;
  /**
   * 参与人姓名
   */
  userName?: string;
}

/**
 * IndeTaskProcessDocResponse，独立任务过程文档
 */
export interface IndeTaskProcessDocResponse {
  /**
   * 创建人姓名
   */
  createUserName?: string;
  /**
   * 附件id(关联附件表)
   */
  docId?: number;
  /**
   * 附件唯一标识
   */
  docKey?: string;
  /**
   * 附件名
   */
  docName?: string;
  /**
   * 大小
   */
  docSize?: number;
  /**
   * 附件格式
   */
  docType?: string;
  /**
   * 是否文件夹(0 否,1 是)
   */
  folderFlag?: number;
  /**
   * 独立任务ID
   */
  indeTaskId?: number;
  /**
   * 自增主键
   */
  indeTaskProcessDocId?: number;
  /**
   * 父文档ID,或文件夹ID
   */
  parentIndeTaskProcessDocId?: number;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 更新人姓名
   */
  updateUserName?: string;
}

/**
 * ConfigTaskTagResponse，任务标签
 */
export interface ConfigTaskTagResponse {
  /**
   * 子任务标签列表
   */
  children?: ConfigTaskTagResponse[];
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 创建者ID
   */
  createUser?: number;
  /**
   * 父标签ID或者所属标签组ID
   */
  parentTaskTagId?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 排序码
   */
  sortNum?: number;
  /**
   * 是否是标签组(1 是,0 否)
   */
  tagGroupFlag?: number;
  /**
   * 任务标签颜色
   */
  taskTagColor?: string;
  /**
   * 任务标签描述
   */
  taskTagDesc?: string;
  /**
   * 任务标签ID
   */
  taskTagId?: number;
  /**
   * 任务标签标题
   */
  taskTagName?: string;
  /**
   * 任务标签状态 (1 启用,0 禁用)
   */
  taskTagStatus?: number;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 更新时间
   */
  updateTime?: Date;
  /**
   * 更新者ID
   */
  updateUser?: number;
}

export interface IndeTaskEditRequest {
  indeTaskId?: number;
  parentIndeTaskId?: number;
  indeTaskName?: string;
  indeTaskStatus?: number;
  indeTaskResponsible?: number;
  indeTaskResponsibleDept?: number;
  indeTaskType?: number;
  planProgress?: number;
  actualProgress?: number;
  indeTaskAcceptCriteria?: string;
  planStartDate?: string;
  actualStartDate?: string;
  planFinishDate?: string;
  actualFinishDate?: string;
  duration?: number;
  workflowId?: string;
  autoProgressFlag?: number;
  indeTaskDesc?: string;
  remark?: string;
}

/**
 * 获取独立任务详情
 * @param indeTaskId 独立任务ID
 * @returns 任务详情数据
 */
export async function getIndependentTaskDetail(
  indeTaskId: string | number
): Promise<IndeTaskResponse> {
  return send({
    method: 'GET',
    url: `/projectmanage/inde-task/${indeTaskId}`,
  });
}

/**
 * 保存独立任务详情
 * @param data 任务详情数据
 */
export async function saveIndependentTaskDetail(
  data: IndeTaskEditRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task/edit`,
    data,
  });
}
