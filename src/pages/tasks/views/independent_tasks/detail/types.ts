/**
 * 项目任务详情响应接口 - 基于API返回的数据结构
 */
export interface ProjectTaskDetailResponse {
  /** 项目任务ID */
  projTaskId: number;
  /** 项目任务父任务ID */
  parentProjTaskId: number;
  /** 所属项目ID */
  projId: number;
  /** 阶段ID */
  phaseProjTaskId: number;
  /** 项目任务名称 */
  projTaskName: string;
  /** 项目任务状态 */
  projTaskStatus: number;
  /** 项目任务责任人 */
  projTaskResponsible: number;
  /** 项目任务责任人项目角色 */
  projTaskResponsibleProjRole: string;
  /** 项目任务责任部门 */
  projTaskResponsibleDept: number;
  /** 计划进度 */
  planProgress: number;
  /** 实际进度 */
  actualProgress: number;
  /** 超期天数 */
  overdueDays: number;
  /** 作业类型 */
  taskType: number;
  /** 任务作业类型 */
  taskActivityType: number;
  /** 专业 */
  major: string;
  /** 系统ID */
  systemId: number;
  /** 项目任务说明 */
  projTaskDesc: string;
  /** 项目任务验收标准 */
  projTaskAcceptCriteria: string;
  /** 计划开始日期 */
  planStartDate: string;
  /** 实际开始日期 */
  actualStartDate: string;
  /** 计划完成日期 */
  planFinishDate: string;
  /** 工期 */
  duration: number;
  /** 工作流ID */
  workflowId: string;
  /** 是否阶段 */
  phaseFlag: number;
  /** 是否自动进度 */
  autoProgressFlag: number;
  /** 实际完成日期 */
  actualFinishDate: string;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 创建者ID */
  createUser: number;
  /** 更新者ID */
  updateUser: number;
  /** 备注 */
  remark: string;
  /** 租户ID */
  tenantId: number;
  /** 标签列表 */
  tags: TaskTag[];
  /** 参与人列表 */
  participants: TaskParticipant[];
  /** 附件列表 */
  attaments: TaskAttachment[];
  /** 前置任务列表 */
  predTasks: TaskPredecessor[];
  /** 责任人姓名 */
  responsibleName: string;
  /** 项目名称 */
  projectName: string;
}

/**
 * 任务标签
 */
export interface TaskTag {
  /** 标签ID */
  taskTagId: number;
  /** 父标签ID */
  parentTaskTagId: number;
  /** 标签组标识 */
  tagGroupFlag: number;
  /** 标签名称 */
  taskTagName: string;
  /** 标签描述 */
  taskTagDesc: string;
  /** 标签颜色 */
  taskTagColor: string;
  /** 标签状态 */
  taskTagStatus: number;
  /** 排序号 */
  sortNum: number;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 创建者ID */
  createUser: number;
  /** 更新者ID */
  updateUser: number;
  /** 备注 */
  remark: string;
  /** 租户ID */
  tenantId: number;
}

/**
 * 任务参与人
 */
export interface TaskParticipant {
  /** 用户ID */
  userId: number;
  /** 用户名称 */
  userName: string;
}

/**
 * 任务附件
 */
export interface TaskAttachment {
  /** 文档ID */
  docId: number;
  /** 租户ID */
  tenantId: number;
  /** 文档名称 */
  docName: string;
  /** 文档标识 */
  docKey: string;
  /** 文档路径 */
  docPath: string;
  /** 文档大小 */
  docSize: number;
  /** 文档类型 */
  docType: string;
  /** 请求ID */
  requestId: string;
  /** 创建者ID */
  createUser: number;
  /** 创建时间 */
  createTime: string;
  /** 更新者ID */
  updateUser: number;
  /** 更新时间 */
  updateTime: string;
  /** 创建者姓名 */
  createUserName: string;
  /** 更新者姓名 */
  updateUserName: string;
}

/**
 * 前置任务
 */
export interface TaskPredecessor {
  /** 前置任务ID */
  taskPredId: number;
  /** 项目任务ID */
  projTaskId: number;
  /** 前置项目任务ID */
  predProjTaskId: number;
  /** 项目ID */
  projId: number;
  /** 前置计划ID */
  predPlanId: number;
  /** 前置项目ID */
  predProjId: number;
  /** 前置类型 */
  predType: number;
  /** 滞后小时数 */
  lagHrCnt: number;
  /** 是否强制 */
  isForce: number;
  /** 创建者ID */
  createUser: number;
  /** 创建时间 */
  createTime: string;
  /** 更新者ID */
  updateUser: number;
  /** 更新时间 */
  updateTime: string;
  /** 备注 */
  remark: string;
  /** 租户ID */
  tenantId: number;
}

/**
 * 独立任务详情表单数据类型
 */
export interface IndependentTaskDetailForm {
  /** 独立任务ID */
  indeTaskId: string | number;
  /** 独立任务名称 */
  indeTaskName: string;
  /** 独立任务状态(1 未开始,2 进行中,3 已关闭) */
  indeTaskStatus: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  indeTaskType: number;
  /** 独立任务责任人ID */
  indeTaskResponsible: number;
  /** 责任人姓名 */
  responsibleName: string;
  /** 指派人ID */
  assigneer: number;
  /** 指派时间 */
  assigneeTime: string;
  /** 任务接受时间 */
  acceptTime: string;
  /** 参与人列表 */
  participants: TaskParticipant[];
  /** 独立任务说明 */
  indeTaskDesc: string;
  /** 独立任务验收标准 */
  indeTaskAcceptCriteria: string;
  /** 计划开始日期 */
  planStartDate: string;
  /** 实际开始日期 */
  actualStartDate: string;
  /** 计划完成日期 */
  planFinishDate: string;
  /** 实际完成日期 */
  actualFinishDate: string;
  /** 实际进度 */
  actualProgress: number;
  /** 工期 */
  duration: number;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag: number;
  /** 独立任务绑定的工作流定义ID */
  workflowId: string;
  /** 超期天数 */
  overdueDays: number;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 创建者ID */
  createUser: number;
  /** 任务标签列表 */
  tags: TaskTag[];
  /** 过程文档列表 */
  processDocs: TaskAttachment[];
}

/**
 * 页签配置类型
 */
export interface TabConfig {
  label: string;
  name: string;
  add: boolean;
  component: any;
  actions?: any; // 可选的操作组件
}

// 通用分页响应类型
export interface PageResponse<T> {
  list: T[];
  pageNum: number;
  pageSize: number;
  total: number;
  pages: number;
}

// API标准响应类型
export interface ApiResponse<T> {
  code: number;
  timestamp: number;
  data: T;
}
