import { send } from '@/libs/request';
import type { PageResponse } from '../types';

// 风险相关类型定义
export interface IndeTaskRiskResponse {
  indeTaskRiskId?: number;
  indeTaskId?: number;
  indeTaskRiskName?: string;
  indeTaskRiskStatus?: number;
  indeTaskRiskType?: number | string;
  indeTaskRiskLevel?: number;
  disCloseFlag?: number;
  publicReplyFlag?: number;
  closingTime?: string;
  completionDate?: string;
  indeTaskResponsible?: number;
  createTime?: string;
  updateTime?: string;
  createUser?: number;
  updateUser?: number;
  remark?: string;
  tenantId?: number;
  indeTaskResponsibleName?: string;
  createUserName?: string;
}

/**
 * IndeTaskRiskAddRequest，独立任务风险新增入参对象
 */
export interface IndeTaskRiskAddRequest {
  /**
   * 截止时间
   */
  closingTime?: string;
  /**
   * 是否公开 (1是0否)
   */
  disCloseFlag?: number;
  /**
   * 独立任务ID
   */
  indeTaskId?: number;
  /**
   * 独立任务风险负责人ID
   */
  indeTaskResponsible?: number;
  /**
   * 风险级别 (1高2中3低)
   */
  indeTaskRiskLevel: number;
  /**
   * 独立任务风险名称
   */
  indeTaskRiskName: string;
  /**
   * 风险分类
   */
  indeTaskRiskType: number | string;
  /**
   * 公开回复 (1是0否)
   */
  publicReplyFlag?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 文档列表
   */
  riskDocs?: IndeTaskRiskDocAddRequest[];
  [property: string]: any;
}

/**
 * IndeTaskRiskDocAddRequest，独立任务风险,风险回答附件入参对象
 */
export interface IndeTaskRiskDocAddRequest {
  /**
   * 附件id(关联附件表)
   */
  docId: number;
  /**
   * 附件唯一标识
   */
  docKey: string;
  /**
   * 附件名
   */
  docName: string;
  /**
   * 是否文件夹(0 否,1 是)
   */
  folderFlag: number;
  /**
   * 父文档ID,或文件夹ID
   */
  parentIndeTaskRiskDocId?: number;
  [property: string]: any;
}

export interface IndeTaskRiskEditRequest {
  indeTaskRiskId?: number;
  indeTaskId?: number;
  indeTaskRiskName?: string;
  indeTaskRiskStatus?: number;
  indeTaskRiskType?: number | string;
  indeTaskRiskLevel?: number;
  disCloseFlag?: number;
  publicReplyFlag?: number;
  closingTime?: string;
  completionDate?: string;
  indeTaskResponsible?: number;
  remark?: string;
}

export interface IndeTaskRiskQueryRequest {
  indeTaskId?: number;
  indeTaskRiskName?: string;
  indeTaskRiskStatus?: number;
  indeTaskRiskType?: number | string;
  indeTaskRiskLevel?: number;
  indeTaskResponsible?: number;
  createUser?: number;
  startTime?: string;
  endTime?: string;
}

export interface IndeTaskRiskDeleteRequest {
  indeTaskRiskId: number;
}

export interface IndeTaskRiskAssignRequest {
  indeTaskRiskId: number;
  indeTaskResponsible: number;
}

/**
 * 获取独立任务风险列表（分页）
 */
export async function getIndeTaskRiskPage(
  pageNum: number,
  pageSize: number,
  queryRequest?: IndeTaskRiskQueryRequest
): Promise<PageResponse<IndeTaskRiskResponse>> {
  const response = await send({
    method: 'POST',
    url: `/projectmanage/inde-task-risk/page/${pageSize}/${pageNum}`,
    data: queryRequest || {},
  });
  
  return response;
}

/**
 * 获取独立任务风险列表
 */
export async function getIndeTaskRiskList(
  queryRequest?: IndeTaskRiskQueryRequest
): Promise<IndeTaskRiskResponse[]> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-risk/list`,
    data: queryRequest || {},
  });
}

/**
 * 添加独立任务风险
 */
export async function addIndeTaskRisk(
  data: IndeTaskRiskAddRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-risk/add`,
    data,
  });
}

/**
 * 编辑独立任务风险
 */
export async function editIndeTaskRisk(
  data: IndeTaskRiskEditRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-risk/edit`,
    data,
  });
}

/**
 * 删除独立任务风险
 */
export async function deleteIndeTaskRisk(
  data: IndeTaskRiskDeleteRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-risk/delete`,
    data,
  });
}

/**
 * 批量删除独立任务风险
 */
export async function batchDeleteIndeTaskRisk(
  data: IndeTaskRiskDeleteRequest[]
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-risk/batch/delete`,
    data,
  });
}

/**
 * 获取独立任务风险详情
 */
export async function getIndeTaskRiskDetail(
  indeTaskRiskId: number
): Promise<IndeTaskRiskResponse> {
  return send({
    method: 'GET',
    url: `/projectmanage/inde-task-risk/${indeTaskRiskId}`,
  });
}

/**
 * 关闭独立任务风险
 */
export async function closeIndeTaskRisk(
  data: IndeTaskRiskDeleteRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-risk/close`,
    data,
  });
}

/**
 * 转换风险为问题
 */
export async function convertRiskToIssue(
  data: IndeTaskRiskDeleteRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-risk/convert/issue`,
    data,
  });
}

/**
 * 重新指派独立任务风险
 */
export async function assignIndeTaskRisk(
  data: IndeTaskRiskAssignRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-risk/assign`,
    data,
  });
} 