import { send } from '@/libs/request';
import type { PageResponse } from '../types';

/**
 * IndeTaskMeetingResponse，独立任务会议
 */
export interface IndeTaskMeetingResponse {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建者ID
   */
  createUser?: number;
  /**
   * 会议纪要
   */
  docs?: IndeTaskMeetingDocResponse[];
  /**
   * 独立任务ID
   */
  indeTaskId?: number;
  /**
   * 发起人ID
   */
  indeTaskInitiator?: number;
  /**
   * 会议地点
   */
  indeTaskLocation?: string;
  /**
   * 会议ID
   */
  indeTaskMeetingId?: number;
  /**
   * 会议状态(1未开始, 2进行中, 3已完成, 4已取消)
   */
  indeTaskMeetingStatus?: number;
  /**
   * 会议时间
   */
  indeTaskMeetingTime?: string;
  /**
   * 会议链接
   */
  indeTaskMeetingUrl?: string;
  /**
   * 会议主题
   */
  indeTaskSubject?: string;
  /**
   * 参与人员
   */
  participants?: ParticipantResponse[];
  /**
   * 备注
   */
  remark?: string;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 更新者ID
   */
  updateUser?: number;
  [property: string]: any;
}

/**
 * IndeTaskMeetingDocResponse，独立任务会议纪要
 */
export interface IndeTaskMeetingDocResponse {
  /**
   * 创建人姓名
   */
  createUserName?: string;
  /**
   * 附件id(关联附件表)
   */
  docId?: number;
  /**
   * 附件唯一标识
   */
  docKey?: string;
  /**
   * 附件名
   */
  docName?: string;
  /**
   * 大小
   */
  docSize?: number;
  /**
   * 附件格式
   */
  docType?: string;
  /**
   * 是否文件夹(0 否,1 是)
   */
  folderFlag?: number;
  /**
   * 自增主键
   */
  indeTaskMeetingDocId?: number;
  /**
   * 独立任务ID
   */
  indeTaskMeetingId?: number;
  /**
   * 父文档ID,或文件夹ID
   */
  parentIndeTaskMeetingDocId?: number;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 更新人姓名
   */
  updateUserName?: string;
  [property: string]: any;
}

/**
 * ParticipantResponse，参与人响应对象
 */
export interface ParticipantResponse {
  /**
   * 参与人ID
   */
  userId?: number;
  /**
   * 参与人姓名
   */
  userName?: string;
  [property: string]: any;
}

/**
 * IndeTaskMeetingEditRequest，独立任务会议编辑请求
 */
export interface IndeTaskMeetingEditRequest {
  /**
   * 独立任务ID
   */
  indeTaskId?: number;
  /**
   * 发起人ID
   */
  indeTaskInitiator?: number;
  /**
   * 会议地点
   */
  indeTaskLocation?: string;
  /**
   * 会议ID
   */
  indeTaskMeetingId?: number;
  /**
   * 会议状态(1未开始, 2进行中, 3已完成, 4已取消)
   */
  indeTaskMeetingStatus?: number;
  /**
   * 会议时间
   */
  indeTaskMeetingTime?: string;
  /**
   * 会议链接
   */
  indeTaskMeetingUrl?: string;
  /**
   * 会议主题
   */
  indeTaskSubject?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 参与人员
   */
  participants?: ParticipantResponse[];
  [property: string]: any;
}

/**
 * 查询独立任务会议请求参数
 */
export interface IndeTaskMeetingQueryRequest {
  /**
   * 独立任务ID
   */
  indeTaskId?: number;
  /**
   * 会议主题
   */
  indeTaskSubject?: string;
  /**
   * 会议状态
   */
  indeTaskMeetingStatus?: number;
  [property: string]: any;
}

/**
 * 获取独立任务会议列表（分页）
 */
export async function getIndeTaskMeetingPage(
  pageSize: number,
  pageNum: number,
  queryRequest?: IndeTaskMeetingQueryRequest
): Promise<PageResponse<IndeTaskMeetingResponse>> {
  const response = await send({
    method: 'POST',
    url: `/projectmanage/inde-task-meeting/list`,
    data: {
      ...queryRequest,
    },
  });
  
  return {
    list: response || [],
    total: response?.length || 0,
    pageNum: pageNum,
    pageSize: pageSize,
    pages: Math.ceil((response?.length || 0) / pageSize)
  };
}

/**
 * 获取独立任务会议列表
 */
export async function getIndeTaskMeetingList(
  queryRequest?: IndeTaskMeetingQueryRequest
): Promise<IndeTaskMeetingResponse[]> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-meeting/list`,
    data: queryRequest || {},
  });
}

/**
 * 添加独立任务会议
 */
export async function addIndeTaskMeeting(
  data: IndeTaskMeetingEditRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-meeting/add`,
    data,
  });
}

/**
 * 编辑独立任务会议
 */
export async function editIndeTaskMeeting(
  data: IndeTaskMeetingEditRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-meeting/edit`,
    data,
  });
}

/**
 * 删除独立任务会议
 */
export async function deleteIndeTaskMeeting(
  indeTaskMeetingId: number
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-meeting/delete`,
    data: {
      indeTaskMeetingId
    },
  });
}

/**
 * 获取独立任务会议详情
 */
export async function getIndeTaskMeetingDetail(
  indeTaskMeetingId: number
): Promise<IndeTaskMeetingResponse> {
  return send({
    method: 'GET',
    url: `/projectmanage/inde-task-meeting/${indeTaskMeetingId}`,
  });
} 