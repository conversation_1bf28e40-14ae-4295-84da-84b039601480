import { send } from '@/libs/request';
import type { PageResponse } from '../types';

// 问题相关类型定义
export interface IndeTaskIssueResponse {
  indeTaskIssueId?: number;
  indeTaskId?: number;
  indeTaskIssueName?: string;
  indeTaskIssueStatus?: number;
  indeTaskIssueType?: number | string;
  indeTaskIssueLevel?: number;
  closingTime?: string;
  completionDate?: string;
  indeTaskResponsible?: number;
  createTime?: string;
  updateTime?: string;
  createUser?: number;
  updateUser?: number;
  remark?: string;
  tenantId?: number;
  issueResponsibleName?: string;
  createUserName?: string;
  deliveryDocs?: any[];
  issueDocs?: any[];
}

export interface IndeTaskIssueAddRequest {
  /**
   * 截止时间
   */
  closingTime?: string;
  /**
   * 是否公开 (1是0否)
   */
  disCloseFlag?: number;
  /**
   * 独立任务ID
   */
  indeTaskId?: number;
  /**
   * 独立任务问题ID
   */
  indeTaskIssueId?: number;
  /**
   * 问题级别 (1高2中3低)
   */
  indeTaskIssueLevel: number;
  /**
   * 独立任务问题名称
   */
  indeTaskIssueName: string;
  /**
   * 问题分类
   */
  indeTaskIssueType: number | string;
  /**
   * 独立任务问题负责人ID
   */
  indeTaskResponsible?: number;
  /**
   * 文档列表
   */
  issueDocs?: IndeTaskIssueDocAddRequest[];
  /**
   * 公开回复 (1是0否)
   */
  publicReplyFlag?: number;
  /**
   * 备注
   */
  remark?: string;
}

/**
 * IndeTaskIssueDocAddRequest，独立任务问题,问题回答附件入参对象
 */
export interface IndeTaskIssueDocAddRequest {
  /**
   * 附件id(关联附件表)
   */
  docId?: number;
  /**
   * 附件唯一标识
   */
  docKey?: string;
  /**
   * 附件名
   */
  docName?: string;
  /**
   * 是否文件夹(0 否,1 是)
   */
  folderFlag: number;
  /**
   * 父文档ID,或文件夹ID
   */
  parentIndeTaskIssueDocId?: number;
}

export interface IndeTaskIssueEditRequest {
  indeTaskIssueId?: number;
  indeTaskId?: number;
  indeTaskIssueName?: string;
  indeTaskIssueStatus?: number;
  indeTaskIssueType?: number | string;
  indeTaskIssueLevel?: number;
  closingTime?: string;
  completionDate?: string;
  indeTaskResponsible?: number;
  remark?: string;
}

export interface IndeTaskIssueQueryRequest {
  indeTaskId?: number;
  indeTaskIssueName?: string;
  indeTaskIssueStatus?: number;
  indeTaskIssueType?: number;
  indeTaskIssueLevel?: number;
  indeTaskResponsible?: number;
  createUser?: number;
  startTime?: string;
  endTime?: string;
}

export interface IndeTaskIssueDeleteRequest {
  indeTaskIssueId: number;
}

export interface IndeTaskIssueAssignRequest {
  indeTaskIssueId: number;
  indeTaskResponsible: number;
}

/**
 * 获取独立任务问题列表（分页）
 */
export async function getIndeTaskIssuePage(
  pageNum: number,
  pageSize: number,
  queryRequest?: IndeTaskIssueQueryRequest
): Promise<PageResponse<IndeTaskIssueResponse>> {
  const response = await send({
    method: 'POST',
    url: `/projectmanage/inde-task-issue/page/${pageSize}/${pageNum}`,
    data: queryRequest || {},
  });
  
  return response;
}

/**
 * 获取独立任务问题列表
 */
export async function getIndeTaskIssueList(
  queryRequest?: IndeTaskIssueQueryRequest
): Promise<IndeTaskIssueResponse[]> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-issue/list`,
    data: queryRequest || {},
  });
}

/**
 * 添加独立任务问题
 */
export async function addIndeTaskIssue(
  data: IndeTaskIssueAddRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-issue/add`,
    data,
  });
}

/**
 * 编辑独立任务问题
 */
export async function editIndeTaskIssue(
  data: IndeTaskIssueEditRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-issue/edit`,
    data,
  });
}

/**
 * 删除独立任务问题
 */
export async function deleteIndeTaskIssue(
  data: IndeTaskIssueDeleteRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-issue/delete`,
    data,
  });
}

/**
 * 批量删除独立任务问题
 */
export async function batchDeleteIndeTaskIssue(
  data: IndeTaskIssueDeleteRequest[]
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-issue/batch/delete`,
    data,
  });
}

/**
 * 获取独立任务问题详情
 */
export async function getIndeTaskIssueDetail(
  indeTaskIssueId: number
): Promise<IndeTaskIssueResponse> {
  return send({
    method: 'GET',
    url: `/projectmanage/inde-task-issue/${indeTaskIssueId}`,
  });
}

/**
 * 关闭独立任务问题
 */
export async function closeIndeTaskIssue(
  data: IndeTaskIssueDeleteRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-issue/close`,
    data,
  });
}

/**
 * 转换问题为风险
 */
export async function convertIssueToRisk(
  data: IndeTaskIssueDeleteRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-issue/convert/risk`,
    data,
  });
}

/**
 * 重新指派独立任务问题
 */
export async function assignIndeTaskIssue(
  data: IndeTaskIssueAssignRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-issue/assign`,
    data,
  });
} 