import { send } from '@/libs/request';
import type { PageResponse } from '../types';

// 过程文档相关类型定义
export interface IndeTaskProcessDocResponse {
  indeTaskProcessDocId?: number;
  indeTaskId?: number;
  parentIndeTaskProcessDocId?: number;
  folderFlag?: number;
  docName?: string;
  docKey?: string;
  docId?: number;
  tenantId?: number;
  docSize?: number;
  docType?: string;
  createUserName?: string;
  updateUserName?: string;
}

export interface IndeTaskProcessDocAddRequest {
  parentIndeTaskProcessDocId?: number;
  folderFlag: number;
  docName: string;
  docKey: string;
  docId: number;
}

export interface IndeTaskProcessDocEditRequest {
  indeTaskProcessDocId?: number;
  indeTaskId?: number;
  parentIndeTaskProcessDocId?: number;
  folderFlag?: number;
  docName?: string;
  docKey?: string;
  docId?: number;
}

export interface IndeTaskProcessDocQueryRequest {
  indeTaskId?: number;
  parentIndeTaskProcessDocId?: number;
  folderFlag?: number;
  docName?: string;
}

export interface IndeTaskProcessDocDeleteRequest {
  indeTaskProcessDocId: number;
}

/**
 * 获取独立任务过程文档列表（分页）
 */
export async function getIndeTaskProcessDocPage(
  pageNum: number,
  pageSize: number,
  queryRequest?: IndeTaskProcessDocQueryRequest
): Promise<PageResponse<IndeTaskProcessDocResponse>> {
  const response = await send({
    method: 'POST',
    url: `/projectmanage/inde-task-process-doc/page/${pageSize}/${pageNum}`,
    data: queryRequest || {},
  });

  return response;
}

/**
 * 获取独立任务过程文档列表
 */
export async function getIndeTaskProcessDocList(
  queryRequest?: IndeTaskProcessDocQueryRequest
): Promise<IndeTaskProcessDocResponse[]> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-process-doc/list`,
    data: queryRequest || {},
  });
}

/**
 * 添加独立任务过程文档
 */
export async function addIndeTaskProcessDoc(
  taskId: number | string,
  docData: IndeTaskProcessDocAddRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-process-doc/add/${taskId}`,
    data: {
      parentIndeTaskProcessDocId: docData.parentIndeTaskProcessDocId,
      folderFlag: docData.folderFlag,
      docName: docData.docName,
      docKey: docData.docKey,
      docId: docData.docId,
    },
  });
}

/**
 * 编辑独立任务过程文档
 */
export async function editIndeTaskProcessDoc(
  data: IndeTaskProcessDocEditRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-process-doc/edit`,
    data,
  });
}

/**
 * 删除独立任务过程文档
 */
export async function deleteIndeTaskProcessDoc(
  data: IndeTaskProcessDocDeleteRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-process-doc/delete`,
    data,
  });
}

/**
 * 批量删除独立任务过程文档
 */
export async function batchDeleteIndeTaskProcessDoc(
  data: IndeTaskProcessDocDeleteRequest[]
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-process-doc/batch/delete`,
    data,
  });
}

/**
 * 获取独立任务过程文档详情
 */
export async function getIndeTaskProcessDocDetail(
  indeTaskProcessDocId: number
): Promise<IndeTaskProcessDocResponse> {
  return send({
    method: 'GET',
    url: `/projectmanage/inde-task-process-doc/${indeTaskProcessDocId}`,
  });
}

/**
 * 过程文档转交付文档
 */
export async function toDeliveryDoc(data: {
  indeTaskProcessDocId: number;
}): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-process-doc/to/delivery/doc`,
    data,
  });
}
