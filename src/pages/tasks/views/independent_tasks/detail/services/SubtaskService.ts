import { send } from '@/libs/request';
import type { PageResponse } from '../types';

// 子任务响应类型定义
export interface SubtaskResponse {
  indeTaskId?: number;
  parentIndeTaskId?: number;
  indeTaskName?: string;
  indeTaskStatus?: number;
  indeTaskResponsible?: number;
  indeTaskResponsibleDept?: number;
  indeTaskType?: number;
  planProgress?: number;
  actualProgress?: number;
  overdueDays?: number;
  indeTaskAcceptCriteria?: string;
  planStartDate?: string;
  actualStartDate?: string;
  planFinishDate?: string;
  duration?: number;
  workflowId?: string;
  autoProgressFlag?: number;
  actualFinishDate?: string;
  indeTaskDesc?: string;
  assigneer?: number;
  assigneeTime?: string;
  createTime?: string;
  updateTime?: string;
  createUser?: number;
  updateUser?: number;
  remark?: string;
  tenantId?: number;
  responsibleName?: string;
}

// 子任务添加请求类型
export interface SubtaskAddRequest {
  tenantId?: number;
  parentIndeTaskId?: number;
  indeTaskName: string;
  indeTaskResponsible: number;
  indeTaskResponsibleDept?: number;
  indeTaskType?: number;
  planStartDate?: string;
  planFinishDate?: string;
  workflowId?: string;
  autoProgressFlag?: number;
  indeTaskDesc?: string;
}

// 子任务编辑请求类型
export interface SubtaskEditRequest extends SubtaskAddRequest {
  indeTaskId?: number;
}

// 子任务查询请求类型
export interface SubtaskQueryRequest {
  parentIndeTaskId?: number;
  indeTaskName?: string;
  indeTaskStatus?: number;
  indeTaskResponsible?: number;
  indeTaskType?: number;
  startTime?: string;
  endTime?: string;
}

// 子任务删除请求类型
export interface SubtaskDeleteRequest {
  indeTaskId: number;
}

/**
 * 获取子任务列表（分页）
 */
export async function getSubtaskPage(
  pageNum: number,
  pageSize: number,
  queryRequest?: SubtaskQueryRequest
): Promise<PageResponse<SubtaskResponse>> {
  const response = await send({
    method: 'POST',
    url: `/projectmanage/inde-task/page/${pageSize}/${pageNum}`,
    data: queryRequest || {},
  });
  
  return response;
}

/**
 * 获取子任务列表
 */
export async function getSubtaskList(
  queryRequest?: SubtaskQueryRequest
): Promise<SubtaskResponse[]> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task/list`,
    data: queryRequest || {},
  });
}

/**
 * 添加子任务
 */
export async function addSubtask(
  data: SubtaskAddRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task/add`,
    data,
  });
}

/**
 * 编辑子任务
 */
export async function editSubtask(
  data: SubtaskEditRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task/edit`,
    data,
  });
}

/**
 * 删除子任务
 */
export async function deleteSubtask(
  data: SubtaskDeleteRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task/delete`,
    data,
  });
}

/**
 * 批量删除子任务
 */
export async function batchDeleteSubtask(
  data: SubtaskDeleteRequest[]
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task/batch/delete`,
    data,
  });
}

/**
 * 获取子任务详情
 */
export async function getSubtaskDetail(
  indeTaskId: number
): Promise<SubtaskResponse> {
  return send({
    method: 'GET',
    url: `/projectmanage/inde-task/${indeTaskId}`,
  });
} 