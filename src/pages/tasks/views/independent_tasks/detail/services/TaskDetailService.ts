import { send } from '@/libs/request';
import {
  acceptIndependentTask,
  rejectIndependentTask,
  addIndependentTaskTag,
  removeIndependentTaskTag,
  taskTags,
  workflowDefinitionList,
  changeIndependentTask,
} from '@/service_url/tasks';
import type { TaskTag } from '../types';

/**
 * 接受任务请求
 */
export interface IndeTaskAcceptRequest {
  /** 任务ID */
  indeTaskId: number;
  /** 接受意见 */
  acceptOpinion: string;
  /** 过程文档列表 */
  processDocs: Array<{
    docId: number;
    docName: string;
    docSize: number;
    docType: string;
    docUrl?: string;
  }>;
}

/**
 * 驳回任务请求
 */
export interface IndeTaskRejectRequest {
  /** 任务ID */
  indeTaskId: number;
  /** 驳回意见 */
  rejectOpinion: string;
  /** 过程文档列表 */
  processDocs: Array<{
    docId: number;
    docName: string;
    docSize: number;
    docType: string;
    docUrl?: string;
  }>;
}

/**
 * 工作流定义响应
 */
export interface WorkflowDefinitionResponse {
  /** 流程定义ID */
  id: string;
  /** 流程定义key */
  key: string;
  /** 流程定义名称 */
  name: string;
  /** 版本 */
  version: number;
  /** 部署ID */
  deploymentId: string;
  /** 是否挂起 */
  suspended: boolean;
  /** 租户ID */
  tenantId?: number;
  /** 描述 */
  description?: string;
}

/**
 * 接受独立任务
 * @param data 接受任务参数
 * @returns 是否成功
 */
export async function acceptIndeTask(
  data: IndeTaskAcceptRequest
): Promise<{ success: boolean }> {
  try {
    const response = await send({
      method: 'POST',
      url: acceptIndependentTask,
      data,
    });
    return {
      success: response?.code === 200,
    };
  } catch (error) {
    console.error('接受任务失败:', error);
    throw error;
  }
}

/**
 * 驳回独立任务
 * @param data 驳回任务参数
 * @returns 是否成功
 */
export async function rejectIndeTask(
  data: IndeTaskRejectRequest
): Promise<{ success: boolean }> {
  try {
    const response = await send({
      method: 'POST',
      url: rejectIndependentTask,
      data,
    });
    return {
      success: response?.code === 200,
    };
  } catch (error) {
    console.error('驳回任务失败:', error);
    throw error;
  }
}

/**
 * 获取任务标签列表
 * @param params 筛选参数
 * @returns 标签列表
 */
export async function getTaskTagList(params?: {
  taskTagStatus?: number;
}): Promise<TaskTag[]> {
  try {
    const response = await send({
      method: 'POST',
      url: taskTags,
      data: params || {},
    });

    return response || [];
  } catch (error) {
    console.error('获取任务标签列表失败:', error);
    return [];
  }
}

/**
 * 获取工作流定义列表
 * @param tenantId 租户ID
 * @param latest 是否只获取最新版本
 * @returns 工作流定义列表
 */
export async function getWorkflowDefinitions(
  tenantId?: number,
  latest: boolean = true
): Promise<WorkflowDefinitionResponse[]> {
  try {
    const response = await send({
      method: 'POST',
      url: workflowDefinitionList,
      data: {
        tenantId,
        latest,
      },
    });

    return response?.data || [];
  } catch (error) {
    console.error('获取工作流定义列表失败:', error);
    return [];
  }
}

/**
 * 添加标签到独立任务
 * @param taskId 任务ID
 * @param tags 要添加的标签列表
 * @returns 是否成功
 */
export async function addTagToIndependentTask(
  taskId: number,
  tags: Array<{ taskTagId: number }>
) {
  const response = await send({
    method: 'POST',
    url: addIndependentTaskTag,
    data: {
      indeTaskId: taskId,
      tags,
    },
  });

  return {
    success: !!response,
  };
}

/**
 * 从独立任务中移除标签
 * @param indeTaskId 独立任务ID
 * @param taskTagId 标签ID
 * @returns 是否成功
 */
export async function removeTagFromIndependentTask(
  indeTaskId: number,
  taskTagId: number
) {
  const response = await send({
    method: 'POST',
    url: removeIndependentTaskTag,
    data: {
      taskTagId,
      indeTaskId,
    },
  });

  return {
    success: !!response,
  };
}

/**
 * 返回独立任务列表页
 * 注: 这不是API调用，只是前端路由跳转的辅助函数
 */
export function backToTaskList() {
  return {
    path: '/tasks/independent/list',
  };
}

/**
 * 独立任务变更请求参数
 */
export interface IndeTaskChangeRequest {
  tenantId?: number;
  indeTaskId: number;
  indeTaskName: string;
  indeTaskResponsible: number;
  planStartDate?: string;
  planFinishDate?: string;
  duration?: number;
  autoProgressFlag?: number;
  workflowId?: string;
  participants?: Array<{
    userId: number;
    tenantId?: number;
  }>;
  changeReason?: string;
}

/**
 * 提交独立任务变更
 * @param data 任务变更参数
 * @returns 是否成功
 */
export async function submitIndeTaskChange(
  data: IndeTaskChangeRequest
): Promise<{ success: boolean }> {
  try {
    const response = await send({
      method: 'POST',
      url: changeIndependentTask,
      data,
    });
    return {
      success: response?.code === 200,
    };
  } catch (error) {
    console.error('提交任务变更失败:', error);
    throw error;
  }
}
