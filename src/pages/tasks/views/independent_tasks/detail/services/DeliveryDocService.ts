import { send } from '@/libs/request';
import type { PageResponse } from '../types';

// 交付文档相关类型定义
export interface IndeTaskDeliveryDocResponse {
  indeTaskDeliveryDocId?: number;
  indeTaskId?: number;
  parentIndeTaskDeliveryDocId?: number;
  folderFlag?: number;
  docName?: string;
  docKey?: string;
  docId?: number;
  tenantId?: number;
  docSize?: number;
  docType?: string;
  createUserName?: string;
  updateUserName?: string;
  createTime?: string;
  updateTime?: string;
  createUser?: number;
  updateUser?: number;
}

export interface IndeTaskDeliveryDocAddRequest {
  indeTaskId?: number;
  parentIndeTaskDeliveryDocId?: number;
  folderFlag?: number;
  docName?: string;
  docKey?: string;
  docId?: number;
  docType?: string; // 添加文档类型字段
}

export interface IndeTaskDeliveryDocEditRequest {
  indeTaskDeliveryDocId?: number;
  indeTaskId?: number;
  parentIndeTaskDeliveryDocId?: number;
  folderFlag?: number;
  docName?: string;
  docKey?: string;
  docId?: number;
}

export interface IndeTaskDeliveryDocQueryRequest {
  indeTaskId?: number;
  parentIndeTaskDeliveryDocId?: number;
  folderFlag?: number;
  docName?: string;
}

export interface IndeTaskDeliveryDocDeleteRequest {
  indeTaskDeliveryDocId: number;
}

/**
 * 获取独立任务交付文档列表（分页）
 */
export async function getIndeTaskDeliveryDocPage(
  pageNum: number,
  pageSize: number,
  queryRequest?: IndeTaskDeliveryDocQueryRequest
): Promise<PageResponse<IndeTaskDeliveryDocResponse>> {
  const response = await send({
    method: 'POST',
    url: `/projectmanage/inde-task-delivery-doc/page/${pageSize}/${pageNum}`,
    data: queryRequest || {},
  });

  return response;
}

/**
 * 获取独立任务交付文档列表
 */
export async function getIndeTaskDeliveryDocList(
  queryRequest?: IndeTaskDeliveryDocQueryRequest
): Promise<IndeTaskDeliveryDocResponse[]> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-delivery-doc/list`,
    data: queryRequest || {},
  });
}

/**
 * 添加独立任务交付文档
 */
export async function addIndeTaskDeliveryDoc(
  taskId: number | string,
  data: IndeTaskDeliveryDocAddRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-delivery-doc/add/${taskId}`,
    data,
  });
}

/**
 * 编辑独立任务交付文档
 */
export async function editIndeTaskDeliveryDoc(
  data: IndeTaskDeliveryDocEditRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-delivery-doc/edit`,
    data,
  });
}

/**
 * 删除独立任务交付文档
 */
export async function deleteIndeTaskDeliveryDoc(
  data: IndeTaskDeliveryDocDeleteRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-delivery-doc/delete`,
    data,
  });
}

/**
 * 批量删除独立任务交付文档
 */
export async function batchDeleteIndeTaskDeliveryDoc(
  data: IndeTaskDeliveryDocDeleteRequest[]
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-delivery-doc/batch/delete`,
    data,
  });
}

/**
 * 获取独立任务交付文档详情
 */
export async function getIndeTaskDeliveryDocDetail(
  indeTaskDeliveryDocId: number
): Promise<IndeTaskDeliveryDocResponse> {
  return send({
    method: 'GET',
    url: `/projectmanage/inde-task-delivery-doc/${indeTaskDeliveryDocId}`,
  });
}

/**
 * 交付文档转过程文档
 */
export async function toProcessDoc(data: {
  indeTaskDeliveryDocId: number;
}): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-delivery-doc/to/process/doc`,
    data,
  });
}
