import { send } from '@/libs/request';

/**
 * IndeTaskReportResponse，独立任务汇报
 */
export interface IndeTaskReportResponse {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建者ID
   */
  createUser?: number;
  /**
   * 数据类型(report,record)
   */
  dataType?: string;
  /**
   * 汇报内容
   */
  indeTaskDesc?: string;
  /**
   * 独立任务ID
   */
  indeTaskId?: number;
  /**
   * 独立任务汇报状态(2 已接受,0 待澄清,1 已汇报)
   */
  indeTaskReportStatus?: number;
  /**
   * 独立任务进度，范围0.00到100.00
   */
  progress?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 文档列表
   */
  reportDocs?: IndeTaskReportDocResponse[];
  /**
   * 汇报ID
   */
  reportId?: number;
  /**
   * 汇报时间
   */
  reportTime?: string;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 更新者ID
   */
  updateUser?: number;
  /**
   * 独立任务汇报工作流定义ID
   */
  workflowId?: string;
}

/**
 * IndeTaskReportDocResponse，独立任务进度汇报附件
 */
export interface IndeTaskReportDocResponse {
  /**
   * 附件id(关联附件表)
   */
  docId?: number;
  /**
   * 附件唯一标识
   */
  docKey?: string;
  /**
   * 附件名
   */
  docName?: string;
  /**
   * 是否文件夹(0 否,1 是)
   */
  folderFlag?: number;
  /**
   * 独立任务ID
   */
  indeTaskId?: number;
  /**
   * 自增主键
   */
  indeTaskReportDocId?: number;
  /**
   * 父文档ID,或文件夹ID
   */
  parentIndeTaskReportDocId?: number;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 文档类型
   */
  docType?: string;
}

/**
 * IndeTaskReportEditRequest，独立任务汇报
 */
export interface IndeTaskReportEditRequest {
  /**
   * 数据类型(report,record)
   */
  dataType?: string;
  /**
   * 汇报内容
   */
  indeTaskDesc?: string;
  /**
   * 独立任务ID
   */
  indeTaskId?: number;
  /**
   * 独立任务汇报状态(2 已接受,0 待澄清,1 已汇报)
   */
  indeTaskReportStatus?: number;
  /**
   * 独立任务进度，范围0.00到100.00
   */
  progress?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 文档列表
   */
  reportDocs?: IndeTaskReportDocEditRequest[];
  /**
   * 汇报ID
   */
  reportId?: number;
  /**
   * 汇报时间
   */
  reportTime?: string;
  /**
   * 独立任务汇报工作流定义ID
   */
  workflowId?: string;
  [property: string]: any;
}

/**
 * IndeTaskReportDocEditRequest，独立任务进度汇报附件
 */
export interface IndeTaskReportDocEditRequest {
  /**
   * 附件id(关联附件表)
   */
  docId?: number;
  /**
   * 附件唯一标识
   */
  docKey?: string;
  /**
   * 附件名
   */
  docName?: string;
  /**
   * 是否文件夹(0 否,1 是)
   */
  folderFlag?: number;
  /**
   * 独立任务ID
   */
  indeTaskId?: number;
  /**
   * 自增主键
   */
  indeTaskReportDocId?: number;
  /**
   * 父文档ID,或文件夹ID
   */
  parentIndeTaskReportDocId?: number;
}

export interface IndeTaskReportQueryRequest {
  indeTaskId?: number | string;
  indeTaskReportStatus?: number;
  startTime?: string;
  endTime?: string;
  createUser?: number;
}

export interface IndeTaskReportDeleteRequest {
  reportId: number;
}

/**
 * 获取独立任务汇报列表（分页）
 */
export async function getIndeTaskReportPage(
  queryRequest?: IndeTaskReportQueryRequest
): Promise<IndeTaskReportResponse[]> {
  const response = await send({
    method: 'POST',
    url: `/projectmanage/inde-task-report/list`,
    data: queryRequest || {},
  });

  if (response && Array.isArray(response)) {
    return response;
  }
  return [];
}

/**
 * 获取独立任务汇报列表
 */
export async function getIndeTaskReportList(
  queryRequest?: IndeTaskReportQueryRequest
): Promise<IndeTaskReportResponse[]> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-report/list`,
    data: queryRequest || {},
  });
}

/**
 * 添加独立任务汇报
 */
export async function addIndeTaskReport(
  data: IndeTaskReportEditRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-report/add`,
    data,
  });
}

/**
 * 编辑独立任务汇报
 */
export async function editIndeTaskReport(
  data: IndeTaskReportEditRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-report/edit`,
    data,
  });
}

/**
 * 删除独立任务汇报
 */
export async function deleteIndeTaskReport(
  reportId: number | string
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-report/delete`,
    data: {
      reportId,
    },
  });
}

/**
 * 批量删除独立任务汇报
 */
export async function batchDeleteIndeTaskReport(
  data: IndeTaskReportDeleteRequest[]
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/inde-task-report/batch/delete`,
    data,
  });
}

/**
 * 获取独立任务汇报详情
 */
export async function getIndeTaskReportDetail(
  reportId: number
): Promise<IndeTaskReportResponse> {
  return send({
    method: 'GET',
    url: `/projectmanage/inde-task-report/${reportId}`,
  });
}
