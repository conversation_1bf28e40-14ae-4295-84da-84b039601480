import { send } from '@/libs/request';
import type { PageResponse } from '../types';

// 评审记录相关类型定义
export interface ApprovalResponse {
  id?: number;
  taskId?: number;
  taskType?: string; // 'independent' | 'project'
  approvalType?: string;
  approvalStatus?: number;
  submitter?: string;
  submitTime?: string;
  reviewer?: string;
  reviewTime?: string;
  reviewComment?: string;
  approvalResult?: number; // 1: 通过, 2: 驳回, 3: 待审核
  workflowInstanceId?: string;
  createTime?: string;
  updateTime?: string;
  createUser?: number;
  updateUser?: number;
  remark?: string;
  tenantId?: number;
  attachments?: any[];
}

export interface ApprovalAddRequest {
  taskId?: number;
  taskType?: string;
  approvalType?: string;
  submitter?: string;
  submitTime?: string;
  workflowInstanceId?: string;
  remark?: string;
  attachmentIds?: number[];
  reportId: number;
}

export interface ApprovalEditRequest {
  id?: number;
  taskId?: number;
  taskType?: string;
  approvalType?: string;
  approvalStatus?: number;
  reviewer?: string;
  reviewTime?: string;
  reviewComment?: string;
  approvalResult?: number;
  workflowInstanceId?: string;
  remark?: string;
}

export interface ApprovalQueryRequest {
  taskId?: number;
  taskType?: string;
  approvalType?: string;
  approvalStatus?: number;
  submitter?: string;
  reviewer?: string;
  approvalResult?: number;
  startTime?: string;
  endTime?: string;
}

export interface ApprovalDeleteRequest {
  id: number;
}

/**
 * 获取评审记录列表（分页）
 */
export async function getApprovalPage(
  pageNum: number,
  pageSize: number,
  queryRequest?: ApprovalQueryRequest
): Promise<PageResponse<ApprovalResponse>> {
  const response = await send({
    method: 'POST',
    url: `/projectmanage/approval/page/${pageSize}/${pageNum}`,
    data: queryRequest || {},
  });

  return response;
}

/**
 * 获取评审记录列表
 */
export async function getApprovalList(
  queryRequest?: ApprovalQueryRequest
): Promise<ApprovalResponse[]> {
  return send({
    method: 'POST',
    url: `/projectmanage/approval/list`,
    data: queryRequest || {},
  });
}

/**
 * 添加评审记录
 */
export async function addApproval(data: ApprovalAddRequest): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/approval/add`,
    data,
  });
}

/**
 * 编辑评审记录
 */
export async function editApproval(data: ApprovalEditRequest): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/approval/edit`,
    data,
  });
}

/**
 * 删除评审记录
 */
export async function deleteApproval(
  data: ApprovalDeleteRequest
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/approval/delete`,
    data,
  });
}

/**
 * 批量删除评审记录
 */
export async function batchDeleteApproval(
  data: ApprovalDeleteRequest[]
): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/approval/batch/delete`,
    data,
  });
}

/**
 * 获取评审记录详情
 */
export async function getApprovalDetail(id: number): Promise<ApprovalResponse> {
  return send({
    method: 'GET',
    url: `/projectmanage/approval/${id}`,
  });
}

/**
 * 提交评审
 */
export async function submitApproval(data: {
  reportId: number;
  indeTaskId: number;
}): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/approval/submit`,
    data,
  });
}

/**
 * 审批评审
 */
export async function reviewApproval(data: ApprovalEditRequest): Promise<any> {
  return send({
    method: 'POST',
    url: `/projectmanage/approval/review`,
    data,
  });
}
