export interface TaskItem {
  /**
   * 实际完成日期
   */
  actualFinishDate?: string;
  /**
   * 实际进度
   */
  actualProgress?: number;
  /**
   * 实际开始日期
   */
  actualStartDate?: string;
  /**
   * 指派人
   */
  assigneer?: number;
  /**
   * 指派时间
   */
  assigneeTime?: string;
  /**
   * 附件列表
   */
  attaments?: SysAttachmentResponse[];
  /**
   * 计算的关键任务标识(1是0 否)
   */
  automaticCriticalTaskFlag?: number;
  /**
   * 是否自动进度(1 是 0 否)
   */
  autoProgressFlag?: number;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建者ID
   */
  createUser?: number;
  /**
   * 手动标注的关键任务标识(1是0 否)
   */
  criticalTaskFlag?: number;
  /**
   * 工期
   */
  duration?: number;
  /**
   * 专业
   */
  major?: number;
  /**
   * 超期
   */
  overdueDays?: number;
  /**
   * 项目任务父任务ID
   */
  parentProjTaskId?: number;
  /**
   * 参与人列表
   */
  participants?: ParticipantResponse[];
  /**
   * 是非阶段(1是,0否)
   */
  phaseFlag?: number;
  /**
   * 阶段ID
   */
  phaseProjTaskId?: number;
  /**
   * 计划完成日期
   */
  planFinishDate?: string;
  /**
   * 计划ID
   */
  planId?: number;
  /**
   * 计划进度
   */
  planProgress?: number;
  /**
   * 计划开始日期
   */
  planStartDate?: string;
  /**
   * 前置任务列表
   */
  predTasks?: ProjectTaskPredResponse[];
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 所属项目ID
   */
  projId?: number;
  /**
   * 项目任务验收标准
   */
  projTaskAcceptCriteria?: string;
  /**
   * 项目任务说明
   */
  projTaskDesc?: string;
  /**
   * 项目任务ID
   */
  projTaskId?: string;
  /**
   * 项目任务名称
   */
  projTaskName?: string;
  /**
   * 项目任务责任人
   */
  projTaskResponsible?: number;
  /**
   * 项目任务责任部门
   */
  projTaskResponsibleDept?: number;
  /**
   * 项目任务责任人项目角色
   */
  projTaskResponsibleProjRole?: string;
  /**
   * 项目任务状态(1 未开始,2 进行中,3 已关闭)
   */
  projTaskStatus?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 责任人姓名
   */
  responsibleName?: string;
  /**
   * 系统
   */
  systemId?: number;
  /**
   * 任务标签列表
   */
  tags?: ConfigProjectTaskTagResponse[];
  /**
   * 任务作业类型(级联关系待定)
   */
  taskActivityType?: number;
  /**
   * 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业,级联关系待定)
   */
  taskType?: number;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 更新时间
   */
  updateTime?: Date;
  /**
   * 更新者ID
   */
  updateUser?: number;
  /**
   * 项目任务绑定的工作流定义ID
   */
  workflowId?: string;
}

/**
 * SysAttachmentResponse，文件
 */
export interface SysAttachmentResponse {
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 创建人
   */
  createUser?: number;
  /**
   * 创建人姓名
   */
  createUserName?: string;
  /**
   * 附件id
   */
  docId?: number;
  /**
   * 附件标识
   */
  docKey?: string;
  /**
   * 附件名
   */
  docName?: string;
  /**
   * 附件地址
   */
  docPath?: string;
  /**
   * 大小
   */
  docSize?: number;
  /**
   * 附件格式
   */
  docType?: string;
  /**
   * 请求id
   */
  requestId?: string;
  /**
   * 租户id
   */
  tenantId?: number;
  /**
   * 最后修改时间
   */
  updateTime?: Date;
  /**
   * 最后修改人
   */
  updateUser?: number;
  /**
   * 修改人姓名
   */
  updateUserName?: string;
  [property: string]: any;
}

/**
 * ParticipantResponse，参与人响应对象
 */
export interface ParticipantResponse {
  /**
   * 参与人ID
   */
  userId?: number;
  /**
   * 参与人姓名
   */
  userName?: string;
  [property: string]: any;
}

/**
 * ProjectTaskPredResponse，项目任务前置
 */
export interface ProjectTaskPredResponse {
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 创建人
   */
  createUser?: number;
  /**
   * 是否强制约束(0 否 1 是)
   */
  isForce?: number;
  /**
   * 延迟小时数
   */
  lagHrCnt?: number;
  /**
   * 前缀任务所属的计划ID
   */
  predPlanId?: number;
  /**
   * 前缀任务所属的项目ID
   */
  predProjId?: number;
  /**
   * 前缀项目任务ID
   */
  predProjTaskId?: number;
  /**
   * 前置类型(4中关系:SF,FF,FS,SS)
   */
  predType?: number;
  /**
   * 项目ID
   */
  projId?: number;
  /**
   * 项目任务id
   */
  projTaskId?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 主键
   */
  taskPredId?: number;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 修改时间
   */
  updateTime?: Date;
  /**
   * 修改人
   */
  updateUser?: number;
  [property: string]: any;
}

/**
 * ConfigProjectTaskTagResponse，项目任务标签
 */
export interface ConfigProjectTaskTagResponse {
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 创建者ID
   */
  createUser?: number;
  /**
   * 父项目任务标签ID或者所属标签组ID
   */
  parentProjTaskTagId?: number;
  /**
   * 项目标签状态 (1 启用,0 禁用)
   */
  projTagStatus?: number;
  /**
   * 项目标签颜色
   */
  projTaskTagColor?: string;
  /**
   * 项目标签描述
   */
  projTaskTagDesc?: string;
  /**
   * 项目任务标签ID
   */
  projTaskTagId?: number;
  /**
   * 项目标签标题
   */
  projTaskTagName?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 排序码
   */
  sortNum?: number;
  /**
   * 是否是标签组(1 是,0 否)
   */
  tagGroupFlag?: number;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 更新时间
   */
  updateTime?: Date;
  /**
   * 更新者ID
   */
  updateUser?: number;
  [property: string]: any;
}

/**
 * 排序参数接口
 */
export interface SortParam {
  /** 排序字段名 */
  field: string;
  /** 排序方向：'asc' 或 'desc' */
  direction: string;
}

/**
 * 列头筛选条件接口
 */
export interface FilterConditions {
  /** 项目ID筛选 */
  projId?: string;
  /** 项目名称筛选 */
  projectName?: string;
  /** 任务ID筛选 */
  projTaskId?: string;
  /** 任务名称筛选 */
  projTaskName?: string;
  /** 状态筛选 */
  projTaskStatus?: number[];
  /** 责任部门筛选 */
  projTaskResponsibleDept?: string;
  /** 项目角色筛选 */
  projTaskResponsibleProjRole?: string;
  /** 责任人筛选 */
  responsibleName?: string;
  /** 任务类型筛选 */
  taskType?: number[];
  /** 专业筛选 */
  major?: number[];
  /** 计划开始日期范围筛选 */
  beginPlanStartDate?: string;
  endPlanStartDate?: string;
  /** 实际开始日期范围筛选 */
  beginActualStartDate?: string;
  endActualStartDate?: string;
  /** 计划完成日期范围筛选 */
  beginPlanFinishDate?: string;
  endPlanFinishDate?: string;
  /** 超期筛选 */
  overdueDays?: number[];
  /** 实际进度筛选 */
  actualProgress?: string;
}

/**
 * 筛选参数接口
 * 用于我的任务列表的筛选条件
 */
export interface FilterParams {
  /** 关键词，可选，用于模糊搜索任务名称 */
  keyword?: string;
  /** 列头筛选条件 */
  filters?: FilterConditions;
  /** 排序参数 */
  sorts?: SortParam[];
}

/**
 * 我的任务列表组件属性
 * 定义我的任务列表组件的输入属性
 */
export interface MyTaskListProps {
  /** 任务列表数据 */
  tasks: TaskItem[];
  /** 加载状态，是否正在加载数据 */
  loading: boolean;
}

/**
 * 任务状态枚举
 * 定义流程任务的所有可能状态
 */
export enum TaskStatus {
  /** 待接受状态，任务已分配但尚未被接受 */
  PENDING_ACCEPT = '待接受',
  /** 已驳回状态，任务被用户驳回 */
  REJECTED = '已驳回',
  /** 未开始状态，任务已接受但尚未开始执行 */
  NOT_STARTED = '未开始',
  /** 进行中状态，任务正在执行中 */
  IN_PROGRESS = '进行中',
  /** 已完成状态，任务已完成 */
  COMPLETED = '已完成',
  /** 已关闭状态，任务已关闭 */
  CLOSED = '已关闭',
}

/**
 * 任务操作类型枚举
 * 定义对任务可执行的操作类型
 */
export enum TaskAction {
  /** 查看操作 */
  VIEW = 'view',
  /** 接受操作 */
  ACCEPT = 'accept',
  /** 驳回操作 */
  REJECT = 'reject',
  /** 删除操作 */
  DELETE = 'delete',
  /** 编辑操作 */
  EDIT = 'edit',
}

/**
 * 甘特图任务项接口
 * 用于甘特图组件的任务数据结构
 */
export interface GanttTaskItem {
  /** 任务ID，唯一标识符 */
  id: string | number;
  /** 任务文本，显示在甘特图中的任务名称 */
  text: string;
  /** 开始日期，YYYY-MM-DD 格式 */
  start_date: string;
  /** 结束日期，YYYY-MM-DD 格式 */
  end_date: string;
  /** 进度，百分比（0-1） */
  progress: number;
  /** 父任务ID，用于表示任务层级关系 */
  parent?: number;
  /** 任务类型，如：task、project、milestone */
  type: string;
  /** 是否展开，用于控制子任务的显示 */
  open: boolean;
  /** 任务状态，可选 */
  status?: string;
  /** 负责人，可选 */
  responsible_user?: string;
  /** 任务类型，可选 */
  task_type?: string;
  /** 实际开始日期，可选，YYYY-MM-DD 格式 */
  actual_start_date?: string;
  /** 进度显示文本，可选，用于甘特图进度列显示 */
  progress_display?: string;
}

/**
 * 列配置接口
 * 用于表格列的显示配置
 */
export interface ColumnConfig {
  /** 列名称，对应数据字段 */
  name: string;
  /** 列标题，显示在表头 */
  label: string;
  /** 是否选中显示，用于列的显示/隐藏控制 */
  checked: boolean;
  /** 列宽度，可选 */
  width?: number;
  /** 对齐方式，可选 */
  align?: 'left' | 'center' | 'right';
  /** 是否固定列，可选 */
  fixed?: boolean | 'left' | 'right';
}

/**
 * 我的任务查询参数接口 - 基于 Swagger 流程任务查询
 * 用于查询当前用户的流程任务列表
 */
export interface GetMyTasksParams {
  /** 关键词，可选，用于模糊搜索任务名称或描述 */
  keyword?: string;
  tenantId?: number;
}

/**
 * 我的任务响应数据接口 - 基于 Swagger ApiResult 结构
 * 分页查询我的任务列表的响应数据
 */
export interface GetMyTasksResponse {
  /** 任务列表，当前页的任务数据 */
  list: TaskItem[];
  /** 总记录数，符合查询条件的任务总数 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页大小 */
  pageSize: number;
  /** 是否还有更多数据，用于判断是否可以继续加载 */
  hasMore: boolean;
}

/**
 * API 响应基础类型 - 基于 Swagger ApiResult
 * 统一的 API 响应格式，所有接口都遵循此结构
 */
export interface ApiResult<T = any> {
  /** 响应状态码，200表示成功 */
  code: number;
  /** 响应消息，描述请求结果 */
  message: string;
  /** 响应时间戳，ISO 8601 格式 */
  timestamp: string;
  /** 响应数据，泛型类型，根据具体接口而定 */
  data: T;
}

/**
 * 流程任务请求 - 基于 Swagger TaskRequest
 * 用于流程任务相关操作的请求参数
 */
export interface TaskRequest {
  /** 租户ID，可选，用于多租户环境下的数据隔离 */
  tenantId?: number;
  /** 任务ID，可选，流程任务的唯一标识 */
  taskId?: string;
  /** 流程实例ID，可选，标识任务所属的流程实例 */
  processInstanceId?: string;
  /** 执行ID，可选，流程执行的标识 */
  executionId?: string;
  /** 业务键，可选，关联的业务对象标识 */
  businessKey?: string;
  /** 流程名称，可选，任务所属的流程定义名称 */
  processName?: string;
  /** 任务名称，可选，流程中的任务节点名称 */
  taskName?: string;
  /** 发起人，可选，流程的发起用户 */
  starter?: string;
  /** 指派人，可选，任务的当前处理人 */
  assignee?: string;
  /** 开始时间，可选，任务的开始时间，ISO 8601 格式 */
  startTime?: string;
  /** 结束时间，可选，任务的结束时间，ISO 8601 格式 */
  endTime?: string;
  /** 创建时间，可选，任务的创建时间，ISO 8601 格式 */
  createTime?: string;
  /** 表单键，可选，关联的表单标识 */
  formKey?: string;
  /** 备注，可选，任务的备注信息 */
  comment?: string;
}

/**
 * 接受任务参数接口
 * 用于接受分配给用户的任务
 */
export interface AcceptTaskParams {
  /** 任务ID，唯一标识要接受的任务 */
  taskId: string | number;
  /** 接受注释，用户提供的关于任务接受的说明 */
  acceptComment: string;
  /** 附件列表，可选，接受任务时可以上传的附件 */
  attachments?: any[];
}

/**
 * 驳回任务参数接口
 * 用于驳回分配给用户的任务
 */
export interface RejectTaskParams {
  /** 任务ID，唯一标识要驳回的任务 */
  taskId: string | number;
  /** 驳回原因，用户提供的驳回理由分类 */
  rejectReason: string;
  /** 驳回注释，用户提供的详细驳回说明 */
  rejectComment: string;
}

/**
 * 删除任务参数接口
 * 用于删除任务
 */
export interface DeleteTaskParams {
  /** 任务ID列表，要删除的任务ID数组 */
  taskIds: (string | number)[];
}
