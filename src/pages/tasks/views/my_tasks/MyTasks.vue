<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { tableConfig } from '@/app-config.ts';
import { useMyTasksStore } from './MyTasksStore';
import type { TaskItem } from './types';
import {
  MoreFilled,
  Setting,
  FullScreen,
  Loading,
} from '@element-plus/icons-vue';
import BtTab from '@/components/non_biz/bt_tab/BtTab.vue';
import BtColumn from '@/components/non_biz/bt_column/BtColumn.vue';
import BtSearch from '@/components/non_biz/bt_search/BtSearch.vue';
import BtThFilter from '@/components/non_biz/bt_th_filter/BtThFilter.vue';
import BtGantt from '@/components/non_biz/bt_gantt/BtGantt.vue';
import BtTooltip from '@/components/non_biz/bt_tooltip/BtTooltip.vue';
import type { TaskItemWithChildren } from './MyTasksService';

// Store使用
const store = useMyTasksStore();
const { tasksLoading, ganttData, columnList } = storeToRefs(store);

// 组件卸载状态管理
const isUnmounted = ref(false);

// Tab页签相关
const curTypeIndex = ref(0);
const typeList = ref([
  { name: '任务列表', load: true },
  { name: '甘特图', load: false },
]);

// 切换视图
function handleTypeChange(index: number) {
  // 检查组件是否已卸载
  if (isUnmounted.value) return;

  curTypeIndex.value = index;

  // 确保甘特图视图被标记为已加载
  if (index === 1) {
    typeList.value[1].load = true;
    // 切换到甘特图视图，确保使用当前筛选后的任务数据
    nextTick(() => {
      // 双重检查：组件状态和引用存在性
      if (!isUnmounted.value && ganttRef.value) {
        try {
          ganttRef.value.render();
        } catch (error) {
          console.warn('甘特图渲染失败:', error);
        }
      }
    });
  }
}

// 初始化
async function init() {
  // 预加载甘特图视图
  typeList.value[1].load = true;
  // 调用 Store 的初始化方法
  await store.init();
}

// 表格列配置
const columnRef = ref<any>();

const showColumn = computed(() => {
  return columnList.value.filter((item) => item.show);
});

// 筛选相关
function changeFilter(val: any, field: string) {
  // 更新对应列的筛选值
  const column = columnList.value.find((col) => col.field === field);
  if (column) {
    column.filterValue = val;
  }

  // 更新Store中的筛选条件
  store.updateFilterConditions(field, val);

  // 收集并更新排序参数
  collectAndUpdateSortParams();

  // 重新加载任务列表
  store.resetSearch();
}

// 收集排序参数并更新到Store
function collectAndUpdateSortParams() {
  const sorts: any[] = [];
  columnList.value.forEach((column) => {
    if (column.filterOrder) {
      sorts.push({
        field: column.field,
        direction: column.filterOrder,
      });
    }
  });
  store.updateSortParams(sorts);
}

// 搜索相关 - 使用store中的搜索关键词
const keyword = computed({
  get: () => store.searchKeyword,
  set: (value: string) => store.setKeyword(value),
});

function toSearch() {
  // 重新加载任务列表
  store.resetSearch();
}

// 甘特图相关
const ganttRef = ref<InstanceType<typeof BtGantt>>();

// 甘特图筛选与主搜索保持同步
const ganttFilter = computed({
  get: () => store.searchKeyword,
  set: (value: string) => store.setKeyword(value),
});
const dateType = ref('day');
const dateTypeList = ref([
  { label: '日', value: 'day' },
  { label: '周', value: 'week' },
  { label: '月', value: 'month' },
  { label: '年', value: 'year' },
]);

// 甘特图层级展开功能
function openLevel(level: number) {
  if (ganttRef.value) {
    ganttRef.value.treeOpen(level);
  }
}

// 甘特图操作功能
function ganttOperate(type: string) {
  if (!ganttRef.value) return;

  switch (type) {
    case 'expand':
      ganttRef.value.expand();
      break;
    case 'export_png':
      ganttRef.value.exportTo('png', {
        name: '我的任务甘特图.png',
      });
      break;
    case 'export_pdf':
      ganttRef.value.exportTo('pdf', {
        name: '我的任务甘特图.pdf',
      });
      break;
    default:
      console.warn('未知的甘特图操作类型:', type);
  }
}

// 甘特图配置
const ganttConfig = computed(() => {
  return {
    autofit: false, // 禁用自动宽度适应，防止列宽被压缩
    grid_width: 800, // 设置固定网格宽度（7列总宽1130px + 边距）
    columns: [
      { name: 'text', label: '任务名称', width: 120, tree: true },
      { name: 'status', label: '状态', width: 120 },
      { name: 'responsible_user', label: '责任人', width: 120 },
      { name: 'task_type', label: '任务类型', width: 120 },
      { name: 'actual_start_date', label: '实际开始时间', width: 120 },
      { name: 'end_date', label: '计划完成时间', width: 120 },
      { name: 'progress_display', label: '进度', width: 120 },
    ],
  };
});

// 获取状态文本 - 基于 Swagger ProjectTaskStatus 枚举
function getStatusText(status: number | undefined) {
  const statusTextMap: Record<number, string> = {
    1: '未开始',
    2: '进行中',
    3: '已关闭',
  };
  return statusTextMap[status || 1] || '未开始';
}

// 获取状态类型 - 用于 el-tag 的 type 属性
function getStatusType(status: number | undefined) {
  const statusTypeMap: Record<number, string> = {
    1: 'info', // 未开始 - 灰色
    2: 'warning', // 进行中 - 橙色
    3: 'success', // 已关闭 - 绿色
  };
  return statusTypeMap[status || 1] || 'info';
}

// 格式化进度
function formatProgress(val: number): string {
  return val + '%';
}

// 获取任务类型文本
function getTaskTypeText(taskType: number | undefined): string {
  if (!taskType) return '';
  const taskTypeMap = {
    1: '里程碑作业',
    2: '任务作业',
    3: 'wbs作业',
    4: '配合作业',
  };
  return taskTypeMap[taskType as keyof typeof taskTypeMap] || '';
}

// 获取专业文本
function getMajorText(major: number | undefined): string {
  if (!major) return '';
  const majorMap = {
    1: '产品策划',
    2: '造型开发',
    3: '平台开发',
    4: '工程开发',
    5: '整车工程/总布置',
    6: '试验认证',
    7: '生产准备',
    8: '项目管理/系统工程',
    9: '新兴技术开发',
  };
  return majorMap[major as keyof typeof majorMap] || '';
}

// 格式化日期显示
function formatDate(dateStr: string | undefined): string {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  } catch {
    return dateStr;
  }
}

// 生成层级序号的函数
function generateTaskNumbers(
  tasks: TaskItemWithChildren[],
  parentNumber: string = ''
): TaskItem[] {
  return tasks.map((task, index) => {
    const currentNumber = parentNumber
      ? `${parentNumber}.${index + 1}`
      : `${index + 1}`;
    const updatedTask = {
      ...task,
      taskNumber: currentNumber,
    };

    if (task.children && task.children.length > 0) {
      updatedTask.children = generateTaskNumbers(task.children, currentNumber);
    }

    return updatedTask;
  });
}

// 任务数据 - 添加序号
const tasksWithNumbers = computed(() => {
  const tasks = generateTaskNumbers(store.tasks);
  return tasks;
});

// 生命周期
onMounted(() => {
  // 初始化排序参数
  collectAndUpdateSortParams();
  init();
});
</script>
<template>
  <div class="my-task-list">
    <div class="header">
      <BtTab :list="typeList" :value="curTypeIndex" @change="handleTypeChange">
        <template #right>
          <div v-show="curTypeIndex === 0" class="right-opt">
            <div class="flex flex-center">
              <el-dropdown placement="bottom-end" class="item">
                <el-button class="btn">Excel</el-button>
                <template #dropdown>
                  <el-dropdown-menu style="width: 215px">
                    <el-dropdown-item>导入列表（Excel）</el-dropdown-item>
                    <el-dropdown-item>导出列表（Excel）</el-dropdown-item>
                    <el-dropdown-item>下载模板（Excel）</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <BtSearch
                class="item"
                v-model="keyword"
                placeholder="请输入任务名称搜索"
                @search="toSearch"
                @clear="toSearch"
              ></BtSearch>
              <BtColumn ref="columnRef" v-model="columnList"></BtColumn>
            </div>
          </div>
          <div v-show="curTypeIndex === 1" class="right-opt flex flex-center">
            <div class="flex flex-center">
              <el-dropdown placement="bottom-end" class="item">
                <el-button class="btn">展开</el-button>
                <template #dropdown>
                  <el-dropdown-menu style="width: 215px">
                    <el-dropdown-item @click="openLevel(1)">
                      展开第一层
                    </el-dropdown-item>
                    <el-dropdown-item @click="openLevel(2)">
                      展开第二层
                    </el-dropdown-item>
                    <el-dropdown-item @click="openLevel(3)">
                      展开第三层
                    </el-dropdown-item>
                    <el-dropdown-item @click="openLevel(4)">
                      展开第四层
                    </el-dropdown-item>
                    <el-dropdown-item @click="openLevel(0)">
                      全部展开
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-dropdown placement="bottom-end" class="item">
                <el-button class="btn">Excel</el-button>
                <template #dropdown>
                  <el-dropdown-menu style="width: 215px">
                    <el-dropdown-item> 导出列表（Excel） </el-dropdown-item>
                    <el-dropdown-item @click="ganttOperate('export_png')">
                      导出甘特图（PNG）
                    </el-dropdown-item>
                    <el-dropdown-item @click="ganttOperate('export_pdf')">
                      导出甘特图（PDF）
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-radio-group class="item" v-model="dateType">
                <el-radio-button
                  v-for="(item, index) in dateTypeList"
                  :key="index"
                  :value="item.value"
                  >{{ item.label }}</el-radio-button
                >
              </el-radio-group>
              <BtTooltip
                class="icon-tooltip"
                effect="dark"
                content="全屏"
                placement="bottom"
              >
                <el-icon class="item icon" @click="ganttOperate('expand')"
                  ><FullScreen
                /></el-icon>
              </BtTooltip>
              <BtSearch
                class="item"
                v-model="ganttFilter"
                placeholder="请输入任务名称搜索"
                @search="toSearch"
                @clear="toSearch"
              ></BtSearch>
              <el-icon class="item icon"><Setting /></el-icon>
            </div>
          </div>
        </template>
      </BtTab>
    </div>

    <div class="content-wrapper">
      <!-- 列表视图 -->
      <div v-show="curTypeIndex === 0" class="list-view">
        <!-- 任务列表容器 -->
        <div class="table-wrapper">
          <el-table
            ref="tableContainerRef"
            v-loading="tasksLoading && tasksWithNumbers.length === 0"
            class="table"
            :data="tasksWithNumbers"
            row-key="projTaskId"
            default-expand-all
          >
            <el-table-column type="selection" width="33" fixed="left" />

            <!-- 序号列 -->
            <el-table-column label="#" width="32" align="left" type="">
              <template #default="{ row }">
                <span class="task-number">{{ row.taskNumber }}</span>
              </template>
            </el-table-column>

            <!-- 各列单独处理，确保筛选图标与列对应 -->
            <template v-for="column in showColumn" :key="column.field">
              <!-- 任务名称列 -->
              <el-table-column
                v-if="column.field === 'projectName'"
                :property="column.field"
                :label="column.label"
                :width="column.width"
                :indent="20"
                show-overflow-tooltip
              >
                <template #header>
                  <BtThFilter
                    v-if="column.filter"
                    :title="column.label"
                    :type="column.filter.type"
                    :inputType="column.filter.inputType"
                    :showOrder="column.filter.showOrder"
                    :placeholder="column.filter.placeholder"
                    :options="column.filter.data"
                    v-model="column.filterValue"
                    v-model:order="column.filterOrder"
                    @change="changeFilter($event, column.field)"
                  ></BtThFilter>
                  <span v-else>{{ column.label }}</span>
                </template>
                <template #default="{ row }">
                  {{ row.projectName || 'N/A' }}
                </template>
              </el-table-column>

              <!-- 状态列 -->
              <el-table-column
                v-else-if="column.field === 'projTaskStatus'"
                :property="column.field"
                :label="column.label"
                :width="column.width"
              >
                <template #header>
                  <BtThFilter
                    v-if="column.filter"
                    :title="column.label"
                    :type="column.filter.type"
                    :inputType="column.filter.inputType"
                    :showOrder="column.filter.showOrder"
                    :placeholder="column.filter.placeholder"
                    :options="column.filter.data"
                    v-model="column.filterValue"
                    v-model:order="column.filterOrder"
                    @change="changeFilter($event, column.field)"
                  ></BtThFilter>
                  <span v-else>{{ column.label }}</span>
                </template>
                <template #default="{ row }">
                  {{ getStatusText(row.projTaskStatus) }}
                </template>
              </el-table-column>

              <!-- 进度列 -->
              <el-table-column
                v-else-if="column.field === 'actualProgress'"
                :property="column.field"
                :label="column.label"
                :width="column.width"
              >
                <template #header>
                  <BtThFilter
                    v-if="column.filter"
                    :title="column.label"
                    :type="column.filter.type"
                    :inputType="column.filter.inputType"
                    :showOrder="column.filter.showOrder"
                    :placeholder="column.filter.placeholder"
                    :options="column.filter.data"
                    v-model="column.filterValue"
                    v-model:order="column.filterOrder"
                    @change="changeFilter($event, column.field)"
                  ></BtThFilter>
                  <span v-else>{{ column.label }}</span>
                </template>
                <template #default="{ row }">
                  <el-progress
                    class="progress"
                    color="#52C41A"
                    :stroke-width="12"
                    :percentage="row.actualProgress || 0"
                    :format="formatProgress"
                  />
                </template>
              </el-table-column>

              <!-- 超期列 -->

              <!-- 任务名称列 -->
              <el-table-column
                v-else-if="column.field === 'projTaskName'"
                :property="column.field"
                :label="column.label"
                :width="column.width"
                :indent="20"
                show-overflow-tooltip
              >
                <template #header>
                  <BtThFilter
                    v-if="column.filter"
                    :title="column.label"
                    :type="column.filter.type"
                    :inputType="column.filter.inputType"
                    :showOrder="column.filter.showOrder"
                    :placeholder="column.filter.placeholder"
                    :options="column.filter.data"
                    v-model="column.filterValue"
                    v-model:order="column.filterOrder"
                    @change="changeFilter($event, column.field)"
                  ></BtThFilter>
                  <span v-else>{{ column.label }}</span>
                </template>
              </el-table-column>

              <!-- 任务类型列 -->
              <el-table-column
                v-else-if="column.field === 'taskType'"
                :property="column.field"
                :label="column.label"
                :width="column.width"
              >
                <template #header>
                  <BtThFilter
                    v-if="column.filter"
                    :title="column.label"
                    :type="column.filter.type"
                    :inputType="column.filter.inputType"
                    :showOrder="column.filter.showOrder"
                    :placeholder="column.filter.placeholder"
                    :options="column.filter.data"
                    v-model="column.filterValue"
                    v-model:order="column.filterOrder"
                    @change="changeFilter($event, column.field)"
                  ></BtThFilter>
                  <span v-else>{{ column.label }}</span>
                </template>
                <template #default="{ row }">
                  {{ getTaskTypeText(row.taskType) }}
                </template>
              </el-table-column>

              <!-- 专业列 -->
              <el-table-column
                v-else-if="column.field === 'major'"
                :property="column.field"
                :label="column.label"
                :width="column.width"
              >
                <template #header>
                  <BtThFilter
                    v-if="column.filter"
                    :title="column.label"
                    :type="column.filter.type"
                    :inputType="column.filter.inputType"
                    :showOrder="column.filter.showOrder"
                    :placeholder="column.filter.placeholder"
                    :options="column.filter.data"
                    v-model="column.filterValue"
                    v-model:order="column.filterOrder"
                    @change="changeFilter($event, column.field)"
                  ></BtThFilter>
                  <span v-else>{{ column.label }}</span>
                </template>
                <template #default="{ row }">
                  {{ getMajorText(row.major) }}
                </template>
              </el-table-column>

              <!-- 实际开始时间列 -->
              <el-table-column
                v-else-if="column.field === 'actualStartDate'"
                :property="column.field"
                :label="column.label"
                :width="column.width"
              >
                <template #header>
                  <BtThFilter
                    v-if="column.filter"
                    :title="column.label"
                    :type="column.filter.type"
                    :inputType="column.filter.inputType"
                    :showOrder="column.filter.showOrder"
                    :placeholder="column.filter.placeholder"
                    :options="column.filter.data"
                    v-model="column.filterValue"
                    v-model:order="column.filterOrder"
                    @change="changeFilter($event, column.field)"
                  ></BtThFilter>
                  <span v-else>{{ column.label }}</span>
                </template>
                <template #default="{ row }">
                  {{ formatDate(row.actualStartDate) }}
                </template>
              </el-table-column>

              <!-- 计划开始时间列 -->
              <el-table-column
                v-else-if="column.field === 'planStartDate'"
                :property="column.field"
                :label="column.label"
                :width="column.width"
              >
                <template #header>
                  <BtThFilter
                    v-if="column.filter"
                    :title="column.label"
                    :type="column.filter.type"
                    :inputType="column.filter.inputType"
                    :showOrder="column.filter.showOrder"
                    :placeholder="column.filter.placeholder"
                    :options="column.filter.data"
                    v-model="column.filterValue"
                    v-model:order="column.filterOrder"
                    @change="changeFilter($event, column.field)"
                  ></BtThFilter>
                  <span v-else>{{ column.label }}</span>
                </template>
                <template #default="{ row }">
                  {{ formatDate(row.planStartDate) }}
                </template>
              </el-table-column>

              <!-- 计划完成时间列 -->
              <el-table-column
                v-else-if="column.field === 'planFinishDate'"
                :property="column.field"
                :label="column.label"
                :width="column.width"
              >
                <template #header>
                  <BtThFilter
                    v-if="column.filter"
                    :title="column.label"
                    :type="column.filter.type"
                    :inputType="column.filter.inputType"
                    :showOrder="column.filter.showOrder"
                    :placeholder="column.filter.placeholder"
                    :options="column.filter.data"
                    v-model="column.filterValue"
                    v-model:order="column.filterOrder"
                    @change="changeFilter($event, column.field)"
                  ></BtThFilter>
                  <span v-else>{{ column.label }}</span>
                </template>
                <template #default="{ row }">
                  {{ formatDate(row.planFinishDate) }}
                </template>
              </el-table-column>

              <!-- 超期列 -->
              <el-table-column
                v-else-if="column.field === 'overdueDays'"
                :property="column.field"
                :label="column.label"
                :width="column.width"
              >
                <template #header>
                  <BtThFilter
                    v-if="column.filter"
                    :title="column.label"
                    :type="column.filter.type"
                    :inputType="column.filter.inputType"
                    :showOrder="column.filter.showOrder"
                    :placeholder="column.filter.placeholder"
                    :options="column.filter.data"
                    v-model="column.filterValue"
                    v-model:order="column.filterOrder"
                    @change="changeFilter($event, column.field)"
                  ></BtThFilter>
                  <span v-else>{{ column.label }}</span>
                </template>
                <template #default="{ row }">
                  <el-tag v-if="row.overdueDays > 0" type="danger">是</el-tag>
                  <el-tag v-else type="success">否</el-tag>
                </template>
              </el-table-column>

              <!-- 其他列 -->
              <el-table-column
                v-else
                :property="column.field"
                :label="column.label"
                :width="column.width"
                show-overflow-tooltip
              >
                <template #header>
                  <BtThFilter
                    v-if="column.filter"
                    :title="column.label"
                    :type="column.filter.type"
                    :inputType="column.filter.inputType"
                    :showOrder="column.filter.showOrder"
                    :placeholder="column.filter.placeholder"
                    :options="column.filter.data"
                    v-model="column.filterValue"
                    v-model:order="column.filterOrder"
                    @change="changeFilter($event, column.field)"
                  ></BtThFilter>
                  <span v-else>{{ column.label }}</span>
                </template>
                <template #default="{ row }">
                  {{ row[column.field] }}
                </template>
              </el-table-column>
            </template>

            <!-- 操作列 -->
            <el-table-column
              label="操作"
              v-bind="tableConfig.optColumnAttr"
              fixed="right"
            >
              <template #default="">
                <el-dropdown placement="bottom-end">
                  <div class="more-opt">
                    <el-icon class="icon"><MoreFilled /></el-icon>
                  </div>
                </el-dropdown>
              </template>
            </el-table-column>

            <!-- 表格底部插槽 - 加载状态提示 -->
            <template #append>
              <!-- 加载更多提示 -->
              <div
                v-if="tasksLoading && tasksWithNumbers.length > 0"
                class="table-loading-more"
              >
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>正在加载更多...</span>
              </div>
            </template>
          </el-table>
        </div>
      </div>

      <!-- 甘特图视图 -->
      <div v-show="curTypeIndex === 1" class="gantt-view">
        <div class="gantt-container">
          <BtGantt
            v-if="typeList[1].load"
            ref="ganttRef"
            :config="ganttConfig"
            :dateType="dateType"
            :data="ganttData"
            :filterValue="ganttFilter"
          ></BtGantt>
          <div v-else class="gantt-loading">
            <el-empty description="正在加载甘特图..." />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.my-task-list {
  height: 100%;
  display: flex;
  flex-direction: column;

  .content-wrapper {
    flex: 1;
    display: flex;
    overflow: hidden;
    position: relative;
  }

  .list-view {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 16px;
  }

  .gantt-view {
    width: 100%;
    height: 100%;
    display: flex;
    overflow: hidden;
    padding: 16px;

    .gantt-container {
      width: 100%;
      height: 100%;
      overflow-x: auto; /* 水平滚动条，当内容超出时显示 */
    }

    .gantt-loading {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .table-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .table-loading-more,
  .table-no-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 20px;
    color: var(--el-text-color-secondary);
    font-size: 13px;
    transition: all 0.3s ease;

    .el-icon {
      margin-right: 6px;
      font-size: 14px;
    }
  }

  .table-no-more {
    color: var(--el-text-color-placeholder);
  }

  // 鼠标悬停效果
  .table-loading-more:hover {
    background-color: var(--el-fill-color-light);
  }

  .table {
    flex: 1;

    // 表格底部插槽样式
    :deep(.el-table__append-wrapper) {
      border-top: none;
    }

    :deep(.el-table__indent) {
      padding-left: 12px;
    }

    .task-number {
      color: var(--el-text-color-primary);
    }

    .progress {
      :deep(.el-progress__text) {
        font-size: 12px !important;
      }
    }
  }

  .footer {
    padding: 10px 20px;
    text-align: right;
  }

  .gantt-container {
    flex: 1;
    border-radius: 4px;
    overflow: hidden;

    .gantt-loading {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
