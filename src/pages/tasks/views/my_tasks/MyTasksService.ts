import { send } from '@/libs/request';
import type { FilterParams, TaskItem } from './types';
import { myTasksList } from '@/service_url/tasks';

export interface TaskItemWithChildren extends TaskItem {
  children?: TaskItemWithChildren[];
}

/**
 * 将树形数据转换为列表数据
 * @param tasks 任务数据数组
 * @returns 按照父子关系的列表数据
 */
function treeToList(tasks: TaskItem[]): TaskItemWithChildren[] {
  const list: TaskItemWithChildren[] = [];

  tasks.forEach((task) => {
    const taskWithChildren: TaskItemWithChildren = {
      ...task,
      children: [],
    };
    list.push(taskWithChildren);
  });

  return list;
}

// 获取我的任务列表
export async function getMyTasks(params: FilterParams): Promise<TaskItem[]> {
  // 构建请求数据
  const requestData: any = {
    // 任务名称搜索
    projTaskName: params.keyword,
  };

  // 处理筛选条件
  if (params.filters) {
    const filters = params.filters;

    // 项目ID筛选
    if (filters.projId) {
      requestData.projId = filters.projId;
    }

    // 项目名称筛选
    if (filters.projectName) {
      requestData.projectName = filters.projectName;
    }

    // 任务ID筛选
    if (filters.projTaskId) {
      requestData.projTaskId = filters.projTaskId;
    }

    // 任务名称筛选
    if (filters.projTaskName) {
      requestData.projTaskName = filters.projTaskName;
    }

    // 状态筛选
    if (filters.projTaskStatus && filters.projTaskStatus.length > 0) {
      requestData.projTaskStatusList = filters.projTaskStatus;
    }

    // 责任部门筛选
    if (filters.projTaskResponsibleDept) {
      requestData.projTaskResponsibleDept = filters.projTaskResponsibleDept;
    }

    // 项目角色筛选
    if (filters.projTaskResponsibleProjRole) {
      requestData.projTaskResponsibleProjRole =
        filters.projTaskResponsibleProjRole;
    }

    // 责任人筛选
    if (filters.responsibleName) {
      requestData.responsibleName = filters.responsibleName;
    }

    // 任务类型筛选
    if (filters.taskType && filters.taskType.length > 0) {
      requestData.taskTypeList = filters.taskType;
    }

    // 专业筛选
    if (filters.major && filters.major.length > 0) {
      requestData.majorList = filters.major;
    }

    // 计划开始日期范围筛选
    if (filters.beginPlanStartDate && filters.endPlanStartDate) {
      requestData.beginPlanStartDate = filters.beginPlanStartDate;
      requestData.endPlanStartDate = filters.endPlanStartDate;
    }

    // 实际开始日期范围筛选
    if (filters.beginActualStartDate && filters.endActualStartDate) {
      requestData.beginActualStartDate = filters.beginActualStartDate;
      requestData.endActualStartDate = filters.endActualStartDate;
    }

    // 计划完成日期范围筛选
    if (filters.beginPlanFinishDate && filters.endPlanFinishDate) {
      requestData.beginPlanFinishDate = filters.beginPlanFinishDate;
      requestData.endPlanFinishDate = filters.endPlanFinishDate;
    }

    // 超期筛选
    if (filters.overdueDays && filters.overdueDays.length > 0) {
      requestData.overdueDaysList = filters.overdueDays;
    }

    // 实际进度筛选
    if (filters.actualProgress) {
      requestData.actualProgress = filters.actualProgress;
    }
  }

  // 处理排序参数
  if (params.sorts && params.sorts.length > 0) {
    requestData.sorts = params.sorts;
  }

  const response = await send({
    method: 'POST',
    url: myTasksList,
    data: requestData,
  });

  // 检查关键数据是否存在
  if (!response || !Array.isArray(response)) {
    return [];
  }

  const treeData = treeToList(response);
  console.log('🚀 ~ treeData:', treeData);
  return treeData;
}
