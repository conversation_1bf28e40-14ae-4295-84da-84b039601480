import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import type {
  TaskItem,
  FilterParams,
  GanttTaskItem,
  FilterConditions,
  SortParam,
} from './types';
import * as myTasksService from './MyTasksService';
import type { TaskItemWithChildren } from './MyTasksService';

// 辅助函数
function getStatusText(status: number | undefined): string {
  const statusTextMap: Record<number, string> = {
    1: '未开始',
    2: '进行中',
    3: '已关闭',
  };
  return statusTextMap[status || 1] || '未开始';
}

function getTaskTypeText(taskType: number): string {
  const taskTypeMap = {
    1: '里程碑作业',
    2: '任务作业',
    3: 'WBS作业',
    4: '配合作业',
  };
  return taskTypeMap[taskType as keyof typeof taskTypeMap] || '未知';
}

export const useMyTasksStore = defineStore(
  'myTasksStore',
  () => {
    // 1. 响应式状态定义
    const tasks = ref<TaskItem[]>([]);
    const tasksLoading = ref(false);
    const searchKeyword = ref('');

    // 分页相关状态
    const page = ref(1);
    const pageSize = ref(20);
    const hasMore = ref(true);

    // 筛选条件
    const filterConditions = ref<FilterConditions>({});

    // 排序条件
    const sortParams = ref<SortParam[]>([]);

    // 列配置
    const columnList = ref([
      {
        field: 'projId',
        label: '项目ID',
        width: 144,
        align: 'left',
        show: false,
        filter: {
          type: 'input' as const,
          showOrder: false,
          placeholder: '请输入项目ID',
        },
        filterValue: '',
      },
      {
        field: 'projectName',
        label: '项目名称',
        width: 144,
        align: 'left',
        show: true,
        filter: { type: 'input' as const, showOrder: false, showSearch: true },
        filterValue: '',
      },
      {
        field: 'projTaskName',
        label: '任务名称',
        width: 144,
        align: 'left',
        show: true,
        filter: { type: 'input' as const, showOrder: false, showSearch: true },
        filterValue: '',
      },
      {
        field: 'projTaskId',
        label: '任务ID',
        width: 144,
        align: 'left',
        show: false,
        filter: {
          type: 'input' as const,
          showOrder: false,
          placeholder: '请输入任务ID',
        },
        filterValue: '',
      },
      {
        field: 'projTaskStatus',
        label: '状态',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox' as const,
          showOrder: false,
          data: [
            { value: 1, label: '未开始' },
            { value: 2, label: '进行中' },
            { value: 3, label: '已关闭' },
          ],
        },
        filterValue: [],
      },
      {
        field: 'projTaskResponsibleDept',
        label: '责任部门',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          showOrder: false,
          placeholder: '请输入责任部门',
        },
        filterValue: '',
      },
      {
        field: 'projTaskResponsibleProjRole',
        label: '项目角色',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          showOrder: false,
          placeholder: '请输入项目角色',
        },
        filterValue: '',
      },
      {
        field: 'responsibleName',
        label: '责任人',
        width: 120,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          showOrder: false,
          placeholder: '请输入责任人',
        },
        filterValue: '',
      },
      {
        field: 'taskType',
        label: '任务类型',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox' as const,
          showOrder: false,
          data: [
            { value: 1, label: '里程碑作业' },
            { value: 2, label: '任务作业' },
            { value: 3, label: 'WBS作业' },
            { value: 4, label: '配合作业' },
          ],
        },
        filterValue: [],
      },
      {
        field: 'major',
        label: '专业/系统',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox' as const,
          data: [
            { value: 1, label: '产品策划' },
            { value: 2, label: '造型开发' },
            { value: 3, label: '平台开发' },
            { value: 4, label: '工程开发' },
            { value: 5, label: '整车工程/总布置' },
            { value: 6, label: '试验认证' },
            { value: 7, label: '生产准备' },
            { value: 8, label: '项目管理/系统工程' },
            { value: 9, label: '新兴技术开发' },
          ],
        },
        filterValue: [],
      },
      {
        field: 'planStartDate',
        label: '计划开始时间',
        width: 176,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          inputType: 'dateRange' as const,
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'actualStartDate',
        label: '实际开始时间',
        width: 176,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          inputType: 'dateRange' as const,
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'planFinishDate',
        label: '计划完成时间',
        width: 176,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          inputType: 'dateRange' as const,
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'overdueDays',
        label: '超期',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox' as const,
          data: [
            { value: 1, label: '是' },
            { value: 0, label: '否' },
          ],
        },
        filterValue: [],
      },
      {
        field: 'actualProgress',
        label: '进度',
        width: '144',
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          showOrder: true,
          placeholder: '请输入进度值',
        },
        filterValue: '',
        filterOrder: '',
      },
    ]);

    // 2. 计算属性
    const filterParams = computed<FilterParams>(() => {
      return {
        keyword: searchKeyword.value,
        filters: filterConditions.value,
        sorts: sortParams.value,
      };
    });

    // 甘特图数据转换 - 基于 TaskItem 转换为 GanttTaskItem，支持子任务展示
    const ganttData = computed(() => {
      // 递归函数：将嵌套的任务结构展平为甘特图需要的平级数据
      const flattenTasks = (tasks: TaskItemWithChildren[]): GanttTaskItem[] => {
        const result: GanttTaskItem[] = [];

        const processTask = (task: TaskItemWithChildren): GanttTaskItem => {
          // 获取开始和结束日期，如果不存在则使用当前日期
          const startDate = task.actualStartDate;
          const endDate = task.planFinishDate;

          // 计算进度，使用实际进度或默认为0
          const progress = task.actualProgress || 0;

          return {
            id: task.projTaskId || task.projId!,
            text: task.projTaskName || '',
            start_date: startDate || '',
            end_date: endDate || '',
            progress: progress / 100, // 甘特图内部进度需要转换为0-1的小数
            // parent: task.parentProjTaskId || 0,
            type:
              task.children && task.children.length > 0 ? 'project' : 'task', // 有子任务的显示为项目类型
            open: false, // 默认不展开子任务，通过展开按钮控制
            // 甘特图显示字段
            status: getStatusText(task.projTaskStatus),
            responsible_user: task.responsibleName || '',
            task_type: getTaskTypeText(task.taskType || 2),
            actual_start_date: task.actualStartDate?.toString() || '',
            // 进度列显示为百分比格式
            progress_display: `${progress}%`,
          };
        };

        // 递归处理每个任务及其子任务
        for (const task of tasks) {
          // 添加当前任务
          result.push(processTask(task));

          // 递归处理子任务
          if (task.children && task.children.length > 0) {
            const childTasks = flattenTasks(task.children);
            result.push(...childTasks);
          }
        }

        return result;
      };

      return {
        data: flattenTasks(tasks.value),
        links: [], // 暂时没有任务依赖关系
      };
    });

    // 3. 异步方法
    // 初始化方法
    async function init() {
      try {
        // 加载任务数据
        await loadTasks();
      } catch (error) {
        console.error('我的任务初始化失败:', error);
      }
    }

    async function loadTasks() {
      tasksLoading.value = true;

      const params = filterParams.value;

      const result = await myTasksService.getMyTasks(params);

      tasks.value = result;
      tasksLoading.value = false;
    }

    // 4. 同步方法
    function resetData() {
      tasks.value = [];
      page.value = 1;
      hasMore.value = true;
      searchKeyword.value = '';
    }

    function setKeyword(keyword: string) {
      searchKeyword.value = keyword;
    }

    function resetSearch() {
      page.value = 1;
      hasMore.value = true;
      loadTasks();
    }

    /**
     * 更新排序参数
     */
    function updateSortParams(sorts: SortParam[]) {
      sortParams.value = sorts;
    }

    /**
     * 更新筛选条件
     */
    function updateFilterConditions(field: string, value: any) {
      // 处理日期范围字段
      if (field === 'actualStartDate') {
        if (
          Array.isArray(value) &&
          value.length === 2 &&
          value[0] &&
          value[1]
        ) {
          // 有效的日期范围选择
          filterConditions.value.beginActualStartDate = value[0];
          filterConditions.value.endActualStartDate = value[1];
        } else {
          // 清除操作：删除日期范围字段
          delete filterConditions.value.beginActualStartDate;
          delete filterConditions.value.endActualStartDate;
        }
      } else if (field === 'planStartDate') {
        if (
          Array.isArray(value) &&
          value.length === 2 &&
          value[0] &&
          value[1]
        ) {
          // 有效的日期范围选择
          filterConditions.value.beginPlanStartDate = value[0];
          filterConditions.value.endPlanStartDate = value[1];
        } else {
          // 清除操作：删除日期范围字段
          delete filterConditions.value.beginPlanStartDate;
          delete filterConditions.value.endPlanStartDate;
        }
      } else if (field === 'planFinishDate') {
        if (
          Array.isArray(value) &&
          value.length === 2 &&
          value[0] &&
          value[1]
        ) {
          // 有效的日期范围选择
          filterConditions.value.beginPlanFinishDate = value[0];
          filterConditions.value.endPlanFinishDate = value[1];
        } else {
          // 清除操作：删除日期范围字段
          delete filterConditions.value.beginPlanFinishDate;
          delete filterConditions.value.endPlanFinishDate;
        }
      } else {
        // 其他字段直接赋值
        if (
          value === null ||
          value === undefined ||
          (Array.isArray(value) && value.length === 0) ||
          value === ''
        ) {
          // 清除操作：删除字段
          delete (filterConditions.value as any)[field];
        } else {
          // 设置字段值
          (filterConditions.value as any)[field] = value;
        }
      }
    }

    /**
     * 清空筛选条件
     */
    function clearFilterConditions() {
      filterConditions.value = {};
    }

    return {
      // 状态
      tasks,
      tasksLoading,
      page,
      pageSize,
      hasMore,
      searchKeyword,
      filterConditions,
      sortParams,
      columnList,

      // 计算属性
      filterParams,
      ganttData,

      // 方法
      init,
      loadTasks,
      resetData,
      setKeyword,
      resetSearch,
      updateSortParams,
      updateFilterConditions,
      clearFilterConditions,
    };
  },
  {
    // 5. 持久化配置 - 只持久化必要数据
    persist: {
      pick: ['columnList'],
      key: 'myTasks_columnList',
    },
  }
);
