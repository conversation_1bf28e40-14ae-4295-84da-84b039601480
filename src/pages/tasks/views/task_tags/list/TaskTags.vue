<script setup lang="ts">
import {
  ref,
  computed,
  nextTick,
  onMounted,
  onActivated,
  useTemplateRef,
} from 'vue';
import { storeToRefs } from 'pinia';
import { MoreFilled, FullScreen, Setting } from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';
import { tableConfig } from '@/app-config.ts';
import { useRouter } from 'vue-router';

// 组件导入
import BtTree from '@/components/non_biz/bt_tree/BtTree.vue';
import BtTab from '@/components/non_biz/bt_tab/BtTab.vue';
import BtSearch from '@/components/non_biz/bt_search/BtSearch.vue';
import BtColumn from '@/components/non_biz/bt_column/BtColumn.vue';
import BtThFilter from '@/components/non_biz/bt_th_filter/BtThFilter.vue';
import BtGantt from '@/components/non_biz/bt_gantt/BtGantt.vue';
import BtTooltip from '@/components/non_biz/bt_tooltip/BtTooltip.vue';
import { Search as ElSearch } from '@element-plus/icons-vue';

// Store 导入
import { useTaskTagsStore } from './TaskTagsStore';
import type { TaskTagTreeNode, TaskItemResponse } from './types';

// 类型别名，便于使用
type TaskItem = TaskItemResponse;

// 路由
const router = useRouter();

// Store 使用
const store = useTaskTagsStore();
const {
  isLoading,
  tagTreeNodes,
  taskListLoading,
  viewOptions,
  searchKeyword,
  ganttData,
  columnList,
} = storeToRefs(store);

// ==================== 初始化 ====================
onMounted(() => {
  // 页面首次加载时强制刷新数据
  refreshData();
});

// 页面激活时的处理（处理keep-alive缓存）
onActivated(() => {
  // 每次页面激活时都刷新标签树数据
  refreshData();
});

// 统一的数据刷新方法
async function refreshData() {
  await store.refresh();
}

// 任务数据 - 添加序号
const tasks = computed(() => {
  const result = generateTaskNumbers(store.taskList);
  return result;
});

// 获取状态类型 - 基于 Swagger ProjectTaskStatus 枚举
function getStatusType(status: number | undefined) {
  const statusMap: Record<number, string> = {
    1: 'info', // 未开始
    2: 'warning', // 进行中
    3: 'success', // 已关闭
  };
  return statusMap[status || 1] || 'info';
}

// 获取状态文本 - 基于 Swagger ProjectTaskStatus 枚举
function getStatusText(status: number | undefined) {
  const statusTextMap: Record<number, string> = {
    1: '未开始',
    2: '进行中',
    3: '已关闭',
  };
  return statusTextMap[status || 1] || '未开始';
}

// 获取任务类型文本 - 支持undefined参数，空值返回空字符串
function getTaskTypeText(taskType: number | undefined): string {
  if (taskType === undefined || taskType === null) {
    return '';
  }
  const taskTypeMap = {
    1: '里程碑作业',
    2: '任务作业',
    3: 'WBS作业',
    4: '配合作业',
  };
  return taskTypeMap[taskType as keyof typeof taskTypeMap] || '';
}

// 获取专业文本 - 处理专业字段，空值返回空字符串
function getMajorText(major: number | undefined): string {
  if (major === undefined || major === null) {
    return '';
  }
  const majorMap = {
    1: '产品策划',
    2: '造型开发',
    3: '平台开发',
    4: '工程开发',
    5: '整车工程/总布置',
    6: '试验认证',
    7: '生产准备',
    8: '项目管理/系统工程',
    9: '新兴技术开发',
  };
  return majorMap[major as keyof typeof majorMap] || '';
}

// 格式化日期 - 处理日期字段，空值返回空字符串
function formatDate(date: string | undefined): string {
  if (!date) {
    return '';
  }
  return date;
}

// 生成层级序号的函数
function generateTaskNumbers(
  tasks: TaskItem[],
  parentNumber: string = ''
): TaskItem[] {
  return tasks.map((task, index) => {
    const currentNumber = parentNumber
      ? `${parentNumber}.${index + 1}`
      : `${index + 1}`;
    const updatedTask = {
      ...task,
      taskNumber: currentNumber,
    };

    if (task.children && task.children.length > 0) {
      updatedTask.children = generateTaskNumbers(task.children, currentNumber);
    }

    return updatedTask;
  });
}

// ==================== 左侧标签树相关 ====================
const tabList = ref([{ name: '标签' }]);
const curTab = ref(0);

// 组件引用
const tableContainerRef = useTemplateRef<any>('tableContainerRef'); // 表格容器引用，用于无限滚动

// 处理树节点点击事件
function handleTreeNodeClick(payload: { data: any; node: any }) {
  const { data } = payload;

  // 如果点击的是标签组，不进行筛选
  if (data.isGroup || data.tagGroupFlag === 1) {
    return;
  }

  // 转换为TaskTagTreeNode类型
  const selectedNode: TaskTagTreeNode = {
    label: data.label,
    value: data.value,
    color: data.color,
    status: data.status,
    isGroup: data.isGroup,
    children: data.children,
    rawData: data.rawData,
  };

  // 调用store方法处理单个标签选择
  store.handleTagTreeChange(selectedNode);
}

// ==================== 右侧视图切换相关 ====================
const curTypeIndex = ref(0);

// 切换视图
function handleTypeChange(index: number) {
  curTypeIndex.value = index;
  const viewType = viewOptions.value[index]?.value;
  if (viewType) {
    store.switchViewType(viewType);

    // 确保甘特图视图被标记为已加载
    if (viewType === 'gantt') {
      viewOptions.value[1].load = true;
      // 切换到甘特图视图，确保使用当前筛选后的任务数据
      nextTick(() => {
        ganttRef.value?.render();
      });
    }
  }
}

// ==================== 搜索功能 ====================
const keyword = ref('');

function toSearch() {
  store.setSearchKeyword(keyword.value);
}

// ==================== 表格列配置 ====================
const columnRef = ref<any>();

const showColumn = computed(() => {
  return columnList.value.filter((item) => item.show);
});

// 筛选相关
function changeFilter(val: any, field: string) {
  console.log('筛选变更', val, field);

  // 更新对应列的筛选值
  const column = columnList.value.find((col) => col.field === field);
  if (column) {
    column.filterValue = val;
  }

  // 收集筛选条件并更新Store
  updateFilterConditions();

  // 重新加载任务列表
  store.loadTaskList();
}

// 收集表格列筛选条件并更新Store
function updateFilterConditions() {
  const filterConditions: any = {};

  columnList.value.forEach((column) => {
    const value = column.filterValue;

    // 检查值是否有效（非空字符串、非空数组）
    const hasValue = Array.isArray(value) ? value.length > 0 : value;

    if (hasValue) {
      // 根据字段名映射到API参数
      switch (column.field) {
        case 'projId':
          filterConditions.projId = value;
          break;
        case 'projectName':
          filterConditions.projectName = value;
          break;
        case 'projTaskId':
          filterConditions.projTaskId = value;
          break;
        case 'projTaskName':
          filterConditions.projTaskName = value;
          break;
        case 'projTaskStatus':
          // 多选状态，传递数组
          filterConditions.projTaskStatus = Array.isArray(value)
            ? value
            : [Number(value)];
          break;
        case 'projTaskResponsibleDept':
          filterConditions.projTaskResponsibleDept = value;
          break;
        case 'projTaskResponsibleProjRole':
          filterConditions.projTaskResponsibleProjRole = value;
          break;
        case 'responsibleName':
          filterConditions.responsibleName = value;
          break;
        case 'taskType':
          // 多选任务类型，传递数组
          filterConditions.taskType = Array.isArray(value) ? value : [value];
          break;
        case 'major':
          // 多选专业，传递数组
          filterConditions.major = Array.isArray(value) ? value : [value];
          break;
        case 'actualProgress':
          filterConditions.actualProgress = Number(value);
          break;
        case 'overdueDays':
          // 多选超期，传递数组
          filterConditions.overdueDays = Array.isArray(value) ? value : [value];
          break;
        case 'actualStartDate':
          // 日期范围处理
          if (Array.isArray(value) && value.length === 2) {
            filterConditions.beginActualStartDate = value[0];
            filterConditions.endActualStartDate = value[1];
          }
          break;
        case 'planStartDate':
          // 日期范围处理
          if (Array.isArray(value) && value.length === 2) {
            filterConditions.beginPlanStartDate = value[0];
            filterConditions.endPlanStartDate = value[1];
          }
          break;
        case 'planFinishDate':
          // 日期范围处理
          if (Array.isArray(value) && value.length === 2) {
            filterConditions.beginPlanFinishDate = value[0];
            filterConditions.endPlanFinishDate = value[1];
          }
          break;
        case 'createTime':
          // 日期范围处理
          if (Array.isArray(value) && value.length === 2) {
            filterConditions.beginCreateTime = value[0];
            filterConditions.endCreateTime = value[1];
          }
          break;
        case 'updateTime':
          // 日期范围处理
          if (Array.isArray(value) && value.length === 2) {
            filterConditions.beginUpdateTime = value[0];
            filterConditions.endUpdateTime = value[1];
          }
          break;
      }
    }
  });

  // 收集排序参数
  const sorts: any[] = [];
  columnList.value.forEach((column) => {
    if (column.filterOrder) {
      sorts.push({
        field: column.field,
        direction: column.filterOrder,
      });
    }
  });

  // 添加排序参数到筛选条件
  if (sorts.length > 0) {
    filterConditions.sorts = sorts;
  }

  // 更新Store中的筛选条件
  store.setFilterParams(filterConditions);
}

// ==================== 甘特图相关 ====================
const ganttRef = ref<InstanceType<typeof BtGantt>>();

// 甘特图筛选与主搜索保持同步
const ganttFilter = computed({
  get: () => searchKeyword.value,
  set: (value: string) => store.setSearchKeyword(value),
});

const dateType = ref('day');
const dateTypeList = ref([
  { label: '日', value: 'day' },
  { label: '周', value: 'week' },
  { label: '月', value: 'month' },
  { label: '年', value: 'year' },
]);

// 甘特图配置
const ganttConfig = computed(() => {
  return {
    autofit: false, // 禁用自动宽度适应，防止列宽被压缩
    grid_width: 800, // 设置固定网格宽度（7列总宽1130px + 边距）
    columns: [
      { name: 'text', label: '任务名称', width: 120, tree: true },
      { name: 'status', label: '状态', width: 120 },
      { name: 'responsible_user', label: '责任人', width: 120 },
      { name: 'start_date', label: '开始时间', width: 120 },
      { name: 'end_date', label: '结束时间', width: 120 },
      { name: 'progress_display', label: '进度', width: 120 },
    ],
  };
});

// 甘特图层级展开功能
function openLevel(level: number) {
  if (ganttRef.value) {
    ganttRef.value.treeOpen(level);
  }
}

// 甘特图操作功能
function ganttOperate(type: string) {
  if (!ganttRef.value) return;

  switch (type) {
    case 'expand':
      ganttRef.value.expand();
      break;
    case 'export_png':
      ganttRef.value.exportTo('png', {
        name: '任务标签甘特图.png',
      });
      break;
    case 'export_pdf':
      ganttRef.value.exportTo('pdf', {
        name: '任务标签甘特图.pdf',
      });
      break;
    default:
      console.warn('未知的甘特图操作类型:', type);
  }
}

// 查看任务详情
function manageTags() {
  // 跳转到独立任务详情页
  router.push({
    name: 'TaskTagManage',
  });
}

// ==================== 树形组件搜索功能 ====================
const treeRef = ref<InstanceType<typeof BtTree>>();

function toggleTreeSearch() {
  // 调用BtTree组件暴露的搜索切换功能
  if (treeRef.value) {
    treeRef.value.toggleSearch();
  }
}

// ==================== 工具函数 ====================

function formatProgress(percentage: number): string {
  return `${percentage}%`;
}

// ==================== 操作相关 ====================

/**
 * 处理取消标签操作
 */
function handleRemoveTag(task: any) {
  ElMessageBox.confirm(
    `确定要从任务"${task.projTaskName}"中移除选中的标签吗？`,
    '取消标签',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const res = await store.cancelTag(task);
      if (!res.success) return;

      // 重新加载任务列表以反映更改
      store.loadTaskList();
    })
    .catch(() => {
      // 用户取消操作
    });
}
</script>

<template>
  <div
    class="task-tags"
    v-loading="isLoading"
    element-loading-text="正在加载..."
  >
    <!-- 视图切换头部 -->
    <div class="header">
      <BtTab
        :list="viewOptions"
        :value="curTypeIndex"
        @change="handleTypeChange"
      >
        <template #right>
          <div v-show="curTypeIndex === 0" class="right-opt">
            <div class="flex flex-center">
              <el-dropdown placement="bottom-end" class="item">
                <el-button class="btn">Excel</el-button>
                <template #dropdown>
                  <el-dropdown-menu style="width: 215px">
                    <el-dropdown-item>导出列表（Excel）</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <BtSearch
                class="item"
                v-model="keyword"
                placeholder="请输入任务名称搜索"
                @search="toSearch"
                @clear="toSearch"
              ></BtSearch>
              <BtColumn ref="columnRef" v-model="columnList"></BtColumn>
            </div>
          </div>

          <!-- 甘特图工具栏 -->
          <div v-show="curTypeIndex === 1" class="right-opt flex flex-center">
            <div class="flex flex-center">
              <el-dropdown placement="bottom-end" class="item">
                <el-button class="btn">展开</el-button>
                <template #dropdown>
                  <el-dropdown-menu style="width: 215px">
                    <el-dropdown-item @click="openLevel(1)">
                      展开第一层
                    </el-dropdown-item>
                    <el-dropdown-item @click="openLevel(2)">
                      展开第二层
                    </el-dropdown-item>
                    <el-dropdown-item @click="openLevel(3)">
                      展开第三层
                    </el-dropdown-item>
                    <el-dropdown-item @click="openLevel(4)">
                      展开第四层
                    </el-dropdown-item>
                    <el-dropdown-item @click="openLevel(0)">
                      全部展开
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-dropdown placement="bottom-end" class="item">
                <el-button class="btn">Excel</el-button>
                <template #dropdown>
                  <el-dropdown-menu style="width: 215px">
                    <el-dropdown-item> 导出列表（Excel） </el-dropdown-item>
                    <el-dropdown-item @click="ganttOperate('export_png')">
                      导出甘特图（PNG）
                    </el-dropdown-item>
                    <el-dropdown-item @click="ganttOperate('export_pdf')">
                      导出甘特图（PDF）
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-radio-group class="item" v-model="dateType">
                <el-radio-button
                  v-for="(item, index) in dateTypeList"
                  :key="index"
                  :value="item.value"
                  >{{ item.label }}</el-radio-button
                >
              </el-radio-group>
              <BtTooltip
                class="icon-tooltip"
                effect="dark"
                content="全屏"
                placement="bottom"
              >
                <el-icon class="item icon" @click="ganttOperate('expand')"
                  ><FullScreen
                /></el-icon>
              </BtTooltip>
              <BtSearch
                class="item"
                v-model="ganttFilter"
                placeholder="请输入任务名称搜索"
                @search="toSearch"
                @clear="toSearch"
              ></BtSearch>
              <el-icon class="item icon"><Setting /></el-icon>
            </div>
          </div>
        </template>
      </BtTab>
    </div>
    <div v-show="curTypeIndex === 0" class="content-wrapper">
      <div class="list-view">
        <!-- 左侧标签树 -->
        <div class="sidebar">
          <BtTree
            ref="treeRef"
            name="标签"
            subname="标签组"
            :tabList="tabList"
            :curTab="curTab"
            v-model="tagTreeNodes"
            :showCheckbox="false"
            :showAdd="false"
            :indent="18"
            :showExpandIcon="true"
            @nodeClick="handleTreeNodeClick"
          >
            <!-- 自定义节点内容 -->
            <template #node="{ node, data }">
              <div class="custom-tree-node">
                <!-- 颜色指示器 - 只有叶子节点且有颜色时显示 -->
                <div
                  v-if="data.color && !data.isGroup && data.tagGroupFlag !== 1"
                  class="color-indicator"
                  :style="{
                    backgroundColor: data.color || data.rawData?.taskTagColor,
                  }"
                ></div>
                <!-- 标签文本 -->
                <span class="node-label">{{ node.label }}</span>
              </div>
            </template>

            <template #right>
              <slot name="right">
                <div class="opt flex flex-center">
                  <div class="item">
                    <BtTooltip
                      class="icon-tooltip"
                      effect="dark"
                      content="标签维护"
                      placement="bottom"
                      @click.stop="manageTags"
                    >
                      <SvgIcon class="icon" name="任务标签维护" />
                    </BtTooltip>
                  </div>
                  <div class="item" @click="toggleTreeSearch">
                    <BtTooltip
                      class="icon-tooltip"
                      effect="dark"
                      content="搜索"
                      placement="bottom"
                    >
                      <el-icon class="icon"><ElSearch /></el-icon>
                    </BtTooltip>
                  </div>
                </div>
              </slot> </template
          ></BtTree>
        </div>

        <div class="cont">
          <!-- 任务列表容器 -->
          <div class="table-wrapper">
            <el-table
              ref="tableContainerRef"
              v-loading="taskListLoading"
              class="table"
              :data="tasks"
              row-key="projTaskId"
              default-expand-all
            >
              <el-table-column type="selection" width="33" fixed="left" />

              <!-- 序号列 -->
              <el-table-column label="#" width="32" align="left" type="">
                <template #default="{ row }">
                  <span class="task-number">{{ row.taskNumber }}</span>
                </template>
              </el-table-column>

              <!-- 各列单独处理，确保筛选图标与列对应 -->
              <template v-for="column in showColumn" :key="column.field">
                <!-- 项目ID列 -->
                <el-table-column
                  v-if="column.field === 'projId'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                  show-overflow-tooltip
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <span>{{ row.projId || '' }}</span>
                  </template>
                </el-table-column>

                <!-- 项目名称列 -->
                <el-table-column
                  v-if="column.field === 'projectName'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                  show-overflow-tooltip
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <span class="project-name">{{
                      row.projectName || ''
                    }}</span>
                  </template>
                </el-table-column>

                <!-- 任务ID列 -->
                <el-table-column
                  v-else-if="column.field === 'projTaskId'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                  show-overflow-tooltip
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <span>{{ row.projTaskId || '' }}</span>
                  </template>
                </el-table-column>

                <!-- 项目任务名称列 -->
                <el-table-column
                  v-else-if="column.field === 'projTaskName'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                  :indent="20"
                  show-overflow-tooltip
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <span class="task-name">{{ row.projTaskName || '' }}</span>
                  </template>
                </el-table-column>

                <!-- 状态列 -->
                <el-table-column
                  v-else-if="column.field === 'projTaskStatus'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    {{ getStatusText(row.projTaskStatus) }}
                  </template>
                </el-table-column>

                <!-- 责任部门列 -->
                <el-table-column
                  v-else-if="column.field === 'projTaskResponsibleDept'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                  show-overflow-tooltip
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <span>{{ row.projTaskResponsibleDept || '' }}</span>
                  </template>
                </el-table-column>

                <!-- 项目角色列 -->
                <el-table-column
                  v-else-if="column.field === 'projTaskResponsibleProjRole'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                  show-overflow-tooltip
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <span>{{ row.projTaskResponsibleProjRole || '' }}</span>
                  </template>
                </el-table-column>

                <!-- 任务类型列 -->
                <el-table-column
                  v-else-if="column.field === 'taskType'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                  show-overflow-tooltip
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <span>{{ getTaskTypeText(row.taskType) }}</span>
                  </template>
                </el-table-column>

                <!-- 专业/系统列 -->
                <el-table-column
                  v-else-if="column.field === 'major'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                  show-overflow-tooltip
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <span>{{ getMajorText(row.major) }}</span>
                  </template>
                </el-table-column>

                <!-- 进度列 -->
                <el-table-column
                  v-else-if="column.field === 'actualProgress'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <el-progress
                      class="progress"
                      color="#52C41A"
                      :stroke-width="12"
                      :percentage="row.actualProgress || 0"
                      :format="formatProgress"
                    />
                  </template>
                </el-table-column>

                <!-- 超期列 -->
                <el-table-column
                  v-else-if="column.field === 'overdueDays'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <el-tag v-if="row.overdueDays > 0" type="danger">是</el-tag>
                    <el-tag v-else type="success">否</el-tag>
                  </template>
                </el-table-column>

                <!-- 责任人列 -->
                <el-table-column
                  v-else-if="column.field === 'responsibleName'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                  show-overflow-tooltip
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <span>{{ row.responsibleName || '' }}</span>
                  </template>
                </el-table-column>

                <!-- 实际开始时间列 -->
                <el-table-column
                  v-else-if="column.field === 'actualStartDate'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <span>{{ formatDate(row.actualStartDate) }}</span>
                  </template>
                </el-table-column>

                <!-- 计划开始日期列 -->
                <el-table-column
                  v-else-if="column.field === 'planStartDate'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <span>{{ formatDate(row.planStartDate) }}</span>
                  </template>
                </el-table-column>

                <!-- 计划完成日期列 -->
                <el-table-column
                  v-else-if="column.field === 'planFinishDate'"
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    <span>{{ formatDate(row.planFinishDate) }}</span>
                  </template>
                </el-table-column>

                <!-- 其他列 -->
                <el-table-column
                  v-else
                  :property="column.field"
                  :label="column.label"
                  :width="column.width"
                  show-overflow-tooltip
                >
                  <template #header>
                    <BtThFilter
                      v-if="column.filter"
                      :title="column.label"
                      :type="column.filter.type"
                      :inputType="column.filter.inputType"
                      :showOrder="column.filter.showOrder"
                      :placeholder="column.filter.placeholder"
                      :options="column.filter.data"
                      v-model="column.filterValue"
                      v-model:order="column.filterOrder"
                      @change="changeFilter($event, column.field)"
                    ></BtThFilter>
                    <span v-else>{{ column.label }}</span>
                  </template>
                  <template #default="{ row }">
                    {{ row[column.field] || '' }}
                  </template>
                </el-table-column>
              </template>

              <!-- 操作列 -->
              <el-table-column
                label="操作"
                v-bind="tableConfig.optColumnAttr"
                fixed="right"
              >
                <template #default="{ row }">
                  <el-dropdown placement="bottom-end">
                    <div class="more-opt">
                      <el-icon class="icon"><MoreFilled /></el-icon>
                    </div>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="handleRemoveTag(row)">
                          取消标签
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 甘特图视图 -->
    <div v-show="curTypeIndex === 1" class="gantt-view">
      <div class="gantt-container">
        <BtGantt
          v-if="viewOptions[1].load"
          ref="ganttRef"
          :config="ganttConfig"
          :dateType="dateType"
          :data="ganttData"
          :filterValue="ganttFilter"
        ></BtGantt>
        <div v-else class="gantt-loading">
          <el-empty description="正在加载甘特图..." />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.task-tags {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .content-wrapper {
    flex: 1;
    display: flex;
    overflow: hidden;
    position: relative;

    .list-view {
      width: 100%;
      height: 100%;
      display: flex;
      overflow: hidden;
      padding: 16px;
    }

    .sidebar {
      width: 300px;
      height: 100%;
      margin-right: 10px;
      flex-shrink: 0;

      :deep(.bt_tree) {
        display: inline-flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        overflow: hidden;
        padding: 0;

        .container {
          flex-grow: 1;
          overflow-y: auto;
          padding: 0;
          height: 100%;
        }
      }

      // 自定义树节点样式
      :deep(.custom-tree-node) {
        display: flex;
        align-items: center;
        width: 100%;

        .color-indicator {
          width: 12px;
          height: 12px;
          margin-right: 6px;
          flex-shrink: 0;
          border: 1px solid var(--el-border-color-lighter);
          border-radius: 2px; // 方形，轻微圆角
        }

        .node-label {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .item {
        margin-left: 16px;
      }

      .icon-tooltip {
        cursor: pointer;
      }
    }
  }

  .cont {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px;
    padding-bottom: 0;
    overflow: hidden;
    min-height: 0; // 确保flex子元素能够正确收缩
    border: 1px solid var(--el-border-color);

    .table-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .table-loading-more,
    .table-no-more {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px 20px;
      color: var(--el-text-color-secondary);
      font-size: 13px;

      transition: all 0.3s ease;

      .el-icon {
        margin-right: 6px;
        font-size: 14px;
      }
    }

    .table-no-more {
      color: var(--el-text-color-placeholder);
    }

    // 鼠标悬停效果
    .table-loading-more:hover {
      background-color: var(--el-fill-color-light);
    }

    .table {
      flex: 1;

      // 表格底部插槽样式
      :deep(.el-table__append-wrapper) {
        border-top: none;
      }

      :deep(.el-table__indent) {
        padding-left: 12px;
      }

      .task-name {
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .task-number {
        color: var(--el-text-color-primary);
      }

      .progress {
        :deep(.el-progress__text) {
          font-size: 12px !important;
        }
      }

      // 表格底部加载状态样式
      .table-loading-more,
      .table-no-more {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        color: #909399;
        font-size: 14px;

        .el-icon {
          margin-right: 8px;
        }
      }

      .table-loading-more {
        color: #409eff;
      }

      .table-no-more {
        color: #909399;
      }
    }
  }

  .footer {
    padding: 10px 20px;
    text-align: right;
  }

  // 甘特图视图样式
  .gantt-view {
    width: 100%;
    height: 100%;
    display: flex;
    overflow: hidden;
    padding: 10px;

    .gantt-container {
      width: 100%;
      height: 100%;
      flex: 1;
      border-radius: 4px;
      overflow: hidden;

      .gantt-loading {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
