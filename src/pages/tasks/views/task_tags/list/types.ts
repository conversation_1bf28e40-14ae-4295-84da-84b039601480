/**
 * 任务标签页面相关类型定义
 * 基于 Swagger API 文档定义，复制自 swagger/output.ts
 */

/**
 * 任务标签响应对象 - 基于 Swagger ConfigIndeTaskTagResponse
 */
export interface TaskTagResponse {
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 创建者ID
   */
  createUser?: number;
  /**
   * 父标签ID或者所属标签组ID
   */
  parentTaskTagId?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 排序码
   */
  sortNum?: number;
  /**
   * 是否是标签组(1 是,0 否)
   */
  tagGroupFlag?: number;
  /**
   * 任务标签颜色
   */
  taskTagColor?: string;
  /**
   * 任务标签描述
   */
  taskTagDesc?: string;
  /**
   * 任务标签ID
   */
  taskTagId?: number;
  /**
   * 任务标签标题
   */
  taskTagName?: string;
  /**
   * 任务标签状态 (1 启用,0 禁用)
   */
  taskTagStatus?: number;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 更新时间
   */
  updateTime?: Date;
  /**
   * 更新者ID
   */
  updateUser?: number;
  /** 子标签列表，用于树形结构 */
  children?: TaskTagResponse[];
}

/**
 * 任务标签查询请求 - 基于 Swagger ConfigIndeTaskTagQueryRequest
 */
export interface TaskTagQueryRequest {
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 创建者ID
   */
  createUser?: number;
  /**
   * 删除标记(0: 未删除, 大于 1: 已删除)
   */
  deleted?: number;
  /**
   * 父标签ID或者所属标签组ID
   */
  parentTaskTagId?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 是否是标签组(1 是,0 否)
   */
  tagGroupFlag?: number;
  /**
   * 任务标签颜色
   */
  taskTagColor?: string;
  /**
   * 任务标签描述
   */
  taskTagDesc?: string;
  /**
   * 任务标签ID
   */
  taskTagId?: number;
  /**
   * 任务标签标题
   */
  taskTagName?: string;
  /**
   * 任务标签状态 (1 启用,0 禁用)
   */
  taskTagStatus?: number;
  /**
   * 租户ID
   */
  tenantId?: number;
  /**
   * 更新时间
   */
  updateTime?: Date;
  /**
   * 更新者ID
   */
  updateUser?: number;
}

/**
 * 任务标签树节点 - 用于BtTree组件
 */
export interface TaskTagTreeNode {
  /** 节点标签，显示名称 */
  label: string;
  /** 节点值，唯一标识 */
  value: number;
  /** 标签颜色 */
  color?: string;
  /** 标签状态 */
  status?: number;
  /** 是否是标签组 */
  isGroup?: boolean;
  /** 子节点列表 */
  children?: TaskTagTreeNode[];
  /** 原始数据 */
  rawData?: TaskTagResponse;
}

/**
 * 任务项响应对象 - 基于 Swagger ProjectTaskResponse
 */
export interface TaskItemResponse {
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务父任务ID */
  parentProjTaskId?: number;
  /** 所属项目ID */
  projId?: number;
  /** 阶段ID */
  phaseProjTaskId?: number;
  /** 项目任务名称 */
  projTaskName?: string;
  /** 项目任务状态(1 未开始,2 进行中,3 已关闭) */
  projTaskStatus?: number;
  /** 项目任务责任人 */
  projTaskResponsible?: number;
  /** 项目任务责任人项目角色 */
  projTaskResponsibleProjRole?: string;
  /** 项目任务责任部门 */
  projTaskResponsibleDept?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期 */
  overdueDays?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业,级联关系待定) */
  taskType?: number;
  /** 任务作业类型(级联关系待定) */
  taskActivityType?: number;
  /** 专业 */
  major?: number;
  /** 系统 */
  systemId?: number;
  /** 项目任务说明 */
  projTaskDesc?: string;
  /** 项目任务验收标准 */
  projTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 项目任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是非阶段(1是,0否) */
  phaseFlag?: number;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 任务标签列表 */
  tags?: TaskTagResponse[];
  /** 参与人列表 */
  participants?: any[];
  /** 附件列表 */
  attaments?: any[];
  /** 前置任务列表 */
  predTasks?: any[];
  /** 责任人姓名 */
  responsibleName?: string;
  /** 项目名称 */
  projectName?: string;
  /** 子任务列表，用于树形结构 */
  children?: TaskItemResponse[];
  /** 任务序号，用于显示 */
  taskNumber?: string;
}

/**
 * 任务列表查询参数
 */
export interface TaskListQueryParams {
  /** 标签ID列表，用于按标签筛选任务 */
  taskTagId?: number;
  /** 关键词搜索 */
  keyword?: string;
  /**查询类型(1全部;2我负责的;3我参与的;4我指派的;5我代办的) */
  queryFlag?: number;
  /** 项目ID筛选 */
  projId?: string;
  /** 项目名称筛选 */
  projectName?: string;
  /** 任务ID筛选 */
  projTaskId?: string;
  /** 任务名称筛选 */
  projTaskName?: string;
  /** 状态筛选 */
  projTaskStatus?: number[];
  /** 责任部门筛选 */
  projTaskResponsibleDept?: string;
  /** 项目角色筛选 */
  projTaskResponsibleProjRole?: string;
  /** 责任人筛选 */
  responsibleName?: string;
  /** 任务类型筛选 */
  taskType?: number[];
  /** 专业筛选 */
  major?: number[];
  /** 实际进度筛选 */
  actualProgress?: number;
  /** 超期筛选 */
  overdueDays?: number[];
  /** 实际开始日期范围 - 开始 */
  beginActualStartDate?: string;
  /** 实际开始日期范围 - 结束 */
  endActualStartDate?: string;
  /** 计划开始日期范围 - 开始 */
  beginPlanStartDate?: string;
  /** 计划开始日期范围 - 结束 */
  endPlanStartDate?: string;
  /** 计划完成日期范围 - 开始 */
  beginPlanFinishDate?: string;
  /** 计划完成日期范围 - 结束 */
  endPlanFinishDate?: string;
  /** 创建时间范围 - 开始 */
  beginCreateTime?: string;
  /** 创建时间范围 - 结束 */
  endCreateTime?: string;
  /** 更新时间范围 - 开始 */
  beginUpdateTime?: string;
  /** 更新时间范围 - 结束 */
  endUpdateTime?: string;
  /** 排序参数 */
  sorts?: SortParam[];
}

/**
 * 排序参数接口
 */
export interface SortParam {
  /** 排序字段名 */
  field: string;
  /** 排序方向：'asc' 或 'desc' */
  direction: string;
}

/**
 * 分页响应结果
 */
export interface PageResult<T> {
  /** 数据列表 */
  list: T[];
  /** 总记录数 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页大小 */
  pageSize: number;
  /** 总页数 */
  totalPages: number;
}

/**
 * 视图类型枚举
 */
export enum ViewType {
  /** 列表视图 */
  LIST = 'list',
  /** 甘特图视图 */
  GANTT = 'gantt',
}

/**
 * 视图选项
 */
export interface ViewOption {
  /** 视图名称 */
  name: string;
  /** 视图类型 */
  value: ViewType;
  /** 是否已加载 */
  load: boolean;
}

/**
 * 甘特图任务数据
 */
export interface GanttTaskData {
  /** 任务ID */
  id: number;
  /** 任务名称 */
  text: string;
  /** 开始时间 (YYYY-MM-DD 格式) */
  start_date: string;
  /** 结束时间 (YYYY-MM-DD 格式) */
  end_date: string;
  /** 进度 (0-1 之间的小数) */
  progress: number;
  /** 父任务ID */
  parent?: number;
  /** 甘特图任务类型 (task/project/milestone) */
  type?: string;
  /** 任务类型 */
  task_type?: string;
  /** 状态 */
  status?: string;
  /** 责任人 */
  responsible_user?: string;
  /** 进度显示文本 */
  progress_display?: string;
  /** 持续时间（天数） */
  duration?: number;
  /** 是否展开 */
  open?: boolean;
  /** 任务颜色 */
  color?: string;
  /** 是否只读 */
  readonly?: boolean;
}

/**
 * 甘特图配置
 */
export interface GanttConfig {
  /** 列配置 */
  columns: Array<{
    name: string;
    label: string;
    width: number;
    tree?: boolean;
  }>;
}

/**
 * API 响应基础类型
 */
export interface ApiResult<T = any> {
  /** 响应状态码 */
  code: number;
  /** 响应消息 */
  message: string;
  /** 响应时间戳 */
  timestamp: string;
  /** 响应数据 */
  data: T;
}

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  /** 未开始 */
  NOT_STARTED = 1,
  /** 进行中 */
  IN_PROGRESS = 2,
  /** 已完成 */
  COMPLETED = 3,
  /** 已关闭 */
  CLOSED = 4,
}

/**
 * 任务状态标签映射
 */
export const TaskStatusLabels = {
  [TaskStatus.NOT_STARTED]: '未开始',
  [TaskStatus.IN_PROGRESS]: '进行中',
  [TaskStatus.COMPLETED]: '已完成',
  [TaskStatus.CLOSED]: '已关闭',
} as const;

/**
 * 标签状态枚举
 */
export enum TagStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1,
}

/**
 * 标签组标识枚举
 */
export enum TagGroupFlag {
  /** 不是标签组 */
  NOT_GROUP = 0,
  /** 是标签组 */
  IS_GROUP = 1,
}
