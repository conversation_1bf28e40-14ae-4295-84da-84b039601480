/**
 * 任务标签页面状态管理
 */

import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import type {
  TaskTagResponse,
  TaskTagTreeNode,
  TaskItemResponse,
  TaskListQueryParams,
  ViewType,
  ViewOption,
  GanttTaskData,
} from './types';
import * as taskTagsService from './TaskTagsService';

export const useTaskTagsStore = defineStore(
  'taskTagsStore',
  () => {
    // ==================== 基础状态 ====================
    const isLoading = ref(false);
    const isInitialized = ref(false);

    // ==================== 标签树相关状态 ====================
    const tagTreeData = ref<TaskTagResponse[]>([]);
    const selectedTagId = ref<number>(0);
    const tagTreeLoading = ref(false);

    // ==================== 任务列表相关状态 ====================
    const taskList = ref<TaskItemResponse[]>([]);
    const taskListLoading = ref(false);
    const taskListTotal = ref(0);
    const currentPage = ref(1);
    const pageSize = ref(20);

    // ==================== 搜索和筛选状态 ====================
    const searchKeyword = ref('');
    const filterParams = ref<TaskListQueryParams>({});

    // ==================== 列配置 ====================
    const columnList = ref([
      {
        field: 'projId',
        label: '项目ID',
        width: 120,
        align: 'left',
        show: false,
        filter: {
          type: 'input' as const,
          showOrder: false,
          placeholder: '请输入项目ID',
        },
        filterValue: '',
      },
      {
        field: 'projectName',
        label: '项目名称',
        width: 144,
        align: 'left',
        show: true,
        filter: { type: 'input' as const, showOrder: false, showSearch: true },
        filterValue: '',
      },
      {
        field: 'projTaskId',
        label: '任务ID',
        width: 144,
        align: 'left',
        show: false,
        filter: {
          type: 'input' as const,
          showOrder: false,
          placeholder: '请输入任务ID',
        },
        filterValue: '',
      },
      {
        field: 'projTaskName',
        label: '任务名称',
        width: 144,
        align: 'left',
        show: true,
        filter: { type: 'input' as const, showOrder: false, showSearch: true },
        filterValue: '',
      },
      {
        field: 'projTaskStatus',
        label: '状态',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox' as const,
          showOrder: false,
          data: [
            { value: 1, label: '未开始' },
            { value: 2, label: '进行中' },
            { value: 3, label: '已关闭' },
          ],
        },
        filterValue: [],
      },
      {
        field: 'projTaskResponsibleDept',
        label: '责任部门',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          showOrder: false,
          placeholder: '请输入责任部门',
        },
        filterValue: '',
      },
      {
        field: 'projTaskResponsibleProjRole',
        label: '项目角色',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          showOrder: false,
          placeholder: '请输入项目角色',
        },
        filterValue: '',
      },
      {
        field: 'responsibleName',
        label: '责任人',
        width: 120,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          showOrder: false,
          placeholder: '请输入责任人',
        },
        filterValue: '',
      },
      {
        field: 'taskType',
        label: '任务类型',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox' as const,
          data: [
            { value: 1, label: '里程碑作业' },
            { value: 2, label: '任务作业' },
            { value: 3, label: 'WBS作业' },
            { value: 4, label: '配合作业' },
          ],
        },
        filterValue: [],
      },
      {
        field: 'major',
        label: '专业',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox' as const,
          data: [
            { value: 1, label: '产品策划' },
            { value: 2, label: '造型开发' },
            { value: 3, label: '平台开发' },
            { value: 4, label: '工程开发' },
            { value: 5, label: '整车工程/总布置' },
            { value: 6, label: '试验认证' },
            { value: 7, label: '生产准备' },
            { value: 8, label: '项目管理/系统工程' },
            { value: 9, label: '新兴技术开发' },
          ],
        },
        filterValue: [],
      },
      {
        field: 'actualStartDate',
        label: '实际开始时间',
        width: 176,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          inputType: 'dateRange' as const,
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'planStartDate',
        label: '计划开始时间',
        width: 176,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          inputType: 'dateRange' as const,
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'planFinishDate',
        label: '计划完成时间',
        width: 176,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          inputType: 'dateRange' as const,
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'actualProgress',
        label: '实际进度',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'input' as const,
          showOrder: true,
          placeholder: '请输入进度值',
        },
        filterValue: '',
        filterOrder: '',
      },
      {
        field: 'overdueDays',
        label: '超期',
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox' as const,
          data: [
            { value: 1, label: '是' },
            { value: 0, label: '否' },
          ],
        },
        filterValue: [],
      },
      {
        field: 'createTime',
        label: '创建时间',
        width: 176,
        align: 'left',
        show: false,
        filter: {
          type: 'input' as const,
          inputType: 'dateRange' as const,
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'updateTime',
        label: '更新时间',
        width: 176,
        align: 'left',
        show: false,
        filter: {
          type: 'input' as const,
          inputType: 'dateRange' as const,
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
    ]);

    // ==================== 视图切换状态 ====================
    const currentViewType = ref<ViewType>('list' as ViewType);
    const viewOptions = ref<ViewOption[]>([
      { name: '任务列表', value: 'list' as ViewType, load: true },
      { name: '甘特图', value: 'gantt' as ViewType, load: false },
    ]);

    // ==================== 计算属性 ====================

    /**
     * 是否还有更多数据
     */
    const hasMore = computed(() => {
      return taskListTotal.value > currentPage.value * pageSize.value;
    });

    /**
     * 转换标签数据为树形结构
     */
    const tagTreeNodes = computed((): TaskTagTreeNode[] => {
      const convertToTreeNodes = (
        tags: TaskTagResponse[]
      ): TaskTagTreeNode[] => {
        return tags.map((tag) => {
          const node: TaskTagTreeNode = {
            label: tag.taskTagName || '未命名标签',
            value: tag.taskTagId || 0,
            color: tag.taskTagColor,
            status: tag.taskTagStatus,
            isGroup: tag.tagGroupFlag === 1,
            rawData: tag,
          };

          // 递归处理子节点
          if (tag.children && tag.children.length > 0) {
            node.children = convertToTreeNodes(tag.children);
          }

          return node;
        });
      };

      return convertToTreeNodes(tagTreeData.value);
    });

    /**
     * 甘特图数据 - 返回 DHTMLX Gantt 需要的格式 { data: [] }
     */
    const ganttData = computed(() => {
      // 递归函数：将嵌套的任务结构展平为甘特图需要的平级数据
      const flattenTasks = (tasks: TaskItemResponse[]): GanttTaskData[] => {
        const result: GanttTaskData[] = [];

        const processTask = (
          task: TaskItemResponse,
          index: number
        ): GanttTaskData => {
          // 格式化日期为甘特图需要的格式 (YYYY-MM-DD)
          const formatDateForGantt = (dateStr: string | undefined): string => {
            if (!dateStr) {
              return new Date().toISOString().split('T')[0];
            }

            try {
              // 如果是 "YYYY-MM-DD HH:mm:ss" 格式，提取日期部分
              if (dateStr.includes(' ')) {
                return dateStr.split(' ')[0];
              }

              // 如果是 ISO 格式，转换为 YYYY-MM-DD
              const date = new Date(dateStr);
              if (!isNaN(date.getTime())) {
                return date.toISOString().split('T')[0];
              }

              // 默认返回今天
              return new Date().toISOString().split('T')[0];
            } catch (error) {
              console.warn('日期格式化失败:', dateStr, error);
              return new Date().toISOString().split('T')[0];
            }
          };

          // 获取开始和结束日期，优先使用计划日期
          const startDate = formatDateForGantt(
            task.planStartDate || task.actualStartDate
          );
          const endDate = formatDateForGantt(
            task.planFinishDate || task.actualFinishDate
          );

          // 确保进度值在 0-1 之间
          const progress = Math.max(
            0,
            Math.min(1, (task.actualProgress || 0) / 100)
          );

          return {
            id: task.projTaskId || index + 1,
            text: task.projTaskName || '未命名任务',
            start_date: startDate,
            end_date: endDate,
            progress: progress, // 甘特图内部进度需要转换为0-1的小数
            parent: task.parentProjTaskId || 0,
            type:
              task.children && task.children.length > 0 ? 'project' : 'task',
            open: false, // 默认不展开子任务，通过展开按钮控制
            // 甘特图显示字段
            status: getTaskStatusText(task.projTaskStatus),
            responsible_user: task.responsibleName || '',
            progress_display: `${task.actualProgress || 0}%`,
            task_type: 'task',
            // 添加额外的甘特图字段
            duration: calculateDuration(startDate, endDate),
            readonly: true, // 只读模式
          };
        };

        // 处理每个任务
        tasks.forEach((task, index) => {
          result.push(processTask(task, index));

          // 递归处理子任务
          if (task.children && task.children.length > 0) {
            result.push(...flattenTasks(task.children));
          }
        });

        return result;
      };

      // DHTMLX Gantt 要求的数据格式：{ data: [], links: [] }
      const taskData = flattenTasks(taskList.value);

      console.log('甘特图数据转换:', {
        原始任务数量: taskList.value.length,
        展平后任务数量: taskData.length,
        任务数据示例: taskData.slice(0, 3),
      });

      return {
        data: taskData,
        links: [], // 暂时不处理任务依赖关系
      };
    });

    /**
     * 计算任务持续时间（天数）
     */
    function calculateDuration(startDate: string, endDate: string): number {
      try {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end.getTime() - start.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays || 1; // 至少1天
      } catch (error) {
        return 1;
      }
    }

    /**
     * 当前选中的标签信息
     */
    const selectedTagsInfo = computed(() => {
      const findTagsById = (
        tags: TaskTagResponse[],
        id: number
      ): TaskTagResponse[] => {
        const result: TaskTagResponse[] = [];

        for (const tag of tags) {
          if (tag.taskTagId === id) {
            result.push(tag);
          }
          if (tag.children && tag.children.length > 0) {
            result.push(...findTagsById(tag.children, id));
          }
        }

        return result;
      };

      return findTagsById(tagTreeData.value, selectedTagId.value);
    });

    // ==================== 方法 ====================

    /**
     * 初始化页面数据
     */
    async function init() {
      if (isInitialized.value) return;

      isLoading.value = true;
      try {
        await Promise.all([loadTagTree(), loadTaskList()]);
        isInitialized.value = true;
      } catch (error) {
        console.error('初始化任务标签页面失败:', error);
      } finally {
        isLoading.value = false;
      }
    }

    /**
     * 加载标签树数据
     */
    async function loadTagTree() {
      tagTreeLoading.value = true;
      try {
        const result = await taskTagsService.getEnabledTaskTags();
        tagTreeData.value = result;
      } catch (error) {
        console.error('加载标签树失败:', error);
        tagTreeData.value = [];
      } finally {
        tagTreeLoading.value = false;
      }
    }

    /**
     * 加载任务列表
     */
    async function loadTaskList() {
      taskListLoading.value = true;

      // 前置检查：如果没有选中任何标签节点，直接返回空数组
      if (!selectedTagId.value || selectedTagId.value === 0) {
        taskList.value = [];
        taskListLoading.value = false;
        return;
      }

      const params: TaskListQueryParams = {
        ...filterParams.value,
        taskTagId: selectedTagId.value,
        keyword: searchKeyword.value || undefined,
      };
      const result = await taskTagsService.getAllTasksByTag(params);

      taskList.value = result;

      taskListLoading.value = false;
    }

    /**
     * 处理标签树节点选择变化
     */
    function handleTagTreeChange(selectedNode: TaskTagTreeNode) {
      selectedTagId.value = selectedNode.value;
      loadTaskList(); // 重新加载任务列表
    }

    /**
     * 设置搜索关键词
     */
    function setSearchKeyword(keyword: string) {
      searchKeyword.value = keyword;
      loadTaskList();
    }

    /**
     * 加载更多任务（无限滚动）
     */
    function loadMoreTasks() {
      if (hasMore.value && !taskListLoading.value) {
        currentPage.value += 1;
        loadTaskList(); // 不重置，追加数据
      }
    }

    /**
     * 切换视图类型
     */
    function switchViewType(viewType: ViewType) {
      currentViewType.value = viewType;

      // 标记甘特图视图为已加载
      if (viewType === 'gantt') {
        const ganttView = viewOptions.value.find((v) => v.value === 'gantt');
        if (ganttView) {
          ganttView.load = true;
        }
      }
    }

    /**
     * 取消标签
     */
    async function cancelTag(task: any) {
      const result = await taskTagsService.removeTag(task, selectedTagId.value);
      if (result) {
        return {
          success: true,
        };
      } else {
        return {
          success: false,
          message: '取消标签失败',
        };
      }
    }

    /**
     * 刷新数据
     */
    async function refresh() {
      await Promise.all([loadTagTree(), loadTaskList()]);
    }

    /**
     * 重置筛选条件
     */
    function resetFilters() {
      selectedTagId.value = 0;
      searchKeyword.value = '';
      filterParams.value = {};
      loadTaskList();
    }

    /**
     * 设置筛选参数
     */
    function setFilterParams(params: any) {
      filterParams.value = params;
    }

    /**
     * 获取任务状态文本
     */
    function getTaskStatusText(status: number | undefined): string {
      const statusMap: Record<number, string> = {
        1: '未开始',
        2: '进行中',
        3: '已完成',
        4: '已关闭',
      };
      return statusMap[status || 1] || '未知';
    }

    /**
     * 获取任务状态类型（用于el-tag）
     */
    function getTaskStatusType(status: number | undefined): string {
      const typeMap: Record<number, string> = {
        1: 'info',
        2: 'warning',
        3: 'success',
        4: 'danger',
      };
      return typeMap[status || 1] || 'info';
    }

    return {
      // 状态
      isLoading,
      isInitialized,
      tagTreeData,
      selectedTagId,
      tagTreeLoading,
      taskList,
      taskListLoading,
      taskListTotal,
      currentPage,
      pageSize,
      searchKeyword,
      filterParams,
      currentViewType,
      viewOptions,
      columnList,

      // 计算属性
      tagTreeNodes,
      ganttData,
      hasMore,
      selectedTagsInfo,

      // 方法
      init,
      loadTagTree,
      loadTaskList,
      handleTagTreeChange,
      setSearchKeyword,
      switchViewType,
      loadMoreTasks,
      refresh,
      resetFilters,
      setFilterParams,
      getTaskStatusText,
      getTaskStatusType,
      cancelTag,
    };
  },
  {
    persist: {
      pick: [
        'selectedTagIds',
        'searchKeyword',
        'currentViewType',
        'columnList',
      ],
      key: 'taskTags_columnList',
    },
  }
);
