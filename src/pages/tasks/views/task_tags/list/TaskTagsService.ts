/**
 * 任务标签页面相关API服务
 */

import { send } from '@/libs/request';
import type {
  TaskTagResponse,
  TaskTagQueryRequest,
  TaskItemResponse,
  TaskListQueryParams,
} from './types';
import {
  taskTagsTaskList,
  taskTags as TASK_TAG_LIST,
  removeIndependentTaskTag,
  removeProjectTaskTag,
} from '@/service_url/tasks';

/**
 * 获取任务标签列表
 * @param params 查询参数
 * @returns 标签列表
 */
export async function getTaskTagList(
  params?: TaskTagQueryRequest
): Promise<TaskTagResponse[]> {
  const response = await send({
    method: 'POST',
    url: TASK_TAG_LIST,
    data: params || {},
  });

  // 检查响应数据是否有效
  if (!response || !Array.isArray(response)) {
    return [];
  }

  const convertedData = buildTagTree(response);
  return convertedData || [];
}

/**
 * 构建标签树形结构
 * @param tags 扁平的标签列表
 * @returns 树形结构的标签列表
 */
function buildTagTree(tags: TaskTagResponse[]): TaskTagResponse[] {
  const tagMap = new Map<number, TaskTagResponse>();
  const rootTags: TaskTagResponse[] = [];

  // 创建标签映射
  tags.forEach((tag) => {
    if (tag.taskTagId) {
      tagMap.set(tag.taskTagId, { ...tag, children: [] });
    }
  });

  // 构建树形结构
  tags.forEach((tag) => {
    const currentTag = tagMap.get(tag.taskTagId!);
    if (!currentTag) return;

    if (tag.parentTaskTagId && tagMap.has(tag.parentTaskTagId)) {
      // 有父标签，添加到父标签的children中
      const parentTag = tagMap.get(tag.parentTaskTagId);
      if (parentTag && parentTag.children) {
        parentTag.children.push(currentTag);
      }
    } else {
      // 没有父标签，是根标签
      rootTags.push(currentTag);
    }
  });

  return rootTags;
}

/**
 * 根据标签获取项目任务列表
 * @param params 查询参数
 * @returns 任务列表
 */
export async function getProjectTasksByTag(
  params: TaskListQueryParams
): Promise<TaskItemResponse[]> {
  // 构建请求数据，包含所有筛选参数
  const requestData: any = {
    taskTagId: params.taskTagId,
    queryFlag: params.queryFlag,
    // 项目相关筛选
    projId: params.projId,
    projectName: params.projectName,
    // 任务相关筛选
    projTaskId: params.projTaskId,
    // 任务名称搜索 - 优先使用搜索关键词，否则使用筛选条件
    projTaskName: params.keyword || params.projTaskName,
    projTaskStatusList: params.projTaskStatus,
    // 责任相关筛选
    projTaskResponsibleDept: params.projTaskResponsibleDept,
    projTaskResponsibleProjRole: params.projTaskResponsibleProjRole,
    responsibleName: params.responsibleName,
    // 分类筛选
    taskTypeList: params.taskType,
    majorList: params.major,
    // 其他筛选
    actualProgress: params.actualProgress,
    overdueDaysList: params.overdueDays,
    // 日期范围参数
    beginActualStartDate: params.beginActualStartDate,
    endActualStartDate: params.endActualStartDate,
    beginPlanStartDate: params.beginPlanStartDate,
    endPlanStartDate: params.endPlanStartDate,
    beginPlanFinishDate: params.beginPlanFinishDate,
    endPlanFinishDate: params.endPlanFinishDate,
    beginCreateTime: params.beginCreateTime,
    endCreateTime: params.endCreateTime,
    beginUpdateTime: params.beginUpdateTime,
    endUpdateTime: params.endUpdateTime,
    // 排序参数
    sorts: params.sorts,
  };

  const response = await send({
    method: 'POST',
    url: taskTagsTaskList,
    data: requestData,
  });

  // 检查响应数据是否有效
  if (!response || !Array.isArray(response)) {
    return [];
  }

  return response;
}

/**
 * 获取所有任务（项目任务 + 独立任务）
 * @param params 查询参数
 * @returns 合并的任务列表
 */
export async function getAllTasksByTag(
  params: TaskListQueryParams
): Promise<TaskItemResponse[]> {
  // 直接调用项目任务获取函数，该函数已经有错误处理
  const taskList = await getProjectTasksByTag(params);
  return taskList;
}

/**
 * 搜索任务标签
 * @param keyword 搜索关键词
 * @returns 匹配的标签列表
 */
export async function searchTaskTags(
  keyword: string
): Promise<TaskTagResponse[]> {
  const params: TaskTagQueryRequest = {
    taskTagName: keyword,
    taskTagStatus: 1, // 只搜索启用的标签
  };
  // 直接调用标签列表获取函数，该函数已经有错误处理
  return await getTaskTagList(params);
}

/**
 * 根据标签ID获取标签详情
 * @param tagId 标签ID
 * @returns 标签详情
 */
export async function getTaskTagById(
  tagId: number
): Promise<TaskTagResponse | null> {
  const params: TaskTagQueryRequest = {
    taskTagId: tagId,
  };
  // 直接调用标签列表获取函数，该函数已经有错误处理
  const result = await getTaskTagList(params);
  return result.length > 0 ? result[0] : null;
}

/**
 * 获取启用的标签列表
 * @returns 启用的标签列表
 */
export async function getEnabledTaskTags(): Promise<TaskTagResponse[]> {
  const params: TaskTagQueryRequest = {
    taskTagStatus: 1, // 只获取启用的标签
  };
  // 直接调用标签列表获取函数，该函数已经有错误处理
  return await getTaskTagList(params);
}

export async function removeTag(task: TaskItemResponse, taskTagId: number) {
  const { projTaskId, projId } = task;
  let res: undefined;
  if (projId && projId !== -1) {
    res = await send({
      method: 'POST',
      url: removeProjectTaskTag,
      data: {
        projTaskId,
        taskTagId,
      },
    });
  } else {
    res = await send({
      method: 'POST',
      url: removeIndependentTaskTag,
      data: {
        taskTagId,
        indeTaskId: projTaskId,
      },
    });
  }
  return res;
}
