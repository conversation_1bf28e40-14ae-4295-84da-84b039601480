/**
 * 任务标签维护页面相关API服务
 */

import { send } from '@/libs/request';
import {
  taskTagsList,
  addTaskTag as addTaskTagUrl,
  editTaskTag as editTaskTagUrl,
  deleteTaskTag as deleteTaskTagUrl,
  batchSaveTaskTag,
} from '@/service_url/tasks';
import type {
  TaskTagManageResponse,
  TaskTagManageQueryRequest,
  TaskTagManageEditRequest,
  TaskTagManageDeleteRequest,
} from './types';

/**
 * 获取任务标签列表
 * @param params 查询参数（包含 taskTagName 用于标签名称搜索）
 * @returns 标签列表
 */
export async function getTaskTagManageList(
  params?: TaskTagManageQueryRequest
): Promise<TaskTagManageResponse[]> {
  const response = await send({
    method: 'POST',
    url: taskTagsList,
    data: params || {},
  });

  // 检查响应数据是否有效
  if (!response || !Array.isArray(response)) {
    return [];
  }

  // 构建树形结构
  const treeData = buildTagTree(response);
  return treeData;
}

/**
 * 新增任务标签
 * @param data 标签数据
 * @returns 操作结果
 */
export async function addTaskTag(
  data: TaskTagManageEditRequest
): Promise<boolean> {
  const response = await send({
    method: 'POST',
    url: addTaskTagUrl,
    data,
  });

  // 验证响应数据，确保操作成功
  if (response === null || response === undefined) {
    throw new Error('新增标签失败：服务器响应异常');
  }

  return true;
}

/**
 * 编辑任务标签
 * @param data 标签数据
 * @returns 操作结果
 */
export async function updateTaskTag(
  data: TaskTagManageEditRequest
): Promise<boolean> {
  const response = await send({
    method: 'POST',
    url: editTaskTagUrl,
    data,
  });

  // 验证响应数据，确保操作成功
  if (response === null || response === undefined) {
    throw new Error('编辑标签失败：服务器响应异常');
  }

  return true;
}

export async function saveTaskTagTree(
  updates: TaskTagManageResponse[]
): Promise<boolean> {
  const response = await send({
    method: 'POST',
    url: batchSaveTaskTag,
    data: updates,
  });

  if (response) {
    return true;
  }
  return false;
}

/**
 * 删除任务标签
 * @param taskTagId 标签ID
 * @returns 操作结果
 */
export async function deleteTaskTag(taskTagId: number): Promise<boolean> {
  const data: TaskTagManageDeleteRequest = {
    taskTagId,
  };
  const response = await send({
    method: 'POST',
    url: deleteTaskTagUrl,
    data,
  });

  // 验证响应数据，确保操作成功
  if (response === null || response === undefined) {
    throw new Error('删除标签失败：服务器响应异常');
  }

  return true;
}

/**
 * 更新任务标签状态
 * @param taskTagId 标签ID
 * @param status 新状态 (1 启用, 0 禁用)
 * @returns 操作结果
 */
export async function updateTaskTagStatus(
  taskTagId: number,
  status: number
): Promise<boolean> {
  // 先获取标签详情
  const tagList = await getTaskTagManageList({ taskTagId });

  // 验证标签是否存在
  if (!tagList || tagList.length === 0) {
    throw new Error('标签不存在');
  }

  const tag = tagList[0];

  // 验证标签数据完整性
  if (!tag.taskTagId || !tag.taskTagName || tag.tagGroupFlag === undefined) {
    throw new Error('标签数据不完整');
  }

  const updateData: TaskTagManageEditRequest = {
    taskTagId: tag.taskTagId,
    parentTaskTagId: tag.parentTaskTagId,
    tagGroupFlag: tag.tagGroupFlag,
    taskTagName: tag.taskTagName,
    taskTagDesc: tag.taskTagDesc,
    taskTagColor: tag.taskTagColor || '#409EFF', // 提供默认颜色
    taskTagStatus: status,
    remark: tag.remark,
  };

  return await updateTaskTag(updateData);
}

/**
 * 构建标签树形结构
 * @param tags 标签列表
 * @returns 树形结构数据
 */
function buildTagTree(tags: TaskTagManageResponse[]): TaskTagManageResponse[] {
  if (!Array.isArray(tags) || tags.length === 0) {
    return [];
  }

  // 创建ID映射
  const tagMap = new Map<number, TaskTagManageResponse>();
  const rootTags: TaskTagManageResponse[] = [];

  // 初始化所有标签，确保children数组存在
  tags.forEach((tag) => {
    if (tag.taskTagId) {
      tagMap.set(tag.taskTagId, { ...tag, children: [] });
    }
  });

  // 构建树形结构
  tags.forEach((tag) => {
    if (!tag.taskTagId) return;

    const currentTag = tagMap.get(tag.taskTagId);
    if (!currentTag) return;

    if (tag.parentTaskTagId && tagMap.has(tag.parentTaskTagId)) {
      // 有父标签，添加到父标签的children中
      const parentTag = tagMap.get(tag.parentTaskTagId);
      if (parentTag && parentTag.children) {
        parentTag.children.push(currentTag);
      }
    } else {
      // 没有父标签，是根标签
      rootTags.push(currentTag);
    }
  });

  // 按sortNum排序
  const sortTags = (tagList: TaskTagManageResponse[]) => {
    tagList.sort((a, b) => (a.sortNum || 0) - (b.sortNum || 0));
    tagList.forEach((tag) => {
      if (tag.children && tag.children.length > 0) {
        sortTags(tag.children);
      }
    });
  };

  sortTags(rootTags);
  return rootTags;
}

/**
 * 获取标签组选项列表（用于下拉选择）
 * @returns 标签组选项
 */
export async function getTagGroupOptions(): Promise<
  Array<{ value: number; label: string }>
> {
  const params: TaskTagManageQueryRequest = {
    tagGroupFlag: 1, // 只获取标签组
    taskTagStatus: 1, // 只获取启用的标签组
  };
  // 直接调用标签列表获取函数，该函数已经有错误处理
  const tagGroups = await getTaskTagManageList(params);

  return tagGroups.map((group) => ({
    value: group.taskTagId!,
    label: group.taskTagName!,
  }));
}

/**
 * 计算新的排序号
 * @param siblings 同级节点列表
 * @param targetIndex 目标位置索引
 * @returns 新的排序号
 */
export function calculateNewSortNum(
  siblings: TaskTagManageResponse[],
  targetIndex: number
): number {
  if (siblings.length === 0) {
    return 1000; // 默认排序号
  }

  if (targetIndex === 0) {
    // 插入到开头
    const firstSortNum = siblings[0].sortNum || 1000;
    return Math.max(firstSortNum - 100, 100);
  }

  if (targetIndex >= siblings.length) {
    // 插入到末尾
    const lastSortNum = siblings[siblings.length - 1].sortNum || 1000;
    return lastSortNum + 100;
  }

  // 插入到中间
  const prevSortNum = siblings[targetIndex - 1].sortNum || 1000;
  const nextSortNum = siblings[targetIndex].sortNum || 1000;
  return Math.floor((prevSortNum + nextSortNum) / 2);
}

/**
 * 验证拖拽操作是否合法
 * @param draggedNode 被拖拽的节点
 * @param targetParent 目标父节点
 * @param allNodes 所有节点列表
 * @returns 验证结果
 */
export function validateDragOperation(
  draggedNode: TaskTagManageResponse,
  targetParent: TaskTagManageResponse | null,
  allNodes: TaskTagManageResponse[]
): { allowed: boolean; reason?: string } {
  // 不能拖拽到自己的子节点下
  if (targetParent && isDescendant(targetParent, draggedNode, allNodes)) {
    return {
      allowed: false,
      reason: '不能将节点拖拽到其子节点下',
    };
  }

  // 不能拖拽到自己
  if (targetParent && targetParent.taskTagId === draggedNode.taskTagId) {
    return {
      allowed: false,
      reason: '不能将节点拖拽到自己',
    };
  }

  return { allowed: true };
}

/**
 * 检查节点是否是另一个节点的后代
 * @param node 要检查的节点
 * @param ancestor 祖先节点
 * @param allNodes 所有节点列表
 * @returns 是否是后代
 */
function isDescendant(
  node: TaskTagManageResponse,
  ancestor: TaskTagManageResponse,
  allNodes: TaskTagManageResponse[]
): boolean {
  if (!node.parentTaskTagId) {
    return false;
  }

  if (node.parentTaskTagId === ancestor.taskTagId) {
    return true;
  }

  const parent = allNodes.find((n) => n.taskTagId === node.parentTaskTagId);
  if (parent) {
    return isDescendant(parent, ancestor, allNodes);
  }

  return false;
}

/**
 * 验证是否可以删除标签
 * @param taskTagId 标签ID
 * @returns 是否可以删除
 */
export async function canDeleteTag(
  taskTagId: number
): Promise<{ canDelete: boolean; reason?: string }> {
  // 首先验证标签是否存在
  const targetTagList = await getTaskTagManageList({ taskTagId });
  if (!targetTagList || targetTagList.length === 0) {
    return {
      canDelete: false,
      reason: '标签不存在',
    };
  }

  // 检查是否有子标签
  const params: TaskTagManageQueryRequest = {
    parentTaskTagId: taskTagId,
  };
  const childTags = await getTaskTagManageList(params);

  // 验证子标签查询结果
  if (!Array.isArray(childTags)) {
    return {
      canDelete: false,
      reason: '验证失败，请稍后重试',
    };
  }

  if (childTags.length > 0) {
    return {
      canDelete: false,
      reason: '该标签组下还有子标签，请先删除子标签',
    };
  }

  // TODO: 这里可以添加更多验证逻辑，比如检查是否有任务在使用该标签

  return { canDelete: true };
}
