<script setup lang="ts">
import {
  onMounted,
  useTemplateRef,
  computed,
  nextTick,
  watch,
  onUnmounted,
  ref,
} from 'vue';
import { storeToRefs } from 'pinia';
import {
  ElButton,
  ElTable,
  ElTableColumn,
  ElSwitch,
  ElInput,
  ElColorPicker,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElCheckbox,
  ElMessage,
} from 'element-plus';
import { MoreFilled } from '@element-plus/icons-vue';
import Sortable from 'sortablejs';
import BtSearch from '@/components/non_biz/bt_search/BtSearch.vue';
import BtColumn from '@/components/non_biz/bt_column/BtColumn.vue';
import { useTaskTagManageStore } from './TaskTagManageStore';

const store = useTaskTagManageStore();
const {
  isLoading,
  searchKeyword,
  columnConfig,
  contextMenu,
  selectedRowIds,
  numberedTagList,
} = storeToRefs(store);

// 监听编辑状态变化，自动聚焦输入框
watch(
  () => store.currentEditingField,
  async (newField) => {
    if (newField.rowId && newField.fieldName) {
      await nextTick();
      // 查找对应的输入框并聚焦
      const inputSelector = `input[data-field="${newField.fieldName}"][data-row="${newField.rowId}"]`;
      const inputElement = document.querySelector(
        inputSelector
      ) as HTMLInputElement;
      if (inputElement) {
        inputElement.focus();
        inputElement.select(); // 选中所有文本
      }
    }
  },
  { immediate: true }
);

// 显示的列
const showColumn = computed(() => {
  return columnConfig.value.filter((item: any) => item.show);
});

// 预设颜色
const presetColors = [
  '#409EFF',
  '#67C23A',
  '#E6A23C',
  '#F56C6C',
  '#909399',
  '#606266',
  '#303133',
  '#C0C4CC',
  '#DCDFE6',
  '#E4E7ED',
  '#EBEEF5',
  '#F2F6FC',
];

// 列配置引用
const columnRef = useTemplateRef<any>('columnRef');

// 搜索处理
function toSearch() {
  store.setSearchKeyword(searchKeyword.value);
}

// 处理操作命令
function handleCommand(command: string, row: any) {
  switch (command) {
    case 'delete':
      store.deleteTag(row);
      break;
  }
}

// 行内编辑辅助函数
function getRowId(row: any): string {
  return String(row.taskTagId || row.tempId || '');
}

// 通过 class 来设置id key 来获取
function getRowClass(rowItem: any): string {
  const row = rowItem.row;
  const className = `row-${row.taskTagId || row.tempId}`;
  return className;
}

function getEditValue(row: any, field: string): string {
  const rowId = getRowId(row);
  const editData = store.getRowEditData(rowId);

  // 如果有编辑数据且该字段有值，使用编辑数据
  if (
    editData?.editingData &&
    editData.editingData[field as keyof typeof editData.editingData] !==
      undefined
  ) {
    return editData.editingData[
      field as keyof typeof editData.editingData
    ] as string;
  }

  // 否则使用原始数据
  return row[field] || '';
}

function updateEditValue(row: any, field: string, value: any) {
  const rowId = getRowId(row);

  // 如果是颜色字段且行不在编辑状态，先启动行编辑
  if (field === 'taskTagColor' && !store.isRowEditing(rowId)) {
    store.startEditRow(row);
  }

  store.updateRowEditData(rowId, field, value);
}

// 处理字段编辑完成（确保数据已保存到编辑缓存）
function handleFieldFinish(row: any, field: string, event: any) {
  // 获取输入框的当前值
  const currentValue = event.target?.value || '';

  // 确保最新值已更新到编辑数据中
  updateEditValue(row, field, currentValue);

  // 然后退出编辑状态
  store.finishCurrentEditingField();
}

// 保存所有正在编辑的行
async function saveAllEditingRows() {
  await store.saveAllTags();
}

// 获取状态字段的值（支持编辑系统）
function getStatusValue(row: any): boolean {
  const rowId = getRowId(row);
  const editData = store.getRowEditData(rowId);

  // 如果有编辑数据，使用编辑数据
  if (
    editData?.editingData &&
    editData.editingData.taskTagStatus !== undefined
  ) {
    return editData.editingData.taskTagStatus === 1;
  }

  // 否则使用原始数据
  return row.taskTagStatus === 1;
}

// 处理状态字段变更
function handleStatusChange(row: any, value: string | number | boolean) {
  const rowId = getRowId(row);
  // 将值转换为布尔值，然后转换为数字状态
  const boolValue = Boolean(value);
  const newStatus = boolValue ? 1 : 0;

  // 如果行不在编辑状态，先启动行编辑
  if (!store.isRowEditing(rowId)) {
    store.startEditRow(row);
  }

  // 更新编辑数据
  store.updateRowEditData(rowId, 'taskTagStatus', newStatus);
}

// 移除重复的初始化调用，统一在下面的onMounted中处理

// ==================== 右键菜单相关 ====================

// 处理表格行右键点击事件
function handleRowRightClick(row: any, _column: any, event: MouseEvent) {
  store.showContextMenu(event, row);
}

// 处理右键菜单选项点击
function handleMenuItemClick(action: string) {
  store.handleContextMenuAction(action as any);
}

// ==================== 复选框相关 ====================

// 计算属性：是否全选
const isAllSelected = computed(() => {
  const visibleRows = numberedTagList.value;
  if (visibleRows.length === 0) return false;
  return visibleRows.every((row: any) => isRowSelected(row));
});

// 计算属性：是否半选状态
const isIndeterminate = computed(() => {
  const visibleRows = numberedTagList.value;
  if (visibleRows.length === 0) return false;
  const selectedCount = visibleRows.filter((row: any) =>
    isRowSelected(row)
  ).length;
  return selectedCount > 0 && selectedCount < visibleRows.length;
});

// 判断行是否被选中
function isRowSelected(row: any): boolean {
  const rowId = getRowId(row);
  return selectedRowIds.value.includes(rowId);
}

// 处理全选/取消全选
function handleSelectAllCustom(checked: any) {
  const isChecked = Boolean(checked);
  const visibleRows = numberedTagList.value;
  const rowIds = visibleRows.map((row: any) => getRowId(row));

  if (isChecked) {
    // 全选：添加所有可见行的ID
    const newSelectedIds = [...new Set([...selectedRowIds.value, ...rowIds])];
    store.selectedRowIds = newSelectedIds;
  } else {
    // 取消全选：移除所有可见行的ID
    store.selectedRowIds = selectedRowIds.value.filter(
      (id: any) => !rowIds.includes(String(id))
    );
  }
}

// 处理单行选择
function handleRowSelectCustom(row: any, checked: any) {
  const isChecked = Boolean(checked);
  const rowId = getRowId(row);

  if (isChecked) {
    // 选中：添加到选中列表
    if (!selectedRowIds.value.includes(rowId)) {
      store.selectedRowIds = [...selectedRowIds.value, rowId];
    }
  } else {
    // 取消选中：从选中列表移除
    store.selectedRowIds = selectedRowIds.value.filter((id) => id !== rowId);
  }
}

// ==================== 拖拽排序相关 ====================

// 表格引用
const tableRef = useTemplateRef<any>('tableRef');
// Sortable 实例
let sortableInstance: Sortable | null = null;
// 存储验证失败的错误信息
let validationError: string | null = null;
// 表格刷新key，用于强制重新渲染
const tableRefreshKey = ref(0);
// 标记是否需要在拖拽结束后恢复数据
let needsRecovery = false;

// 验证拖拽移动
function validateDragMove(evt: any): boolean {
  // 直接将事件对象传递给store中的验证函数
  const validation = store.validateDragMove(evt);

  if (!validation.allowed) {
    // 存储错误信息
    validationError = validation.reason || '不允许此拖拽操作';
    // 设置恢复标记
    needsRecovery = true;

    return false;
  }

  // 清除之前的错误信息和恢复标记
  validationError = null;
  needsRecovery = false;
  return true;
}

// 处理验证失败的情况
function handleValidationFailure() {
  // 检查是否需要恢复且有错误信息
  if (needsRecovery && validationError) {
    // 第一步：恢复数据快照，这会触发Vue的响应式更新重新渲染表格
    store.rollbackDrag();

    // 第二步：强制刷新表格组件，确保DOM完全重新渲染
    tableRefreshKey.value++;

    // 第三步：等待DOM更新完成后重新初始化Sortable实例
    nextTick(() => {
      // 销毁当前的Sortable实例
      destroyDragSort();
      // 重新初始化Sortable实例
      initDragSort();
    });

    // 第四步：显示错误提示
    ElMessage.warning(validationError);

    // 第五步：重置状态
    validationError = null;
    needsRecovery = false;
  }
}

// 初始化拖拽功能 - 简单的表格行拖拽
function initDragSort() {
  nextTick(() => {
    const tableEl = tableRef.value?.$el;
    if (!tableEl) {
      return;
    }

    const tbody = tableEl.querySelector('.el-table__body-wrapper tbody');
    if (!tbody) {
      return;
    }

    // 为表格创建Sortable实例
    sortableInstance = Sortable.create(tbody, {
      animation: 150,
      handle: '.checkbox-drag-wrapper',
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',
      onMove: validateDragMove, // 启用移动验证
      onStart: () => {
        store.startDrag();
        // 清除之前的错误信息和恢复标记
        validationError = null;
        needsRecovery = false;
      },
      onEnd: (evt) => {
        if (needsRecovery) {
          handleValidationFailure();
          return;
        }
        handleDragEnd(evt);
      },
    });
  });
}

// 处理拖拽结束
function handleDragEnd(evt: any) {
  const { item, oldIndex, newIndex } = evt;

  if (oldIndex === newIndex) {
    store.rollbackDrag();
    return;
  }

  const flattenTagList = store.flattenTagList(store.tagList);

  // 根据 index 获取对应的标签
  const draggedTag = flattenTagList[oldIndex];
  const targetTag = flattenTagList[newIndex];

  // 从DOM元素中提取ID
  const draggedId = extractIdFromElement(item);
  if (!draggedId || getRowId(draggedTag) !== draggedId) {
    console.error('拖拽元素ID不匹配');
    store.rollbackDrag();
    return;
  }

  // 判断是插入到目标元素之前还是之后
  const insertAfter = newIndex > oldIndex;

  store.handleDragSort(draggedTag, targetTag, insertAfter);
}

// 从DOM元素的className中提取ID
function extractIdFromElement(element: HTMLElement): string | null {
  const className = element.className;
  const match = className.match(/row-(.+?)(?:\s|$)/);
  return match ? match[1] : null;
}

// 拖拽功能已简化，不需要额外的嵌套处理函数

// 销毁拖拽实例
function destroyDragSort() {
  if (sortableInstance) {
    sortableInstance.destroy();
    sortableInstance = null;
  }
}

// 组件挂载时初始化
onMounted(() => {
  store.init();
  // 等待数据加载完成后初始化拖拽
  nextTick(() => {
    initDragSort();
  });
});

// 组件卸载时清理
onUnmounted(() => {
  destroyDragSort();
});
</script>

<template>
  <div class="task-tag-manage" v-loading="isLoading">
    <!-- 页面标题和操作按钮 -->
    <div class="header">
      <div class="title">任务标签维护</div>
      <div class="right-opt flex flex-center">
        <el-button class="btn" @click="store.createNewTagGroup">
          新建任务标签组
        </el-button>
        <el-button class="btn" @click="store.createNewTaskTag">
          新建任务标签
        </el-button>
        <!-- 保存按钮 - 始终显示 -->
        <el-button class="btn" @click="saveAllEditingRows"> 保存 </el-button>
        <BtSearch
          class="item"
          v-model="searchKeyword"
          placeholder="请输入标签名称搜索"
          @search="toSearch"
          @clear="toSearch"
        />
        <BtColumn ref="columnRef" v-model="columnConfig" />
      </div>
    </div>

    <!-- 标签列表表格 -->
    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        v-loading="isLoading"
        class="table"
        :key="tableRefreshKey"
        :data="numberedTagList"
        :row-key="getRowId"
        :row-class-name="getRowClass"
        default-expand-all
        @row-contextmenu="handleRowRightClick"
        @selection-change="store.handleSelectionChange"
      >
        <!-- 自定义复选框列，集成拖拽功能 -->
        <el-table-column
          width="48"
          align="center"
          class-name="checkbox-drag-column"
          type=""
        >
          <template #header>
            <el-checkbox
              :model-value="isAllSelected"
              :indeterminate="isIndeterminate"
              @change="handleSelectAllCustom"
              size="small"
            />
          </template>
          <template #default="{ row }">
            <div class="checkbox-drag-wrapper" title="点击选择，拖拽排序">
              <el-checkbox
                :model-value="isRowSelected(row)"
                @change="(checked: any) => handleRowSelectCustom(row, checked)"
                size="small"
              />
            </div>
          </template>
        </el-table-column>

        <!-- 序号列 -->
        <el-table-column label="#" width="32" align="left" type="">
          <template #default="{ row }">
            <span class="task-number">{{ row.tagNumber }}</span>
          </template>
        </el-table-column>

        <!-- 动态列渲染 -->
        <template v-for="column in showColumn" :key="column.field">
          <!-- 任务标签组列 -->
          <el-table-column
            v-if="column.field === 'taskTagGroup'"
            :label="column.label"
            :minWidth="column.minWidth"
            :indent="20"
            show-overflow-tooltip
            type=""
          >
            <template #default="{ row }">
              <div v-if="row.tagGroupFlag === 1" style="width: 100%">
                <!-- 编辑模式 -->
                <el-input
                  v-if="store.isFieldEditing(row, 'taskTagName')"
                  :model-value="getEditValue(row, 'taskTagName')"
                  @input="updateEditValue(row, 'taskTagName', $event)"
                  @blur="handleFieldFinish(row, 'taskTagName', $event)"
                  @keyup.enter="handleFieldFinish(row, 'taskTagName', $event)"
                  @keyup.esc="store.cancelCurrentEditingField()"
                  size="small"
                  :data-field="'taskTagName'"
                  :data-row="getRowId(row)"
                  style="width: 100%"
                />
                <!-- 显示模式 -->
                <span
                  v-else
                  class="tag-group-name"
                  @dblclick="store.startEditField(row, 'taskTagName')"
                >
                  {{ getEditValue(row, 'taskTagName') }}
                </span>
              </div>
              <span v-else class="na-text"></span>
            </template>
          </el-table-column>

          <!-- 任务标签列 -->
          <el-table-column
            v-else-if="column.field === 'taskTagName'"
            :label="column.label"
            :minWidth="column.minWidth"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div v-if="row.tagGroupFlag === 0" style="width: 100%">
                <!-- 编辑模式 -->
                <el-input
                  v-if="store.isFieldEditing(row, 'taskTagName')"
                  :model-value="getEditValue(row, 'taskTagName')"
                  @input="updateEditValue(row, 'taskTagName', $event)"
                  @blur="handleFieldFinish(row, 'taskTagName', $event)"
                  @keyup.enter="handleFieldFinish(row, 'taskTagName', $event)"
                  @keyup.esc="store.cancelCurrentEditingField()"
                  size="small"
                  :data-field="'taskTagName'"
                  :data-row="getRowId(row)"
                  style="width: 100%"
                />
                <!-- 显示模式 -->
                <span
                  v-else
                  class="tag-name"
                  @dblclick="store.startEditField(row, 'taskTagName')"
                >
                  {{ getEditValue(row, 'taskTagName') }}
                </span>
              </div>
              <span v-else class="na-text">N/A</span>
            </template>
          </el-table-column>

          <!-- 标签描述列 -->
          <el-table-column
            v-else-if="column.field === 'taskTagDesc'"
            :label="column.label"
            :minWidth="column.minWidth"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div v-if="row.tagGroupFlag === 0" style="width: 100%">
                <!-- 编辑模式 -->
                <el-input
                  v-if="store.isFieldEditing(row, 'taskTagDesc')"
                  :model-value="getEditValue(row, 'taskTagDesc')"
                  @input="updateEditValue(row, 'taskTagDesc', $event)"
                  @blur="handleFieldFinish(row, 'taskTagDesc', $event)"
                  @keyup.enter="handleFieldFinish(row, 'taskTagDesc', $event)"
                  @keyup.esc="store.cancelCurrentEditingField()"
                  size="small"
                  placeholder="请输入标签描述"
                  :data-field="'taskTagDesc'"
                  :data-row="getRowId(row)"
                  style="width: 100%"
                />
                <!-- 显示模式 -->
                <span
                  v-else
                  @dblclick="store.startEditField(row, 'taskTagDesc')"
                >
                  {{ getEditValue(row, 'taskTagDesc') || '-' }}
                </span>
              </div>
              <span v-else class="na-text">N/A</span>
            </template>
          </el-table-column>

          <!-- 颜色列 -->
          <el-table-column
            v-else-if="column.field === 'taskTagColor'"
            :label="column.label"
            :width="column.width"
            align="center"
          >
            <template #default="{ row }">
              <div v-if="row.tagGroupFlag === 1" class="na-text">N/A</div>
              <div v-else class="color-edit">
                <!-- 颜色选择器始终可编辑 -->
                <el-color-picker
                  :model-value="getEditValue(row, 'taskTagColor')"
                  @change="updateEditValue(row, 'taskTagColor', $event)"
                  :predefine="presetColors"
                  size="small"
                />
                <span class="color-value">{{
                  getEditValue(row, 'taskTagColor')
                }}</span>
              </div>
            </template>
          </el-table-column>

          <!-- 状态列 -->
          <el-table-column
            v-else-if="column.field === 'taskTagStatus'"
            :label="column.label"
            :width="column.width"
            align="center"
          >
            <template #default="{ row }">
              <el-switch
                :model-value="getStatusValue(row)"
                @change="handleStatusChange(row, $event)"
                :active-value="true"
                :inactive-value="false"
              />
            </template>
          </el-table-column>
        </template>

        <!-- 操作列 -->
        <el-table-column label="操作" width="48" align="center" fixed="right">
          <template #default="{ row }">
            <el-dropdown
              placement="bottom-end"
              @command="(command: string) => handleCommand(command, row)"
            >
              <div class="more-opt">
                <el-icon class="icon"><MoreFilled /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="dropdown-menu">
                  <el-dropdown-item command="delete">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 自定义右键菜单 -->
    <div
      v-show="contextMenu.visible"
      class="context-menu"
      :style="{
        position: 'fixed',
        left: contextMenu.position.x + 'px',
        top: contextMenu.position.y + 'px',
        zIndex: 9999,
      }"
      @click.stop
    >
      <div class="context-menu-list">
        <div
          v-for="option in contextMenu.options"
          :key="option.action"
          :class="{
            'context-menu-item': true,
            disabled: !option.enabled,
          }"
          @click="handleMenuItemClick(option.action)"
        >
          {{ option.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.task-tag-manage {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 48px;
  border-bottom: 1px solid var(--border-color);

  .title {
    font-size: var(--el-font-size-base);
    font-weight: 500;
    height: var(--el-tabs-header-height);
  }

  .right-opt {
    .item {
      margin-left: var(--base-margin);
    }

    .btn {
      margin-left: var(--base-margin);
      height: 32px;
      padding: 0 16px;
      font-size: 14px;
      border-radius: 4px;
    }

    :deep(.bt-search) {
      width: 240px;
    }

    :deep(.bt-column) {
      .el-button {
        height: 32px;
        padding: 0 12px;
        font-size: 14px;
      }
    }
  }
}

.table-wrapper {
  flex: 1;
  overflow: auto;
  padding: 16px;

  :deep(.el-table__header) {
    th {
      background-color: #fafafa;
      color: #161616;
      font-weight: 500;
      border-bottom: 1px solid #e4e7ed;
    }
  }

  :deep(.el-table__body) {
    tr {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }

  :deep(.el-table__row) {
    .cell {
      display: inline-flex;

      .el-color-picker__trigger {
        border: none;

        .el-color-picker__color {
          border: none;
        }

        .el-icon.el-color-picker__icon.is-icon-arrow-down {
          display: none;
        }
      }
    }
    .el-table__expand-icon.el-table__expand-icon--expanded {
      display: none;
    }
    .el-table__placeholder {
      display: none;
    }
  }
}

.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-block {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.color-value {
  font-size: 12px;
  color: #606266;
}

.tag-group-name {
  font-weight: 600;
  color: #303133;
}

.tag-name {
  color: #606266;
}

.na-text {
  color: #c0c4cc;
  font-style: italic;
}

.color-picker-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-preview {
  font-size: 14px;
  color: #606266;
  font-family: monospace;
}

.task-number {
  color: #606266;
  font-size: 14px;
}

.more-opt {
  position: relative;
  width: 32px;
  height: 32px;
  margin-left: -100%;
  outline: unset;

  .icon {
    position: absolute;
    top: 50%;
    left: 100%;
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    font-size: 16px;
    font-weight: bold;
    transform: translateY(-50%) rotate(90deg);
    cursor: pointer;
    border-radius: var(--border-radius);
  }

  &:hover {
    .icon {
      background: var(--hover-bg-color);
    }
  }

  &:active {
    .icon {
      box-shadow: var(--active-shadow);
    }
  }
}

/* 弹窗表单样式 - 参考AddTask */
.form {
  .row {
    display: flex;
    gap: 20px;

    .el-form-item {
      flex: 1;
    }
  }

  .select {
    width: 100%;
  }

  .date-select {
    width: 100%;
  }

  .w-100 {
    width: 100%;
  }
}

/* 下拉菜单样式 */
.dropdown-menu {
  min-width: 100px;
}

/* 通用flex样式 */
.flex {
  display: flex;
}

.flex-center {
  align-items: center;
}

/* 行内编辑样式 */
.edit-actions {
  display: flex;
  gap: 8px;
  justify-content: center;

  .el-button {
    padding: 4px 8px;
    font-size: 12px;
  }
}

.color-edit {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;

  .color-value {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
}

.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  cursor: pointer;

  &:hover {
    background-color: var(--el-fill-color-light);
    border-radius: 4px;
    padding: 2px 4px;
  }
}

.color-block {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  border: 1px solid var(--el-border-color);
}

.color-value {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 可编辑元素的悬停效果 */
.tag-group-name,
.tag-name {
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--el-fill-color-light);
  }
}

/* 编辑状态下的行高亮 */
:deep(.el-table__row) {
  &.editing-row {
    background-color: var(--el-color-primary-light-9);
  }
}

/* 复选框拖拽功能样式 */
.checkbox-drag-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
  cursor: grab;
  transition: all 0.2s;

  &:hover {
    background-color: var(--el-color-primary-light-9);
  }

  &:active {
    cursor: grabbing;
  }
}

/* Sortable.js 拖拽样式 */
:deep(.sortable-ghost) {
  opacity: 0.5;
  background-color: var(--el-color-primary-light-9);
}

:deep(.sortable-chosen) {
  background-color: var(--el-color-primary-light-8);
}

:deep(.sortable-drag) {
  opacity: 0.8;
  transform: rotate(5deg);
}

/* 复选框拖拽列样式 */
:deep(.checkbox-drag-column) {
  .cell {
    padding: 0; /* 移除默认padding，让拖拽区域更大 */
  }
}

/* 保持向后兼容的复选框样式 */
.checkbox-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自定义右键菜单样式 */
.context-menu {
  .context-menu-list {
    min-width: 120px;
    background: #ffffff;
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 4px 0;

    .context-menu-item {
      padding: 8px 16px;
      font-size: 14px;
      color: var(--el-text-color-regular);
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: var(--el-fill-color-light);
      }

      &.disabled {
        color: var(--el-text-color-disabled);
        cursor: not-allowed;

        &:hover {
          background-color: transparent;
        }
      }
    }
  }
}
</style>
