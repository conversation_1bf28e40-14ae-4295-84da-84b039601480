/**
 * 任务标签维护页面状态管理
 */

import { ref, computed, nextTick } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage, ElMessageBox } from 'element-plus';
import type {
  TaskTagManageResponse,
  TaskTagFormData,
  TagGroupOption,
  TempRowData,
  InlineEditData,
  ContextMenuState,
  ContextMenuOption,
  DragMoveValidationResult,
} from './types';
import { RowEditState, ContextMenuAction, DragState } from './types';
import * as taskTagManageService from './TaskTagManageService';

export const useTaskTagManageStore = defineStore(
  'taskTagManageStore',
  () => {
    // ==================== 基础状态 ====================
    const isLoading = ref(false);
    const isInitialized = ref(false);

    // ==================== 标签列表相关状态 ====================
    const tagList = ref<TaskTagManageResponse[]>([]);
    const originalTagList = ref<TaskTagManageResponse[]>([]);
    const selectedTagIds = ref<number[]>([]);
    const selectedRowIds = ref<(number | string)[]>([]); // 支持临时行和正式行

    // ==================== 行内编辑相关状态 ====================
    const editingRows = ref<Map<string | number, InlineEditData>>(new Map());
    const tempRowCounter = ref(0);

    // ==================== 拖拽排序相关状态 ====================
    const dragState = ref<DragState>(DragState.IDLE);
    const dragOriginalData = ref<TaskTagManageResponse[]>([]);
    const isDragging = ref(false);

    // ==================== 搜索和过滤状态 ====================
    const searchKeyword = ref('');
    const columnConfig = ref([
      {
        field: 'taskTagGroup',
        label: '任务标签组',
        minWidth: 144,
        align: 'left',
        show: true,
      },
      {
        field: 'taskTagName',
        label: '任务标签',
        minWidth: 144,
        align: 'left',
        show: true,
      },
      {
        field: 'taskTagDesc',
        label: '标签描述',
        minWidth: 144,
        align: 'left',
        show: true,
      },
      {
        field: 'taskTagColor',
        label: '标签颜色',
        width: 144,
        align: 'left',
        show: true,
      },
      {
        field: 'taskTagStatus',
        label: '状态',
        width: 144,
        align: 'left',
        show: true,
      },
    ]);

    // ==================== 表单相关状态（保留用于其他功能） ====================

    const formLoading = ref(false);
    const tagGroupOptions = ref<TagGroupOption[]>([]);

    // ==================== 右键菜单状态 ====================
    const contextMenu = ref<ContextMenuState>({
      visible: false,
      position: { x: 0, y: 0 },
      currentRow: null,
      options: [],
    });

    // ==================== 计算属性 ====================
    // 生成层级序号的函数
    function generateTagNumbers(
      tags: TaskTagManageResponse[],
      parentNumber: string = ''
    ): TaskTagManageResponse[] {
      const tagList = tags.map((tag, index) => {
        const currentNumber = parentNumber
          ? `${parentNumber}.${index + 1}`
          : `${index + 1}`;
        const updatedTag = {
          ...tag,
          tagNumber: currentNumber,
        };

        if (tag.children && tag.children.length > 0) {
          updatedTag.children = generateTagNumbers(tag.children, currentNumber);
        }

        return updatedTag;
      });

      return tagList;
    }

    const numberedTagList = computed(() => {
      const baseList = tagList.value;

      // 为过滤后的数据生成序号
      console.log('🚀 ~ numberedTagList ~ generateTagNumbers:');
      const result = generateTagNumbers(baseList);
      return result;
    });

    const visibleColumns = computed(() => {
      return columnConfig.value.filter((col) => col.show);
    });

    const hasSelectedTags = computed(() => selectedTagIds.value.length > 0);

    // ==================== 行内编辑相关计算属性 ====================
    // 获取当前选中的行数据
    const selectedRows = computed(() => {
      return tagList.value.filter((tag) =>
        selectedTagIds.value.includes(tag.taskTagId!)
      );
    });

    // 检查是否有行正在编辑
    const hasEditingRows = computed(() => {
      return editingRows.value.size > 0;
    });

    // ==================== 初始化方法 ====================
    async function init() {
      if (isInitialized.value) return;

      await loadTagList();

      isInitialized.value = true;
    }

    // ==================== 数据加载方法 ====================
    async function loadTagList() {
      isLoading.value = true;
      try {
        // 构建查询参数，包含搜索关键词
        const params = searchKeyword.value
          ? { taskTagName: searchKeyword.value }
          : undefined;
        const result = await taskTagManageService.getTaskTagManageList(params);

        tagList.value = result;

        originalTagList.value = tagList.value;
        regenerateTagNumbers();
      } catch (error) {
        ElMessage.error('加载标签列表失败');
        tagList.value = [];
        originalTagList.value = [];
      } finally {
        isLoading.value = false;
      }
    }

    async function loadTagGroupOptions() {
      try {
        const options = await taskTagManageService.getTagGroupOptions();
        tagGroupOptions.value = options.map((option) => ({
          value: option.value,
          label: option.label,
        }));
      } catch (error) {
        tagGroupOptions.value = [];
      }
    }

    // ==================== CRUD操作方法 ====================

    /**
     * 删除本地临时标签
     */
    function deleteLocalTag(tag: TaskTagManageResponse) {
      const tagId = tag.taskTagId || (tag as any).tempId;

      // 递归删除函数
      function removeFromArray(tags: TaskTagManageResponse[]): boolean {
        for (let i = 0; i < tags.length; i++) {
          const item = tags[i];

          // 检查是否是要删除的标签
          const itemId = item.taskTagId || (item as any).tempId;
          if (itemId === tagId) {
            tags.splice(i, 1);
            return true;
          }

          // 递归检查子标签
          if (item.children && item.children.length > 0) {
            if (removeFromArray(item.children)) {
              return true;
            }
          }
        }
        return false;
      }

      // 从tagList中删除
      removeFromArray(tagList.value);

      // 如果有编辑状态，也要清除
      const rowId = String(tagId);
      if (editingRows.value.has(rowId)) {
        editingRows.value.delete(rowId);
      }

      // 清除当前编辑字段状态
      if (currentEditingField.value.rowId === rowId) {
        currentEditingField.value = { rowId: null, fieldName: null };
      }
    }

    async function deleteTag(tag: TaskTagManageResponse) {
      try {
        // 检查是否为临时行（本地创建的）
        const isTemp = (tag as any).isTemp || !tag.taskTagId;

        if (isTemp) {
          // 临时行：直接本地删除，不调用API
          await ElMessageBox.confirm(
            `确定要删除标签"${tag.taskTagName}"吗？`,
            '删除确认',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          );

          // 从数据结构中删除临时行
          deleteLocalTag(tag);
          ElMessage.success('删除标签成功');
        } else {
          // 正式行：调用API删除
          // 验证是否可以删除
          const validation = await taskTagManageService.canDeleteTag(
            tag.taskTagId!
          );
          if (!validation.canDelete) {
            ElMessage.warning(validation.reason || '无法删除该标签');
            return;
          }

          await ElMessageBox.confirm(
            `确定要删除标签"${tag.taskTagName}"吗？`,
            '删除确认',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          );

          await taskTagManageService.deleteTaskTag(tag.taskTagId!);
          ElMessage.success('删除标签成功');
          await loadTagList();
        }
      } catch (error: any) {
        if (error !== 'cancel') {
          ElMessage.error('删除标签失败');
        }
      }
    }

    // ==================== 表格操作方法 ====================
    function handleSelectionChange(selection: TaskTagManageResponse[]) {
      // 保持原有的 selectedTagIds 逻辑（用于批量删除等功能）
      selectedTagIds.value = selection
        .map((item) => item.taskTagId!)
        .filter(Boolean);

      // 新增支持临时行的选择逻辑
      selectedRowIds.value = selection
        .map((item) => {
          // 优先使用 taskTagId，如果没有则使用 tempId
          return item.taskTagId || (item as any).tempId;
        })
        .filter(Boolean);
    }

    async function batchDelete() {
      if (selectedRowIds.value.length === 0) {
        ElMessage.warning('请先选择要删除的标签');
        return;
      }

      try {
        await ElMessageBox.confirm(
          `确定要删除选中的 ${selectedRowIds.value.length} 个标签吗？`,
          '批量删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );

        // 分离临时行和正式行
        const tempIds: string[] = [];
        const realIds: number[] = [];

        for (const id of selectedRowIds.value) {
          if (typeof id === 'string') {
            tempIds.push(id);
          } else {
            realIds.push(id);
          }
        }

        // 删除临时行（本地删除）
        for (const tempId of tempIds) {
          const tempTag = findTagByIdOrTempId(tagList.value, undefined, tempId);
          if (tempTag) {
            deleteLocalTag(tempTag);
          }
        }

        // 删除正式行（调用API）
        for (const tagId of realIds) {
          await taskTagManageService.deleteTaskTag(tagId);
        }

        ElMessage.success('批量删除成功');
        selectedTagIds.value = [];
        selectedRowIds.value = [];

        // 如果有正式行被删除，重新加载列表
        if (realIds.length > 0) {
          await loadTagList();
        }
      } catch (error: any) {
        if (error !== 'cancel') {
          console.error('批量删除失败:', error);
          ElMessage.error('批量删除失败');
        }
      }
    }

    // ==================== 搜索和过滤方法 ====================
    function setSearchKeyword(keyword: string) {
      searchKeyword.value = keyword;
      // 触发数据重新加载
      loadTagList();
    }

    function clearSearch() {
      searchKeyword.value = '';
    }

    function updateColumnConfig(newConfig: any[]) {
      columnConfig.value = newConfig;
    }

    function toggleColumn(field: string) {
      const column = columnConfig.value.find((col) => col.field === field);
      if (column) {
        column.show = !column.show;
      }
    }

    // ==================== 刷新方法 ====================
    async function refresh() {
      await loadTagList();
    }

    // ==================== 行内编辑相关方法 ====================
    // 当前编辑的字段状态
    const currentEditingField = ref<{
      rowId: string | number | null;
      fieldName: string | null;
    }>({
      rowId: null,
      fieldName: null,
    });

    // 开始编辑特定字段
    function startEditField(row: TaskTagManageResponse, fieldName: string) {
      // 如果已经有字段在编辑，先完成编辑（不保存到服务器）
      if (
        currentEditingField.value.rowId &&
        currentEditingField.value.fieldName
      ) {
        finishCurrentEditingField();
      }

      // 使用与getRowId一致的逻辑来获取rowId
      const rowId = String(
        row.taskTagId || (row as any).tempId || `temp_${tempRowCounter.value++}`
      );

      // 设置当前编辑的字段
      currentEditingField.value = {
        rowId,
        fieldName,
      };

      // 如果行编辑数据不存在，创建它
      if (!editingRows.value.has(rowId)) {
        const editData: InlineEditData = {
          rowId,
          editState: RowEditState.EDITING,
          editingData: {
            taskTagName: row.taskTagName || '',
            taskTagDesc: row.taskTagDesc || '',
            taskTagColor: row.taskTagColor || '#409EFF',
            taskTagStatus: row.taskTagStatus ?? 1, // 使用空值合并运算符，避免0值被替换
            tagGroupFlag: row.tagGroupFlag ?? 0, // 使用空值合并运算符，避免0值被替换
            parentTaskTagId: row.parentTaskTagId,
            remark: row.remark || '',
          },
          originalData: { ...row },
        };
        editingRows.value.set(rowId, editData);
      }
    }

    // 检查特定字段是否在编辑
    function isFieldEditing(
      row: TaskTagManageResponse,
      fieldName: string
    ): boolean {
      // 使用与getRowId一致的逻辑来获取rowId，但不生成新的临时ID
      const rowId = String(row.taskTagId || (row as any).tempId || '');
      return (
        currentEditingField.value.rowId === rowId &&
        currentEditingField.value.fieldName === fieldName
      );
    }

    // 完成当前字段编辑（失焦，不保存到服务器）
    function finishCurrentEditingField() {
      if (currentEditingField.value.rowId) {
        currentEditingField.value = { rowId: null, fieldName: null };
      }
    }

    // 保存当前编辑的字段（调用API保存）
    async function saveCurrentEditingField() {
      if (currentEditingField.value.rowId) {
        await saveEditRow(currentEditingField.value.rowId);
        currentEditingField.value = { rowId: null, fieldName: null };
      }
    }

    // 取消当前字段编辑
    function cancelCurrentEditingField() {
      if (currentEditingField.value.rowId) {
        cancelEditRow(currentEditingField.value.rowId);
        currentEditingField.value = { rowId: null, fieldName: null };
      }
    }

    // 开始编辑行
    function startEditRow(row: TaskTagManageResponse) {
      // 使用与getRowId一致的逻辑来获取rowId，确保编辑状态管理的一致性
      const rowId = String(
        row.taskTagId || (row as any).tempId || `temp_${tempRowCounter.value++}`
      );

      // 检查是否已经存在编辑数据，如果存在则保留用户的修改
      const existingEditData = editingRows.value.get(rowId);

      if (existingEditData) {
        // 如果已经存在编辑数据，只更新编辑状态，不覆盖用户的修改
        existingEditData.editState = RowEditState.EDITING;
        return;
      }

      // 创建新的编辑数据
      const editData: InlineEditData = {
        rowId,
        editState: RowEditState.EDITING,
        editingData: {
          taskTagName: row.taskTagName || '',
          taskTagDesc: row.taskTagDesc || '',
          taskTagColor: row.taskTagColor || '#409EFF',
          taskTagStatus: row.taskTagStatus ?? 1, // 使用空值合并运算符，避免0值被替换
          tagGroupFlag: row.tagGroupFlag ?? 0, // 使用空值合并运算符，避免0值被替换
          parentTaskTagId: row.parentTaskTagId,
          remark: row.remark || '',
        },
        originalData: { ...row },
      };

      editingRows.value.set(rowId, editData);
    }

    // 取消编辑行
    function cancelEditRow(rowId: string | number) {
      const editData = editingRows.value.get(rowId);
      if (editData && editData.editState === RowEditState.CREATING) {
        // 如果是新建行，直接从列表中移除
        removeNewRow(rowId);
      }
      editingRows.value.delete(rowId);
    }

    // 保存所有标签，树表
    async function saveAllTags() {
      const data = tagList.value;
      function mergeData(
        data: TaskTagManageResponse[]
      ): TaskTagManageResponse[] {
        return data.map((item) => {
          const id = item.taskTagId || (item as any).tempId;
          const children = item.children;
          const editData = getRowEditData(String(id))?.editingData;
          const _data = Object.assign(item, editData || {});
          if (children?.length) {
            return Object.assign(_data, {
              children: mergeData(children),
            });
          }
          return _data;
        });
      }

      const requestData = mergeData(data);

      const success = await taskTagManageService.saveTaskTagTree(requestData);

      success && ElMessage.success('保存成功');

      loadTagList();
    }

    // 保存编辑行
    async function saveEditRow(rowId: string | number) {
      const editData = editingRows.value.get(rowId);
      if (!editData) return;

      try {
        const requestData = {
          taskTagId:
            editData.editState === RowEditState.CREATING
              ? undefined
              : Number(rowId),
          tagGroupFlag: editData.editingData.tagGroupFlag ?? 0, // 使用空值合并运算符，避免0值被替换
          taskTagName: editData.editingData.taskTagName || '',
          taskTagDesc: editData.editingData.taskTagDesc || '',
          taskTagColor: editData.editingData.taskTagColor || '#409EFF',
          taskTagStatus: editData.editingData.taskTagStatus ?? 1, // 使用空值合并运算符，避免0值被替换
          parentTaskTagId: editData.editingData.parentTaskTagId,
          remark: editData.editingData.remark || '',
        };

        if (editData.editState === RowEditState.CREATING) {
          await taskTagManageService.addTaskTag(requestData);
          ElMessage.success('新建标签成功');
        } else {
          await taskTagManageService.updateTaskTag(requestData);
          ElMessage.success('编辑标签成功');
        }

        editingRows.value.delete(rowId);
        await loadTagList();
      } catch (error) {
        console.error('保存标签失败:', error);
        ElMessage.error('保存标签失败');
      }
    }

    // 检查行是否正在编辑
    function isRowEditing(rowId: string | number): boolean {
      return editingRows.value.has(rowId);
    }

    // 获取行的编辑数据
    function getRowEditData(
      rowId: string | number
    ): InlineEditData | undefined {
      return editingRows.value.get(rowId);
    }

    // 更新行的编辑数据
    function updateRowEditData(
      rowId: string | number,
      field: string,
      value: any
    ) {
      const editData = editingRows.value.get(rowId);
      if (editData) {
        editData.editingData[field as keyof TaskTagFormData] = value;
      }
    }

    // 新建标签组（行内编辑模式）- 支持批量创建
    function createNewTagGroup() {
      if (selectedRowIds.value.length === 0) {
        // 无选中状态：创建一个标签组到表格第一行
        createSingleTagGroup();
      } else {
        // 有选中状态：为每个选中的标签创建一个标签组
        const selectedIds = [...selectedRowIds.value]; // 复制数组，避免在循环中修改

        // 从后往前处理，避免插入位置偏移问题
        for (let i = selectedIds.length - 1; i >= 0; i--) {
          const selectedId = selectedIds[i];
          createSingleTagGroupForTarget(selectedId);
        }
      }
    }

    // 创建单个标签组（无选中状态）
    function createSingleTagGroup() {
      const tempId = `temp_${tempRowCounter.value++}`;

      const newRow: TempRowData = {
        tempId,
        isTemp: true,
        editState: RowEditState.CREATING,
        taskTagId: undefined,
        taskTagName: '新标签组',
        taskTagDesc: '',
        taskTagColor: '#409EFF',
        taskTagStatus: 1,
        tagGroupFlag: 1, // 标签组
        parentTaskTagId: undefined, // 根级别
        sortNum: 0,
        createTime: undefined,
        updateTime: undefined,
        createUser: undefined,
        updateUser: undefined,
        deleted: 0,
        remark: '',
        tenantId: undefined,
        children: [],
        tagNumber: undefined,
      };

      // 插入到表格第一行
      tagList.value.unshift(newRow as any);
      startEditRow(newRow as any);
    }

    // 为指定目标创建标签组
    function createSingleTagGroupForTarget(targetId: number | string) {
      const tempId = `temp_${tempRowCounter.value++}`;

      const newRow: TempRowData = {
        tempId,
        isTemp: true,
        editState: RowEditState.CREATING,
        taskTagId: undefined,
        taskTagName: '新标签组',
        taskTagDesc: '',
        taskTagColor: '#409EFF',
        taskTagStatus: 1,
        tagGroupFlag: 1, // 标签组
        parentTaskTagId: undefined, // 根级别
        sortNum: 0,
        createTime: undefined,
        updateTime: undefined,
        createUser: undefined,
        updateUser: undefined,
        deleted: 0,
        remark: '',
        tenantId: undefined,
        children: [],
        tagNumber: undefined,
      };

      // 找到目标标签的根级别位置
      let rootIndex = -1;
      for (let i = 0; i < tagList.value.length; i++) {
        if (isTagOrDescendant(tagList.value[i], targetId)) {
          rootIndex = i;
          break;
        }
      }

      if (rootIndex !== -1) {
        // 在根级别的下一个位置插入
        tagList.value.splice(rootIndex + 1, 0, newRow as any);
      } else {
        // 如果找不到，插入到末尾
        tagList.value.push(newRow as any);
      }

      // 只为第一个创建的标签启动编辑状态
      if (selectedRowIds.value.indexOf(targetId) === 0) {
        startEditRow(newRow as any);
      }
    }

    // 新建任务标签（行内编辑模式）- 使用与右键菜单"下方添加标签"相同的逻辑
    function createNewTaskTag() {
      if (selectedRowIds.value.length === 0) {
        // 无选中状态：创建一个任务标签到表格末尾
        createSingleTaskTag();
      } else {
        // 有选中状态：对每个选中的标签执行"下方添加标签"操作
        const selectedIds = [...selectedRowIds.value]; // 复制数组，避免在循环中修改

        // 从后往前处理，避免插入位置偏移问题
        for (let i = selectedIds.length - 1; i >= 0; i--) {
          const selectedId = selectedIds[i];
          // 找到选中的行数据
          const selectedRow = findTagByIdOrTempId(
            tagList.value,
            typeof selectedId === 'number' ? selectedId : undefined,
            typeof selectedId === 'string' ? selectedId : undefined
          );
          if (selectedRow) {
            // 对选中行执行"下方添加标签"逻辑
            addTagBelow(selectedRow);
          }
        }
      }
    }

    // 创建单个任务标签（无选中状态）- 在首行创建根级标签
    function createSingleTaskTag() {
      // 使用与右键菜单相同的数据获取方式
      const tagType = getNewTagData();

      // 创建临时ID
      const tempId = `temp_${tempRowCounter.value++}`;

      // 使用与addTagBelow相同的数据结构
      const newTag: TempRowData = {
        tempId,
        isTemp: true,
        editState: RowEditState.CREATING,
        taskTagId: undefined,
        parentTaskTagId: undefined, // 根级别
        tagGroupFlag: tagType.tagGroupFlag,
        taskTagName: tagType.tagName,
        taskTagDesc: tagType.tagDesc,
        taskTagColor: '#409EFF',
        taskTagStatus: 1,
        sortNum: 0,
        createTime: undefined,
        updateTime: undefined,
        createUser: undefined,
        updateUser: undefined,
        deleted: 0,
        remark: '',
        tenantId: undefined,
        children: [],
        tagNumber: undefined,
      };

      // 插入到表格首行（根级别）
      tagList.value.unshift(newTag as any);

      // 启动编辑状态
      startEditRow(newTag as any);
    }

    /**
     * 检查标签或其后代是否包含指定ID
     */
    function isTagOrDescendant(
      tag: TaskTagManageResponse,
      targetId: number | string
    ): boolean {
      // 检查当前标签
      if (typeof targetId === 'string') {
        if ((tag as any).tempId === targetId) return true;
      } else {
        if (tag.taskTagId === targetId) return true;
      }

      // 递归检查子标签
      if (tag.children && tag.children.length > 0) {
        for (const child of tag.children) {
          if (isTagOrDescendant(child, targetId)) {
            return true;
          }
        }
      }

      return false;
    }

    /**
     * 在树形结构中查找标签及其位置信息（支持临时行）
     */
    // function findTagInTree(
    //   tags: TaskTagManageResponse[],
    //   targetId: number | string,
    //   parentArray: TaskTagManageResponse[] = tags
    // ): { parentArray: TaskTagManageResponse[]; index: number } | null {
    //   for (let i = 0; i < tags.length; i++) {
    //     const tag = tags[i];

    //     // 支持临时行和正式行的查找
    //     let isMatch = false;
    //     if (typeof targetId === 'string') {
    //       // 查找临时行
    //       isMatch = (tag as any).tempId === targetId;
    //     } else {
    //       // 查找正式行
    //       isMatch = tag.taskTagId === targetId;
    //     }

    //     if (isMatch) {
    //       return { parentArray, index: i };
    //     }

    //     // 递归查找子标签
    //     if (tag.children && tag.children.length > 0) {
    //       const childResult = findTagInTree(
    //         tag.children,
    //         targetId,
    //         tag.children
    //       );
    //       if (childResult) {
    //         return childResult;
    //       }
    //     }
    //   }

    //   return null;
    // }

    // 移除新建行
    function removeNewRow(rowId: string | number) {
      const removeFromList = (
        list: TaskTagManageResponse[]
      ): TaskTagManageResponse[] => {
        return list.filter((tag) => {
          if ((tag as any).tempId === rowId) {
            return false;
          }
          if (tag.children) {
            tag.children = removeFromList(tag.children);
          }
          return true;
        });
      };

      tagList.value = removeFromList(tagList.value);
    }

    // 批量保存所有编辑行
    async function saveAllEditingRows() {
      const editingRowIds = Array.from(editingRows.value.keys());

      if (editingRowIds.length === 0) {
        ElMessage.info('没有需要保存的数据');
        return;
      }

      let successCount = 0;
      let errorCount = 0;

      for (const rowId of editingRowIds) {
        try {
          await saveEditRow(rowId);
          successCount++;
        } catch (error) {
          errorCount++;
        }
      }

      if (errorCount === 0) {
        ElMessage.success(`成功保存 ${successCount} 条数据`);
      } else {
        ElMessage.warning(
          `保存完成：成功 ${successCount} 条，失败 ${errorCount} 条`
        );
      }
    }

    // ==================== 右键菜单相关方法 ====================

    /**
     * 递归查找标签（支持临时行和正式行）
     */
    function findTagByIdOrTempId(
      tags: TaskTagManageResponse[],
      taskTagId?: number,
      tempId?: string
    ): TaskTagManageResponse | null {
      for (const tag of tags) {
        // 如果是临时行，使用 tempId 匹配
        if (tempId && (tag as any).tempId === tempId) {
          return tag;
        }
        // 如果是正式行，使用 taskTagId 匹配
        if (taskTagId && tag.taskTagId === taskTagId) {
          return tag;
        }
        if (tag.children) {
          const found = findTagByIdOrTempId(tag.children, taskTagId, tempId);
          if (found) {
            return found;
          }
        }
      }
      return null;
    }

    /**
     * 右键菜单添加的都是任务标签
     */
    function getNewTagData(): {
      tagGroupFlag: number;
      tagName: string;
      tagDesc: string;
    } {
      // 右键菜单添加的都是任务标签，不区分类型
      return {
        tagGroupFlag: 0, // 都是任务标签
        tagName: '新标签',
        tagDesc: '新标签描述',
      };
    }

    /**
     * 显示右键菜单
     */
    function showContextMenu(event: MouseEvent, row: TaskTagManageResponse) {
      event.preventDefault();

      // 设置菜单位置
      contextMenu.value.position.x = event.clientX;
      contextMenu.value.position.y = event.clientY;

      // 设置当前行
      contextMenu.value.currentRow = row;

      // 生成菜单选项
      contextMenu.value.options = generateContextMenuOptions();

      // 显示菜单
      contextMenu.value.visible = true;

      // 添加全局点击事件监听，用于隐藏菜单
      document.addEventListener('click', hideContextMenu);
    }

    /**
     * 隐藏右键菜单
     */
    function hideContextMenu() {
      contextMenu.value.visible = false;
      contextMenu.value.currentRow = null;
      contextMenu.value.options = [];

      // 移除全局点击事件监听
      document.removeEventListener('click', hideContextMenu);
    }

    /**
     * 生成右键菜单选项
     */
    function generateContextMenuOptions(): ContextMenuOption[] {
      const options: ContextMenuOption[] = [
        {
          action: ContextMenuAction.ADD_ABOVE,
          label: '上方添加标签',
          enabled: true,
        },
        {
          action: ContextMenuAction.ADD_BELOW,
          label: '下方添加标签',
          enabled: true,
        },
        {
          action: ContextMenuAction.ADD_CHILD,
          label: '添加子标签',
          enabled: true,
        },
      ];

      return options;
    }

    // ==================== 拖拽排序相关方法 ====================

    /**
     * 获取标签的父级ID
     * @param item 标签项
     * @returns 父级ID
     */
    function getParentId(item: TaskTagManageResponse): number | string | null {
      const parentTaskTagId = item.parentTaskTagId;
      const parentTempId = (item as any).parentTempId;
      const result = parentTaskTagId || parentTempId || null;

      return result;
    }

    /**
     * 获取标签的唯一ID
     * @param item 标签项
     * @returns 唯一ID
     */
    function getItemId(item: TaskTagManageResponse): number {
      const taskTagId = item.taskTagId;
      const tempId = (item as any).tempId;
      const result = taskTagId || tempId;

      return result;
    }

    /**
     * 从DOM元素的className中提取ID
     * @param element DOM元素
     * @returns 提取的ID
     */
    function extractIdFromElement(element: HTMLElement): string | null {
      const className = element.className;
      const match = className.match(/row-(.+?)(?:\s|$)/);
      return match ? match[1] : null;
    }

    /**
     * 将树形结构的标签列表扁平化
     * @param treeList 树形标签列表
     * @returns 扁平化的标签列表
     */
    function flattenTagList(
      treeList: TaskTagManageResponse[]
    ): TaskTagManageResponse[] {
      const result: TaskTagManageResponse[] = [];

      function traverse(items: TaskTagManageResponse[]) {
        for (const item of items) {
          result.push(item);
          if (item?.children && item?.children?.length > 0) {
            traverse(item.children);
          }
        }
      }

      traverse(treeList);
      return result;
    }

    /**
     * 根据ID在列表中查找标签项（支持树形结构）
     * @param id 标签ID
     * @param list 标签列表（可能是树形结构）
     * @returns 找到的标签项和在扁平列表中的索引
     */
    function findItemById(
      id: string,
      list: TaskTagManageResponse[]
    ): { item: TaskTagManageResponse | null; index: number } {
      // 先将树形结构扁平化
      const flatList = flattenTagList(list);

      for (let i = 0; i < flatList.length; i++) {
        const item = flatList[i];
        const itemId = getItemId(item);
        if (String(itemId) === id) {
          return { item, index: i };
        }
      }
      return { item: null, index: -1 };
    }

    /**
     * 判断是否为标签组
     * @param item 标签项
     * @returns 是否为标签组
     */
    function isTagGroup(item: TaskTagManageResponse | TempRowData): boolean {
      return item.tagGroupFlag === 1;
    }

    /**
     * 判断是否为根级位置
     * @param item 标签项
     * @returns 是否为根级
     */
    function isRootLevel(item: TaskTagManageResponse | TempRowData): boolean {
      return !item.parentTaskTagId && !(item as any).parentTempId;
    }

    /**
     * 验证拖拽移动是否允许（保持标签组层级限制）
     * @param evt Sortable事件对象，包含dragged和related DOM元素
     * @returns 验证结果
     */
    function validateDragMove(evt: any): DragMoveValidationResult {
      const { dragged, related, willInsertAfter } = evt;

      if (!dragged || !related) {
        return { allowed: false, reason: '无效的拖拽元素' };
      }

      // 使用扁平的标签列表，包含所有层级的标签
      const currentList = flattenTagList(tagList.value);

      // 从DOM元素中提取ID
      const draggedId = extractIdFromElement(dragged);
      const relatedId = extractIdFromElement(related);

      if (!draggedId || !relatedId) {
        return { allowed: false, reason: '无法识别拖拽元素' };
      }

      // 根据ID查找标签项
      const { item: draggedItem } = findItemById(draggedId, currentList);
      const { item: relatedItem } = findItemById(relatedId, currentList);

      if (!draggedItem || !relatedItem) {
        return { allowed: false, reason: '找不到对应的标签项' };
      }

      // 不能拖拽到自己
      if (draggedId === relatedId) {
        return { allowed: false, reason: '不能拖拽到自己' };
      }

      // 检查被拖拽元素是否为标签组
      if (isTagGroup(draggedItem)) {
        // 标签组只能在根级位置排序，不能移动到子级位置
        if (!isRootLevel(relatedItem)) {
          return {
            allowed: false,
            reason: '标签组只能在根级位置排序，不能移动到子级位置',
          };
        }

        // 如果是插入到目标元素之后，且目标元素有子元素，也不允许
        if (
          willInsertAfter &&
          relatedItem.children &&
          relatedItem.children.length > 0
        ) {
          return {
            allowed: false,
            reason: '标签组不能插入到有子标签的元素后面',
          };
        }
      }

      // 普通标签可以自由拖拽
      return { allowed: true };
    }

    /**
     * 开始拖拽操作
     */
    function startDrag() {
      dragState.value = DragState.DRAGGING;
      isDragging.value = true;
      // 保存原始数据快照，用于回滚
      dragOriginalData.value = JSON.parse(JSON.stringify(tagList.value));
    }

    /**
     * 结束拖拽操作
     */
    function endDrag() {
      dragState.value = DragState.IDLE;
      isDragging.value = false;
    }

    /**
     * 处理拖拽排序（仅本地调整，不调用接口）
     * @param draggedTag 拖拽元素
     * @param targetTag 目标位置元素
     * @param insertAfter 是否插入到目标元素之后
     */
    function handleDragSort(
      draggedTag: TaskTagManageResponse,
      targetTag: TaskTagManageResponse,
      insertAfter: boolean = false
    ) {
      dragState.value = DragState.DRAGGING;

      // 实现跨层级拖拽
      performDragSort(draggedTag, targetTag, insertAfter);

      // 重新计算序号
      regenerateTagNumbers();

      endDrag();
    }

    /**
     * 回滚拖拽操作
     */
    function rollbackDrag() {
      if (dragOriginalData.value.length > 0) {
        tagList.value = JSON.parse(JSON.stringify(dragOriginalData.value));
      }
      endDrag();
    }

    /**
     * 重新生成标签序号
     */
    function regenerateTagNumbers() {
      const generateNumbers = (
        items: TaskTagManageResponse[],
        parentNumber: string = ''
      ): void => {
        items.forEach((item, index) => {
          const currentNumber = parentNumber
            ? `${parentNumber}.${index + 1}`
            : `${index + 1}`;

          // 确保tagNumber字段存在并更新
          item.tagNumber = currentNumber;

          // 递归处理子节点
          if (item.children && item.children.length > 0) {
            generateNumbers(item.children, currentNumber);
          }
        });
      };

      // 立即更新序号
      generateNumbers(tagList.value);

      // 确保在下一个tick中强制触发响应式更新
      nextTick(() => {
        // 创建新的数组引用以触发Vue的响应式更新
        tagList.value = JSON.parse(JSON.stringify(tagList.value));
      });
    }

    /**
     * 处理拖拽排序（优化版本：正确处理同容器和跨容器拖拽）
     * @param draggedTag 拖拽元素
     * @param targetTag 目标位置元素
     * @param insertAfter 是否插入到目标元素之后
     */
    function performDragSort(
      draggedTag: TaskTagManageResponse,
      targetTag: TaskTagManageResponse,
      insertAfter: boolean = false
    ) {
      const newList = JSON.parse(JSON.stringify(tagList.value));

      // 在树形结构中查找拖拽元素和目标元素及其容器
      const draggedId = getItemId(draggedTag);
      const draggedResult = findItemInTree(draggedId, newList);
      const targetId = getItemId(targetTag);
      const targetResult = findItemInTree(targetId, newList);

      if (!draggedResult || !targetResult) {
        console.error('找不到拖拽或目标元素');
        return;
      }

      const draggedContainer = draggedResult.container;
      const targetContainer = targetResult.container;
      const draggedIndex = draggedResult.index;
      let targetIndex = targetResult.index;

      // 创建拖拽元素的副本
      const draggedElement = JSON.parse(JSON.stringify(draggedTag));

      // 更新父子关系
      if (isTagGroup(draggedElement)) {
        // 标签组始终保持在根级
        draggedElement.parentTaskTagId = undefined;
        draggedElement.parentTempId = undefined;
      } else {
        // 普通标签根据目标位置调整层级
        if (targetResult.parent) {
          // 目标在子级，拖拽元素也移到同一子级
          draggedElement.parentTaskTagId =
            targetResult.parent.taskTagId || undefined;
          draggedElement.parentTempId =
            (targetResult.parent as any).tempId || undefined;
        } else {
          // 目标在根级，拖拽元素也移到根级
          draggedElement.parentTaskTagId = undefined;
          draggedElement.parentTempId = undefined;
        }
      }

      // 处理同容器拖拽的索引调整
      if (draggedContainer === targetContainer && draggedIndex < targetIndex) {
        targetIndex--; // 删除元素后，目标索引需要减1
      }

      // 先从原位置删除
      draggedContainer.splice(draggedIndex, 1);

      // 计算最终插入位置
      const finalInsertIndex = insertAfter ? targetIndex + 1 : targetIndex;

      // 插入到新位置
      targetContainer.splice(finalInsertIndex, 0, draggedElement);

      // 强制触发响应式更新，确保Vue检测到数据变化
      tagList.value = newList;
    }

    /**
     * 在树形结构中查找元素及其容器信息
     * @param itemId 要查找的元素ID
     * @param treeList 树形结构列表
     * @param parent 父级元素
     * @returns 包含元素、容器数组和索引的结果
     */
    function findItemInTree(
      itemId: number,
      treeList: TaskTagManageResponse[],
      parent?: TaskTagManageResponse
    ): {
      item: TaskTagManageResponse;
      container: TaskTagManageResponse[];
      parent?: TaskTagManageResponse;
      index: number;
    } | null {
      // 在根级查找
      for (let i = 0; i < treeList.length; i++) {
        const item = treeList[i];
        if (item.taskTagId === itemId || (item as any).tempId === itemId) {
          return { item, container: treeList, parent: parent, index: i };
        }

        // 递归在子级查找
        if (item.children && item.children.length > 0) {
          const childResult = findItemInTree(itemId, item.children, item);
          if (childResult) {
            return childResult;
          }
        }
      }

      return null;
    }

    /**
     * 处理右键菜单选项点击
     */
    function handleContextMenuAction(action: ContextMenuAction) {
      if (!contextMenu.value.currentRow) return;

      const row = contextMenu.value.currentRow;

      switch (action) {
        case ContextMenuAction.ADD_ABOVE:
          addTagAbove(row);
          break;
        case ContextMenuAction.ADD_BELOW:
          addTagBelow(row);
          break;
        case ContextMenuAction.ADD_CHILD:
          addChildTag(row);
          break;
      }

      // 隐藏菜单
      hideContextMenu();
    }

    /**
     * 上方添加标签（本地操作）
     */
    function addTagAbove(row: TaskTagManageResponse) {
      // 计算新标签的sortNum：比当前行小0.5，确保排在上方
      const sortNum = (row.sortNum || 0) - 0.5;

      // 右键菜单添加的都是任务标签
      const tagType = getNewTagData();

      // 创建临时ID
      const tempId = `temp_${tempRowCounter.value++}`;

      // 处理父级关系：支持临时行和正式行
      const parentTaskTagId = row.parentTaskTagId;
      const parentTempId = (row as any).parentTempId;

      const newTag: TempRowData = {
        tempId,
        isTemp: true,
        editState: RowEditState.CREATING,
        taskTagId: undefined,
        parentTaskTagId, // 保持与当前行相同的父级
        parentTempId, // 临时行的父级ID
        tagGroupFlag: tagType.tagGroupFlag,
        taskTagName: tagType.tagName,
        taskTagDesc: tagType.tagDesc,
        taskTagColor: '#409EFF',
        taskTagStatus: 1,
        sortNum,
        remark: '',
      };

      // 插入到正确的层级位置
      if (parentTaskTagId || parentTempId) {
        // 如果当前行有父级，需要插入到父级的children中
        const parentTag = findTagByIdOrTempId(
          tagList.value,
          parentTaskTagId,
          parentTempId
        );
        if (parentTag) {
          if (!parentTag.children) {
            parentTag.children = [];
          }
          // 找到当前行在父级children中的位置
          const currentIndex = parentTag.children.findIndex(
            (item: TaskTagManageResponse) => {
              // 修复匹配逻辑：优先匹配tempId，避免undefined的taskTagId误匹配
              if ((row as any).tempId && (item as any).tempId) {
                return (item as any).tempId === (row as any).tempId;
              } else if (row.taskTagId && item.taskTagId) {
                return item.taskTagId === row.taskTagId;
              }
              return false;
            }
          );
          if (currentIndex !== -1) {
            parentTag.children.splice(currentIndex, 0, newTag as any);
          } else {
            parentTag.children.unshift(newTag as any);
          }
        }
      } else {
        // 如果当前行是根级，直接插入到tagList中
        const currentIndex = tagList.value.findIndex((item) => {
          // 修复匹配逻辑：优先匹配tempId，避免undefined的taskTagId误匹配
          if ((row as any).tempId && (item as any).tempId) {
            return (item as any).tempId === (row as any).tempId;
          } else if (row.taskTagId && item.taskTagId) {
            return item.taskTagId === row.taskTagId;
          }
          return false;
        });
        if (currentIndex !== -1) {
          tagList.value.splice(currentIndex, 0, newTag as any);
        } else {
          tagList.value.unshift(newTag as any);
        }
      }

      // 启动编辑状态
      startEditRow(newTag as any);
    }

    /**
     * 下方添加标签（本地操作）
     */
    function addTagBelow(row: TaskTagManageResponse) {
      // 右键菜单添加的都是任务标签
      const tagType = getNewTagData();

      // 创建临时ID
      const tempId = `temp_${tempRowCounter.value++}`;

      // 检查当前行是否有子标签
      const hasChildren = row.children && row.children.length > 0;

      let newTag: TempRowData;

      if (hasChildren) {
        // 如果有子标签，添加为第一个子标签
        const sortNum = 1; // 子标签从1开始

        newTag = {
          tempId,
          isTemp: true,
          editState: RowEditState.CREATING,
          taskTagId: undefined,
          parentTaskTagId: row.taskTagId, // 当前行作为父级
          parentTempId: (row as any).tempId, // 临时行的父级ID
          tagGroupFlag: tagType.tagGroupFlag,
          taskTagName: tagType.tagName,
          taskTagDesc: tagType.tagDesc,
          taskTagColor: '#409EFF',
          taskTagStatus: 1,
          sortNum,
          remark: '',
          tagNumber: `${row.tagNumber || '1'}.1`, // 设置子标签序号
        };

        // 找到 tagList.value 中的实际对象并插入子标签
        const actualRow = findTagByIdOrTempId(
          tagList.value,
          row.taskTagId,
          (row as any).tempId
        );

        if (actualRow) {
          if (!actualRow.children) {
            actualRow.children = [];
          }
          actualRow.children.unshift(newTag as any);

          // 重新生成所有子标签的序号
          if (actualRow.children.length > 1) {
            actualRow.children.forEach((child, index) => {
              (child as any).tagNumber = `${actualRow.tagNumber || '1'}.${
                index + 1
              }`;
            });
          }
        }
      } else {
        // 如果没有子标签，添加为同级标签（在当前行下方）
        const sortNum = (row.sortNum || 0) + 1;

        // 处理父级关系：支持临时行和正式行
        const parentTaskTagId = row.parentTaskTagId;
        const parentTempId = (row as any).parentTempId;

        newTag = {
          tempId,
          isTemp: true,
          editState: RowEditState.CREATING,
          taskTagId: undefined,
          parentTaskTagId, // 保持与当前行相同的父级
          parentTempId, // 临时行的父级ID
          tagGroupFlag: tagType.tagGroupFlag,
          taskTagName: tagType.tagName,
          taskTagDesc: tagType.tagDesc,
          taskTagColor: '#409EFF',
          taskTagStatus: 1,
          sortNum,
          remark: '',
        };

        // 插入到正确的层级位置（同级）
        if (parentTaskTagId || parentTempId) {
          // 如果当前行有父级，需要插入到父级的children中
          const parentTag = findTagByIdOrTempId(
            tagList.value,
            parentTaskTagId,
            parentTempId
          );
          if (parentTag) {
            if (!parentTag.children) {
              parentTag.children = [];
            }
            // 找到当前行在父级children中的位置
            const currentIndex = parentTag.children.findIndex(
              (item: TaskTagManageResponse) => {
                // 修复匹配逻辑：优先匹配tempId，避免undefined的taskTagId误匹配
                if ((row as any).tempId && (item as any).tempId) {
                  return (item as any).tempId === (row as any).tempId;
                } else if (row.taskTagId && item.taskTagId) {
                  return item.taskTagId === row.taskTagId;
                }
                return false;
              }
            );
            if (currentIndex !== -1) {
              parentTag.children.splice(currentIndex + 1, 0, newTag as any);
            } else {
              parentTag.children.push(newTag as any);
            }
          }
        } else {
          // 如果当前行是根级，直接插入到tagList中
          const currentIndex = tagList.value.findIndex((item) => {
            // 修复匹配逻辑：优先匹配tempId，避免undefined的taskTagId误匹配
            if ((row as any).tempId && (item as any).tempId) {
              return (item as any).tempId === (row as any).tempId;
            } else if (row.taskTagId && item.taskTagId) {
              return item.taskTagId === row.taskTagId;
            }
            return false;
          });

          if (currentIndex !== -1) {
            tagList.value.splice(currentIndex + 1, 0, newTag as any);
          } else {
            tagList.value.push(newTag as any);
          }
        }
      }

      // 启动编辑状态
      startEditRow(newTag as any);
    }

    /**
     * 添加子标签（本地操作）
     */
    function addChildTag(row: TaskTagManageResponse) {
      const sortNum = 1; // 子标签从1开始

      // 创建临时ID
      const tempId = `temp_${tempRowCounter.value++}`;

      // 右键菜单添加的都是任务标签
      const tagType = getNewTagData();

      // 使用新的查找方法来找到当前标签
      const currentTag = findTagByIdOrTempId(
        tagList.value,
        row.taskTagId,
        (row as any).tempId
      );

      let newTag: TempRowData | null = null;

      if (currentTag) {
        if (!currentTag.children) {
          currentTag.children = [];
        }

        // 计算新子标签的序号
        const childIndex = currentTag.children.length + 1;
        const childTagNumber = `${currentTag.tagNumber || '1'}.${childIndex}`;

        newTag = {
          tempId,
          isTemp: true,
          editState: RowEditState.CREATING,
          taskTagId: undefined,
          parentTaskTagId: row.taskTagId, // 只有正式行才有taskTagId
          parentTempId: (row as any).tempId, // 临时行的父级ID
          tagGroupFlag: tagType.tagGroupFlag,
          taskTagName: tagType.tagName,
          taskTagDesc: tagType.tagDesc,
          taskTagColor: '#409EFF',
          taskTagStatus: 1,
          sortNum,
          remark: '',
          tagNumber: childTagNumber, // 设置子标签序号
        };

        // 子标签添加到children数组的末尾
        currentTag.children.push(newTag as any);
      }

      // 启动编辑状态
      if (newTag) {
        startEditRow(newTag as any);
      }
    }

    return {
      // 状态
      isLoading,
      isInitialized,
      tagList,
      originalTagList,
      selectedTagIds,
      selectedRowIds,
      searchKeyword,
      columnConfig,
      formLoading,
      tagGroupOptions,

      // 行内编辑状态
      editingRows,
      currentEditingField,
      // 拖拽状态
      dragState,
      isDragging,
      // 右键菜单状态
      contextMenu,

      // 计算属性
      numberedTagList,
      visibleColumns,
      hasSelectedTags,
      selectedRows,
      hasEditingRows,

      // 原有方法
      init,
      loadTagList,
      loadTagGroupOptions,
      setSearchKeyword,
      clearSearch,
      updateColumnConfig,
      toggleColumn,
      deleteTag,
      handleSelectionChange,
      batchDelete,
      refresh,
      flattenTagList,

      // 行内编辑方法
      startEditRow,
      startEditField,
      isFieldEditing,
      finishCurrentEditingField,
      saveCurrentEditingField,
      cancelCurrentEditingField,
      cancelEditRow,
      saveEditRow,
      saveAllEditingRows,
      isRowEditing,
      getRowEditData,
      updateRowEditData,
      createNewTagGroup,
      createNewTaskTag,

      // 拖拽排序方法
      startDrag,
      handleDragSort,
      rollbackDrag,
      validateDragMove,

      saveAllTags,
      // 右键菜单方法
      showContextMenu,
      hideContextMenu,
      handleContextMenuAction,
    };
  },
  {
    persist: {
      pick: ['columnConfig'],
      key: 'taskTagManage_columnConfig',
    },
  }
);
