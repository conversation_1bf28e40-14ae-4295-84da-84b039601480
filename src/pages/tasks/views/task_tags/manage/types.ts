/**
 * 任务标签维护页面相关类型定义
 * 基于 Swagger API 文档定义，复制自 swagger/output.ts
 */

/**
 * 任务标签响应对象 - 基于 Swagger ConfigIndeTaskTagResponse
 */
export interface TaskTagManageResponse {
  /** 任务标签ID */
  taskTagId?: number;
  /** 父标签ID或者所属标签组ID */
  parentTaskTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag?: number;
  /** 任务标签标题 */
  taskTagName?: string;
  /** 任务标签描述 */
  taskTagDesc?: string;
  /** 任务标签颜色 */
  taskTagColor?: string;
  /** 任务标签状态 (1 启用,0 禁用) */
  taskTagStatus?: number;
  /** 排序码 */
  sortNum?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 租户ID */
  tenantId?: number;
  /** 子标签列表，用于树形结构 */
  children?: TaskTagManageResponse[];
  /** 层级序号，用于显示 */
  tagNumber?: string;
}

/**
 * 任务标签编辑请求 - 基于 Swagger ConfigIndeTaskTagEditRequest
 */
export interface TaskTagManageEditRequest {
  /** 租户ID */
  tenantId?: number;
  /** 任务标签ID */
  taskTagId?: number;
  /** 父标签ID或者所属标签组ID */
  parentTaskTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag: number;
  /** 任务标签标题 */
  taskTagName: string;
  /** 任务标签描述 */
  taskTagDesc?: string;
  /** 任务标签颜色 */
  taskTagColor: string;
  /** 任务标签状态 (1 启用,0 禁用) */
  taskTagStatus: number;
  /** 排序码 */
  sortNum?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 任务标签查询请求 - 基于 Swagger ConfigIndeTaskTagQueryRequest
 */
export interface TaskTagManageQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 任务标签ID */
  taskTagId?: number;
  /** 父标签ID或者所属标签组ID */
  parentTaskTagId?: number;
  /** 是否是标签组(1 是,0 否) */
  tagGroupFlag?: number;
  /** 任务标签标题 */
  taskTagName?: string;
  /** 任务标签描述 */
  taskTagDesc?: string;
  /** 任务标签颜色 */
  taskTagColor?: string;
  /** 任务标签状态 (1 启用,0 禁用) */
  taskTagStatus?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 任务标签删除请求 - 基于 Swagger ConfigIndeTaskTagDeleteRequest
 */
export interface TaskTagManageDeleteRequest {
  tenantId?: number;
  /** 任务标签ID */
  taskTagId: number;
}

/**
 * 表单数据类型
 */
export interface TaskTagFormData {
  /** 任务标签ID，编辑时使用 */
  taskTagId?: number;
  /** 父标签ID，创建子标签时使用 */
  parentTaskTagId?: number;
  /** 是否是标签组 */
  tagGroupFlag: number;
  /** 标签名称 */
  taskTagName: string;
  /** 标签描述 */
  taskTagDesc: string;
  /** 标签颜色 */
  taskTagColor: string;
  /** 标签状态 */
  taskTagStatus: number;
  /** 备注 */
  remark: string;
}

/**
 * 表格列配置类型
 */
export interface TableColumn {
  /** 字段名 */
  field: string;
  /** 列标题 */
  label: string;
  /** 列宽度 */
  width?: string;
  /** 是否显示 */
  show: boolean;
  /** 是否可排序 */
  sortable?: boolean;
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right';
}

/**
 * 操作类型枚举
 */
export enum OperationType {
  /** 新增标签组 */
  ADD_GROUP = 'ADD_GROUP',
  /** 新增标签 */
  ADD_TAG = 'ADD_TAG',
  /** 编辑 */
  EDIT = 'EDIT',
  /** 删除 */
  DELETE = 'DELETE',
  /** 状态切换 */
  TOGGLE_STATUS = 'TOGGLE_STATUS',
}

/**
 * 弹窗模式枚举
 */
export enum DialogMode {
  /** 新增标签组 */
  ADD_GROUP = 'ADD_GROUP',
  /** 新增标签 */
  ADD_TAG = 'ADD_TAG',
  /** 编辑 */
  EDIT = 'EDIT',
}

/**
 * 标签组选项类型
 */
export interface TagGroupOption {
  /** 标签组ID */
  value: number;
  /** 标签组名称 */
  label: string;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 行编辑状态枚举
 */
export enum RowEditState {
  /** 显示模式 */
  DISPLAY = 'display',
  /** 编辑模式 */
  EDITING = 'editing',
  /** 新建模式 */
  CREATING = 'creating',
}

/**
 * 临时行数据类型（用于新建行）
 */
export interface TempRowData extends Partial<TaskTagManageResponse> {
  /** 临时ID，用于标识新建行 */
  tempId: string;
  /** 是否为临时行 */
  isTemp: boolean;
  /** 编辑状态 */
  editState: RowEditState;
  /** 临时行的父级ID */
  parentTempId?: string;
}

/**
 * 行内编辑数据类型
 */
export interface InlineEditData {
  /** 行ID（可能是taskTagId或tempId） */
  rowId: string | number;
  /** 编辑状态 */
  editState: RowEditState;
  /** 编辑中的数据 */
  editingData: Partial<TaskTagFormData>;
  /** 原始数据（用于取消编辑时恢复） */
  originalData: TaskTagManageResponse | TempRowData;
}

/**
 * 表单验证规则类型
 */
export interface FormRules {
  taskTagName: Array<{
    required?: boolean;
    message: string;
    trigger: string;
    min?: number;
    max?: number;
  }>;
  taskTagColor: Array<{
    required?: boolean;
    message: string;
    trigger: string;
  }>;
}

/**
 * 右键菜单选项枚举
 */
export enum ContextMenuAction {
  /** 上方添加标签 */
  ADD_ABOVE = 'ADD_ABOVE',
  /** 下方添加标签 */
  ADD_BELOW = 'ADD_BELOW',
  /** 添加子标签 */
  ADD_CHILD = 'ADD_CHILD',
}

/**
 * 右键菜单选项配置
 */
export interface ContextMenuOption {
  /** 选项标识 */
  action: ContextMenuAction;
  /** 显示文本 */
  label: string;
  /** 是否可用 */
  enabled: boolean;
}

/**
 * 右键菜单状态
 */
export interface ContextMenuState {
  /** 是否显示菜单 */
  visible: boolean;
  /** 菜单位置 */
  position: {
    x: number;
    y: number;
  };
  /** 当前选中的行数据 */
  currentRow: TaskTagManageResponse | null;
  /** 菜单选项列表 */
  options: ContextMenuOption[];
}

/**
 * 拖拽移动验证结果
 */
export interface DragMoveValidationResult {
  /** 是否允许移动 */
  allowed: boolean;
  /** 错误消息 */
  message?: string;
  /** 不允许的原因 */
  reason?: string;
}

/**
 * 拖拽状态枚举
 */
export enum DragState {
  /** 空闲状态 */
  IDLE = 'idle',
  /** 拖拽中 */
  DRAGGING = 'dragging',
}

/**
 * 排序更新请求
 */
export interface SortUpdateRequest {
  /** 任务标签ID */
  taskTagId: number;
  /** 父标签ID */
  parentTaskTagId?: number;
  /** 标签组标识 */
  tagGroupFlag: number;
  /** 标签名称 */
  taskTagName: string;
  /** 标签描述 */
  taskTagDesc?: string;
  /** 标签颜色 */
  taskTagColor: string;
  /** 标签状态 */
  taskTagStatus: number;
  /** 排序号 */
  sortNum: number;
  /** 备注 */
  remark?: string;
}
