import { defineStore } from 'pinia';
import type {
  ProjectTaskResponse,
  ProjectTreeNode,
  TabType,
  FilterParams,
  TaskListState,
  ProjectTaskQueryRequest,
} from './types';
import { exportProjectTask, getProjects, getTasks } from './TaskListService';
import { ElMessage } from 'element-plus';

export const useTaskListStore = defineStore('taskList', {
  state: (): TaskListState => ({
    // 树形结构数据
    treeData: [],
    treeLoading: false,
    currentNode: [],

    // 任务列表数据
    tasks: [],
    tasksLoading: false,

    // 页签数据
    activeTab: 1,

    // 搜索关键字
    searchKeyword: '',

    // 分页参数
    page: 1,
    pageSize: 20,
    hasMore: false,

    // 列配置 - 基于 Swagger ProjectTaskResponse 字段
    columnList: [
      {
        field: 'projTaskId',
        name: 'projTaskId',
        label: '项目任务ID',
        checked: false,
        width: 144,
        align: 'left',
        show: false,
        filter: {
          type: 'input',
          inputType: 'input',
          showOrder: false,
          placeholder: '请输入任务ID',
        },
        filterValue: '',
      },
      {
        field: 'projTaskName',
        name: 'projTaskName',
        label: '项目任务名称',
        checked: true,
        width: 144,
        align: 'left',
        fixed: 'left',
        show: true,
        filter: {
          type: 'input',
          inputType: 'input',
          showOrder: false,
          placeholder: '请输入任务名称',
        },
        filterValue: '',
      },
      {
        field: 'projTaskStatus',
        name: 'projTaskStatus',
        label: '状态',
        checked: true,
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox',
          showOrder: false,
          data: [
            { label: '未开始', value: 1 },
            { label: '进行中', value: 2 },
            { label: '已关闭', value: 3 },
          ],
        },
        filterValue: [],
      },
      {
        field: 'projTaskResponsibleDept',
        name: 'projTaskResponsibleDept',
        label: '责任部门',
        checked: true,
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'input',
          inputType: 'input',
          showOrder: false,
          placeholder: '请输入责任部门',
        },
        filterValue: '',
      },
      {
        field: 'projTaskResponsibleProjRole',
        name: 'projTaskResponsibleProjRole',
        label: '项目角色',
        checked: true,
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'input',
          inputType: 'input',
          showOrder: false,
          placeholder: '请输入项目角色',
        },
        filterValue: '',
      },
      {
        field: 'responsibleName',
        name: 'responsibleName',
        label: '责任人',
        checked: true,
        width: 120,
        align: 'left',
        show: true,
        filter: {
          type: 'input',
          inputType: 'input',
          showOrder: true,
          placeholder: '请输入责任人姓名',
        },
        filterValue: '',
        filterOrder: '',
      },
      {
        field: 'taskType',
        name: 'taskType',
        label: '作业类型',
        checked: true,
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox',
          showOrder: false,
          data: [
            { label: '里程碑作业', value: 1 },
            { label: '任务作业', value: 2 },
            { label: 'wbs作业', value: 3 },
            { label: '配合作业', value: 4 },
          ],
        },
        filterValue: [],
      },
      {
        field: 'taskActivityType',
        name: 'taskActivityType',
        label: '任务作业类型',
        checked: true,
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox',
          showOrder: false,
          data: [
            { label: '设计任务', value: 1 },
            { label: '试验任务', value: 2 },
            { label: '评审任务', value: 3 },
            { label: '采购任务', value: 4 },
          ],
        },
        filterValue: [],
      },
      {
        field: 'major',
        name: 'major',
        label: '专业',
        checked: true,
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'checkbox',
          showOrder: false,
          data: [
            { label: '产品策划', value: 1 },
            { label: '造型开发', value: 2 },
            { label: '平台开发', value: 3 },
            { label: '工程开发', value: 4 },
            { label: '整车工程/总布置', value: 5 },
            { label: '试验认证', value: 6 },
            { label: '生产准备', value: 7 },
            { label: '项目管理/系统工程', value: 8 },
            { label: '新兴技术开发', value: 9 },
          ],
        },
        filterValue: [],
      },
      {
        field: 'planStartDate',
        name: 'planStartDate',
        label: '计划开始日期',
        checked: true,
        width: 176,
        align: 'left',
        show: true,
        filter: {
          type: 'input',
          inputType: 'dateRange',
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'actualStartDate',
        name: 'actualStartDate',
        label: '实际开始时间',
        checked: true,
        width: 176,
        align: 'left',
        show: true,
        filter: {
          type: 'input',
          inputType: 'dateRange',
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'planFinishDate',
        name: 'planFinishDate',
        label: '计划完成日期',
        checked: true,
        width: 176,
        align: 'left',
        show: true,
        filter: {
          type: 'input',
          inputType: 'dateRange',
          showOrder: true,
        },
        filterValue: [],
        filterOrder: '',
      },
      {
        field: 'overdueDays',
        name: 'overdueDays',
        label: '超期',
        checked: true,
        width: 144,
        align: 'center',
        show: true,
        filterValue: [],
        filter: {
          type: 'checkbox',
          showOrder: false,
          data: [
            { label: '是', value: 1 },
            { label: '否', value: 0 },
          ],
        },
      },
      {
        field: 'actualProgress',
        name: 'actualProgress',
        label: '进度',
        checked: true,
        width: 144,
        align: 'left',
        show: true,
        filter: {
          type: 'input',
          inputType: 'input',
          showOrder: true,
          placeholder: '请输入进度值',
        },
        filterValue: '',
        filterOrder: '',
      },
    ],

    // 标签页配置 - 基于 Swagger TabType 定义
    tabs: [
      { name: 'all', label: '全部', value: 1 },
      { name: 'responsible', label: '我负责的', value: 2 },
      { name: 'participant', label: '我参与的', value: 3 },
      { name: 'assigned', label: '我指派的', value: 4 },
      { name: 'todo', label: '我待办的', value: 5 },
    ],

    // 筛选条件
    filterConditions: {},
  }),

  actions: {
    /**
     * 初始化数据
     */
    async init() {
      await this.fetchProjectsList();
      await this.fetchTasksList();
    },

    /**
     * 获取项目树列表
     */
    async fetchProjectsList() {
      this.treeLoading = true;
      const result = await getProjects();
      this.treeData = result;
      this.treeLoading = false;
    },

    /**
     * 导出项目任务
     */
    async exportProjectTask() {
      await exportProjectTask();

      ElMessage.success('导出成功');
    },

    /**
     * 获取任务列表
     */
    async fetchTasksList() {
      this.tasksLoading = true;

      // 前置检查：如果没有选中任何项目节点，直接返回空数组
      if (this.currentNode.length === 0) {
        this.tasks = [];
        this.tasksLoading = false;
        return;
      }

      const params: FilterParams & ProjectTaskQueryRequest = {
        tabType: this.activeTab,
        keyword: this.searchKeyword,
        filters: this.filterConditions,
        sorts: this.collectSortParams(),
        projIds: this.currentNode.map((item) => item.projId),
      };
      const result = await getTasks(params);
      this.tasks = result;
      this.tasksLoading = false;
    },

    /**
     * 切换当前项目节点
     * @param node 选中的项目节点
     */
    setCurrentNode(node: ProjectTreeNode[]) {
      this.currentNode = node;
      // 重置页码
      this.page = 1;
      // 重新加载任务列表
      this.fetchTasksList();
    },

    /**
     * 切换标签页
     * @param tabType 标签页类型
     */
    setActiveTab(tabType: TabType) {
      this.activeTab = tabType;
      // 重置页码
      this.page = 1;
      // 重新加载任务列表
      this.fetchTasksList();
    },

    /**
     * 搜索任务
     * @param keyword 搜索关键字
     */
    searchTasks(keyword: string) {
      this.searchKeyword = keyword;
      // 重置页码
      this.page = 1;
      // 重新加载任务列表
      this.fetchTasksList();
    },

    /**
     * 加载更多任务
     */
    loadMoreTasks() {
      if (this.hasMore && !this.tasksLoading) {
        this.page += 1;
        this.fetchTasksList();
      }
    },

    /**
     * 处理标签页变化
     */
    handleTabChange(tab: TabType) {
      this.setActiveTab(tab);
    },

    /**
     * 加载任务列表（兼容方法）
     */
    loadTasks() {
      this.fetchTasksList();
    },

    /**
     * 处理树节点变化
     */
    handleTreeChange(treeNodes: ProjectTreeNode[]) {
      console.log('🚀 ~ handleTreeChange ~ treeNodes:', treeNodes);
      this.setCurrentNode(treeNodes);
    },

    /**
     * 将数字类型的 TabType 转换为字符串类型（兼容旧版本 Service）
     */
    getTabTypeString(tabType: TabType): string {
      switch (tabType) {
        case 1:
          return 'all';
        case 2:
          return 'responsible';
        case 3:
          return 'participant';
        case 4:
          return 'assigned';
        case 5:
          return 'todo';
        default:
          return 'all';
      }
    },

    // 生成任务编号
    generateTaskNumbers(taskList: ProjectTaskResponse[], startIndex = 0) {
      taskList.forEach((task, index) => {
        task.taskNumber = String(startIndex + index + 1);
        if (task.children && task.children.length > 0) {
          this.generateTaskNumbers(task.children, 0);
        }
      });
    },

    /**
     * 收集排序参数
     */
    collectSortParams() {
      const sorts: any[] = [];
      this.columnList.forEach((column) => {
        if (column.filterOrder) {
          sorts.push({
            field: column.field,
            direction: column.filterOrder,
          });
        }
      });
      return sorts;
    },

    /**
     * 更新筛选条件
     */
    updateFilterConditions(field: string, value: any) {
      // 处理日期范围字段
      if (field === 'planStartDate') {
        if (
          Array.isArray(value) &&
          value.length === 2 &&
          value[0] &&
          value[1]
        ) {
          // 有效的日期范围选择
          this.filterConditions.beginPlanStartDate = value[0];
          this.filterConditions.endPlanStartDate = value[1];
        } else {
          // 清除操作：删除日期范围字段
          delete this.filterConditions.beginPlanStartDate;
          delete this.filterConditions.endPlanStartDate;
        }
      } else if (field === 'actualStartDate') {
        if (
          Array.isArray(value) &&
          value.length === 2 &&
          value[0] &&
          value[1]
        ) {
          // 有效的日期范围选择
          this.filterConditions.beginActualStartDate = value[0];
          this.filterConditions.endActualStartDate = value[1];
        } else {
          // 清除操作：删除日期范围字段
          delete this.filterConditions.beginActualStartDate;
          delete this.filterConditions.endActualStartDate;
        }
      } else if (field === 'planFinishDate') {
        if (
          Array.isArray(value) &&
          value.length === 2 &&
          value[0] &&
          value[1]
        ) {
          // 有效的日期范围选择
          this.filterConditions.beginPlanFinishDate = value[0];
          this.filterConditions.endPlanFinishDate = value[1];
        } else {
          // 清除操作：删除日期范围字段
          delete this.filterConditions.beginPlanFinishDate;
          delete this.filterConditions.endPlanFinishDate;
        }
      } else {
        // 其他字段直接赋值
        if (
          value === null ||
          value === undefined ||
          (Array.isArray(value) && value.length === 0) ||
          value === ''
        ) {
          // 清除操作：删除字段
          delete (this.filterConditions as any)[field];
        } else {
          // 设置字段值
          (this.filterConditions as any)[field] = value;
        }
      }
    },

    /**
     * 清空筛选条件
     */
    clearFilterConditions() {
      this.filterConditions = {};
      // 同时清空列配置中的筛选值
      this.columnList.forEach((column) => {
        if (column.filter) {
          column.filterValue = column.filter.type === 'checkbox' ? [] : '';
          column.filterOrder = '';
        }
      });
    },
  },
  // 5. 持久化配置 - 只持久化必要数据
  persist: {
    pick: ['columnList'],
    key: 'taskList_columnList',
  },
});
