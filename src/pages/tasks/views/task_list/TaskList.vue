<script setup lang="ts">
import { ref, computed, onMounted, useTemplateRef } from 'vue';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import { tableConfig } from '@/app-config.ts';
import { useTaskListStore } from './TaskListStore';
import type { ProjectTreeNode, TabType, TaskItem } from './types';

import { MoreFilled } from '@element-plus/icons-vue';
import BtColumn from '@/components/non_biz/bt_column/BtColumn.vue';
import BtSearch from '@/components/non_biz/bt_search/BtSearch.vue';
import BtThFilter from '@/components/non_biz/bt_th_filter/BtThFilter.vue';
import TaskTabsWithToolbar from '@/pages/tasks/components/task_tabs_with_toolbar/TaskTabsWithToolbar.vue';
import BtTree from '@/components/non_biz/bt_tree/BtTree.vue';
// Store使用
const router = useRouter();
const store = useTaskListStore();
const { tasksLoading } = storeToRefs(store);

// 初始化
function init() {
  store.init();
}

// 生成层级序号的函数
function generateTaskNumbers(
  tasks: TaskItem[],
  parentNumber: string = ''
): TaskItem[] {
  return tasks.map((task, index) => {
    const currentNumber = parentNumber
      ? `${parentNumber}.${index + 1}`
      : `${index + 1}`;
    const updatedTask = {
      ...task,
      taskNumber: currentNumber,
    };

    if (task.children && task.children.length > 0) {
      updatedTask.children = generateTaskNumbers(task.children, currentNumber);
    }

    return updatedTask;
  });
}

function handleTabChange(tab: TabType) {
  // 直接使用数字类型的 TabType，符合 Swagger 定义
  store.handleTabChange(tab);
}

// 表格列配置 - 使用Store中的配置
const columnRef = useTemplateRef<any>('columnRef');

// 显示的列 - 使用Store中的列配置
const showColumn = computed(() => {
  return store.columnList.filter((v) => v.show);
});

// 表格筛选相关
function changeFilter(val: any, field: string) {
  console.log('筛选变更', val, field);

  // 更新对应列的筛选值
  const column = store.columnList.find((col) => col.field === field);
  if (column) {
    column.filterValue = val;
  }

  // 更新Store中的筛选条件
  store.updateFilterConditions(field, val);

  // 重新加载任务列表
  store.loadTasks();
}

// 搜索相关
const keyword = ref('');
function toSearch() {
  store.searchTasks(keyword.value);
}

// 获取状态类型 - 基于 Swagger ProjectTaskStatus 枚举
function getStatusType(status: number | undefined) {
  const statusMap: Record<number, string> = {
    1: 'info', // 未开始
    2: 'warning', // 进行中
    3: 'success', // 已关闭
  };
  return statusMap[status || 1] || 'info';
}

// 获取状态文本 - 基于 Swagger ProjectTaskStatus 枚举
function getStatusText(status: number | undefined) {
  const statusTextMap: Record<number, string> = {
    1: '未开始',
    2: '进行中',
    3: '已关闭',
  };
  return statusTextMap[status || 1] || '未开始';
}

// 格式化进度
function formatProgress(val: number): string {
  return val + '%';
}

// 获取任务类型文本
function getTaskTypeText(taskType: number): string {
  const taskTypeMap = {
    1: '里程碑作业',
    2: '任务作业',
    3: 'WBS作业',
    4: '配合作业',
  };
  return taskTypeMap[taskType as keyof typeof taskTypeMap] || '未知';
}

// 获取专业文本
function getMajorText(major: number): string {
  const majorMap = {
    1: '产品策划',
    2: '造型开发',
    3: '平台开发',
    4: '工程开发',
    5: '整车工程/总布置',
    6: '试验认证',
    7: '生产准备',
    8: '项目管理/系统工程',
    9: '新兴技术开发',
  };
  return majorMap[major as keyof typeof majorMap] || '未知';
}

// 获取任务作业类型文本
function getTaskActivityTypeText(taskActivityType: number): string {
  const taskActivityTypeMap = {
    1: '设计任务',
    2: '试验任务',
    3: '评审任务',
    4: '采购任务',
  };
  return (
    taskActivityTypeMap[taskActivityType as keyof typeof taskActivityTypeMap] ||
    '未知'
  );
}

// 格式化日期显示
function formatDate(dateStr: string): string {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  } catch {
    return dateStr;
  }
}

// 查看任务详情
function handleView(row: TaskItem) {
  router.push({
    path: '/space/plan/detail',
    query: { id: row.projTaskId, projectId: row.projId },
  });
}

// 树形结构相关
const tabList = ref([{ name: '项目' }]);
const curTab = ref(0);

function tabChange(idx: number) {
  curTab.value = idx;
}

// 使用 store 中的树形数据 - 转换为BtTree组件期望的格式
const treeList = computed(() => {
  const convertToTreeFormat = (nodes: ProjectTreeNode[]): ProjectTreeNode[] => {
    return nodes.map((node) => {
      const converted = {
        label: node.projName || '未命名项目',
        value: node.projId,
        ...node,
      };

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        converted.children = convertToTreeFormat(node.children);
      }

      return converted;
    });
  };

  const nodes = convertToTreeFormat(store.treeData);
  return nodes;
});

// 树组件引用
const treeRef = useTemplateRef<any>('treeRef');

// 树节点变化处理
function treeChange(treeNodes: any[]) {
  store.handleTreeChange(treeNodes);
}

// 任务数据 - 添加序号
const tasks = computed(() => {
  const result = generateTaskNumbers(store.tasks);
  return result;
});

// 生命周期
onMounted(() => {
  init();
});
</script>
<template>
  <div class="task-list">
    <div class="content-wrapper">
      <div class="list-view">
        <div class="sidebar">
          <BtTree
            ref="treeRef"
            name="任务"
            subname="项目"
            :tabList="tabList"
            :curTab="curTab"
            v-model="treeList"
            :showCheckbox="true"
            :showAdd="false"
            :indent="18"
            :showExpandIcon="true"
            @tab-change="tabChange"
            @change="treeChange"
          />
        </div>
        <div class="container">
          <!-- 页签 -->
          <div class="header">
            <TaskTabsWithToolbar
              :tabs="store.tabs"
              :activeTab="store.activeTab"
              @change="handleTabChange"
              class="task-tabs"
            >
              <template #toolbar>
                <div class="right-opt flex flex-center">
                  <el-dropdown placement="bottom-end" class="item">
                    <el-button class="btn"> Excel </el-button>
                    <template #dropdown>
                      <el-dropdown-menu style="width: 215px">
                        <el-dropdown-item @click="store.exportProjectTask"
                          >导出列表（Excel）</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                  <BtSearch
                    class="item"
                    v-model="keyword"
                    placeholder="请输入搜索内容"
                    @search="toSearch"
                    @clear="toSearch"
                  ></BtSearch>
                  <BtColumn
                    ref="columnRef"
                    v-model="store.columnList"
                  ></BtColumn>
                </div>
              </template>
            </TaskTabsWithToolbar>
          </div>

          <div class="cont">
            <!-- 任务列表容器 -->
            <div class="table-wrapper">
              <el-table
                ref="tableContainerRef"
                v-loading="tasksLoading"
                class="table"
                :data="tasks"
                row-key="projTaskId"
                default-expand-all
                :row-click="handleView"
              >
                <el-table-column type="selection" width="33" fixed="left" />

                <!-- 序号列 -->
                <el-table-column label="#" width="32" align="left" type="">
                  <template #default="{ row }">
                    <span class="task-number">{{ row.taskNumber }}</span>
                  </template>
                </el-table-column>

                <!-- 各列单独处理，确保筛选图标与列对应 -->
                <template v-for="column in showColumn" :key="column.field">
                  <!-- 项目任务名称列 -->
                  <el-table-column
                    v-if="column.field === 'projTaskName'"
                    :property="column.field"
                    :label="column.label"
                    :width="String(column.width)"
                    :indent="20"
                    show-overflow-tooltip
                  >
                    <template #header>
                      <BtThFilter
                        v-if="column.filter"
                        :title="column.label"
                        :type="column.filter.type"
                        :inputType="column.filter.inputType"
                        :showOrder="column.filter.showOrder"
                        :placeholder="column.filter.placeholder"
                        :options="column.filter.data"
                        v-model="column.filterValue"
                        v-model:order="column.filterOrder"
                        @change="changeFilter($event, column.field)"
                      ></BtThFilter>
                      <span v-else>{{ column.label }}</span>
                    </template>
                    <template #default="{ row }">
                      <span class="task-name">{{ row.projTaskName }}</span>
                    </template>
                  </el-table-column>

                  <!-- 状态列 -->
                  <el-table-column
                    v-else-if="column.field === 'projTaskStatus'"
                    :property="column.field"
                    :label="column.label"
                    :width="column.width"
                  >
                    <template #header>
                      <BtThFilter
                        v-if="column.filter"
                        :title="column.label"
                        :type="column.filter.type"
                        :inputType="column.filter.inputType"
                        :showOrder="column.filter.showOrder"
                        :placeholder="column.filter.placeholder"
                        :options="column.filter.data"
                        v-model="column.filterValue"
                        v-model:order="column.filterOrder"
                        @change="changeFilter($event, column.field)"
                      ></BtThFilter>
                      <span v-else>{{ column.label }}</span>
                    </template>
                    <template #default="{ row }">
                      {{ getStatusText(row.projTaskStatus) }}
                    </template>
                  </el-table-column>

                  <!-- 进度列 -->
                  <el-table-column
                    v-else-if="column.field === 'actualProgress'"
                    :property="column.field"
                    :label="column.label"
                    :width="column.width"
                  >
                    <template #header>
                      <BtThFilter
                        v-if="column.filter"
                        :title="column.label"
                        :type="column.filter.type"
                        :inputType="column.filter.inputType"
                        :showOrder="column.filter.showOrder"
                        :placeholder="column.filter.placeholder"
                        :options="column.filter.data"
                        v-model="column.filterValue"
                        v-model:order="column.filterOrder"
                        @change="changeFilter($event, column.field)"
                      ></BtThFilter>
                      <span v-else>{{ column.label }}</span>
                    </template>
                    <template #default="{ row }">
                      <el-progress
                        class="progress"
                        color="#52C41A"
                        :stroke-width="12"
                        :percentage="row.actualProgress || 0"
                        :format="formatProgress"
                      />
                    </template>
                  </el-table-column>

                  <!-- 超期列 -->
                  <el-table-column
                    v-else-if="column.field === 'overdueDays'"
                    :property="column.field"
                    :label="column.label"
                    :width="column.width"
                    :align="column.align"
                  >
                    <template #header>
                      <BtThFilter
                        v-if="column.filter"
                        :title="column.label"
                        :type="column.filter.type"
                        :inputType="column.filter.inputType"
                        :showOrder="column.filter.showOrder"
                        :placeholder="column.filter.placeholder"
                        :options="column.filter.data"
                        v-model="column.filterValue"
                        v-model:order="column.filterOrder"
                        @change="changeFilter($event, column.field)"
                      ></BtThFilter>
                      <span v-else>{{ column.label }}</span>
                    </template>
                    <template #default="{ row }">
                      <el-tag v-if="row.overdueDays > 0" type="danger"
                        >是</el-tag
                      >
                      <el-tag v-else type="success">否</el-tag>
                    </template>
                  </el-table-column>

                  <!-- 作业类型列 -->
                  <el-table-column
                    v-else-if="column.field === 'taskType'"
                    :property="column.field"
                    :label="column.label"
                    :width="column.width"
                    :align="column.align"
                  >
                    <template #header>
                      <BtThFilter
                        v-if="column.filter"
                        :title="column.label"
                        :type="column.filter.type"
                        :inputType="column.filter.inputType"
                        :showOrder="column.filter.showOrder"
                        :placeholder="column.filter.placeholder"
                        :options="column.filter.data"
                        v-model="column.filterValue"
                        v-model:order="column.filterOrder"
                        @change="changeFilter($event, column.field)"
                      ></BtThFilter>
                      <span v-else>{{ column.label }}</span>
                    </template>
                    <template #default="{ row }">
                      {{ getTaskTypeText(row.taskType) }}
                    </template>
                  </el-table-column>

                  <!-- 专业列 -->
                  <el-table-column
                    v-else-if="column.field === 'major'"
                    :property="column.field"
                    :label="column.label"
                    :width="column.width"
                  >
                    <template #header>
                      <BtThFilter
                        v-if="column.filter"
                        :title="column.label"
                        :type="column.filter.type"
                        :inputType="column.filter.inputType"
                        :showOrder="column.filter.showOrder"
                        :placeholder="column.filter.placeholder"
                        :options="column.filter.data"
                        v-model="column.filterValue"
                        v-model:order="column.filterOrder"
                        @change="changeFilter($event, column.field)"
                      ></BtThFilter>
                      <span v-else>{{ column.label }}</span>
                    </template>
                    <template #default="{ row }">
                      {{ getMajorText(row.major) }}
                    </template>
                  </el-table-column>

                  <!-- 任务作业类型列 -->
                  <el-table-column
                    v-else-if="column.field === 'taskActivityType'"
                    :property="column.field"
                    :label="column.label"
                    :width="column.width"
                    :align="column.align"
                  >
                    <template #header>
                      <BtThFilter
                        v-if="column.filter"
                        :title="column.label"
                        :type="column.filter.type"
                        :inputType="column.filter.inputType"
                        :showOrder="column.filter.showOrder"
                        :placeholder="column.filter.placeholder"
                        :options="column.filter.data"
                        v-model="column.filterValue"
                        v-model:order="column.filterOrder"
                        @change="changeFilter($event, column.field)"
                      ></BtThFilter>
                      <span v-else>{{ column.label }}</span>
                    </template>
                    <template #default="{ row }">
                      {{ getTaskActivityTypeText(row.taskActivityType) }}
                    </template>
                  </el-table-column>

                  <!-- 实际开始时间列 -->
                  <el-table-column
                    v-else-if="column.field === 'actualStartDate'"
                    :property="column.field"
                    :label="column.label"
                    :width="column.width"
                    :align="column.align"
                  >
                    <template #header>
                      <BtThFilter
                        v-if="column.filter"
                        :title="column.label"
                        :type="column.filter.type"
                        :inputType="column.filter.inputType"
                        :showOrder="column.filter.showOrder"
                        :placeholder="column.filter.placeholder"
                        :options="column.filter.data"
                        v-model="column.filterValue"
                        v-model:order="column.filterOrder"
                        @change="changeFilter($event, column.field)"
                      ></BtThFilter>
                      <span v-else>{{ column.label }}</span>
                    </template>
                    <template #default="{ row }">
                      {{ formatDate(row.actualStartDate) }}
                    </template>
                  </el-table-column>

                  <!-- 其他列 -->
                  <el-table-column
                    v-else
                    :property="column.field"
                    :label="column.label"
                    :width="String(column.width)"
                    :align="column.align"
                    show-overflow-tooltip
                  >
                    <template #header>
                      <BtThFilter
                        v-if="column.filter"
                        :title="column.label"
                        :type="column.filter.type"
                        :inputType="column.filter.inputType"
                        :showOrder="column.filter.showOrder"
                        :placeholder="column.filter.placeholder"
                        :options="column.filter.data"
                        v-model="column.filterValue"
                        v-model:order="column.filterOrder"
                        @change="changeFilter($event, column.field)"
                      ></BtThFilter>
                      <span v-else>{{ column.label }}</span>
                    </template>
                    <template #default="{ row }">
                      {{ row[column.field] }}
                    </template>
                  </el-table-column>
                </template>

                <!-- 操作列 -->
                <el-table-column
                  label="操作"
                  v-bind="tableConfig.optColumnAttr"
                  fixed="right"
                >
                  <template #default="">
                    <el-dropdown placement="bottom-end">
                      <div class="more-opt">
                        <el-icon class="icon"><MoreFilled /></el-icon>
                      </div>
                    </el-dropdown>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.task-list {
  height: 100%;
  display: flex;
  flex-direction: column;

  .content-wrapper {
    flex: 1;
    display: flex;
    overflow: hidden;
    position: relative;
  }

  .list-view {
    width: 100%;
    height: 100%;
    display: flex;
    overflow: hidden;
    padding: 10px;
  }

  .sidebar {
    width: 300px;
    height: 100%;
    margin-right: 10px;
    flex-shrink: 0;
  }

  .container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid var(--el-border-color);
  }

  .task-tabs {
    flex-shrink: 0;

    .right-opt {
      .item {
        margin-left: var(--base-margin);
      }
    }
  }

  .cont {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px;
    padding-bottom: 0;
    overflow: hidden;
    min-height: 0; // 确保flex子元素能够正确收缩

    .table-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .table-loading-more,
    .table-no-more {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px 20px;
      color: var(--el-text-color-secondary);
      font-size: 13px;

      transition: all 0.3s ease;

      .el-icon {
        margin-right: 6px;
        font-size: 14px;
      }
    }

    .table-no-more {
      color: var(--el-text-color-placeholder);
    }

    // 鼠标悬停效果
    .table-loading-more:hover {
      background-color: var(--el-fill-color-light);
    }

    .table {
      flex: 1;

      // 表格底部插槽样式
      :deep(.el-table__append-wrapper) {
        border-top: none;
      }

      :deep(.el-table__indent) {
        padding-left: 12px;
      }

      .task-name {
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .task-number {
        color: var(--el-text-color-primary);
      }

      .progress {
        :deep(.el-progress__text) {
          font-size: 12px !important;
        }
      }
    }
  }

  .footer {
    padding: 10px 20px;
    text-align: right;
  }
}

.drop-opt {
  width: 140px;
}

.more-opt {
  cursor: pointer;
  .icon {
    font-size: 16px;
    color: var(--el-text-color-regular);
  }
}
</style>
