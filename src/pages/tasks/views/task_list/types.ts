import type { ProjectTaskResponse } from '@/pages/tasks/type.ts';
export type { ProjectTaskResponse } from '@/pages/tasks/type.ts';

/**
 * 兼容性类型别名 - 保持向后兼容
 */
export type TaskItem = ProjectTaskResponse;

/**
 * 项目成员
 */
export interface ProjectMemberResponse {
  /** ID */
  memberId?: number;
  /** 租户ID */
  tenantId?: number;
  /** 计划模版团队ID */
  teamId?: number;
  /** 数据类型(1 是项目成员数据 2 是模版定义的成员数据) */
  dataType?: number;
  /** 项目ID */
  projectId?: number;
  /** 用户ID */
  userId?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 最后修改人 */
  updateUser?: number;
  /** 最后修改时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
}

/**
 * 项目树节点接口
 * 用于表示左侧项目树的节点结构
 */
export interface ProjectTreeNode {
  /** 项目ID，唯一标识 */
  projId: number;
  /** 租户ID */
  tenantId?: number;
  /** 项目组合ID */
  portfolioId?: number;
  /** 项目集ID */
  programId?: number;
  /** 项目编码 */
  projCode?: string;
  /** 父项目ID */
  parentProjId?: number;
  /** 项目名称 */
  projName?: string;
  /** 项目类型 */
  projType?: number;
  /** 项目级别 */
  projLevel?: number;
  /** 项目分类 */
  projCategory?: number;
  /** 计划开始时间 */
  planStartDate?: string;
  /** 计划完成时间 */
  planFinishDate?: string;
  /** 实际完成时间 */
  actualFinishDate?: string;
  /** 实际开始时间 */
  actualStartDate?: string;
  /** 超期天数 */
  overdueDays?: number;
  /** 实际进度百分比 */
  actualProgress?: number;
  /** 计划进度百分比 */
  planProgress?: number;
  /** 项目状态 */
  status?: number;
  /** 责任人 */
  responsible?: number;
  /** 所属部门 */
  deptId?: number;
  /** 创建人 */
  createUser?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新人 */
  updateUser?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remark?: string;
  /** 账户ID */
  acctId?: number;
  /** 原始项目ID */
  origProjId?: number;
  /** 来源项目ID */
  sourceProjId?: number;
  /** 项目成员列表 */
  members?: ProjectMemberResponse[];
  /** 责任人姓名 */
  responsibleName?: string;
  /** 所属部门名称 */
  deptName?: string;
  /** 子项目列表 */
  children?: ProjectTreeNode[];
}

/**
 * 标签页项接口
 * 用于表示任务列表上方的标签页配置
 */
export interface TabItem {
  /** 标签页名称，用于标识 */
  name: string;
  /** 标签页显示标签 */
  label: string;
}

/**
 * 标签页类型 - 基于 Swagger ProjectTaskQueryRequest.queryFlag
 * 定义任务列表支持的标签页类型
 * 1全部;2我负责的;3我参与的;4我指派的;5我待办的
 */
export type TabType =
  /** 全部任务 */
  | 1
  /** 我负责的任务 */
  | 2
  /** 我参与的任务 */
  | 3
  /** 我指派的任务 */
  | 4
  /** 我待办的任务 */
  | 5;

/**
 * 任务状态枚举 - 基于 Swagger 项目任务状态定义
 */
export enum ProjectTaskStatus {
  /** 未开始 */
  NOT_STARTED = 1,
  /** 进行中 */
  IN_PROGRESS = 2,
  /** 已关闭 */
  CLOSED = 3,
}

/**
 * 任务状态标签映射
 */
export const TaskStatusLabels = {
  [ProjectTaskStatus.NOT_STARTED]: '未开始',
  [ProjectTaskStatus.IN_PROGRESS]: '进行中',
  [ProjectTaskStatus.CLOSED]: '已关闭',
} as const;

/**
 * 排序参数接口
 */
export interface SortParam {
  /** 排序字段名 */
  field: string;
  /** 排序方向：'asc' 或 'desc' */
  direction: string;
}

/**
 * 列头筛选条件接口
 */
export interface FilterConditions {
  /** 项目任务ID筛选 */
  projTaskId?: string;
  /** 任务名称筛选 */
  projTaskName?: string;
  /** 状态筛选 */
  projTaskStatus?: number[];
  /** 责任部门筛选 */
  projTaskResponsibleDept?: string;
  /** 项目角色筛选 */
  projTaskResponsibleProjRole?: string;
  /** 责任人筛选 */
  responsibleName?: string;
  /** 任务类型筛选 */
  taskType?: number[];
  /** 任务作业类型筛选 */
  taskActivityType?: number[];
  /** 专业筛选 */
  major?: number[];
  /** 计划开始日期范围筛选 */
  beginPlanStartDate?: string;
  endPlanStartDate?: string;
  /** 实际开始日期范围筛选 */
  beginActualStartDate?: string;
  endActualStartDate?: string;
  /** 计划完成日期范围筛选 */
  beginPlanFinishDate?: string;
  endPlanFinishDate?: string;
  /** 实际进度筛选 */
  actualProgress?: string;
  /** 超期筛选 */
  overdueDays?: boolean[];
  /** 所属项目筛选 */
  projectName?: string;
}

/**
 * 筛选参数接口
 * 用于任务列表的筛选条件
 */
export interface FilterParams {
  /** 标签页类型，决定显示哪类任务 */
  tabType: TabType;
  /** 关键词，可选，用于模糊搜索任务名称 */
  keyword?: string;
  /** 页码，可选，从1开始的页码 */
  page?: number;
  /** 每页大小，可选，每页返回的记录数 */
  pageSize?: number;
  /** 列头筛选条件 */
  filters?: FilterConditions;
  /** 排序参数 */
  sorts?: SortParam[];
}

export interface TaskListProps {
  tasks: ProjectTaskResponse[];
  loading: boolean;
}

export interface TaskTreeProps {
  treeData: ProjectTreeNode[];
  loading: boolean;
}

export interface TaskTabsProps {
  activeTab: TabType;
}

export interface TaskTabsEmits {
  (e: 'change', tab: TabType): void;
}

export interface TaskTreeEmits {
  (e: 'node-click', node: ProjectTreeNode): void;
}

/**
 * API 响应基础类型 - 基于 Swagger ApiResult 结构
 * 统一的 API 响应格式，所有接口都遵循此结构
 */
export interface ApiResult<T = any> {
  /** 响应状态码，200表示成功 */
  code: number;
  /** 响应消息，描述请求结果 */
  message: string;
  /** 响应时间戳，ISO 8601 格式 */
  timestamp: string;
  /** 响应数据，泛型类型，根据具体接口而定 */
  data: T;
}

/**
 * 兼容性 API 响应类型
 * 扩展 ApiResult，增加 success 字段以保持向后兼容
 */
export interface ApiResponse<T = any> extends ApiResult<T> {
  /** 请求是否成功，可选字段，用于兼容旧版本 */
  success?: boolean;
}

// 删除任务参数类型 - 基于 Swagger ProjectTaskDeleteRequest
export interface DeleteTaskParams {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
}

// 兼容性类型定义
export interface AcceptTaskParams extends ProjectTaskAcceptRequest {}
export interface RejectTaskParams extends ProjectTaskRejectRequest {}

/**
 * 项目任务查询请求 - 基于 Swagger ProjectTaskQueryRequest
 */
export interface ProjectTaskQueryRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务父任务ID */
  parentProjTaskId?: number;
  /** 所属项目ID */
  projIds?: number[];
  /** 阶段ID */
  phaseProjTaskId?: number;
  /** 项目任务名称 */
  projTaskName?: string;
  /** 项目任务状态(1 未开始,2 进行中,3 已关闭) */
  projTaskStatus?: number;
  /** 项目任务责任人 */
  projTaskResponsible?: number;
  /** 项目任务责任人项目角色 */
  projTaskResponsibleProjRole?: string;
  /** 项目任务责任部门 */
  projTaskResponsibleDept?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期天数 */
  overdueDays?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  taskType?: number;
  /** 任务作业类型 */
  taskActivityType?: number;
  /** 专业 */
  major?: string;
  /** 系统 */
  systemId?: number;
  /** 项目任务说明 */
  projTaskDesc?: string;
  /** 项目任务验收标准 */
  projTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 项目任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否阶段(1是,0否) */
  phaseFlag?: number;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 关注人用户ID */
  follower?: number;
  /** 排序条件 */
  sorts?: SortRequest[];
  /** 责任人姓名 */
  responsibleName?: string;
  /** 查询类型(1全部;2我负责的;3我参与的;4我指派的;5我待办的) */
  queryFlag?: number;
  /** 导出字段 */
  exportColumns?: string;
}

/**
 * 项目任务接受请求 - 基于 Swagger ProjectTaskAcceptRequest
 */
export interface ProjectTaskAcceptRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 接受意见 */
  acceptOpinion?: string;
  /** 过程文档列表 */
  processDocs?: ProjectTaskProcessDocAddRequest[];
}

/**
 * 项目任务驳回请求 - 基于 Swagger ProjectTaskRejectRequest
 */
export interface ProjectTaskRejectRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 拒绝意见 */
  rejectOpinion?: string;
  /** 过程文档列表 */
  processDocs?: ProjectTaskProcessDocAddRequest[];
}

/**
 * 项目任务删除请求 - 基于 Swagger ProjectTaskDeleteRequest
 */
export interface ProjectTaskDeleteRequest {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
}

/**
 * 项目任务过程文档新增请求 - 基于 Swagger ProjectTaskProcessDocAddRequest
 */
export interface ProjectTaskProcessDocAddRequest {
  /** 租户ID */
  tenantId?: number;
  /** 自增主键 */
  projTaskProcessDocId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 父文档ID,或文件夹ID */
  parentProjTaskProcessDocId?: number;
  /** 是否文件夹(0 否,1 是) */
  folderFlag?: number;
  /** 附件名 */
  docName?: string;
  /** 附件唯一标识 */
  docKey?: string;
  /** 附件id(关联附件表) */
  docId?: number;
}

/**
 * 排序请求 - 基于 Swagger SortRequest
 */
export interface SortRequest {
  /** 字段名 */
  field?: string;
  /** direction(asc/desc) */
  direction?: string;
}

// 列配置类型
export interface ColumnConfig {
  field: string;
  name: string;
  label: string;
  checked: boolean;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  fixed?: boolean | 'left' | 'right';
  show?: boolean;
  filter?: {
    type?: 'input' | 'radio' | 'checkbox';
    inputType?: 'input' | 'date' | 'dateRange';
    showOrder?: boolean;
    placeholder?: string;
    data?: any[];
  };
  filterValue?: any;
  filterOrder?: any;
}

/**
 * 标签页项接口 - 基于 Swagger TabType 定义
 */
export interface TabItem {
  name: string;
  label: string;
  value: TabType;
}

/**
 * 任务列表状态定义
 */
export interface TaskListState {
  // 树形结构数据
  treeData: ProjectTreeNode[];
  treeLoading: boolean;
  currentNode: ProjectTreeNode[];

  // 任务列表数据
  tasks: ProjectTaskResponse[];
  tasksLoading: boolean;

  // 页签数据
  activeTab: TabType;
  tabs: TabItem[];

  // 搜索关键字
  searchKeyword: string;

  // 分页参数
  page: number;
  pageSize: number;
  hasMore: boolean;

  // 列配置
  columnList: ColumnConfig[];

  // 筛选条件
  filterConditions: FilterConditions;
}
