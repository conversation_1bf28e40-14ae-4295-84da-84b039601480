import { send } from '@/libs/request';
import {
  projectsTreeList,
  projectTasksList,
  deleteProjectTask,
  // 任务详情接口保持不变
  taskDetail,
  exportProjectTask as exportProjectTaskUrl,
} from '@/service_url/tasks';
import type {
  ProjectTreeNode,
  TaskItem,
  DeleteTaskParams,
  FilterParams,
  ProjectTaskQueryRequest,
} from './types';

/**
 * 获取项目树表
 */
export async function getProjects(): Promise<ProjectTreeNode[]> {
  // 实际API调用
  const response = await send({
    method: 'POST',
    url: projectsTreeList,
    data: {},
  });
  if (!response || !Array.isArray(response)) {
    return [];
  }

  return response;
}

/**
 * 获取任务列表
 */
export async function getTasks(
  params: FilterParams & ProjectTaskQueryRequest
): Promise<TaskItem[]> {
  // 构建请求数据
  const requestData: any = {
    queryFlag: params.tabType,
    projIds: params.projIds,
    projTaskName: params.keyword,
  };

  // 处理筛选条件
  if (params.filters) {
    const filters = params.filters;

    // 项目任务ID筛选
    if (filters.projTaskId) {
      requestData.projTaskId = filters.projTaskId;
    }

    // 任务名称筛选
    if (filters.projTaskName) {
      requestData.projTaskName = filters.projTaskName;
    }

    // 状态筛选
    if (filters.projTaskStatus && filters.projTaskStatus.length > 0) {
      requestData.projTaskStatusList = filters.projTaskStatus;
    }

    // 责任部门筛选
    if (filters.projTaskResponsibleDept) {
      requestData.projTaskResponsibleDept = filters.projTaskResponsibleDept;
    }

    // 项目角色筛选
    if (filters.projTaskResponsibleProjRole) {
      requestData.projTaskResponsibleProjRole =
        filters.projTaskResponsibleProjRole;
    }

    // 责任人筛选
    if (filters.responsibleName) {
      requestData.responsibleName = filters.responsibleName;
    }

    // 计划开始日期范围筛选
    if (filters.beginPlanStartDate && filters.endPlanStartDate) {
      requestData.beginPlanStartDate = filters.beginPlanStartDate;
      requestData.endPlanStartDate = filters.endPlanStartDate;
    }

    // 计划完成日期范围筛选
    if (filters.beginPlanFinishDate && filters.endPlanFinishDate) {
      requestData.beginPlanFinishDate = filters.beginPlanFinishDate;
      requestData.endPlanFinishDate = filters.endPlanFinishDate;
    }

    // 实际进度筛选
    if (filters.actualProgress) {
      requestData.actualProgress = filters.actualProgress;
    }

    // 超期筛选
    if (filters.overdueDays && filters.overdueDays.length > 0) {
      requestData.overdueDaysList = filters.overdueDays;
    }

    // 所属项目筛选
    if (filters.projectName) {
      requestData.projectName = filters.projectName;
    }

    // 任务类型筛选
    if (filters.taskType && filters.taskType.length > 0) {
      requestData.taskTypeList = filters.taskType;
    }

    // 任务作业类型筛选
    if (filters.taskActivityType && filters.taskActivityType.length > 0) {
      requestData.taskActivityTypeList = filters.taskActivityType;
    }

    // 专业筛选
    if (filters.major && filters.major.length > 0) {
      requestData.majorList = filters.major;
    }

    // 实际开始日期范围筛选
    if (filters.beginActualStartDate && filters.endActualStartDate) {
      requestData.beginActualStartDate = filters.beginActualStartDate;
      requestData.endActualStartDate = filters.endActualStartDate;
    }
  }

  // 处理排序参数
  if (params.sorts && params.sorts.length > 0) {
    requestData.sorts = params.sorts;
  }

  const response = await send({
    method: 'POST',
    url: projectTasksList,
    data: requestData,
  });

  // 检查关键数据是否存在
  if (!response || !Array.isArray(response)) {
    return [];
  }

  return response;
}

/**
 * 获取任务详情
 */
export async function getTaskDetail(id: string | number): Promise<TaskItem> {
  return send({
    url: taskDetail(id),
  });
}

/**
 * 删除任务
 */
export async function deleteTaskById(params: DeleteTaskParams): Promise<any> {
  return send({
    method: 'DELETE',
    url: deleteProjectTask,
    data: params,
  });
}

/**
 * 导出项目任务
 */
export async function exportProjectTask(
  params?: FilterParams & ProjectTaskQueryRequest
): Promise<void> {
  // 项目任务导出暂时使用简单的POST请求
  // 如果需要支持筛选参数，可以参考独立任务的实现方式
  const response = await send({
    method: 'POST',
    url: exportProjectTaskUrl,
    data: params
      ? {
          queryFlag: params.tabType,
          projIds: params.projIds,
          projTaskName: params.keyword,
        }
      : {},
  });

  // 导出函数通常不需要返回值，文件下载由浏览器处理
  return response;
}
