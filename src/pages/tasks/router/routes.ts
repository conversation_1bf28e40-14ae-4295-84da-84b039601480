import type { RouteRecordRaw } from 'vue-router';
import Layout from '@/components/non_biz/layout/index.vue';

const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'tasks',
    component: Layout,
    meta: {
      title: '任务',
    },
    redirect: '/project-tasks',
    children: [
      {
        path: '/project-tasks',
        name: 'ProjectTasks',
        component: () => import('@/pages/tasks/views/task_list/TaskList.vue'),
        meta: {
          title: '项目任务',
          icon: '项目任务',
        },
      },
      {
        path: '/independent-tasks',
        name: 'IndependentTasks',
        meta: {
          title: '独立任务',
          icon: '独立任务',
        },
        redirect: '/independent-tasks/list',
        children: [
          {
            path: '/independent-tasks/list',
            name: 'IndependentTaskList',
            component: () =>
              import(
                '@/pages/tasks/views/independent_tasks/list/IndependentTasks.vue'
              ),
            meta: {
              title: '独立任务列表',
              icon: '列表',
            },
          },
          {
            path: '/independent-tasks/detail/:id',
            name: 'IndependentTaskDetail',
            component: () =>
              import(
                '@/pages/tasks/views/independent_tasks/detail/IndependentTaskDetail.vue'
              ),
            meta: {
              title: '独立任务详情',
              icon: '详情',
            },
          },
          {
            path: '/independent-tasks/detail/:taskId/issue/:issueId',
            name: 'IndependentTaskIssueDetail',
            component: () =>
              import(
                '@/pages/tasks/views/independent_tasks/detail/components/issue_list/issue_detail/IssueDetail.vue'
              ),
            meta: {
              title: '任务问题详情',
              icon: '详情',
            },
          },
          {
            path: '/independent-tasks/detail/:taskId/risk/:riskId',
            name: 'IndependentTaskRiskDetail',
            component: () =>
              import(
                '@/pages/tasks/views/independent_tasks/detail/components/risk_list/risk_detail/RiskDetail.vue'
              ),
            meta: {
              title: '任务风险详情',
              icon: '详情',
            },
          },
        ],
      },
      {
        path: '/task-tags',
        name: 'TaskTags',
        meta: {
          title: '任务标签',
          icon: '任务标签',
        },
        redirect: '/task-tags/list',
        children: [
          {
            path: '/task-tags/list',
            name: 'TaskTagList',
            component: () =>
              import('@/pages/tasks/views/task_tags/list/TaskTags.vue'),
            meta: {
              title: '标签列表',
              icon: '列表',
            },
          },
          {
            path: '/task-tags/manage',
            name: 'TaskTagManage',
            component: () =>
              import('@/pages/tasks/views/task_tags/manage/TaskTagManage.vue'),
            meta: {
              title: '标签维护',
              icon: '详情',
            },
          },
        ],
      },
      {
        path: '/my-tasks',
        name: 'MyTasks',
        component: () => import('@/pages/tasks/views/my_tasks/MyTasks.vue'),
        meta: {
          title: '我的任务',
          icon: '我的任务',
        },
      },
      {
        path: '/my-schedule',
        name: 'MySchedule',
        component: () =>
          import('@/pages/tasks/views/my_schedule/MySchedule.vue'),
        meta: {
          title: '我的日程',
          icon: '我的日程',
          hideMenu: true,
        },
      },
    ],
  },
];

export default constantRoutes;
