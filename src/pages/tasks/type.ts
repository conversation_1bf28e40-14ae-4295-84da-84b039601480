/**
 * 项目任务响应接口 - 基于 Swagger ProjectTaskResponse
 * 用于表示项目任务列表中的单个任务项
 */
export interface ProjectTaskResponse {
  /** 租户ID */
  tenantId?: number;
  /** 项目任务ID */
  projTaskId?: number;
  /** 项目任务父任务ID */
  parentProjTaskId?: number;
  /** 所属项目ID */
  projId?: number;
  /** 阶段ID */
  phaseProjTaskId?: number;
  /** 项目任务名称 */
  projTaskName?: string;
  /** 项目任务状态(1 未开始,2 进行中,3 已关闭) */
  projTaskStatus?: number;
  /** 项目任务责任人 */
  projTaskResponsible?: number;
  /** 项目任务责任人项目角色 */
  projTaskResponsibleProjRole?: string;
  /** 项目任务责任部门 */
  projTaskResponsibleDept?: number;
  /** 计划进度 */
  planProgress?: number;
  /** 实际进度 */
  actualProgress?: number;
  /** 超期天数 */
  overdueDays?: number;
  /** 作业类型(1 里程碑作业,2 任务作业,3 wbs作业,4 配合作业) */
  taskType?: number;
  /** 任务作业类型 */
  taskActivityType?: number;
  /** 专业 */
  major?: string;
  /** 系统 */
  systemId?: number;
  /** 项目任务说明 */
  projTaskDesc?: string;
  /** 项目任务验收标准 */
  projTaskAcceptCriteria?: string;
  /** 计划开始日期 */
  planStartDate?: string;
  /** 实际开始日期 */
  actualStartDate?: string;
  /** 计划完成日期 */
  planFinishDate?: string;
  /** 工期 */
  duration?: number;
  /** 项目任务绑定的工作流定义ID */
  workflowId?: string;
  /** 是否阶段(1是,0否) */
  phaseFlag?: number;
  /** 是否自动进度(1 是 0 否) */
  autoProgressFlag?: number;
  /** 实际完成日期 */
  actualFinishDate?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建者ID */
  createUser?: number;
  /** 更新者ID */
  updateUser?: number;
  /** 删除标记(0: 未删除, 大于 1: 已删除) */
  deleted?: number;
  /** 备注 */
  remark?: string;
  /** 责任人姓名 */
  responsibleName?: string;
  /** 项目名称 */
  projectName?: string;
  /** 任务层级，用于前端显示 */
  level?: number;
  /** 任务索引，用于排序 */
  index?: number;
  /** 层级序号，如 1, 1.1, 1.2, 2, 2.1 等 */
  taskNumber?: string;
  /** 是否有子任务 */
  hasChildren?: boolean;
  /** 子任务列表，嵌套的任务结构 */
  children?: ProjectTaskResponse[];
}

/**
 * API 响应基础类型
 * 统一的 API 响应格式，所有接口都遵循此结构
 */
export interface ApiResult<T = any> {
  /** 响应状态码，200表示成功 */
  code: number;
  /** 响应消息，描述请求结果 */
  message: string;
  /** 响应时间戳，ISO 8601 格式 */
  timestamp: string;
  /** 响应数据，泛型类型，根据具体接口而定 */
  data: T;
}

/**
 * 分页列表接口响应类型
 * 所有分页列表接口的响应类型都继承此接口
 *
 */
export interface PageListResponse<T = any> {
  /** 数据列表 */
  list: T[];
  /** 总记录数，符合查询条件的任务总数 */
  total: number;
  /** 当前页码 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
  /** 总页数 */
  pages: number;
}
