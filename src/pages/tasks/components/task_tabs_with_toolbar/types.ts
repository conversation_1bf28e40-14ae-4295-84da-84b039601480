// 基于 Swagger 定义的 TabType - 数字类型
export type TabType =
  | 1 // 全部
  | 2 // 我负责的
  | 3 // 我参与的
  | 4 // 我指派的
  | 5; // 我待办的

// 标签页项类型
export interface TabItem {
  name: string;
  label: string;
  value: TabType;
}

// 组件 Props 类型
export interface TaskTabsWithToolbarProps {
  tabs: TabItem[];
  activeTab: TabType;
}

// 组件事件类型
export interface TaskTabsWithToolbarEvents {
  (e: 'change', tab: TabType): void;
}

// 标签页状态类型
export interface TabState {
  currentIndex: number;
  selectedTab: TabItem | null;
}
