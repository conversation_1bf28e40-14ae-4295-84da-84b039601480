<template>
  <div class="task-tabs-with-toolbar">
    <BtTab
      :list="tabs"
      :value="currentTabIndex"
      nameKey="label"
      @change="handleTabChange"
    >
      <template #right>
        <slot name="toolbar"></slot>
      </template>
    </BtTab>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BtTab from '@/components/non_biz/bt_tab/BtTab.vue';
import type {
  TaskTabsWithToolbarProps,
  TaskTabsWithToolbarEvents,
  TabItem,
} from './types';

const props = defineProps<TaskTabsWithToolbarProps>();
const emits = defineEmits<TaskTabsWithToolbarEvents>();

// 将 activeTab 转换为索引
const currentTabIndex = computed(() => {
  return props.tabs.findIndex((tab) => tab.value === props.activeTab);
});

// 处理标签页切换
function handleTabChange(index: number) {
  const tab = props.tabs[index] as TabItem;
  if (tab) {
    emits('change', tab.value);
  }
}
</script>

<style scoped lang="scss"></style>
