<script setup lang="ts">
import { logoImage, logoTextImage } from '@/service_url/shared';
import { storeToRefs } from 'pinia';
import { useLoginStore } from '@pages/main/views/login/LoginStore.ts';

const store = useLoginStore();
const { formData, isLoading } = storeToRefs(store);
</script>

<template>
  <div
    class="main-login"
    v-loading="isLoading"
    element-loading-text="正在登录..."
  >
    <div class="logo">
      <img class="i1" :src="logoImage.src" alt="logoImage.alt" />
      <img class="i2" :src="logoTextImage.src" :alt="logoTextImage.alt" />
    </div>
    <el-form class="form" label-position="top">
      <el-form-item label="用户名/电子邮件/手机号码">
        <el-input v-model="formData.username"></el-input>
      </el-form-item>
      <el-form-item label="密码">
        <el-input v-model="formData.password" type="password"></el-input>
      </el-form-item>
      <div class="else flex flex-center flex-jc-sb">
        <el-checkbox v-model="formData.rememberMe">记住账号</el-checkbox>
        <div class="link">忘记密码？</div>
      </div>
      <div class="operate">
        <el-button class="btn" type="primary" @click="store.login"
          >登录</el-button
        >
        <div class="register">
          还没有账号？<span class="link">立即注册</span>
        </div>
      </div>
    </el-form>
  </div>
</template>

<style scoped lang="scss">
.main-login {
  max-width: 483px;
  margin: 0 auto;
  padding-top: 25vh;
  :deep(.form) {
    .el-form-item {
      margin-bottom: 20px;
    }
    .el-form-item__label {
      font-weight: bold;
      line-height: 17px;
      margin-bottom: 7px;
    }
    .el-input {
      height: 42px;
    }
    .el-input__wrapper {
      border: 1px solid rgba(110, 110, 110, 1);
      box-shadow: unset;
    }
  }
  .logo {
    margin-bottom: 26px;
    img {
      display: block;
      margin: 0 auto;
      &.i1 {
        width: 61px;
        height: 55px;
      }
      &.i2 {
        width: 81px;
        height: 26px;
        margin-top: 10px;
      }
    }
  }
  .else {
    margin-top: -4px;
    line-height: 17px;
  }
  .operate {
    text-align: center;
    margin-top: 48px;
    .btn {
      width: 100%;
      height: 42px;
    }
    .register {
      color: rgba(153, 153, 153, 1);
      line-height: 17px;
      margin: 10px auto;
    }
  }
}
</style>
