import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import type { UserInfo, UserRoutes } from '@pages/main/views/login/types.ts';
import * as loginService from './LoginService.ts';
import { gotoPrePage } from '@/utils/routeUtil.ts';

export const useLoginStore = defineStore(
  'loginStore',
  () => {
    const formData = ref({
      username: 'admin',
      password: 'admin123',
      rememberMe: false,
    });
    const isLoading = ref(false);

    const userInfo = ref<UserInfo>({} as UserInfo);
    const userRoutes = ref<UserRoutes>([]);

    const isLoggedIn = computed(() => {
      return !!userInfo.value.user?.userId;
    });

    async function login() {
      isLoading.value = true;
      const username = formData.value.username;
      const password = formData.value.password;
      const res = await loginService.login(username, password);
      if (res?.token) {
        const [resInfo, resRoutes] = await Promise.all([
          loginService.getUserInfo(),
          loginService.getUserRoutes(),
        ]);
        userInfo.value = resInfo;
        userRoutes.value = resRoutes;
        setTimeout(gotoPrePage, 0);
      }
      isLoading.value = false;
      return res;
    }

    return { formData, isLoading, isLoggedIn, login, userInfo, userRoutes };
  },
  {
    persist: {
      pick: ['userInfo', 'userRoutes'],
    },
  }
);
