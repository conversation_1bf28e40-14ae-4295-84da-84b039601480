import { send } from '@/libs/request';
import { loginUrl, userInfo, userRoutes } from '@/service_url/shared.ts';

export async function login(username: string, password: string) {
  return send({
    method: 'post',
    url: loginUrl,
    data: {
      username,
      password,
    },
  });
}

export async function getUserInfo() {
  return send({ url: userInfo });
}

export async function getUserRoutes() {
  return send({ url: userRoutes });
}
