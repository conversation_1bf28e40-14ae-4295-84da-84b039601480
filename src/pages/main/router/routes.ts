import type { RouteRecordRaw } from 'vue-router';

const constantRoutes: RouteRecordRaw[] = [
    {
        path: '/',
        name: 'btit_main',
        component: () => import('@pages/main/views/main/Main.vue'),
    },
    {
        path: '/login',
        name: 'btit_login',
        component: () => import('@pages/main/views/login/Login.vue'),
        meta: {
            isFullPage: true // 添加一个自定义的元数据，表示这是一个全页面，不使用框架布局
        }
    },
    // {
    //     path: '/register',
    //     name: 'btit_register',
    //     component: () => import('@pages/main/views/login/Register.vue'),
    //     meta: {
    //         isFullPage: true // 添加一个自定义的元数据，表示这是一个全页面，不使用框架布局
    //     }
    // },
    {
        path: '/logout',
        name: 'btit_logout',
        component: {
            template: '<div>logout</div>'
        }
    }
];
export default constantRoutes;
