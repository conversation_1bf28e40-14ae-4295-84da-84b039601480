import {EventTypes} from "./EventTypes.ts";
import {PublishXMLCommand} from "@pages/workflows/views/stage/cmds/PublishXMLCommand.ts";
import {ExportSVGCommand} from "@pages/workflows/views/stage/cmds/ExportSVGCommand.ts";
import {ExportXMLCommand} from "@pages/workflows/views/stage/cmds/ExportXMLCommand.ts";
import {ImportXMLCommand} from "@pages/workflows/views/stage/cmds/ImportXMLCommand.ts";
import {UndoBpmnCommand} from "@pages/workflows/views/stage/cmds/UndoBpmnCommand.ts";
import {RedoBpmnCommand} from "@pages/workflows/views/stage/cmds/RedoBpmnCommand.ts";
import {VCairnConnector} from "v-cairn";

export class WorkflowConnector extends VCairnConnector {
    constructor() {
        super();
        this.initCommands();
    }

    initCommands() {
        this.addCommand(EventTypes.SAVE_AND_PUBLISH, PublishXMLCommand);
        this.addCommand(EventTypes.EXPORT_SVG, ExportSVGCommand);
        this.addCommand(EventTypes.EXPORT_XML, ExportXMLCommand);
        this.addCommand(EventTypes.IMPORT_XML, ImportXMLCommand);
        this.addCommand(EventTypes.BPMN_UNDO, UndoBpmnCommand);
        this.addCommand(EventTypes.BPMN_REDO, RedoBpmnCommand);
    }
}