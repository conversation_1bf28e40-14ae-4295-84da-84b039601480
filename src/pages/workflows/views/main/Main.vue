<script setup lang="ts">

import HeaderView from "@pages/workflows/views/header/HeaderView.vue";
import StageView from "@pages/workflows/views/stage/StageView.vue";
import {MainCtrl} from "./MainCtrl.ts";

const ctrl = MainCtrl();

</script>

<template>
  <div class="main-view">
    <HeaderView @export-svg="ctrl.onExportSvg"
                @publish="ctrl.onPublish"
                @undo-bpmn="ctrl.onUndo"
                @redo-bpmn="ctrl.onRedo"
                @export-xml="ctrl.onExportXml" @import-xml="ctrl.onImportXml" />
    <StageView ref="stageViewRef" />
    <input type="file" ref="inputRef"
           accept=".bpmn,application/bpmn20-xml"
           style="display:none;position:absolute;clip:rect(0 0 0 0);">
  </div>
</template>

<style scoped lang="scss">
.main-view {
  display: flex;
  height: 100%;
  min-height: 0;
  flex-direction: column;
  justify-content: center;
}
</style>