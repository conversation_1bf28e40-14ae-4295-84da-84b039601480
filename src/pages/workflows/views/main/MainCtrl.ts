import {useTemplateRef} from "vue";
import type StageView from "@pages/workflows/views/stage/StageView.vue";
import { VCairnEvent } from 'v-cairn';
import { EventTypes } from "@pages/workflows/common/EventTypes.ts";

export const MainCtrl = () => {

    const stageViewRef = useTemplateRef<InstanceType<typeof StageView>>('stageViewRef');
    const inputRef = useTemplateRef<HTMLInputElement>('inputRef');

    function onImportXml() {
        const v = stageViewRef.value!.getViewer();
        new VCairnEvent(EventTypes.IMPORT_XML, { viewer:v, xml: '', input: inputRef.value! }).emit();
    }

    function onExportXml() {
        const v = stageViewRef.value!.getViewer();
        new VCairnEvent(EventTypes.EXPORT_XML, v).emit();
    }

    function onExportSvg() {
        const v = stageViewRef.value!.getViewer();
        new VCairnEvent(EventTypes.EXPORT_SVG, v).emit();
    }

    function onPublish() {
        const v = stageViewRef.value!.getViewer();
        new VCairnEvent(EventTypes.SAVE_AND_PUBLISH, v).emit();
    }

    function onUndo() {
        const v = stageViewRef.value!.getViewer();
        new VCairnEvent(EventTypes.BPMN_UNDO, v).emit();
    }

    function onRedo() {
        const v = stageViewRef.value!.getViewer();
        new VCairnEvent(EventTypes.BPMN_REDO, v).emit();
    }

    return {
        onImportXml,
        onExportSvg,
        onExportXml,
        onPublish,
        onRedo,
        onUndo
    }


}