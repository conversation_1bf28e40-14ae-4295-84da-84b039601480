import {BtitCommand} from "@/libs/btit/BtitCommand.ts";
import {getTimestamp} from "@/utils/DateUtil.ts";
import {ElMessage, ElMessageBox} from "element-plus";
import { VCairnEvent } from "v-cairn";

export class ExportXMLCommand extends BtitCommand {

    execute(evt:VCairnEvent) {
        const viewer = evt.data;
        const filename = prompt('请输入文件名（可选）', 'bpmn')!;
        if (!filename) return;
        this.saveXMLToLocal(viewer, filename).catch();
    }

    async saveXMLToLocal(viewer: any, filename:string) {
        try {
            const { xml } = await viewer.saveXML({
                format: true,
                preamble: true
            });

            const blob = new Blob([xml!], {
                type: 'application/xml;charset=utf-8'
            });

            const url = window.URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `${filename}_${getTimestamp()}.bpmn`;

            document.body.appendChild(a);
            a.click();

            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            ElMessage({
                message: '导出XML成功',
                type: 'success',
                plain: true,
            });
        } catch (err:any) {
            await ElMessageBox.confirm(err.message || err.toString(), '导出XML失败', {
                type: 'error'
            })
        }
    }

}