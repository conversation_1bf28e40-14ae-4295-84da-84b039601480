import {BtitCommand} from "@/libs/btit/BtitCommand.ts";
import { VCairnEvent } from 'v-cairn';
import {ElMessage, ElMessageBox} from "element-plus";
import {getTimestamp} from "@/utils/DateUtil.ts";

export class ExportSVGCommand extends BtitCommand {

    execute(evt:VCairnEvent) {
        const viewer = evt.data;
        const filename = prompt('请输入文件名（可选）', 'bpmn_diagram')!;
        if (!filename) return;
        this.exportSvg(viewer, filename).catch();
    }

    async exportSvg(viewer:any, filename:string) {

        try {
            const { svg } = await viewer.saveSVG();
            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${filename}_${getTimestamp()}.svg`;
            a.click();
            window.URL.revokeObjectURL(url);
            ElMessage({
                message: '导出SVG成功',
                type: 'success',
                plain: true,
            })
        } catch (err:any) {
            await ElMessageBox.confirm(err.message || err.toString(), '导出SVG失败', {
                type: 'error'
            })
        }
    }

}