import {BtitCommand} from "@/libs/btit/BtitCommand.ts";
import type {ImportXMLEventData} from "@pages/workflows/views/stage/types.ts";
import type Viewer from "bpmn-js";
import { VCairnEvent } from 'v-cairn';

export class ImportXMLCommand extends BtitCommand {

    _viewer: Viewer|null;

    constructor() {
        super();
        this._viewer = null;
        this.onInputChange = this.onInputChange.bind(this);
    }

    execute(evt:VCairnEvent) {
        const data = evt.data as ImportXMLEventData;
        this._viewer = data.viewer;
        if (data.xml) {
            this.loadDefault(data.xml).catch();
        } else {
            this.selectXmlFile(data.input);
        }
    }

    async loadDefault(xml:string) {
        await this._viewer!.importXML(xml);
        this._viewer!.get<any>('canvas').zoom('fit-viewport');
    }

    selectXmlFile(input?:HTMLInputElement) {
        input?.addEventListener('change', this.onInputChange);
        input?.click();
    }

    onInputChange(evt: Event) {
        evt.stopPropagation();
        evt.preventDefault();
        const files = (evt.target as HTMLInputElement).files;
        if (!files!.length) return;
        const file = files![0];
        const reader = new FileReader();
        reader.onload = async () => {
            evt.target!.removeEventListener('change', this.onInputChange);
            await this.loadDefault(reader.result as string);
        };
        reader.readAsText(file);
    }

}