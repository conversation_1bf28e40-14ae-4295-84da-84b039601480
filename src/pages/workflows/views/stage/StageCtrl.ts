import {onMounted, useTemplateRef} from "vue";
import BpmnModeler from 'bpmn-js/lib/Modeler';
import xmlData from './diagram.bpmn?raw';
import Viewer from "bpmn-js";
import { customTranslate } from './modules/transfer';

import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css';
import 'bpmn-js/dist/assets/diagram-js.css';
import 'bpmn-js/dist/assets/bpmn-js.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';
import '@bpmn-io/properties-panel/assets/properties-panel.css';

import {
    BpmnPropertiesPanelModule, // 属性面板
    BpmnPropertiesProviderModule,
} from 'bpmn-js-properties-panel';
import { VCairnEvent } from 'v-cairn';
import {EventTypes} from "@pages/workflows/common/EventTypes.ts";
import type {ImportXMLEventData} from "@pages/workflows/views/stage/types.ts";


export const StageCtrl = () => {


    let viewer:Viewer;
    const canvasRef = useTemplateRef<HTMLElement>('canvasRef');
    const propertiesPanelRef = useTemplateRef<HTMLElement>('js-properties-panel');

    function initEditor() {
        viewer = new BpmnModeler({
            container: canvasRef.value!,
            additionalModules: [
                BpmnPropertiesPanelModule,
                BpmnPropertiesProviderModule,
                {
                    translate: ['value', customTranslate]
                }
            ],
            propertiesPanel: {
                parent: propertiesPanelRef.value!
            }
        });

        const data: ImportXMLEventData = { viewer:viewer, xml: xmlData };
        new VCairnEvent(EventTypes.IMPORT_XML, data).emit();
    }

    onMounted(() => {
        initEditor();
    });

    function getViewer() {
        return viewer;
    }

    return {
        getViewer
    }

};