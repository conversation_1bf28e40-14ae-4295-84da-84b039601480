<script setup lang="ts">
  import {StageCtrl} from "./StageCtrl.ts";
  const ctrl = StageCtrl();

  function getViewer() {
    return ctrl.getViewer();
  }

  defineExpose({
    getViewer
  })

</script>

<template>
  <div ref="stageRef" class="stage-view">
    <div class="canvas" id="canvas" ref="canvasRef"></div>
    <div class="properties-panel-parent" ref="js-properties-panel"></div>
  </div>
</template>

<style scoped lang="scss">
:deep(.canvas) {
  .bjs-powered-by {
    display: none;
  }
}
.stage-view {
  flex: 1;
  display: flex;
  border-bottom: 1px solid #ccc;
  border-top: 1px solid #ccc;
  min-width: 0;
  align-items: stretch;
  .canvas {
    flex: 1;
  }
  .properties-panel-parent {
    flex-basis: 300px;
  }
}
.properties-panel-parent {
  border-left: 1px solid #ccc;
  overflow: auto;
  &:empty {
    display: none;
  }
  > .djs-properties-panel {
    padding-bottom: 70px;
    min-height:100%;
  }
}
</style>