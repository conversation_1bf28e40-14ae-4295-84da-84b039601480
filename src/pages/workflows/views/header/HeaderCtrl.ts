import type {ButtonEventName, ButtonEvents, ButtonItem} from "./types.ts";

export const HeaderCtrl = (emitter:ButtonEvents) => {

    const buttons:ButtonItem[] = [
        { id: 'import-xml', label: '导入XML', disabled: false },
        { id: 'export-xml', label: '导出XML', disabled: false },
        { id: 'export-svg', label: '导出SVG', disabled: false },
        { id: 'publish', label: '保存部署', disabled: false },
        { id: 'undo-bpmn', label: '撤销', disabled: false },
        { id: 'redo-bpmn', label: '重做', disabled: false },
    ];

    function onBtnClicked(item:ButtonItem) {
        const evtType = item.id as ButtonEventName;
        emitter(evtType, item);
    }

    return {
        buttons,
        onBtnClicked
    }

};