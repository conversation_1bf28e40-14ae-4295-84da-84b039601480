export type ButtonItem = {
    label: string,
    disabled: boolean,
    id: string
}

export type ButtonEvents = {
    (e: 'import-xml', item: ButtonItem): void;
    (e: 'export-xml', item: ButtonItem): void,
    (e: 'export-svg', item: ButtonItem): void,
    (e: 'publish', item: ButtonItem): void,
    (e: 'undo-bpmn', item: ButtonItem): void,
    (e: 'redo-bpmn', item: ButtonItem): void
};

export type ButtonEventName = ButtonEvents extends {
    (e: infer E, ...args: any[]): any
} ? E : never;