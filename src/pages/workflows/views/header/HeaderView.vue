<script setup lang="ts">
  import {HeaderCtrl} from "./HeaderCtrl.ts";
  import type { ButtonEvents } from './types.ts';
  const emitter = defineEmits<ButtonEvents>();
  const ctrl = HeaderCtrl(emitter);
</script>

<template>
  <div class="header-view">
    <el-button v-for="item in ctrl.buttons"
               @click="ctrl.onBtnClicked(item)"
               plain>{{ item.label }}</el-button>
  </div>
</template>

<style scoped lang="scss">
.header-view {
  margin: 15px;
}
</style>