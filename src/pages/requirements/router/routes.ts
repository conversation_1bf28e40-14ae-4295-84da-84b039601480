import type { RouteRecordRaw } from "vue-router";
import { RouterView } from "vue-router";
import Layout from "@/components/non_biz/layout/index.vue";


const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/",
    name: "requirements",
    component: Layout,
    meta: {
      title: "需求",
    },
    redirect: "/library",
    children: [
      {
        path: "/library",
        name: "Library",
        component: () => import("@pages/requirements/views/requirements_library/RequirementsLibrary.vue"),
        meta: {
          title: "需求库",
          icon: "需求库",
        },
      },
      {
        path: "/analysis",
        name: "Analysis",
        component: () => import("@pages/requirements/views/requirements_analysis/RequirementsAnalysis.vue"),
        meta: {
          title: "需求分析",
          icon: "需求分析",
        },
      },
      {
        path: "/value",
        name: "Value",
        component: () => import("@pages/requirements/views/requirements_value/RequirementsValue.vue"),
        meta: {
          title: "需求分析",
          icon: "需求分析",
        },
      },
      {
        path: "/my",
        name: "My",
        component: RouterView,
        meta: {
          title: "我的需求",
          icon: "我的需求",
        },
        redirect: '/my/list',
        children: [
          {
            path: '/my/list',
            name: 'MyList',
            component: () => import("@pages/requirements/views/requirements_my/RequirementsMy.vue"),
            meta: {
              title: "我的需求",
              icon: "我的需求",
            }
          },
          {
            path: '/my/deduplication',
            name: 'MyDeduplication',
            component: () => import("@pages/requirements/views/requirements_my/RequirementsDeduplication.vue"),
            meta: {
              title: '需求去重',
              icon: '我的需求'
            }
          }
        ]
      },
      {
        path: "/add",
        name: "Add",
        component: () => import("@pages/requirements/views/requirements_add/RequirementsAdd.vue"),
        meta: {
          title: "新增需求",
          icon: "我的需求",
          hideMenu: true,
        },
      },
    ],
  },
  
];
export default constantRoutes;
