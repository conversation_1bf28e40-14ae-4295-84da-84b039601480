import { ref, computed, useTemplateRef } from "vue";
import { useRouter } from "vue-router";

export function MyCtrl() {
  const curType = ref(0);
  const typeList = ref([
    { key: "list", name: "我待办的需求" },
    { key: "list", name: "我负责的需求" },
    { key: "list", name: "我参与的需求" },
    { key: "list", name: "我关注的需求" },
  ]);
  const changeType = (idx: number) => {
    curType.value = idx;
  };

  const search = ref({
    name: "",
  });
  function toSearch(val: string) {
    console.log(val);
  }

  const columnRef = useTemplateRef<any>('columnRef')
  const columnList = ref([
    { 
      field: 'level', label: '需求层级', width: '100', minWidth: '', show: true,
      filter: {
        options: [
          { label: '原始需求', value: '1', },
          { label: '初始需求', value: '2', },
          { label: '管理需求', value: '3', },
          { label: '数字化需求', value: '4', },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'type', label: '需求类型', width: '100', minWidth: '', show: true,
      filter: {
        options: [
          { label: '咨询需求', value: '1', },
          { label: '功能需求', value: '2', },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'name', label: '需求名称', width: '', minWidth: '200', show: true, 
    },
    { 
      field: 'status', label: '状态', width: '100', minWidth: '', show: true, 
      filter: {
        options: [
          { label: '进行中', value: '1', },
          { label: '开启', value: '2', },
          { label: '关闭', value: '3', },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'benefit', label: '利益相关方', width: '160', minWidth: '', show: true, 
    },
    { 
      field: 'source', label: '来源决策体系', width: '100', minWidth: '', show: true, 
    },
    { 
      field: 'bus_domain', label: '业务域', width: '100', minWidth: '', show: true, 
    },
    { 
      field: 'bus_component', label: '业务组件', width: '100', minWidth: '', show: true, 
    },
    { 
      field: 'bus_progress', label: '业务流程', width: '100', minWidth: '', show: true, 
    },
    { 
      field: 'app_domain', label: '应用域', width: '140', minWidth: '', show: true, 
    },
    { 
      field: 'it', label: 'IT系统', width: '140', minWidth: '', show: true, 
    },
    { 
      field: 'impact_flow', label: '影响业务流', width: '120', minWidth: '', show: true, 
    },
    { 
      field: 'impact_data', label: '影响数据', width: '120', minWidth: '', show: true, 
    },
    { 
      field: 'create_time', label: '需求创建时间', width: '120', minWidth: '', show: true, 
      filter: {
        search: true,
        order: true,
      },
      filterValue: {},
    },
  ])
  const showColumn = computed(()=>{
    return columnList.value.filter(v=>v.show);
  })
  function openColumn(){
    columnRef.value.open();
  }
  function changeFilter(info: any, type: string) {
    console.log(type,"字段筛选变化-------------",info);
  }


  const tableData = ref(<any>[]);
  for (let i = 0; i < 5; i++) {
    tableData.value.push({
      level: '原始需求',
      type: "咨询需求",
      name: "这是需求名称这是需求名称",
      status: '进行中',
      benefit: '文案文案文案',
      source: '文案文案',
      bus_domain: '文案文案',
      bus_component: '文案文案',
      bus_progress: '文案文案',
      app_domain: '文案文案',
      it: '文案文案',
      impact_flow: '文案文案',
      impact_data: '文案文案',
      create_time: '2025/01/08',
    });
  }

  const page = ref(1);
  function changePage(val: number) {
    console.log(val, page.value);
  }

  const router = useRouter();
  function handleAdd() {
    router.push("/add");
  }
  function handleDeduplication() {
    router.push('/my/deduplication')
  }

  return {
    curType,
    typeList,
    changeType,
    page,
    columnList,
    showColumn,
    openColumn,
    tableData,
    changePage,
    search,
    toSearch,
    handleAdd,
    handleDeduplication,
    changeFilter,
  };
}
