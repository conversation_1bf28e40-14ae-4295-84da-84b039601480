<script setup lang="ts">
import { RequirementDeduplicationCtrl } from "./RequirementDeduplicationCtrl";
import { Setting } from "@element-plus/icons-vue";
import DeduplicationAdd from './components/deduplication_add/DeduplicationAdd.vue'
import DeduplicationForm from './components/deduplication_form/DeduplicationForm.vue'
import DeduplicationTable from './components/deduplication_table/DeduplicationTable.vue'
import DeduplicationCard from './components/deduplication_card/DeduplicationCard.vue'

const { 
  curType, typeList, changeType, 
  keyword, handleSearch, handleSet,
  showDialog, openDialog,
} = RequirementDeduplicationCtrl();
</script>
<template>
  <div class="requirements-deduplication">
    <div class="sidebar">
      <DeduplicationForm />
    </div>
    <div class="container">
      <div class="header">
        <BtTab :list="typeList" :value="curType" @change="changeType">
          <template #right>
            <div class="right-opt flex flex-center">
              <el-button class="btn item" @click="openDialog">
                需求去重
              </el-button>
              <BtSearch
                class="item"
                v-model="keyword"
                placeholder="请输入搜索内容"
                @search="handleSearch"
              ></BtSearch>
              <SvgIcon class="icon item" name="列表界面" />
              <SvgIcon class="icon item" name="collapse" />
              <BtTooltip
                class="icon-tooltip "
                effect="dark"
                content="自定义字段"
                placement="bottom"
              >
                <el-icon class="item icon" @click="handleSet"><Setting /></el-icon>
              </BtTooltip>
              <!-- <BtColumn ref="columnRef" v-model="columnList"></BtColumn> -->
              <!-- <el-icon class="item icon" @click="openColumn"><Setting /></el-icon> -->
            </div>
          </template>
        </BtTab>
      </div>
      <div v-show="curType == 0" class="cont">
        <DeduplicationTable ref="tableRef" />
      </div>
      <div v-show="curType == 1" class="cont">
        <DeduplicationCard />
      </div>
    </div>
    <DeduplicationAdd v-model="showDialog" />
  </div>
</template>
<style scoped lang="scss">
.requirements-deduplication {
  height: 100%;
  display: flex;
  padding: 10px;
  .sidebar {
    width: 390px;
    height: 100%;
    margin-right: 10px;
    flex-shrink: 0;
    border: 1px solid var(--el-border-color);
  }
  .container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid var(--el-border-color);
  }
  .cont {
    flex: 1;
    padding: 10px 10px;
    padding-bottom: 0;
    overflow: hidden;
  }
  .footer {
    padding: 10px 20px;
    text-align: right;
  }
}
</style>
