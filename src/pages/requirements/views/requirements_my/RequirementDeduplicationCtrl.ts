import { ref, useTemplateRef } from 'vue';

export function RequirementDeduplicationCtrl() {
  const curType = ref(0);
  const typeList = ref([{ name: '列表界面' }, { name: '卡片界面' }]);
  function changeType(idx: number) {
    curType.value = idx;
  }

  const tableRef = useTemplateRef<any>('tableRef');

  const keyword = ref('');
  function handleSearch(val: string) {
    if (curType.value == 0) {
      tableRef.value.toSearch(val);
    }
  }

  function handleSet() {
    if (curType.value == 0) {
      tableRef.value.openColumn();
    }
  }

  const showDialog = ref(false);
  function openDialog() {
    showDialog.value = true;
  }

  return {
    curType,
    typeList,
    changeType,
    keyword,
    handleSearch,
    handleSet,
    showDialog,
    openDialog,
  };
}
