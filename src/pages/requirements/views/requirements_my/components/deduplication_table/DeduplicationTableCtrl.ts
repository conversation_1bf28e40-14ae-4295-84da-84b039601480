import { ref, computed, useTemplateRef } from "vue";
import { handleTree } from '@/utils/toolUtil'

export function DeduplicationTableCtrl() {
  
  const columnRef = useTemplateRef<any>('columnRef')
  const columnList = ref([
    { 
      field: 'name', label: '需求名称', width: '', minWidth: '250', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'benefit', label: '利益相关方', width: '140', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'source', label: '来源决策体系', width: '150', minWidth: '', show: true,
    },
    { 
      field: 'analysis', label: '需求分析', width: '160', minWidth: '', show: true,
    },
    { 
      field: 'creater', label: '创建人', width: '160', minWidth: '', show: true,
    },
    { 
      field: 'create_time', label: '创建时间', width: '153', minWidth: '', show: true,
      filter: {
        search: true,
        order: true,
      },
      filterValue: {},
    },
    { 
      field: 'update_time', label: '最后更新时间', width: '153', minWidth: '', show: true,
      filter: {
        search: true,
        order: true,
      },
      filterValue: {},
    },
  ])
  const showColumn = computed(()=>{
    return columnList.value.filter(v=>v.show);
  })
  function openColumn(){
    columnRef.value.open();
  }
  function changeFilter(info: any, type: string) {
    console.log(type,"字段筛选变化-------------",info);
  }


  const tableData = ref(<any>[
    { id: '1', parentId: '0', name: '需求名称需求名称需求名称', benefit: '集团', source: '来源决策体系', analysis: '去重', creater: '张三', create_time: '2025-08-08 12:20', update_time: '2025-08-08 12:20' },
    { id: '1.1', parentId: '1', name: '需求名称需求名称需求名称', benefit: '集团', source: '来源决策体系', analysis: '去重', creater: '张三', create_time: '2025-08-08 12:20', update_time: '2025-08-08 12:20' },
    { id: '1.1.1', parentId: '1.1', name: '需求名称需求名称需求名称', benefit: '集团', source: '来源决策体系', analysis: '去重', creater: '张三', create_time: '2025-08-08 12:20', update_time: '2025-08-08 12:20' },
    { id: '2', parentId: '0', name: '需求名称需求名称需求名称', benefit: '集团', source: '来源决策体系', analysis: '去重', creater: '张三', create_time: '2025-08-08 12:20', update_time: '2025-08-08 12:20' },
    { id: '3', parentId: '0', name: '需求名称需求名称需求名称', benefit: '集团', source: '来源决策体系', analysis: '去重', creater: '张三', create_time: '2025-08-08 12:20', update_time: '2025-08-08 12:20' },
    { id: '4', parentId: '0', name: '需求名称需求名称需求名称', benefit: '集团', source: '来源决策体系', analysis: '去重', creater: '张三', create_time: '2025-08-08 12:20', update_time: '2025-08-08 12:20' },
  ]);
  const tableTree = ref(handleTree(tableData.value))
  const indexMethod = (index: number) => {
    return tableData.value[index].id;
  };


  const page = ref(1);
  function changePage(val: number) {
    page.value = val;
  }


  const search = ref({
    name: "",
  });
  function toSearch(val: string) {
    search.value.name = val
  }

  return {
    columnList,
    showColumn,
    openColumn,
    tableData,
    tableTree,
    indexMethod,
    page,
    changePage,
    search,
    toSearch,
    changeFilter,
  };
}
