<script setup lang="ts">
import { tableConfig } from "@/app-config.ts";
import { DeduplicationTableCtrl } from "./DeduplicationTableCtrl";
import { MoreFilled } from "@element-plus/icons-vue";

const { 
  columnList, showColumn, tableTree, indexMethod,
  page, changePage,
  toSearch, openColumn, changeFilter,
} = DeduplicationTableCtrl();

defineExpose({toSearch,openColumn});
</script>
<template>
  <div class="deduplication-table">
    <div class="cont">
      <el-table
        class="table"
        :data="tableTree"
        row-key="id"
        style="width: 100%; height: 100%"
        default-expand-all
      >
        <el-table-column type="selection" width="40" />
        <el-table-column type="index" label="#" width="50" :index="indexMethod"></el-table-column>
        <template v-for="column in showColumn" :key="column.field" >
          <el-table-column :property="column.field" :label="column.label" :width="column.width" :min-width="column.minWidth" >
            <template #header>
              <BtThFilter v-if="column.filter" :title="column.label" :info="column.filter" v-model="column.filterValue" @change="changeFilter($event,column.field)"></BtThFilter>
              <span v-else >{{ column.label }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column fixed="right" label="操作" v-bind="tableConfig.optColumnAttr">
          <template #default>
            <el-dropdown placement="bottom-end">
              <div class="more-opt">
                <el-icon class="icon"><MoreFilled /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="border" style="width: 140px">
                  <el-dropdown-item>关注</el-dropdown-item>
                  <el-dropdown-item>提交</el-dropdown-item>
                  <el-dropdown-item>分发</el-dropdown-item>
                  <el-dropdown-item>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <BtPage v-model="page" :total="100" @change="changePage"></BtPage>
    </div>
    <BtColumn ref="columnRef" v-model="columnList">
      <span></span>
    </BtColumn>
  </div>
</template>
<style scoped lang="scss">
.deduplication-table {
  height: 100%;
  display: flex;
  flex-direction: column;
  .cont {
    flex: 1;
    overflow: hidden;
  }
  .footer {
    padding: 10px 20px;
    text-align: right;
  }
}
</style>
