<script setup lang="ts">
import { DeduplicationAddCtrl } from "./DeduplicationAddCtrl.ts";
import type { PropsType, EventsType } from "./types.ts"

const props = withDefaults(
    defineProps<PropsType>(),
    {
      modelValue: false,
    }
);

const emits = defineEmits<EventsType>();

const {
  dialogVisible,
  open,
  close,
  levelOption,
  userList,
  formData,
  formRules,
  submitForm,
  submitLoading,
} = DeduplicationAddCtrl(props, emits);

defineExpose({ open, close });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    title="需求去重"
    width="796"
    :before-close="close"
  >
    <el-form
      ref="ruleFormRef"
      class="form"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="需求编号" prop="id">
        <el-input
          v-model="formData.id"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>
      <div class="row">
        <el-form-item label="需求层级" prop="level">
          <BtSelect
            class="select"
            :list="levelOption"
            v-model="formData.level"
          ></BtSelect>
        </el-form-item>
        <el-form-item label="项目类型" prop="type">
          <BtSelect
            class="select"
            :list="levelOption"
            v-model="formData.type"
          ></BtSelect>
        </el-form-item>
      </div>
      <div class="row">
        <el-form-item label="开始时间" prop="startDate">
          <el-date-picker
            class="date-select"
            v-model="formData.startDate"
            type="date"
            placeholder="请选择"
          />
        </el-form-item>
        <el-form-item label="完成时间" prop="endDate">
          <el-date-picker
            class="date-select"
            v-model="formData.endDate"
            type="date"
            placeholder="请选择"
          />
        </el-form-item>
      </div>
      <div class="row ">
        <el-form-item label="项目责任人" prop="response">
          <BtPicker
            class="select"
            title="项目责任人"
            :list="userList"
            v-model="formData.response"
          ></BtPicker>
        </el-form-item>
        <el-form-item label="所属部门" prop="dept">
          <BtPicker
            class="select"
            title="参与人"
            :list="userList"
            v-model="formData.dept"
          ></BtPicker>
        </el-form-item>
      </div>
      <el-form-item label="项目成员" prop="member">
        <BtPicker
          class="w-100"
          title="参与人"
          :list="userList"
          showSearch
          multiple
          v-model="formData.member"
          :maxCollapseTags="6"
        ></BtPicker>
      </el-form-item>
      <el-form-item label="需求去重说明" prop="desc">
        <BtInput
          class="textarea"
          v-model="formData.desc"
          :rows="3"
          type="textarea"
          maxlength="300"
          show-word-limit
          placeholder="请输入"
        />
      </el-form-item>
      
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button :loading="submitLoading" type="primary" @click="submitForm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped lang="scss">
.form {
  .row {
    display: flex;
    justify-content: space-between;
  }
  .select {
    width: 215px;
  }
  :deep(.date-select) {
    width: 100%;
  }
  .textarea {
    width: 100%;
  }
}
</style>
