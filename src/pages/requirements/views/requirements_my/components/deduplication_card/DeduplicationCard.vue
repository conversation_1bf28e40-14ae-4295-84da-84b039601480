<script setup lang="ts">
import { DeduplicationCardCtrl } from "./DeduplicationCardCtrl";
import CardList from "../card_list/CardList.vue"

const {
  list,
} = DeduplicationCardCtrl();
</script>
<template>
  <div class="deduplication-card">
    <div class="card-cont">
      <div class="card-row">
        <CardList :list="list"></CardList>
      </div>
      <div class="card-row">
        <CardList :list="list"></CardList>
      </div>
      <div class="card-row">
        <CardList :list="list"></CardList>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.deduplication-card {
  height: 100%;
  padding-right: 5px;
  overflow-y: auto;
}
</style>
