import { ref } from "vue";

export function DeduplicationCardCtrl() {
  const list = ref([
    { id: '1', level: '这是输入好的文案', name: '这是输入好的文案', desc: '这是输入好的文案这是输入好的文案这是输入好的文案这是输入好的文案这是输入好的文案', dept: '这是输入好的文案', creater: '这是输入好的文案', create_time: '这是输入好的文案', duty: '这是输入好的文案'  },
    { id: '2', level: '这是输入好的文案', name: '这是输入好的文案', desc: '这是输入好的文案这是输入好的文案这是输入好的文案这是输入好的文案这是输入好的文案', dept: '这是输入好的文案', creater: '这是输入好的文案', create_time: '这是输入好的文案', duty: '这是输入好的文案'  },
    { id: '3', level: '这是输入好的文案', name: '这是输入好的文案', desc: '这是输入好的文案这是输入好的文案这是输入好的文案这是输入好的文案这是输入好的文案', dept: '这是输入好的文案', creater: '这是输入好的文案', create_time: '这是输入好的文案', duty: '这是输入好的文案'  },
    { id: '4', level: '这是输入好的文案', name: '这是输入好的文案', desc: '这是输入好的文案这是输入好的文案这是输入好的文案这是输入好的文案这是输入好的文案', dept: '这是输入好的文案', creater: '这是输入好的文案', create_time: '这是输入好的文案', duty: '这是输入好的文案'  },
    { id: '5', level: '这是输入好的文案', name: '这是输入好的文案', desc: '这是输入好的文案这是输入好的文案这是输入好的文案这是输入好的文案这是输入好的文案', dept: '这是输入好的文案', creater: '这是输入好的文案', create_time: '这是输入好的文案', duty: '这是输入好的文案'  },
    { id: '6', level: '这是输入好的文案', name: '这是输入好的文案', desc: '这是输入好的文案这是输入好的文案这是输入好的文案这是输入好的文案这是输入好的文案', dept: '这是输入好的文案', creater: '这是输入好的文案', create_time: '这是输入好的文案', duty: '这是输入好的文案'  },
  ])

  return {
    list,
  };
}
