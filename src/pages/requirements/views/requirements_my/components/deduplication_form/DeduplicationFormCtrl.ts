import { ref, useTemplateRef } from "vue";

export function DeduplicationFormCtrl() {
  const typeList = ref([
    { name: '需求去重',},
  ])

  const formData = ref({
    level: "",
    benefit: "",
    source: '',
    bus_domain: '',
    bus_component: '',
    bus_progress: '',
    app_domain: '',
    it: '',
    parent: '',
    project: '',
    impact_flow: 1,
    impact_data: 1,
  });
  const formRules = ref({
    level: [{required: true, message: '请选择需求层级', trigger: 'change'}],
    app_domain: [{required: true, message: '请输入应用域', trigger: 'blur'}],
    it: [{required: true, message: '请输入IT系统', trigger: 'blur'}],
    impact_flow: [{required: true, message: '请选择', trigger: 'change'}],
    impact_data: [{required: true, message: '请选择', trigger: 'change'}],
  })

  const levelOptions = ref([
    { label: '原始需求', value: '1'},
    { label: '初始需求', value: '2'},
    { label: '管理需求', value: '3'},
    { label: '数字化需求', value: '4'},
  ])

  const formRef = useTemplateRef<any>('formRef');
  function submit() {
    formRef.value.validate();
  }
  function reset() {
    formRef.value.resetFields();
  }


  return {
    typeList,
    formData,
    formRules,
    levelOptions,
    submit,
    reset,
  };
}
