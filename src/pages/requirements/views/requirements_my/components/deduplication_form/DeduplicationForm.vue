<script setup lang="ts">
import { DeduplicationFormCtrl } from "./DeduplicationFormCtrl";

const { 
  typeList, formData, formRules,
  levelOptions, submit, reset,
} = DeduplicationFormCtrl();
</script>
<template>
  <div class="deduplication-form">
    <div class="header">
      <BtTab :list="typeList" :value="0" ></BtTab>
    </div>
    <div class="cont">
      <el-form
        ref="formRef"
        class="form"
        :model="formData"
        :rules="formRules"
        label-width="130px"
      >
        <el-form-item label="需求层级" prop="level">
          <BtSelect
            class="select w-100"
            :list="levelOptions"
            v-model="formData.level"
          ></BtSelect>
        </el-form-item>
        <el-form-item label="利益相关方" prop="benefit">
          <el-input
            v-model="formData.benefit"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="来源决策体系" prop="source">
          <el-input
            v-model="formData.benefit"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="业务域" prop="bus_domain">
          <el-input
            v-model="formData.benefit"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="业务组件" prop="bus_component">
          <el-input
            v-model="formData.bus_component"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="业务流程" prop="bus_progress">
          <el-input
            v-model="formData.bus_progress"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="应用域" prop="app_domain">
          <el-input
            v-model="formData.app_domain"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="IT系统" prop="it">
          <el-input
            v-model="formData.it"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="父需求" prop="parent">
          <BtPicker
            class="select w-100"
            title="父需求"
            :list="levelOptions"
            showSearch
            v-model="formData.parent"
          ></BtPicker>
        </el-form-item>
        <el-form-item label="项目" prop="project">
          <BtPicker
            class="select w-100"
            title="父需求"
            :list="levelOptions"
            showSearch
            v-model="formData.project"
          ></BtPicker>
        </el-form-item>
        <el-form-item label="是否影响业务流" prop="impact_flow">
          <el-radio-group v-model="formData.impact_flow" class="radio-group">
            <el-radio :value="1" >是</el-radio>
            <el-radio :value="0" >否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否影响业数据" prop="impact_data">
          <el-radio-group v-model="formData.impact_data" class="radio-group">
            <el-radio :value="1" >是</el-radio>
            <el-radio :value="0" >否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="footer">
        <el-button @click="reset">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </div>
    
  </div>
</template>
<style scoped lang="scss">
.deduplication-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  .cont {
    flex: 1;
    padding: 20px 40px 20px 0;
    overflow-y: auto;
    .radio-group {
      flex: 1;
      justify-content: space-between;
      padding: 0 40px 0 10px;
    }
  }
  .footer {
    text-align: right;
  }
}
</style>
