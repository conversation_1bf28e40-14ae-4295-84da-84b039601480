<script setup lang="ts">
import { CardItemCtrl } from "./CardItemCtrl";
import type { ItemPropsType } from "./types";
import { ArrowUp, ArrowDown } from "@element-plus/icons-vue";

withDefaults(defineProps<ItemPropsType>(), {
  info: () => {},
});

const {
  showAll, toggleShowAll,
} = CardItemCtrl();
</script>
<template>
  <div class="card-box">
    <div class="cont">
      <el-form ref="formRef" class="form" label-width="120px">
        <el-form-item label="需求编号" prop="id">
          <el-input
            class="readonly"
            readonly
            :value="info.id"
            placeholder="--"
          ></el-input>
        </el-form-item>
        <el-form-item label="需求层级" prop="level">
          <el-input
            class="readonly"
            readonly
            :value="info.level"
            placeholder="--"
          ></el-input>
        </el-form-item>
        <el-form-item label="需求名称" prop="name">
          <el-input
            class="readonly"
            readonly
            :value="info.name"
            placeholder="--"
          ></el-input>
        </el-form-item>
        <el-form-item label="需求描述" prop="desc">
          <el-input
            class="readonly"
            readonly
            :value="info.desc"
            placeholder="--"
            :rows="3"
            type="textarea"
          ></el-input>
        </el-form-item>
        <el-form-item label="需求提出部门" prop="dept">
            <el-input
              class="readonly"
              readonly
              :value="info.dept"
              placeholder="--"
            ></el-input>
          </el-form-item>
        <div v-show="showAll">
          <el-form-item label="需求提出人" prop="creater">
            <el-input
              class="readonly"
              readonly
              :value="info.creater"
              placeholder="--"
            ></el-input>
          </el-form-item>
          <el-form-item label="需求提出时间" prop="create_time">
            <el-input
              class="readonly"
              readonly
              :value="info.create_time"
              placeholder="--"
            ></el-input>
          </el-form-item>
          <el-form-item label="需求责任人" prop="duty">
            <el-input
              class="readonly"
              readonly
              :value="info.duty"
              placeholder="--"
              :rows="3"
              type="textarea"
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="footer">
      <el-button text @click="toggleShowAll">
        <div v-show="!showAll">详情<el-icon class="el-icon--right"><ArrowDown /></el-icon></div>
        <div v-show="showAll">收起需求详情<el-icon class="el-icon--right"><ArrowUp /></el-icon></div>
      </el-button>
      <el-checkbox class="check" />
    </div>
  </div>
</template>
<style scoped lang="scss">
.card-box {
  width: 100%;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  .cont {
    padding: 20px 20px 0px 0;
  }
  .footer {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    border-top: 1px solid var(--border-color);
    .check {
      position: absolute;
      right: 20px;
    }
  }
}
</style>
