<script setup lang="ts">
import { CardListCtrl } from "./CardListCtrl.ts";
import type { ListPropsType } from "./types.ts"
import { ArrowLeftBold, ArrowRightBold } from "@element-plus/icons-vue"
import CardItem from "./CardItem.vue";

const props = withDefaults(
    defineProps<ListPropsType>(),
    {
      list: () => [],
    }
);

const {
  pageNum, pageSize, totalPage, changePage,
} = CardListCtrl(props);
</script>
<template>
  <div class="card-list">
    <div class="list">
      <CardItem 
        v-for="(item,index) in list.slice((pageNum-1) * pageSize, pageNum * pageSize)" 
        :key="(pageNum - 1) * pageSize + index" 
        class="item" 
        :info="item"
      ></CardItem>
    </div>
    <div class="pagination">
      <el-button class="btn" @click="changePage(-1)"><el-icon class="icon"><ArrowLeftBold /></el-icon></el-button>
      <div class="txt">
        <span class="cur">{{ pageNum }}</span>/ {{ totalPage }}
      </div>
      <el-button class="btn s2" @click="changePage(1)"><el-icon class="icon"><ArrowRightBold /></el-icon></el-button>
    </div>
  </div>
</template>
<style scoped lang="scss">
.card-list {
  width: 100%;
  .list {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .item {
      width: 49%;
    }
  }
  .pagination {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 16px 0;
    .txt {
      color: #797979;
      margin: 0 16px;
      .cur {
        color: #000;
      }
    }
    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 22px;
      height: 22px;
      padding: 0;
      border-radius: 50%;
      background: #E1E1E1;
      cursor: pointer;
      &::after {
        border-radius: 50%;
      }
      .icon {
        font-size: 18px;
        font-weight: bold;
        color: #ffffff;
      }
      &.s2 {
        background: var(--color-primary);
      }
    }
  }
}
</style>
