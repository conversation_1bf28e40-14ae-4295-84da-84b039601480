import { ref, computed } from "vue"
import type {ListPropsType} from './types'

export function CardListCtrl(props: ListPropsType) {

    const pageSize = ref(2)
    const pageNum = ref(1)

    const totalPage = computed(() => {
        return Math.ceil(props.list.length / pageSize.value)
    })

    function changePage(val: number) {
        let num = pageNum.value + val;
        num = num < 1 ? 1 : num;
        num = num > totalPage.value ? totalPage.value : num;
        pageNum.value = num
    }

    return {
        pageSize,
        pageNum,
        totalPage,
        changePage,
    }
}