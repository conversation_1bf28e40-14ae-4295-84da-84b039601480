<script setup lang="ts">
import { tableConfig } from "@/app-config.ts";
import { MyCtrl } from "./RequirementsMyCtrl.ts";
import bt_tab from "@/components/non_biz/bt_tab/BtTab.vue";
import bt_page from "@/components/non_biz/bt_page/BtPage.vue";
import { useLayoutStore } from "@/components/non_biz/layout/LayoutStore.ts";
import { MoreFilled } from "@element-plus/icons-vue";

const { 
  curType, typeList, changeType, search, toSearch,
  columnList, showColumn, tableData, 
  page, changePage, changeFilter, 
  handleAdd, handleDeduplication,
} = MyCtrl();

const layoutStore = useLayoutStore();
layoutStore.setAddAction(handleAdd)

</script>
<template>
  <div class="requirements-my">
    <div class="header">
      <bt_tab :list="typeList" :value="curType" @change="changeType">
        <template #right>
          <div class="right-opt flex flex-center">
            <el-button class="btn item" @click="handleAdd">
              新建
            </el-button>
            <el-button class="btn item" >
              提交
            </el-button>
            <el-button class="btn item" >
              分发
            </el-button>
            <el-button class="btn item" @click="handleDeduplication">
              去重
            </el-button>
            <el-button class="btn item" >
              Excel
            </el-button>
            <el-button class="btn item" >
              视图
            </el-button>
            <BtSearch
              class="item"
              v-model="search.name"
              placeholder="请输入搜索内容"
              @search="toSearch"
            ></BtSearch>
            <BtColumn ref="columnRef" v-model="columnList" ></BtColumn>
          </div>
        </template>
      </bt_tab>
    </div>
    <div class="container">
      <el-table
        class="table"
        :data="tableData"
        style="width: 100%; height: 100%"
      >
        <el-table-column type="selection" width="33" />
        <el-table-column label="#" width="40" align="center">
          <template #default="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <template v-for="column in showColumn" :key="column.field" >
          <el-table-column :property="column.field" :label="column.label" :width="column.width" :min-width="column.minWidth" >
            <template #header>
              <BtThFilter v-if="column.filter" :title="column.label" :info="column.filter" v-model="column.filterValue" @change="changeFilter($event,column.field)"></BtThFilter>
              <span v-else >{{ column.label }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column fixed="right" label="操作"  v-bind="tableConfig.optColumnAttr" >
          <template #default>
            <el-dropdown placement="bottom-end">
              <div class="more-opt">
                <el-icon class="icon"><MoreFilled /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="border" style="width: 140px">
                  <el-dropdown-item>提交</el-dropdown-item>
                  <el-dropdown-item>分发</el-dropdown-item>
                  <el-dropdown-item>变更</el-dropdown-item>
                  <el-dropdown-item>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <bt_page v-model="page" :total="100" @change="changePage"></bt_page>
    </div>
    
  </div>
</template>
<style scoped lang="scss">
.requirements-my {
  height: 100%;
  display: flex;
  flex-direction: column;
  .container {
    flex: 1;
    padding: 10px 20px;
    overflow: hidden;
  }
  .footer {
    padding: 10px 20px;
    text-align: right;
  }
}
</style>
