import { ref, useTemplateRef } from "vue";
import { useRouter } from "vue-router"

export function RequirementsAddCtrl() {
  const router = useRouter();
  function back() {
    router.back()
  }

  const typeList = ref([
    { label: '原始需求', value: 1 },
    { label: '初始需求', value: 2 },
    { label: '管理需求', value: 3 },
    { label: '数字化需求', value: 4 },
  ])

  const areaList = [
    { label: "一级", value: "1" },
    { label: "二级", value: "2" },
    { label: "三级", value: "3" },
  ];

  const correlationList = ref([
    {
      label: "一级选项一",
      value: "1",
    },
    {
      label: "一级选项二",
      value: "2",
    },
    {
      label: "一级选项三",
      value: "3",
    },
  ]);

  const form = ref({
    name: "",
    type: 1,
    desc: "",
    correlation: "",
    source: "",
    businessArea: "",
    businessComponent: "",
    businessProcess: "",
    application: "",
    system: "",
    urgent: "",
    urgentDesc: "",
    parentRequirement: "",
    project: "",
    isBusinessProcess: "0",
    isData: "0",
  });
  const formRules = ref<any>({
    name: [{ required: true, message: "请输入需求名称", trigger: "blur" }],
    type: [{ required: true, message: "选择需求类型", trigger: "change" }],
    desc: [{ required: true, message: "请输入需求描述", trigger: "blur" }],
    correlation: [
      { required: true, message: "请选择相关方", trigger: "change" },
    ],
    source: [
      { required: true, message: "请选择来源决策体系", trigger: "change" },
    ],
    businessArea: [
      { required: false, message: "请选择业务域", trigger: "change" },
    ],
    businessComponent: [
      { required: false, message: "请选择业务组件", trigger: "change" },
    ],
    businessProcess: [
      { required: false, message: "请选择业务流程", trigger: "change" },
    ],
    application: [
      { required: false, message: "请选择应用域", trigger: "change" },
    ],
    system: [{ required: false, message: "请选择IT系统", trigger: "change" }],
    isBusinessProcess: [
      { required: false, message: "请选择", trigger: "change" },
    ],
    isData: [{ required: false, message: "请选择", trigger: "change" }],
  });
  const ruleFormRef = useTemplateRef<any>("ruleFormRef");
  async function submitForm() {
    await ruleFormRef.value.validate();
  }

  const baseField = ['name', 'type', 'desc', 'correlation', 'source'];
  const requireFieldMap = <{[key: number]: string[]}>{
    1: [],
    2: ['businessArea'],
    3: ['businessArea', 'businessComponent', 'businessProcess', 'isBusinessProcess'],
    4: ['businessArea', 'businessComponent', 'businessProcess', 'application', 'system', 'isBusinessProcess', 'isData'],
  }
  function changeType(val: number) {
    const requiredField = <{[key: string]: number}>{};
    baseField.forEach(v=>{ requiredField[v] = 1 })
    requireFieldMap[val]?.forEach(v=> { requiredField[v] = 1 })

    for(const key in formRules.value) {
      if(requiredField[key]) {
        formRules.value[key][0].required = true
      } else {
        formRules.value[key][0].required = false
      }
    }

    ruleFormRef.value.clearValidate()

  }


  return {
    typeList,
    form,
    areaList,
    formRules,
    correlationList,
    submitForm,
    back,
    changeType,
  };
}
