<script setup lang="ts">
import { RequirementsAddCtrl } from "./RequirementsAddCtrl.ts";

const { 
  typeList, form, areaList, formRules, correlationList,
  changeType, submitForm, back,
} = RequirementsAddCtrl();
</script>

<template>
  <div class="requirements-add">
    <div class="header">
      <span class="title">新建需求</span>
    </div>
    <el-form
      :model="form"
      :rules="formRules"
      ref="ruleFormRef"
      label-width="125px"
      label-position="right"
      :validate-on-rule-change="false"
    >
      <el-form-item label="需求类型" prop="type">
        <el-radio-group v-model="form.type" @change="changeType">
          <el-radio
            v-for="(item, index) in typeList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="原始需求名称" prop="name">
        <BtInput v-model="form.name" placeholder="请输入" clearable></BtInput>
      </el-form-item>
      <el-form-item label="原始需求描述" prop="desc">
        <BtInput
          v-model="form.desc"
          placeholder="请输入"
          type="textarea"
          maxlength="2000"
          show-word-limit
          :rows="4"
        ></BtInput>
      </el-form-item>
      <el-row :gutter="60">
        <el-col :span="14">
          <el-form-item label="利益相关方" prop="correlation">
            <BtPicker
              class="my-inp"
              title="利益相关方"
              :list="correlationList"
              type="tree"
              showSearch
              v-model="form.correlation"
            ></BtPicker>
          </el-form-item>
          <el-form-item label="来源决策体系" prop="source">
            <BtPicker
              class="my-inp"
              title="来源决策体系"
              :list="correlationList"
              showSearch
              v-model="form.source"
            ></BtPicker>
          </el-form-item>
          <el-form-item label="业务域" prop="businessArea">
            <BtSelect
              v-model="form.businessArea"
              placeholder="请选择"
              class="my-inp"
              :list="areaList"
            >
            </BtSelect>
          </el-form-item>
          <el-form-item label="业务组件" prop="businessComponent">
            <BtSelect
              v-model="form.businessComponent"
              placeholder="请选择"
              class="my-inp"
              :list="areaList"
            >
            </BtSelect>
          </el-form-item>
          <el-form-item label="业务流程" prop="businessProcess">
            <BtSelect
              v-model="form.businessProcess"
              placeholder="请选择"
              class="my-inp"
              :list="areaList"
            >
            </BtSelect>
          </el-form-item>
          <el-form-item label="应用域" prop="application">
            <BtSelect
              v-model="form.application"
              placeholder="请选择"
              class="my-inp"
              :list="areaList"
            >
            </BtSelect>
          </el-form-item>
          <el-form-item label="IT系统" prop="system">
            <BtSelect
              v-model="form.system"
              placeholder="请选择"
              class="my-inp"
              :list="areaList"
            >
            </BtSelect>
          </el-form-item>
          <el-form-item label="迫切程度">
            <BtSelect
              v-model="form.urgent"
              placeholder="请选择"
              class="my-inp"
              :list="areaList"
            >
            </BtSelect>
          </el-form-item>
        </el-col>
        <el-col v-if="form.type != 1" :span="10">
          <el-form-item label="父需求">
            <BtPicker
              v-model="form.parentRequirement"
              showSearch
              title="父需求"
              placeholder="请选择"
              class="select"
              :list="correlationList"
            >
            </BtPicker>
          </el-form-item>
          <el-form-item v-if="form.type != 2" label="项目">
            <BtPicker
              v-model="form.project"
              showSearch
              title="项目"
              placeholder="请输入"
              class="select"
              :list="correlationList"
            >
            </BtPicker>
          </el-form-item>
          <el-form-item label="是否影响业务流" prop="isBusinessProcess">
            <el-radio-group
              v-model="form.isBusinessProcess"
              class="radio-group"
            >
              <el-radio label="是" value="0"></el-radio>
              <el-radio label="否" value="1"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否影响数据" prop="isData">
            <el-radio-group v-model="form.isData" class="radio-group">
              <el-radio label="是" value="0"></el-radio>
              <el-radio label="否" value="1"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="迫切程度描述">
        <BtInput
          v-model="form.urgentDesc"
          placeholder="请输入"
          type="textarea"
          :rows="4"
          maxlength="2000"
          show-word-limit
        ></BtInput>
      </el-form-item>
      <el-form-item>
        <div class="flex end-box">
          <el-button @click="back">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">
.requirements-add {
  .header {
    padding: 10px 20px;
    .title {
      font-size: 16px;
      color: #161616;
      font-weight: bold;
    }
  }
  .my-inp {
    width: 100%;
  }
  .select {
    width: 100%;
  }
  .radio-group {
    width: 100%;
    padding-right: 20%;
    justify-content: space-between;
  }
  .end-box {
    width: 100%;
    justify-content: end;
    .confirm {
      background: var(--color-primary);
      color: #fff;
    }
  }

  :deep(.el-form) {
    width: 90%;
    padding-left: 10px;
  }
}
</style>
