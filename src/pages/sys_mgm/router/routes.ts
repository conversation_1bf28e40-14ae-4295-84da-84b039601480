import type { RouteRecordRaw } from "vue-router";
import Layout from "@/components/non_biz/layout/index.vue";


const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/",
    name: "system",
    component: Layout,
    meta: {
      title: "系统管理",
    },
    redirect: "/organization",
    children: [
      {
        path: "/organization",
        name: "SystemOrganization",
        component: () => import("@pages/sys_mgm/views/organization_mgm/OrganizationMgm.vue"),
        meta: {
          title: "组织管理",
          icon: "组织管理",
        },
      }
    ]
  },
  
];
export default constantRoutes;
