import { ref, computed, useTemplateRef } from "vue";

export function OrganizationMgmCtrl() {
  const curType = ref(0)
  const typeList = ref([
    { name: '组织管理',},
    { name: '合作伙伴管理',},
  ])
  function changeType(idx: number) {
    curType.value = idx;
  }
  
  const columnRef = useTemplateRef<any>('columnRef')
  const columnList = ref([
    { 
      field: 'name', label: '姓名', width: '120', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'gender', label: '性别', width: '80', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '男', value: '1' },
          { label: '女', value: '2' },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'org', label: '组织', width: '107', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '组织1', value: '1' },
          { label: '组织2', value: '2' },
          { label: '组织3', value: '3' },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'post', label: '岗位', width: '100', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '岗位1', value: '1' },
          { label: '岗位2', value: '2' },
          { label: '岗位3', value: '3' },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'phone', label: '电话', width: '140', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'email', label: '邮箱', width: '180', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'nickname', label: '用户名', width: '100', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'type', label: '访问类型', width: '107', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '类型1', value: '1' },
          { label: '类型2', value: '2' },
          { label: '类型3', value: '3' },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'role', label: '角色权限', width: '107', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '权限1', value: '1' },
          { label: '权限2', value: '2' },
          { label: '权限3', value: '3' },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'data', label: '数据权限', width: '107', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '权限1', value: '1' },
          { label: '权限2', value: '2' },
          { label: '权限3', value: '3' },
        ]
      },
      filterValue: {},
    },
    { field: 'time', label: '最后登录', width: '130', minWidth: '', show: true },
    { field: 'num', label: '操作次数', width: '82', minWidth: '', show: true },
  ])
  const showColumn = computed(()=>{
    return columnList.value.filter(v=>v.show);
  })
  function openColumn(){
    columnRef.value.open();
  }
  function changeFilter(info: any, type: string) {
    console.log(type,"字段筛选变化-------------",info);
  }


  const tableData = ref(<any>[]);
  for(let i = 0; i < 4; i++) {
    tableData.value.push({
      name: '001张三',
      gender: '男',
      org: '组织名称',
      post: '产品经理',
      phone: '155****5656',
      email: '<EMAIL>',
      nickname: '昵称',
      type: '访问类型',
      role: '无',
      data: '无',
      time: '2025-08-08',
      num: 32,
    })
  }

  const page = ref(1);
  function changePage(val: number) {
    page.value = val;
  }

  const tabList = ref([
    { name: '组织' },
  ])
  const curTab = ref(0);
  function tabChange(idx: number) {
    curTab.value = idx;
    treeList.value = treeAll[idx];
  }
  function tabAdd(val: string) {
    tabList.value.push({
      name: val,
    })
    const idx = tabList.value.length - 1;
    treeAll[curTab.value] = treeList.value;
    treeAll[idx] = treeAll[idx] || [];
    curTab.value = idx;
    treeList.value = treeAll[idx];
  }

  const treeAll = [
    [
      {
        label: '001 人力资源部',
        value: '1',
        children: [
          { label: '002 预算组', value: '1.1' },
          { label: '003 资金组', value: '1.2' },
          { label: '002 财务管理组', value: '1.3' },
        ]
      },
      {
        label: '002 xxxx部',
      }
    ]
  ];
  const treeList = ref(treeAll[curTab.value]);
  function treeChange(){
    console.log(treeList.value);
  }

  const search = ref({
    name: "",
  });
  function toSearch(val: string) {
    console.log(val);
  }

  return {
    curType,
    typeList,
    changeType,
    columnList,
    showColumn,
    openColumn,
    tableData,
    page,
    changePage,
    tabList,
    curTab,
    tabChange,
    tabAdd,
    treeList,
    treeChange,
    search,
    toSearch,
    changeFilter,
  };
}
