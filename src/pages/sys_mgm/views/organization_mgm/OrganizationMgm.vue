<script setup lang="ts">
import { tableConfig } from "@/app-config.ts";
import { OrganizationMgmCtrl } from "./OrganizationMgmCtrl.ts";
import BtTree from "@/components/non_biz/bt_tree/BtTree.vue";
import {
  MoreFilled,
} from "@element-plus/icons-vue";

const {
  curType,
  typeList,
  changeType,
  columnList,
  showColumn,
  tableData,
  page,
  changePage,
  tabList,
  curTab,
  treeList,
  tabChange,
  tabAdd,
  treeChange,
  search, toSearch,
  changeFilter,
} = OrganizationMgmCtrl();
</script>
<template>
  <div class="sys-org">
    <div class="header">
      <BtTab :list="typeList" :value="curType" @change="changeType">
        <template #right>
          <div class="right-opt flex flex-center">
            <el-button class="btn item">新建用户</el-button>
            <BtSearch
              class="item"
              v-model="search.name"
              placeholder="请输入搜索内容"
              @search="toSearch"
            ></BtSearch>
            <BtColumn ref="columnRef" v-model="columnList"></BtColumn>
            <!-- <el-icon class="item icon" @click="openColumn"><Setting /></el-icon> -->
          </div>
        </template>
      </BtTab>
    </div>
    <div class="container">
      <div class="sidebar">
        <BtTree
          name="组织"
          subname="部门"
          :tabList="tabList"
          :curTab="curTab"
          v-model="treeList"
          @tab-change="tabChange"
          @tab-add="tabAdd"
          @change="treeChange"
        ></BtTree>
      </div>
      <div class="cont">
        <el-table
          class="table"
          :data="tableData"
          style="width: 100%; height: 100%"
        >
          <el-table-column type="selection" width="33" />
          <el-table-column label="#" width="40" align="center">
            <template #default="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <template v-for="column in showColumn" :key="column.field">
            <el-table-column
              :property="column.field"
              :label="column.label"
              :width="column.width"
              :min-width="column.minWidth"
            >
              <template #header>
                <BtThFilter v-if="column.filter" :title="column.label" :info="column.filter" v-model="column.filterValue" @change="changeFilter($event,column.field)"></BtThFilter>
                <span v-else >{{ column.label }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column fixed="right" label="操作" v-bind="tableConfig.optColumnAttr">
            <template #default>
              <el-dropdown placement="bottom-end">
                <div class="more-opt">
                  <el-icon class="icon"><MoreFilled /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu class="border" style="width: 140px">
                    <el-dropdown-item>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <div class="footer">
          <BtPage v-model="page" :total="100" @change="changePage"></BtPage>
        </div>
      </div>
    </div>
    
  </div>
</template>
<style scoped lang="scss">
.sys-org {
  height: 100%;
  display: flex;
  flex-direction: column;
  .container {
    flex: 1;
    display: flex;
    padding: 10px ;
    overflow: hidden;
    // border: 1px solid var(--el-border-color);
  }
  .sidebar {
    width: 300px;
    height: 100%;
    margin-right: 10px;
    flex-shrink: 0;
  }
  .cont {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid var(--border-color);
    border-top: 0;
    .table {
      flex: 1;
    }
  }
  .footer {
    padding: 10px 20px;
    text-align: right;
  }
}
</style>
