<script setup lang="ts">
import { useRouter } from "vue-router"

const router = useRouter();
function jump(url:string) {
  router.push(url);
}
</script>
<template>
  <div class="ea-canvas-add">
    <div class="header">
      <div class="title">新建EA</div>
    </div>
    <div class="container">
      <div class="list">
        <div class="li" @click="jump('/ea_canvas/editor')">
          <SvgIcon class="icon" name="create_new_canvas" size="80"></SvgIcon>
          <div class="info">
            <div class="title">创建空白EA文件</div>
            <div class="txt">
              新建一个空白EA文件页面来存放您的文件，规划您的企业架构，并在治理等方面进行协作。
            </div>
          </div>
        </div>
        <div class="li" @click="jump('/ea_canvas/template')">
          <SvgIcon class="icon" name="canvas_from_template" size="80"></SvgIcon>
          <div class="info">
            <div class="title">从EA文件模版创建</div>
            <div class="txt">
              新建一个预先填充了必要信息EA文件，以帮助您快速入门。
            </div>
          </div>
        </div>
        <div class="li">
          <SvgIcon class="icon" name="import_canvas" size="140"></SvgIcon>
          <div class="info">
            <div class="title">导入EA文件</div>
            <div class="txt">
              从外部源（如 PPT、Visio、PDF 或其他文档）迁移数据。
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.ea-canvas-add {
  padding: 15px;
  .header {
    .title {
      font-size: 16px;
      font-weight: bold;
    }
  }
  .container {
    padding: 16px 0px 16px 0;
    .list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .li {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 49%;
        height: 230px;
        padding: 0 5%;
        border-radius: 4px;
        border: 1px solid rgba(227,227,228,1);
        margin-bottom: 2%;
        cursor: pointer;
        &:hover {
          background-color: rgba(251,250,253,1);
        }
        .icon {
          width: 140px;
          text-align: center;
          margin-right: 20px;
          flex-shrink: 0;
        }
        .info {
          flex: 1;
          .title {
            font-size: 18px;
            line-height: 22px;
            color: var(--el-color-primary);
          }
          .txt {
            font-size: 14px;
            line-height: 17px;
            color: rgba(51,51,51,1);
            margin-top: 10px;
          }
        }
      }
    }
  }
}
</style>
