<script setup lang="ts">
  import {RightAsideCtrl} from "./RightAsideCtrl.ts";
  import {onBeforeUnmount, onMounted} from "vue";

  const ctrl = new RightAsideCtrl();
  onMounted(() => {
    ctrl.init();
  });
  onBeforeUnmount(() => {
    ctrl.destroy();
  });
</script>

<template>
  <el-aside class="right-aside"
            :class="[ctrl.asideWidth.value === 0 ? 'no-border' : '']"
            :width="ctrl.asideWidth.value+'px'">
    <el-tabs class="tabbar" stretch
             v-model="ctrl.activeTab.value" @tab-change="ctrl.onTabChanged">
      <el-tab-pane lazy :label="item.label" :name="item.name"
                   v-for="item in ctrl.tabsData" :key="item.name">
        <component :is="item.component" />
      </el-tab-pane>
    </el-tabs>
  </el-aside>
</template>