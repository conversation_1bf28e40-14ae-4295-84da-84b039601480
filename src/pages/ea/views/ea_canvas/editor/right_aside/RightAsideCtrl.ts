import {defineAsyncComponent, ref} from "vue";
import {ASIDE_WIDTH} from "@pages/ea/common/Constants.ts";
import {EditorEventTypes} from "@pages/ea/views/ea_canvas/editor/EditorEventTypes.ts";
import evtBus from "@/libs/request/evtBus.ts";

export class RightAsideCtrl {

    asideWidth = ref(ASIDE_WIDTH);

    activeTab = ref('Attributes');
    tabsData = [
        {
            label: '属性', name: 'Attributes',
            component: defineAsyncComponent(() => import('./tabs/AttributeTabView.vue'))
        },
        {
            label: '格式', name: 'Formatter',
            component: defineAsyncComponent(() => import('./tabs/FormatterTabView.vue'))
        },
        {
            label: '关联关系', name: 'Relationship',
            component: defineAsyncComponent(() => import('./tabs/RelationshipTabView.vue'))
        },
        {
            label: '知识库', name: 'Knowledge',
            component: defineAsyncComponent(() => import('./tabs/KnowledgeTabView.vue'))
        },
    ];

    constructor() {
        this.onTabChanged = this.onTabChanged.bind(this);
        this.onToggleCollapse = this.onToggleCollapse.bind(this);
    }

    init() {
        evtBus.emit(EditorEventTypes.TOGGLE_COLLAPSE_RIGHT, this.onToggleCollapse);
    }

    destroy() {
        evtBus.off(EditorEventTypes.TOGGLE_COLLAPSE_RIGHT, this.onToggleCollapse);
    }

    onToggleCollapse() {
        if(this.asideWidth.value === ASIDE_WIDTH) {
            this.asideWidth.value = 0;
        } else {
            this.asideWidth.value = ASIDE_WIDTH;
        }
    }

    onTabChanged() {}

}