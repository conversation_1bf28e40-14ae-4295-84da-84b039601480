<script setup lang="ts">
  import TopHeader from "./top_header/TopHeader.vue";
  import LeftAside from "./left_aside/LeftAside.vue";
  import RightAside from "./right_aside/RightAside.vue";
  import Stage from "./stage/Stage.vue";
</script>

<template>
    <el-container class="editor" direction="vertical">
      <TopHeader />
      <el-container class="canvas-editor">
        <LeftAside />
        <el-container class="stage-container">
          <Stage />
        </el-container>
        <RightAside />
      </el-container>
    </el-container>
</template>

<style scoped lang="scss">
.editor {
  height: 100%;
 .canvas-editor {
   flex: 1;
   overflow: hidden;

   :deep(.left-aside, .right-aside) {
     background-color: white;

   }
   :deep(.left-aside) {
     border-right: 1px solid var(--el-border-color);
     transition: all 0.3s;
     &.no-border {
       border-right-width: 0;
     }
   }
   :deep(.right-aside) {
     border-left: 1px solid var(--el-border-color);
     transition: all 0.3s;
     &.no-border {
       border-right-width: 0;
     }
   }

   :deep(.tabbar) {
     height: 100%;
     .el-tabs__item {
       padding: 0 10px;
     }
     .el-tabs__item:nth-child(2) {
       padding-left: 10px !important;
     }
     .el-tabs__item:last-child {
       padding-right: 10px !important;
     }
     .el-tabs__nav-wrap {
       padding: 0;
     }
     .el-tabs__header {
       margin-bottom: 0;
     }
     .el-tab-pane {
       height: 100%;
       overflow-y: hidden;
       &:hover {
         overflow-y: auto;
       }
     }
   }


 }
}
</style>