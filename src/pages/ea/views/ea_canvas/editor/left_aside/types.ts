import type Node from 'element-plus/es/components/tree/src/model/node';

export type PropRenderFunction<T> = (data: Array<{[key: string]: any}>, node:Node) => T;

export type EaFileTree = {
    label: string | PropRenderFunction<string>,
    id:string,
    disabled?:boolean,
    isLeaf?:boolean | PropRenderFunction<boolean>,
    version?: string,
    isDir?: boolean,
    class?: string | PropRenderFunction<string>,
    children?: EaFileTree[]
}

export type EAFileTabTollIconItem = {
    icon: string,
    id: string,
    tips: string,
    size: number
}