import {defineStore} from "pinia";
import type {EaFileTree} from "./types.ts";
import {reactive} from "vue";

export const useLeftAsideStore = defineStore('leftAsideStore', () => {
    const eaFileTree = reactive<EaFileTree[]>([
        {
            id: '1',
            label: '页面1'
        },
        {
            id: '2',
            label: '页面2',
            children: [
                { id: '3', label: '页面2', version: 'v1.2' },
                { id: '4', label: '页面2', version: 'v1.3' }
            ]
        },
        {
            id: '5',
            label: '页面3'
        },
        {
            id: '6',
            label: '文件夹1',
            isDir: true,
            children: [
                { id: '7', label: '页面4'},
                { id: '8', label: '页面5'},
                {
                    id: '9',
                    label: '页面6',
                    children: [
                        { id: '10', label: '页面6', version: 'v1.1' },
                        { id: '11', label: '页面6', version: 'v2.1' }
                    ]
                },
                { id: '12', label: '页面7'},
            ]
        }
    ]);

    return {
        eaFileTree
    }
});