<script setup lang="ts">
  import {EaFileTabCtrl} from "./EaFileTabCtrl.ts";
  import {useLeftAsideStore} from "../LeftAsideStore.ts";
  import SvgIcon from "@/components/non_biz/svg_icon/SvgIcon.vue";

  const ctrl = new EaFileTabCtrl();
  const store = useLeftAsideStore();
</script>

<template>
  <div class="ea-file-tab">
    <div class="tool-icons">
      <el-tooltip :key="item.icon" effect="dark"
                  :content="item.tips" placement="top"
                  v-for="item in ctrl.toolIcons">
          <span class="icon-wrapper">
            <el-button class="icon-button" @click="ctrl.onToolIconClicked(item)">
              <SvgIcon class="icon" :name="item.icon" :size="item.size"></SvgIcon>
            </el-button>
          </span>
      </el-tooltip>
    </div>
    <el-tree
        style="width: 100%"
        node-key="id"
        class="ea-file-tree"
        :data="store.eaFileTree"
        :props="ctrl.defaultProps"
        @node-click="ctrl.onNodeClick">
      <template #default="{ node, data }">
        <div class="ea-file-tree-node">
          <div class="icon-part">
            <el-icon size="16" v-if="!node.expanded && data.isDir"><Folder /></el-icon>
            <el-icon size="16" v-if="node.expanded && data.isDir"><FolderOpened /></el-icon>
          </div>
          <span class="label-part">{{ node.label }}<span class="version">{{ (data.version ? `(${data.version})` : '') }}</span></span>
        </div>
      </template>
    </el-tree>
  </div>
</template>

<style scoped lang="scss">
.ea-file-tab {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.tool-icons {
  padding: 15px;
  text-align: right;
}
.icon-wrapper {
  margin-right: 15px;
  display: inline-block;
  padding: 0;
  line-height: 1;

  &:last-child {
    margin-right: 0;
  }

  .icon-button {
    border: none;
    padding: 0;
    height: auto;
  }

  .icon {
    font-size: 20px;
    cursor: pointer;
  }
}
.ea-file-tree {
  flex: 1;
  overflow-y: auto;
  :deep(.is-current .el-tree-node__content) {
    background-color: #F4F8FF;
  }
  :deep(.el-tree-node__content) {
    border-bottom: solid 0.5px #D7D7D7;
    &:hover {
      background-color: #F4F8FF;
    }
  }
}

.ea-file-tree-node {
  display: flex;
  align-items: center;
  .icon-part {
    display: flex;
    align-items: center;
    margin-right: 3px;
  }
  .version {
    font-size: 11px;
    color: #AAAAAA;
  }
}
</style>