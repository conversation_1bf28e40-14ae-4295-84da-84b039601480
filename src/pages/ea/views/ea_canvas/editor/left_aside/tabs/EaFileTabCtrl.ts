import type {EAFileTabTollIconItem, EaFileTree} from "@pages/ea/views/ea_canvas/editor/left_aside/types.ts";
import type Node from 'element-plus/es/components/tree/src/model/node';
import {EditorEventTypes} from "@pages/ea/views/ea_canvas/editor/EditorEventTypes.ts";
import {iconSize} from "@pages/ea/common/Constants.ts";
import evtBus from "@/libs/request/evtBus.ts";

export class EaFileTabCtrl {

    defaultProps = {
        children: 'children',
        label: 'label'
    };

    toolIcons: EAFileTabTollIconItem[] = [
        { id: 'addFile', icon: 'Plus', size: iconSize, tips: '未知功能' },
        { id: 'openFolder', icon: 'FolderOpened', size: iconSize, tips: '未知功能' },
        { id: 'search', icon: 'Search', size: iconSize, tips: '未知功能' },
        { id: 'document', icon: 'Document', size: iconSize, tips: '未知功能' },
        { id: 'expand', icon: 'ZoomIn', size: iconSize, tips: '未知功能' },
        { id: 'down', icon: 'Wallet', size: iconSize, tips: '未知功能' },
    ];

    onNodeClick(data: EaFileTree, node: Node) {
        console.log(data, node);
    }

    onToolIconClicked(item:EAFileTabTollIconItem) {
        evtBus.emit(EditorEventTypes.EA_FILE_TAB_TOOLS_ICON_CLICK, item);
    }

}