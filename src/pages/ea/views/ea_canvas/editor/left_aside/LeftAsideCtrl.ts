import {ref, defineAsyncComponent} from "vue";
import {ASIDE_WIDTH} from "@pages/ea/common/Constants.ts";
import {EditorEventTypes} from "@pages/ea/views/ea_canvas/editor/EditorEventTypes.ts";
import evtBus from "@/libs/request/evtBus.ts";

export class LeftAsideCtrl {

    asideWidth = ref(ASIDE_WIDTH);

    activeTab = ref('EaFile');
    tabsData = [
        {
            label: '页面', name: 'EaFile',
            component: defineAsyncComponent(
                () => import('./tabs/EaFileTabView.vue')
            )
        },
        {
            label: '组织', name: 'Organization',
            component: defineAsyncComponent(
                () => import('./tabs/OrgTabView.vue')
            )
        },
        {
            label: '业务域', name: 'BizArea',
            component: defineAsyncComponent(
                () => import('./tabs/BizAreaTabView.vue')
            )
        },
        {
            label: 'EA类型', name: 'EaType',
            component: defineAsyncComponent(
                () => import('./tabs/EaTypeTabView.vue')
            )
        },
    ];

    constructor() {
        this.onTabChanged = this.onTabChanged.bind(this);
        this.onToggleCollapse = this.onToggleCollapse.bind(this);
    }

    init() {
        evtBus.emit(EditorEventTypes.TOGGLE_COLLAPSE_LEFT, this.onToggleCollapse);
    }

    destroy() {
        evtBus.off(EditorEventTypes.TOGGLE_COLLAPSE_LEFT, this.onToggleCollapse);
    }

    onToggleCollapse() {
        console.log(this, '---');
        if(this.asideWidth.value === ASIDE_WIDTH) {
            this.asideWidth.value = 0;
        } else {
            this.asideWidth.value = ASIDE_WIDTH;
        }

    }

    onTabChanged() {}

}