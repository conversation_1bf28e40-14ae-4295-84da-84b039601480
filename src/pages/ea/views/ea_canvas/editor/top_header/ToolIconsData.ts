import {reactive} from "vue";
import {getFontList} from "@/utils/fontUtil.ts";
import type {IconData} from "@pages/ea/views/ea_canvas/editor/top_header/types.ts";
import { iconSize, fontSizeStep } from '@pages/ea/common/Constants.ts'


export const h1LeftIcons:IconData[] = [
    { icon: 'collapse', id: 'collapse_left', size: iconSize, tips: '收起/展开' },
    { icon: 'DocumentAdd', id: 'add', size: iconSize, tips: '新建' },
    { icon: 'Notebook', id: 'save', size: iconSize, tips: '保存' },
    { icon: 'Back', id: 'undo', size: iconSize, tips: '撤销' },
    { icon: 'Right', id: 'redo', size: iconSize, tips: '重做' },
    { icon: 'Share', id: 'share', size: iconSize, tips: '分享' },
];
export const h1RightIcons: IconData[] = [
    { icon: '查看关联关系', id: 'view_relationship', size: iconSize, tips: '查看关联关系' },
    { icon: '关联关系', id: 'relationship', size: iconSize, tips: '关联关系' },
    { icon: '列表界面', id: 'list_view', size: iconSize, tips: '列表界面' },
    { icon: 'Reading', id: 'split_canvas', size: iconSize, tips: '分画布' },
    { icon: 'Stopwatch', id: 'unknown', size: iconSize, tips: '未知功能' },
    { icon: '', id: '', size: 0, tips: '' },
    { icon: 'DataLine', id: 'fullscreen', size: iconSize, tips: '全屏' },
    { icon: 'collapse', id: 'collapse_right', size: iconSize, tips: '收起/展开' },
];
export const h1CenterBtns = reactive([
    { label: '开始', selected: true },
    { label: '插入', selected: false },
    { label: '图片', selected: false },
]);

export const dropDownData = (() => {
   const n = 10;
   const result = [];
   for(let i=0;i<n;i++){
       result[i] = {
           id: i+1+'',
           selected: i===0,
           label: (i+1)*10+'%'
       };
   }
   return reactive(result);
})();

export const fontList = (() => {
    const fonts = getFontList();
    const selectedFontFamily = '微软雅黑';
    const result = fonts.map(i => {
        return {
            fontFamily: i[0],
            selected: i[0] === selectedFontFamily
        };
    });
    const selectedItem = result.find(i => i.selected);
    if (!selectedItem) {
        result[0].selected = true;
    }
    return reactive(result);
})();

export const fontSizeList = (() => {
    const n = 30;
    const step = fontSizeStep;
    let start = 12;
    const re = [];
    let i = 0;
    while(i < n) {
        re[i] = { value: start, label: start, selected: i === 4 };
        i++;
        start+=step;
    }
    return reactive(re);
})();

export const h2LeftIcons = [
    { icon: 'Football', size: iconSize, tips: '未知功能' },
    { icon: 'Suitcase', size: iconSize, tips: '未知功能' },
    { icon: 'Switch', size: iconSize, tips: '未知功能' },
    { icon: 'CopyDocument', size: iconSize, tips: '未知功能' }
];
export const h2CenterCenterIcons = [
    { icon: 'Ship', value: '+', size: iconSize, tips: '增加字号' },
    { icon: 'Ship', value: '-', size: iconSize, tips: '减小字号' },
];
export const h2CenterLeftIcons = reactive([
    { icon: 'Ship', value: 'bold', size: iconSize, selected: false, tips: '粗体' },
    { icon: 'Ship', value: 'italic', size: iconSize, selected: false, tips: '斜体' },
    { icon: 'Ship', value: 'underline', size: iconSize, selected: false, tips: '下划线' }
]);
export const h2CenterRightIcons = reactive([
    { icon: 'Ship', value: 'start', size: iconSize, selected: true, tips: '左对齐' },
    { icon: 'Ship', value: 'center', size: iconSize, selected: false, tips: '水平居中' },
    { icon: 'Ship', value: 'end', size: iconSize, selected: false, tips: '右对齐' },
    { icon: 'Ship', value: 'justify', size: iconSize, selected: false, tips: '均分' },
    { icon: 'Ship', value: 'justify', size: iconSize, selected: false, tips: '未知功能' },
]);
export const h2RightIcons = [
    { icon: 'Search', size: iconSize, tips: '搜索' },
    { icon: 'PieChart', size: iconSize, tips: '未知功能' },
];