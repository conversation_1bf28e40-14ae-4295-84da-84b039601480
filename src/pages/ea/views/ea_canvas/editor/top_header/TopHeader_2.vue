<script setup lang="ts">

import {ElHeader} from "element-plus";
import {
  h2RightIcons,
  h2LeftIcons,
  fontList,
  fontSizeList,
  h2CenterLeftIcons,
  h2CenterCenterIcons, h2CenterRightIcons
} from "./ToolIconsData.ts";
import SvgIcon from "@/components/non_biz/svg_icon/SvgIcon.vue";
import {TopHeader2Ctrl} from "./TopHeader2Ctrl.ts";

const ctrl = new TopHeader2Ctrl();

</script>

<template>
  <ElHeader class="header-2" height="42px">
    <div class="left">
      <el-tooltip :key="item.icon" effect="dark"
                  :content="item.tips" placement="top"
                  v-for="(item,index) in h2LeftIcons">
          <span class="icon-wrapper" :class="[index === h2LeftIcons.length-1 ? 'last' : '']">
            <el-button class="icon-button">
              <SvgIcon class="icon" :name="item.icon" :size="item.size"></SvgIcon>
            </el-button>
          </span>
      </el-tooltip>
    </div>
    <div class="center">
      <div class="center-1">
        <el-select filterable value-key="fontFamily"
            v-model="ctrl.selectedFontFamily.value"
            style="width: 160px;" placeholder="选择字体">
          <el-option v-for="item in fontList" :key="item.fontFamily" :value="item" :label="item.fontFamily">
            <span :style="{ fontFamily: item.fontFamily, display: 'inline-block' }">
	            {{ item.fontFamily }}
            </span>
          </el-option>
        </el-select>
        <el-select v-model="ctrl.selectedFontSize.value" value-key="value"
         placeholder="选择字体大小" style="width: 70px">
          <el-option v-for="item in fontSizeList" :key="item.value" :label="item.label" :value="item" />
        </el-select>
      </div>
      <div class="center-2">
        <el-tooltip :key="item.icon" effect="dark"
                    v-for="(item) in h2CenterCenterIcons"
                    :content="item.tips" placement="top">
            <span class="icon-wrapper">
              <el-button class="icon-button" @click="ctrl.onH2Center2IconsClick(item)">
                <SvgIcon class="icon" :name="item.icon" :size="item.size"></SvgIcon>
              </el-button>
            </span>
        </el-tooltip>
      </div>
      <div class="center-3">
        <el-tooltip :key="item.icon" effect="dark"
                  v-for="(item,index) in h2CenterLeftIcons"
                  :content="item.tips" placement="top">
          <span class="icon-wrapper" :class="[index === h2CenterLeftIcons.length-1 ? 'last' : '']">
            <el-button class="icon-button" :class="[item.selected ? 'selected' : '']"
                       @click="ctrl.onH2Center3IconsClick(item)">
              <SvgIcon class="icon" :name="item.icon" :size="item.size"></SvgIcon>
            </el-button>
          </span>
        </el-tooltip>
	      <el-tooltip :content="`填充色${ctrl.fillColor.value}`" placement="top">
		      <span class="icon-wrapper">
		        <el-color-picker v-model="ctrl.fillColor.value" />
		      </span>
	      </el-tooltip>
	      <el-tooltip :content="`文本颜色${ctrl.textColor.value}`" placement="top">
		      <span class="icon-wrapper last">
		        <el-color-picker v-model="ctrl.textColor.value" />
		      </span>
	      </el-tooltip>
      </div>
      <div class="divider-wrapper"><el-divider direction="vertical"></el-divider></div>
      <div class="center-4">
        <el-tooltip :key="item.icon" effect="dark"
                    v-for="(item) in h2CenterRightIcons"
                    :content="item.tips" placement="top">
            <span class="icon-wrapper">
              <el-button class="icon-button" @click="ctrl.onH2Center4IconsClick(item)"
                         :class="[item.selected ? 'selected' : '']">
                <SvgIcon class="icon" :name="item.icon" :size="item.size"></SvgIcon>
              </el-button>
            </span>
        </el-tooltip>
      </div>
      <div class="divider-wrapper"><el-divider direction="vertical"></el-divider></div>
    </div>
    <div class="right">
      <el-tooltip :key="item.icon" effect="dark"
                  v-for="(item,index) in h2RightIcons"
                  :content="item.tips" placement="top">
            <span class="icon-wrapper" :class="[index === h2RightIcons.length-1 ? 'last' : '']">
              <el-button class="icon-button">
                <SvgIcon class="icon" :name="item.icon" :size="item.size"></SvgIcon>
              </el-button>
            </span>
      </el-tooltip>
    </div>
  </ElHeader>
</template>

<style scoped lang="scss">
.header-2 {
	--el-border-radius-base: 2px;
  border-bottom: 1px solid var(--el-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  min-width: 0;

	.center {
		flex: 1;
		display: flex;
		justify-content: center;
		margin: 0 18px;
		.center-1, .center-2, .center-3, .center-4 {
      margin-right: 18px;
			display: flex;
			align-items: center;
		}
    .center-1 {
      :deep(.el-select) {
        margin-right: 10px;
      }
      :deep(.el-select:last-child) {
        margin-right: 0;
      }
    }
    .center-2 {
      margin-right: 30px;
    }
    .center-4 {
      margin: 0 30px;
    }

		:deep(.el-divider) {
			height: 100%;
		}
		:deep(.el-select__input) {
			height: auto;
		}
    :deep(.el-select__placeholder){
      width: auto;
    }
		:deep(.el-color-picker__trigger) {
			padding: 0;
			border: none;
			width: 20px;
			height: 20px;
		}
	}

  .icon-wrapper {
    margin-right: 15px;
    display: inline-block;
    padding: 0;
    line-height: 1;

    &:last-child {
      margin-right: 0;
    }

    .icon-button {
      border: none;
      padding: 0;
      height: auto;
	    &.selected {
		    box-shadow: 0px 1px 0px 1px blue;
	    }
    }

    .icon {
      font-size: 20px;
      cursor: pointer;
    }
  }

}
</style>