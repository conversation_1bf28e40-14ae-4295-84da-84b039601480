import {computed, ref} from "vue";
import {fontList, fontSizeList, h2CenterRightIcons} from "./ToolIconsData.ts";
import type {TextAlign} from "./types.ts";
import { ElMessage } from 'element-plus';
import { fontSizeStep, minFontSize, maxFontSize } from "@pages/ea/common/Constants.ts";

export class TopHeader2Ctrl {

    isTextBold = ref(false);
    isTextUnderline = ref(false);
    isTextItalic = ref(false);
    fillColor = ref('#409EFF');
    textColor = ref('#000000');
    textAlign = ref<TextAlign>('start');
    // 超出列表之后的临时值
    fontSize = ref({
        value: fontSizeList[fontSizeList.length-1].value+fontSizeStep,
        label: fontSizeList[fontSizeList.length-1].value+fontSizeStep,
        selected: false
    });

    // 字体名称
    selectedFontFamily = computed({
        get: () => fontList.find(i => i.selected)!,
        set: (newValue) => {
            console.log(newValue, newValue.selected);
            fontList.forEach(i => i.selected = false);
            newValue.selected = true;
        }
    });

    // 选择字体大小
    selectedFontSize = computed({
        get: () => fontSizeList.find(i => i.selected) || this.fontSize.value,
        set: (newValue) => {
            const findIt = fontSizeList.find(i => i.value === newValue.value);
            if (findIt) {
                fontSizeList.forEach(i => i.selected = false);
                newValue.selected = true;
            } else {
                this.fontSize.value = newValue;
            }

        }
    });

    constructor() {
        this.onH2Center4IconsClick = this.onH2Center4IconsClick.bind(this);
        this.onH2Center3IconsClick = this.onH2Center3IconsClick.bind(this);
        this.onH2Center2IconsClick = this.onH2Center2IconsClick.bind(this);

    }

    // 字号增加或减小
    onH2Center2IconsClick(item: { value: string }) {
        let index = fontSizeList.findIndex(i => i === this.selectedFontSize.value);
        // 列表中
        if (index>=0) {
            // 加还是减
            if (item.value === '+') {
                index++;
            } else {
                index--;
            }
            // 列表范围内的选中
            if (index >= 0 && index <= fontSizeList.length-1) {
                this.selectedFontSize.value = fontSizeList[index];
            } else {
                // 第一次超出列表范围，可能是太小，也可能是太大
                fontSizeList.forEach(i => i.selected = false);
                if (index > 0) {
                    this.fontSize.value = {
                        value: fontSizeList[fontSizeList.length-1].value+fontSizeStep,
                        label: fontSizeList[fontSizeList.length-1].value+fontSizeStep,
                        selected: false
                    };
                } else {
                    this.fontSize.value = {
                        value: fontSizeList[0].value-fontSizeStep,
                        label: fontSizeList[0].value-fontSizeStep,
                        selected: false
                    };
                }
                this.selectedFontSize.value = this.fontSize.value;
            }
        } else {
            let hasError = { error:false, dv: 0 };
            // 不再在列表中
            if (item.value === '+') {
                this.fontSize.value.value += fontSizeStep;
                this.fontSize.value.label += fontSizeStep;
                hasError.error = this.fontSize.value.value > maxFontSize;
                hasError.dv = hasError.error ? -fontSizeStep : 0;
            } else {
                this.fontSize.value.value -= fontSizeStep;
                this.fontSize.value.label -= fontSizeStep;
                hasError.error = this.fontSize.value.value < minFontSize;
                hasError.dv = hasError.error ? fontSizeStep : 0;
            }
            if (hasError.error) {
                ElMessage.error('字体大小只能在5到100之间');
                this.fontSize.value.value += hasError.dv;
                this.fontSize.value.label += hasError.dv;
                return;
            }
            this.selectedFontSize.value = this.fontSize.value;
        }

    }

    // 控制粗体斜体下划线
    onH2Center3IconsClick(item: {selected: boolean, value: string}) {
        item.selected = !item.selected;
        if (item.value === 'bold') {
            this.isTextBold.value = item.selected;
        } else if (item.value === 'italic') {
            this.isTextItalic.value = item.selected;
        } else if (item.value === 'underline') {
            this.isTextUnderline.value = item.selected;
        }
    }
    // 文本对齐
    onH2Center4IconsClick(item: {selected:boolean, value: string}) {
        h2CenterRightIcons.forEach(i => i.selected = false);
        item.selected = true;
        this.textAlign.value = item.value as TextAlign;
    }

}