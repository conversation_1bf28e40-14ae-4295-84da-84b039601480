<script setup lang="ts">

import {
  dropDownData,
  h1CenterBtns,
  h1LeftIcons,
  h1RightIcons
} from "@pages/ea/views/ea_canvas/editor/top_header/ToolIconsData.ts";
import {ElHeader} from "element-plus";
import SvgIcon from "@/components/non_biz/svg_icon/SvgIcon.vue";
import {TopHeader1Ctrl} from "@pages/ea/views/ea_canvas/editor/top_header/TopHeader1Ctrl.ts";

const ctrl = new TopHeader1Ctrl();

</script>

<template>
  <ElHeader class="header-1" height="42px">
    <!-- 左侧icon按钮 -->
    <div class="left">
      <el-tooltip :key="item.icon" effect="dark"
                  :content="item.tips" placement="top"
                  v-for="(item,index) in h1LeftIcons">
          <span class="icon-wrapper" :class="[index === h1LeftIcons.length-1 ? 'last' : '']">
            <el-button class="icon-button" @click="ctrl.onH1LeftIconsClick(item)">
              <SvgIcon class="icon" :name="item.icon" :size="item.size"></SvgIcon>
            </el-button>
          </span>
      </el-tooltip>
    </div>
    <!-- 中间三个按钮 -->
    <div class="center">
      <el-button class="tool-button" :class="[item.selected ? 'btn-selected' : '']" round
                 @click="ctrl.onH1BtnClick(item)"
                 v-for="item in h1CenterBtns" :key="item.label">{{ item.label }}</el-button>
    </div>
    <!-- 右侧icon按钮 -->
    <div class="right">
      <template v-for="(item,index) in h1RightIcons">
        <el-tooltip v-if="item.id" :key="item.icon" effect="dark"
                    :content="item.tips" placement="top">
            <span class="icon-wrapper" :class="[index === h1RightIcons.length-1 ? 'last' : '']">
              <el-button class="icon-button" @click="ctrl.onH1RightIconsClick(item)">
                <SvgIcon class="icon" :name="item.icon" :size="item.size"></SvgIcon>
              </el-button>
            </span>
        </el-tooltip>
        <!-- 右侧下拉菜单 -->
        <el-dropdown v-else class="icon-wrapper dropdown" @command="ctrl.onDropdownSelectedItemChange">
          <span class="dropdown-link">{{ ctrl.dropDownSelectedItem.value.label }}</span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :key="dropdownItem.id"
                                :command="dropdownItem"
                                v-for="dropdownItem in dropDownData">{{ dropdownItem.label }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </div>
  </ElHeader>
</template>

<style scoped lang="scss">
.header-1 {
  border-bottom: 1px solid var(--el-border-color);

  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  min-width: 0;

  .center {
    flex: 1;
    display: flex;
    justify-content: center;
    margin: 0 18px;
    .tool-button {
      height: 20px;
      padding: 10px 22px;
      font-size: 12px;
      border: none;
      color: black;
      &:hover {
        background-color: var(--el-color-primary);
        border-radius: var(--el-border-radius-round);
        color: white;
      }
      &:after {
        border-radius: var(--el-border-radius-round);
      }
    }

    .btn-selected {
      background-color: var(--el-color-primary);
      border-radius: var(--el-border-radius-round);
      color: white;
    }
  }

  .icon-wrapper {
    margin-right: 15px;
    display: inline-block;
    padding: 0;
    line-height: 1;

    &:last-child {
      margin-right: 0;
    }

    .icon-button {
      border: none;
      padding: 0;
      height: auto;
    }

    .icon {
      font-size: 20px;
      cursor: pointer;
    }

    .dropdown-link {
      border: none;
      outline: none;
      &::before {
        content: "";
        position: absolute;
        top: -4px;
        bottom: -4px;
        left: -4px;
        right: -4px;
        border-radius: 7px;
        border: 2px solid transparent;
      }
      &:hover {
        color: rgba(51,51,51,1);
        background-color: rgba(236,236,239,1);
        border-color: rgba(137,136,141,1);;
      }
      &:active {
        &::after {
          border-color: rgba(102, 143, 214, 1);
        }
      }
    }


    &.dropdown {
      padding: 5px 5px 5px 5px;
      top: -3px;
      font-size: 12px;
      border-bottom: 0.5px solid #797979;
    }
  }

}
</style>