import {computed} from "vue";
import {dropDownData, h1CenterBtns} from "./ToolIconsData.ts";
import type {IconData} from "@pages/ea/views/ea_canvas/editor/top_header/types.ts";
import {EditorEventTypes} from "@pages/ea/views/ea_canvas/editor/EditorEventTypes.ts";
import evtBus from "@/libs/request/evtBus.ts";

export class TopHeader1Ctrl {

    dropDownSelectedItem = computed(() => {
        const selectedItem = dropDownData.filter(i => i.selected)[0];
        return selectedItem || {label: ''};
    });

    constructor() {
        this.onH1RightIconsClick = this.onH1RightIconsClick.bind(this);
        this.onH1LeftIconsClick = this.onH1LeftIconsClick.bind(this);
        this.onH1BtnClick = this.onH1BtnClick.bind(this);
        this.onDropdownSelectedItemChange = this.onDropdownSelectedItemChange.bind(this);
    }

    onH1BtnClick(item: {selected: boolean}) {
        h1CenterBtns.forEach(i => i.selected = false);
        item.selected = true;
    }

    onH1LeftIconsClick(item: { id:string }) {
        let evtType = '';
        switch(item.id) {
            case 'collapse_left':
                evtType = EditorEventTypes.TOGGLE_COLLAPSE_LEFT;
                break;
        }
        if (!evtType) throw new Error('未知按钮');
        evtBus.emit(evtType, item);
    }

    onH1RightIconsClick(item: IconData) {
        let evtType = '';
        switch(item.id) {
            case 'collapse_right':
                evtType = EditorEventTypes.TOGGLE_COLLAPSE_RIGHT;
                break;
        }
        if (!evtType) throw new Error('未知按钮');
        evtBus.emit(evtType, item);
    }

    onDropdownSelectedItemChange(item: {selected: boolean}) {
        dropDownData.forEach(i => i.selected = false);
        item.selected = true;
    }

}