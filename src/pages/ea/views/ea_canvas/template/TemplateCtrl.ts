import { ref } from "vue";
import { useRouter } from "vue-router";

export function TemplateCtrl() {
  const router = useRouter();

  const curType = ref(0);
  const typeList = ref([
    { name: "EA模板" },
    { name: "EA制品" },
    { name: "建模语言" },
  ]);
  const changeType = (idx: number) => {
    curType.value = idx;
  };

  const list = ref(<any>[]);
  for (let i = 0; i < 8; i++) {
    list.value.push({
      name: "xxxxxxx图",
    });
  }

  const checkTemp = () => {
    // router.push("/group/template");
  };
  function handleAdd(){
    router.push("/ea_canvas/add");
  }

  const search = ref({
    name: "",
  });
  function toSearch(val: string) {
    console.log(val);
  }

  return {
    curType,
    typeList,
    changeType,
    list,
    checkTemp,
    handleAdd,
    search,
    toSearch,
  };
}
