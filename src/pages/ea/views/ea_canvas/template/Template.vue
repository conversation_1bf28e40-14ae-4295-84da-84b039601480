<script setup lang="ts">
import { TemplateCtrl } from "./TemplateCtrl.ts";
import bt_tab from "@/components/non_biz/bt_tab/BtTab.vue";

const { curType, typeList, changeType, list, checkTemp, search, toSearch } = TemplateCtrl();



</script>
<template>
  <div class="ea-canvas-template">
    <div class="header">
      <bt_tab :list="typeList" :value="curType" @change="changeType">
        <template #right>
          <div class="right-opt flex flex-center">
            <BtSearch
              class="item"
              v-model="search.name"
              placeholder="请输入搜索内容"
              @search="toSearch"
            ></BtSearch>
          </div>
        </template>
      </bt_tab>
    </div>
    <div class="container">
      <div class="list">
        <div v-for="(item,index) in list" :key="index" class="li">
          <el-image class="img" src="/brand/logo-img.png" />
          <div class="info">
            <div class="title">{{ item.name }}</div>
            <div class="txt">这是详细文本这是详细文本这是详细文本这是详细文本这是详细文本</div>
          </div>
          <div class="opt">
            <el-button class="btn">预览</el-button>
            <el-button class="btn" type="primary" @click="checkTemp" >使用模板</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.ea-canvas-template {
  .container {
    padding: 10px 20px;
    .select {
      width: 200px;
    }
    .list {
      .li {
        display: flex;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid var(--el-border-color);
        .img {
          width: 38px;
          height: 38px;
          margin-right: 16px;
          flex-shrink: 0;
        }
        .info {
          flex: 1;
          line-height: 17px;
          .title {
            color: #161616;
          }
          .txt {
            color: #666666;
          }
        }
        .opt {
          margin-left: 16px;
          flex-shrink: 0;
          .btn {
            padding-left: 10px;
            padding-right: 10px;
          }
        }
      }
    }
  }
}

</style>
