import type { RouteRecordRaw } from 'vue-router';
import { RouterView } from "vue-router"
import Layout from "@/components/non_biz/layout/index.vue";

const constantRoutes: RouteRecordRaw[] = [
    {
        path: '/',
        name: 'ea_main',
        component: Layout,
        meta: {
            title: "EA",
        },
        redirect: { name: 'ea_libs' },
        children: [
            {
                path: '/ea_analysis',
                name: 'ea_analysis',
                component: () => import('@pages/ea/views/ea_analysis/EaAnalysis.vue'),
                meta: {
                    title: 'EA分析',
                    icon: 'EA分析'
                }
            },
            {
                path: '/ea_canvas',
                name: 'ea_canvas',
                component: RouterView,
                meta: {
                    title: 'EA画布',
                    icon: 'EA画布'
                },
                redirect: '/ea_canvas/add',
                children: [
                    {
                        path: '/ea_canvas/template',
                        name: 'ea_canvas_template',
                        component: () => import('@pages/ea/views/ea_canvas/template/Template.vue'),
                    },
                    {
                        path: '/ea_canvas/add',
                        name: 'ea_canvas_add',
                        component: () => import('@pages/ea/views/ea_canvas/add/Add.vue'),
                    },
                    {
                        path: '/ea_canvas/editor',
                        name: 'ea_canvas_editor',
                        component: () => import('@pages/ea/views/ea_canvas/editor/Editor.vue')
                    }
                ]
            },
            {
                path: '/ea_libs',
                name: 'ea_libs',
                component: () => import('@pages/ea/views/ea_libs/EaLibs.vue'),
                meta: {
                    title: 'EA库',
                    icon: 'EA库'
                }
            },
            {
                path: '/ea_mgm',
                name: 'ea_mgm',
                component: () => import('@pages/ea/views/ea_mgm/EaMgm.vue'),
                meta: {
                    title: 'EA治理',
                    icon: 'EA治理'
                }
            },
            {
                path: '/meta_model',
                name: 'ea_meta_model',
                component: () => import('@pages/ea/views/meta_model/MetaModel.vue'),
                meta: {
                    title: '元模型',
                    icon: '元模型'
                }
            },
            {
                path: '/my_ea',
                name: 'ea_my_ea',
                component: () => import('@pages/ea/views/my_ea/MyEa.vue'),
                meta: {
                    title: '我的EA',
                    icon: '我的EA'
                }
            }
        ]
    }

];
export default constantRoutes;
