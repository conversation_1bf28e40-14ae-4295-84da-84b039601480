import { createApp } from 'vue'
import './style.scss'
import App from './index.vue';
import router from "./router/index";
import {createPinia} from "pinia";
import { startVCairn } from "@/libs/v-carin";
import { ProjectsConnector } from '@/pages/projects/common/ProjectsConnector.ts';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

const app = createApp(App);
app.use(pinia);
startVCairn(app, new ProjectsConnector());
app.use(router);
app.mount('#app');