import { ref } from "vue";
import { useRouter } from "vue-router";

export function GroupCtrl() {
  const router = useRouter();

  const curType = ref(0);
  const typeList = ref([
    { name: "项目模板" },
    { name: "管理文档模板" },
    { name: "技术文档模板" },
  ]);
  const changeType = (idx: number) => {
    curType.value = idx;
  };

  const list = ref([{ name: "空白项目" }]);
  for (let i = 0; i < 8; i++) {
    list.value.push({
      name: "课题项目",
    });
  }

  const checkTemp = () => {
    router.push("/group/template");
  };

  const search = ref({
    name: "",
  });
  function toSearch(val: string) {
    console.log(val);
  }

  return {
    curType,
    typeList,
    changeType,
    list,
    checkTemp,
    search,
    toSearch,
  };
}
