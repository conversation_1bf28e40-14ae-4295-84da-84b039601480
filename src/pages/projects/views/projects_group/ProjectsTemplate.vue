<script setup lang="ts">
import { TemplateCtrl } from "./ProjectsTemplateCtrl.ts";
import bt_tab from "@/components/non_biz/bt_tab/BtTab.vue";

const { 
  curType, typeList, changeType, 
  table, table2, table3, 
  indexMethod, index2Method,
  back,
} = TemplateCtrl();

</script>
<template>
  <div class="project-template">
    <div class="header">
      <div class="title">项目模板</div>
      <bt_tab :list="typeList" :value="curType" @change="changeType"> </bt_tab>
    </div>
    <div class="container">
      <div class="box" v-show="curType == 0">
        <el-table
          class="table"
          :data="table"
          row-key="id"
          default-expand-all
        >
          <el-table-column
            type="index"
            label="#"
            width="50"
            :index="indexMethod"
          >
          </el-table-column>
          <el-table-column prop="name" label="项目任务" min-width="300" />
          <el-table-column prop="user" label="负责人" width="140" />
          <el-table-column prop="start" label="开始日期" width="200" />
          <el-table-column prop="end" label="截止日期" width="200" />
        </el-table>
      </div>
      <div class="box" v-show="curType == 1">
        <img class="nav-img" src="/public/brand/img-1.jpg" alt="">
        <el-table
          class="table"
          :data="table2"
          row-key="id"
          default-expand-all
        >
          <el-table-column
            type="index"
            label="#"
            width="50"
            :index="index2Method"
          >
          </el-table-column>
          <el-table-column prop="name" label="项目角色" width="250" />
          <el-table-column prop="desc" label="角色职责" min-width="250" />
          <el-table-column prop="num" label="成员数" width="142" />
        </el-table>
      </div>
      <div class="box" v-show="curType == 2">
        <el-table class="table" :data="table3"  >
          <el-table-column type="index" label="#" width="50"> </el-table-column>
          <el-table-column prop="name" label="项目应用" width="210" />
          <el-table-column prop="desc" label="应用说明" />
          <el-table-column prop="remark" label="备注" />
        </el-table>
      </div>
    </div>
    <div class="footer">
      <el-button @click="back" >取消</el-button>
      <el-button type="primary">使用模板</el-button>
    </div>
  </div>
</template>
<style scoped lang="scss">
.project-template {
  height: 100%;
  display: flex;
  flex-direction: column;
  .header {
    .title {
      font-size: 16px;
      color: #161616;
      font-weight: bold;
      line-height: 19px;
      padding: 16px 0 8px 22px;
    }
  }
  .container {
    flex: 1;
    padding: 10px 20px;
    overflow: hidden;
    .box {
      width: 100%;
      height: 100%;
      .table {
        width: 100%;
        height: 100%;
      }
      .nav-img {
        display: block;
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
      }
    }
    .list {
      .li {
        display: flex;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid var(--el-border-color);
        .img {
          width: 38px;
          height: 38px;
          margin-right: 16px;
          flex-shrink: 0;
        }
        .info {
          flex: 1;
          line-height: 17px;
          .title {
            color: #161616;
          }
          .txt {
            color: #666666;
          }
        }
        .opt {
          margin-left: 16px;
          flex-shrink: 0;
          .btn {
            padding-left: 10px;
            padding-right: 10px;
          }
        }
      }
    }
  }
  .footer {
    text-align: right;
    padding: 10px 20px;
  }
}
</style>
