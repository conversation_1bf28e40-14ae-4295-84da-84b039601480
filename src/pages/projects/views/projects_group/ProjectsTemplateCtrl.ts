import { ref } from "vue";
import { useRouter } from "vue-router"

export function TemplateCtrl() {
  const curType = ref(0);
  const typeList = ref([
    { name: "项目计划" },
    { name: "项目团队" },
    { name: "项目应用" },
  ]);
  const changeType = (idx: number) => {
    curType.value = idx;
  };

  function getTableIndex(list: any[]): string[] {
    let res = [];
    for (let item of list) {
      res.push(item.id);
      if (item.children) {
        res.push(...getTableIndex(item.children));
      }
    }
    return res;
  }

  const table = ref([
    {
      id: "1",
      name: "项目整体计划初步安排项目整体计划初步安排项目整体计划初步",
      user: "张三",
      start: "2025-01-01",
      end: "2025-01-28",
      children: [
        {
          id: "1.1",
          name: "项目整体计划初步安排项目整体计划初步安排项目整体计划初步",
          user: "张三",
          start: "2025-01-01",
          end: "2025-01-28",
          children: [
            {
              id: "1.1.1",
              name: "项目整体计划初步安排项目整体计划初步安排项目整体计划初步",
              user: "张三",
              start: "2025-01-01",
              end: "2025-01-28",
            },
          ],
        },
      ],
    },
    {
      id: "2",
      name: "项目整体计划初步安排项目整体计划初步安排项目整体计划初步",
      user: "张三",
      start: "2025-01-01",
      end: "2025-01-28",
    },
  ]);
  const indexMap = getTableIndex(table.value);
  const indexMethod = (index: number) => {
    return indexMap[index];
  };

  const table2 = ref([
    {
      id: "1",
      name: "项目经理",
      desc: "角色职责角色职责角色职责角色职责",
      num: 1,
      children: [
        {
          id: "1.1",
          name: "研发经理",
          desc: "角色职责角色职责角色职责角色职责",
          num: 1,
          children: [
            {
              id: "1.1.1",
              name: "研发工程师",
              desc: "角色职责角色职责角色职责角色职责",
              num: 1,
            },
          ],
        },
      ],
    },
    {
      id: "2",
      name: "项目CEO",
      desc: "角色职责角色职责角色职责角色职责",
      num: 1,
    },
  ]);
  const index2Map = getTableIndex(table2.value);
  const index2Method = (index: number) => {
    return index2Map[index];
  };

  const table3 = ref(<any>[]);
  for (let i = 0; i < 10; i++) {
    table3.value.push({
      name: "概览",
      desc: "xxxxxxxxxxxxxxxxxxxxxx",
      remark: "xxxxxxxxxxxxxxxxxxxxxxxxxx",
    });
  }

  const router = useRouter();
  function back(){
    router.back();
  }

  return {
    curType,
    typeList,
    changeType,
    table,
    table2,
    table3,
    indexMethod,
    index2Method,
    back,
  }
}
