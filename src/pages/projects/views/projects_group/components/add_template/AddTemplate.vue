<script setup lang="ts">
import { AddTemplateCtrl } from "./AddTemplateCtrl.ts";
import bt_select from "@/components/non_biz/bt_select/BtSelect.vue";
import bt_transfer from "@/components/non_biz/bt_transfer/BtTransfer.vue";


const { 
  dialogVisible, open, close, formData, formRules, submitForm,
  levelOption, typeOption, tree,
} = AddTemplateCtrl();

defineExpose({ open, close });

</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    title="新建项目"
    width="796"
    :before-close="close"
  >
    <el-form
      ref="ruleFormRef"
      class="form"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
        <el-form-item label="项目名称" prop="name">
          <BtInput v-model="formData.name" placeholder="请输入" clearable  ></BtInput>
        </el-form-item>
      <div class="row">
        <el-form-item label="项目层级" prop="level">
          <bt_select
            class="select"
            :list="levelOption"
            v-model="formData.level"
          ></bt_select>
        </el-form-item>
        <el-form-item label="项目类型" prop="type">
          <bt_select
            class="select"
            :list="typeOption"
            v-model="formData.type"
            showAll
            multiple
          ></bt_select>
        </el-form-item>
      </div>
      <div class="row">
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker
            class="date-select"
            v-model="formData.startTime"
            type="date"
            placeholder="请选择"
          />
        </el-form-item>
        <el-form-item label="完成时间" prop="endTime">
          <el-date-picker
            class="date-select"
            v-model="formData.endTime"
            type="date"
            placeholder="请选择"
          />
        </el-form-item>
      </div>
      <div class="row">
        <el-form-item label="项目负责人" prop="leader">
          <bt_transfer
            class="select"
            type="tree"
            title="项目负责人"
            :list="tree"
            showSearch
            v-model="formData.leader"
          ></bt_transfer>
        </el-form-item>
        <el-form-item label="所属部门" prop="dept">
          <bt_transfer
            class="select"
            type="tree"
            title="所属部门"
            :list="tree"
            showSearch
            checkStrictly
            v-model="formData.dept"
          ></bt_transfer>
        </el-form-item>
      </div>
        <el-form-item label="项目成员" prop="member">
          <el-input-tag v-model="formData.member" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="项目说明" prop="desc">
          <BtInput
            class="textarea"
            v-model="formData.desc"
            :rows="3"
            type="textarea"
            maxlength="300"
            show-word-limit
            placeholder="请输入"
          />
        </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped lang="scss">
.form {
  .row {
    display: flex;
    justify-content: space-between;
  }
  .select {
    width: 215px;
  }
  :deep(.date-select) {
    width: 215px;
  }
  .textarea {
    width: 100%;
  }
}
</style>
