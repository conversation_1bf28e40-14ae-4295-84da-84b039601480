import { ref, useTemplateRef } from "vue";

export function AddTemplateCtrl() {
  const dialogVisible = ref(false);
  function open() {
    dialogVisible.value = true;
  }
  function close() {
    dialogVisible.value = false;
  }

  const formData = ref({
    name: "",
    level: "",
    type: [],
    startTime: "",
    endTime: "",
    leader: [],
    dept: [],
    member: [],
    desc: "",
  });
  const formRules = ref({
    name: [{required: true, message: '请输入项目名称', trigger: 'blur' }],
    type: [{ required: true, message: "请选择项目类型", trigger: "change" }],
    leader: [{ required: true, message: "请选择项目负责人", trigger: "change" }],
  });
  const ruleFormRef = useTemplateRef<any>('ruleFormRef');
  async function submitForm() {
    await ruleFormRef.value.validate();
    close();
  };

  const levelOption = [
    { label: '一级', value: '1', },
    { label: '二级', value: '2', },
    { label: '三级', value: '3', },
  ];

  const typeOption = [
    { label: '类型1', value: '1' },
    { label: '类型2', value: '2' },
    { label: '类型3', value: '3' },
  ];

  const tree = ref([
    {
      label: "一级选项一",
      value: "1",
      children: [
        {
          label: "二级选项一",
          value: "1.1",
        },
        {
          label: "二级选项二",
          value: "1.2",
        },
        {
          label: "二级选项三",
          value: "1.3",
          children: [
            { label: "三级选项一", value: "1.3.1" },
            { label: "三级选项二", value: "1.3.2" },
          ],
        },
      ],
    },
    {
      label: "一级选项二",
      value: "2",
      children: [
        { label: "二级选项阿是分身乏术方式防守打法说法", value: "2.1" },
        { label: "二级选项二", value: "2.2" },
      ],
    },
    {
      label: "一级选项三",
      value: "3",
    },
  ]);

  return {
    dialogVisible,
    open,
    close,
    formData,
    formRules,
    submitForm,
    levelOption,
    typeOption,
    tree,
  };
}
