import { ref, computed, useTemplateRef } from "vue";
import { useRouter } from "vue-router";

export function ProjectMyCtrl() {
  const curType = ref(0);
  const typeList = ref([
    { key: "list", name: "项目列表" },
    { key: "my", name: "我的关注" },
  ]);
  const changeType = (idx: number) => {
    curType.value = idx;
  };

  const search = ref({
    name: "",
  });
  function toSearch(val: string) {
    console.log(val);
  }

  const columnRef = useTemplateRef<any>('columnRef')
  const columnList = ref([
    { 
      field: 'type', label: '项目类型', width: '100', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '软件类型', value: '1', },
          { label: '工具类型', value: '2', },
          { label: '机械类型', value: '3', },
        ]
      },
      filterValue: {},
     },
    { 
      field: 'name', label: '项目名称', width: '', minWidth: '200', show: true, 
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'fzr', label: '项目负责人', width: '165', minWidth: '', show: true, 
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'xmjd', label: '项目阶段', width: '155', minWidth: '', show: true, 
      filter: {
        search: false,
        options: [
          { label: '阶段1', value: '1', },
          { label: '阶段2', value: '2', },
          { label: '阶段3', value: '3', },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'jdfzr', label: '阶段负责人', width: '122', minWidth: '', show: true, 
    },
    { 
      field: 'kssj', label: '计划开始时间', width: '140', minWidth: '', show: true, 
      filter: {
        search: true,
        order: true,
      },
      filterValue: {},
    },
    { 
      field: 'wcsj', label: '计划完成时间', width: '140', minWidth: '', show: true, 
      filter: {
        search: true,
        order: true,
      },
      filterValue: {},
    },
    { 
      field: 'zt', label: '状态', width: '107', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '状态1', value: '1', },
          { label: '状态2', value: '2', },
          { label: '状态3', value: '3', },
        ]
      },
      filterValue: {}
    },
    { 
      field: 'jd', label: '进度', width: '132', minWidth: '', show: true, 
      filter: {
        search: true,
        order: true,
      },
      filterValue: {}
    },
  ])
  const showColumn = computed(()=>{
    return columnList.value.filter(v=>v.show);
  })
  function openColumn(){
    columnRef.value.open();
  }
  function changeFilter(info: any, type: string) {
    console.log(type,"字段筛选变化-------------",info);
  }


  const tableData = ref(<any>[]);
  for (let i = 0; i < 10; i++) {
    tableData.value.push({
      type: "软件项目",
      name: "xxxxxxxxxxxx项目",
      fzr: "张三 c2345",
      xmjd: "招采中",
      jdfzr: "王五  t3568",
      kssj: "2025-08-08",
      wcsj: "2025-09-08",
      zt: "进行中",
      jd: "",
    });
  }

  const page = ref(1);
  function changePage(val: number) {
    console.log(val, page.value);
  }

  const router = useRouter();
  function checkProject() {
    router.push("/space/module");
  }

  return {
    curType,
    typeList,
    changeType,
    page,
    columnList,
    showColumn,
    openColumn,
    tableData,
    changePage,
    search,
    toSearch,
    checkProject,
    changeFilter,
  };
}
