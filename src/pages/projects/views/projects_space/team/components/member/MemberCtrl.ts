import { ref, computed, useTemplateRef } from "vue";
import { ElMessageBox } from 'element-plus'


export function MemberCtrl() {
  
  const columnRef = useTemplateRef<any>('columnRef')
  const columnList = ref([
    { 
      field: 'name', label: '团队成员', width: '183', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'role', label: '团队角色', width: '', minWidth: '400', show: true,
      filter: {
        search: false,
        options: [
          { label: '项目经理', value: '1', },
          { label: '产品经理', value: '2', },
          { label: '成员', value: '3', },
          { label: '后端工程师', value: '4', },
        ],
      },
      filterValue: {},
    },
    { 
      field: 'remark', label: '备注', width: '265', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
  ])
  const showColumn = computed(()=>{
    return columnList.value.filter(v=>v.show);
  })
  function openColumn(){
    columnRef.value.open();
  }
  function changeFilter(info: any, type: string) {
    console.log(type,"字段筛选变化-------------",info);
  }


  const tableData = ref(<any>[]);
  for(let i = 0; i < 5; i++) {
    tableData.value.push({
      name: '张三',
      role: ['项目经理','产品经理','成员','后端工程师'],
      remark: 'xxxxxxxxxxx',
    })
  }
  function handleDel(){
    ElMessageBox.confirm('是否删除该团队成员？','提示',{type: 'warning',});
  }
  function delRole(index: number,tidx: number) {
    tableData.value[index].role.splice(tidx,1);
  }

  const page = ref(1);
  function changePage(val: number) {
    page.value = val;
  }

  return {
    columnList,
    showColumn,
    openColumn,
    tableData,
    handleDel,
    page,
    changePage,
    delRole,
    changeFilter,
  };
}
