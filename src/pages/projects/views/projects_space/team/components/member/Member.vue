<script setup lang="ts">
import { tableConfig } from "@/app-config.ts";
import { MemberCtrl } from "./MemberCtrl.ts";
import {
  MoreFilled,
} from "@element-plus/icons-vue";

const {
  columnList,
  showColumn,
  openColumn,
  tableData,
  handleDel,
  page,
  changePage,
  delRole,
  changeFilter,
} = MemberCtrl();

defineExpose({openColumn});
</script>
<template>
  <div class="team-member">
    <el-table class="table" :data="tableData" style="width: 100%; height: 100%">
      <el-table-column type="selection" width="33" />
      <el-table-column label="#" width="40" align="center">
        <template #default="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <template v-for="column in showColumn" :key="column.field">
        <el-table-column
          v-if="column.field == 'role'"
          :property="column.field"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
        >
          <template #header>
            <BtThFilter v-if="column.filter" :title="column.label" :info="column.filter" v-model="column.filterValue" @change="changeFilter($event,column.field)"></BtThFilter>
            <span v-else >{{ column.label }}</span>
          </template>
          <template #default="{ $index, row }">
            <el-tag
              v-for="(tag, tidx) in row.role"
              :key="tidx"
              class="tag"
              closable
              color="#edf1f9"
              type="info"
              @close="delRole($index,tidx)"
            >
              {{ tag }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :property="column.field"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
        >
          <template #header>
            <BtThFilter v-if="column.filter" :title="column.label" :info="column.filter" v-model="column.filterValue" @change="changeFilter($event,column.field)"></BtThFilter>
            <span v-else >{{ column.label }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column fixed="right" label="操作" v-bind="tableConfig.optColumnAttr">
        <template #default>
          <el-dropdown placement="bottom-end">
            <div class="more-opt">
              <el-icon class="icon"><MoreFilled /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu class="border" style="width: 140px">
                <el-dropdown-item @click="handleDel">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <div class="footer">
      <BtPage v-model="page" :total="100" @change="changePage"></BtPage>
    </div>
    <BtColumn ref="columnRef" v-model="columnList">
      <div style="display: none;"></div>
    </BtColumn>
  </div>
</template>
<style scoped lang="scss">
.team-member {
  height: 100%;
  display: flex;
  flex-direction: column;
  .table {
    flex: 1;
    .tag {
      color: rgba(51,51,51,0.65);
      margin-right: 10px;
    }
  }
  .footer {
    padding: 10px 20px;
    text-align: right;
  }
}
</style>
