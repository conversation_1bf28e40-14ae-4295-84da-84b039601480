<script setup lang="ts">
import { PermissionCtrl } from "./PermissionCtrl.ts";

const {
  roleList, permissionList,
} = PermissionCtrl();
</script>
<template>
  <div class="team-permission">
    <div class="list">
      <div v-for="(item,index) in permissionList" :key="index" class="li">
        <div class="title">{{ item.type }}</div>
        <div class="table">
          <div class="tr">
            <div class="th s1">权限项</div>
            <div v-for="(role,ridx) in roleList" :key="ridx" class="th">{{ role.name }}</div>
          </div>
          <div v-for="(child,cidx) in item.list" :key="cidx" class="tr">
            <div class="td s1">{{ child.name }}{{ item.type }}</div>
            <div v-for="(role,ridx) in roleList" :key="ridx" class="td">
              <el-switch v-model="role.permission[item.key][child.key]" :disabled="role.disabled" />
            </div>
          </div>
          <!-- <div v-for="(child,cidx) in item.list" :key="cidx" class="li"> -->
            <!-- {{ child.name }}{{ item.type }} -->
          <!-- </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.team-permission {
  height: 100%;
  overflow-y: auto;
  .list {
    .li {
      margin-bottom: 18px;
      .title {
        position: relative;
        font-size: 16px;
        line-height: 22px;
        font-weight: bold;
        padding-left: 10px;
        margin-left: 10px;
        margin-bottom: 6px;
        &::before {
          content: "";
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
          width: 2px;
          height: 14px;
          border-radius: 4px;
          background-color: rgba(0,0,0,1);
        }
      }
    }
  }
  .table {
    .tr {
      display: flex;
      border-bottom: 1px solid var(--el-border-color-lighter);
      .th {
        font-weight: bold;
      }
      .th, .td {
        flex: 1;
        text-align: center;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        &.s1 {
          text-align: left;
          justify-content: flex-start;
        }
      }
    }
  }
}
</style>
