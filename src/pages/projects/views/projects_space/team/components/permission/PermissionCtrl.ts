import { ref } from "vue";

interface ListItem<T = any> {
  [key: string]: T;
}

export function PermissionCtrl() {
  const roleList = ref([
    { 
      name: "项目经理",
      disabled: true,
      permission: <ListItem>{
        project: { edit: true, delete: true, check: true, subscribe: true },
        plan: { check: true, add: true, edit: true, delete: true, close: true, import: true, export: true, },
        team: { check: true },
      }
    },
    { 
      name: "研发经理",
      disabled: false,
      permission: <ListItem>{
        project: { edit: false, delete: false, check: false, subscribe: false },
        plan: { check: false, add: false, edit: false, delete: false, close: false, import: false, export: false, },
        team: { check: false },
      }
    },
    { 
      name: "后端工程师",
      disabled: false,
      permission: <ListItem>{
        project: { edit: false, delete: false, check: false, subscribe: false },
        plan: { check: false, add: false, edit: false, delete: false, close: false, import: false, export: false, },
        team: { check: false },
      }
    },
    { 
      name: "前端工程师",
      disabled: false,
      permission: <ListItem>{
        project: { edit: true, delete: true, check: true, subscribe: true },
        plan: { check: true, add: true, edit: true, delete: true, close: true, import: true, export: true, },
        team: { check: true },
      }
    },
    { 
      name: "产品经理",
      disabled: false,
      permission: <ListItem>{
        project: { edit: false, delete: false, check: false, subscribe: false },
        plan: { check: false, add: false, edit: false, delete: false, close: false, import: false, export: false, },
        team: { check: false },
      }
    },
    { 
      name: "测试工程师",
      disabled: false,
      permission: <ListItem>{
        project: { edit: true, delete: true, check: true, subscribe: true },
        plan: { check: true, add: true, edit: true, delete: true, close: true, import: true, export: true, },
        team: { check: true },
      }
    },
    { 
      name: "质量工程师",
      disabled: false,
      permission: <ListItem>{
        project: { edit: false, delete: false, check: false, subscribe: false },
        plan: { check: false, add: false, edit: false, delete: false, close: false, import: false, export: false, },
        team: { check: false },
      }
    },
    
  ]);
  const permissionList = ref([
    {
      type: "项目",
      key: 'project',
      list: [
        { name: "编辑", key: "edit" },
        { name: "删除", key: "delete" },
        { name: "关闭", key: "check" },
        { name: "订阅", key: "subscribe" },
      ],
    },
    {
      type: "项目计划",
      key: 'plan',
      list: [
        { name: "查看", key: "check" },
        { name: "新增", key: "add" },
        { name: "编辑", key: "edit" },
        { name: "删除", key: "delete" },
        { name: "关闭", key: "check" },
        { name: "导入", key: "import" },
        { name: "导出", key: "export" },
      ],
    },
    {
      type: "项目团队",
      key: 'team',
      list: [{ name: "查看", key: "check" }],
    },
  ]);

  return {
    roleList,
    permissionList,
  };
}
