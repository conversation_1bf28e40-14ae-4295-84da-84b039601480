import { ref, computed, useTemplateRef } from "vue";
import { ElMessageBox } from 'element-plus'


export function RoleCtrl() {
  
  const columnRef = useTemplateRef<any>('columnRef')
  const columnList = ref([
    { 
      field: 'name', label: '团队角色', width: '183', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'user', label: '团队成员', width: '', minWidth: '400', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'remark', label: '备注', width: '265', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
  ])
  const showColumn = computed(()=>{
    return columnList.value.filter(v=>v.show);
  })
  function openColumn(){
    columnRef.value.open();
  }
  function changeFilter(info: any, type: string) {
    console.log(type,"字段筛选变化-------------",info);
  }


  const tableData = ref(<any>[]);
  for(let i = 0; i < 5; i++) {
    tableData.value.push({
      name: '项目经理',
      user: ['张三','李四','王五','赵六'],
      remark: 'xxxxxxxxxxx',
    })
  }
  function handleDel(){
    ElMessageBox.confirm('是否删除该团队角色？','提示',{type: 'warning',});
  }
  function delUser(index: number,tidx: number) {
    tableData.value[index].user.splice(tidx,1);
  }

  const page = ref(1);
  function changePage(val: number) {
    page.value = val;
  }

  return {
    columnList,
    showColumn,
    openColumn,
    tableData,
    handleDel,
    page,
    changePage,
    delUser,
    changeFilter,
  };
}
