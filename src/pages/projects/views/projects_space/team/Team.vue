<script setup lang="ts">
import { TeamCtrl } from "./TeamCtrl.ts";
import { Setting } from "@element-plus/icons-vue";
import Member from "./components/member/Member.vue";
import Role from "./components/role/Role.vue";
import Permission from "./components/permission/Permission.vue";

const { 
  curType, typeList, changeType,
  openColumn, search, toSearch,
} = TeamCtrl();

</script>
<template>
  <div class="space-team">
    <div  class="container">
      <div class="header">
        <BtTab :list="typeList" :value="curType" @change="changeType">
          <template #right>
            <div class="right-opt flex flex-center">
              <el-button v-if="curType==0" class="btn item" >新建项目成员</el-button>
              <el-button v-if="curType==1" class="btn item" >新建项目角色</el-button>
              <BtSearch
                class="item"
                v-model="search.name"
                placeholder="请输入搜索内容"
                @search="toSearch"
              ></BtSearch>
              <BtTooltip
                class="icon-tooltip "
                effect="dark"
                content="自定义字段"
                placement="bottom"
              >
                <el-icon class="item icon" @click="openColumn"><Setting /></el-icon>
              </BtTooltip>
            </div>
          </template>
        </BtTab>
      </div>
      <div v-show="curType == 0" class="cont">
        <Member ref="memberRef" />
      </div>
      <div v-show="curType == 1" class="cont">
        <Role v-if="typeList[1].load" ref="roleRef" />
      </div>
      <div v-show="curType == 2" class="cont">
        <Permission v-if="typeList[2].load" ref="permissionRef" />
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.space-team {
  height: 100%;
  display: flex;
  .sidebar {
    width: 300px;
    height: 100%;
    margin-right: 10px;
    flex-shrink: 0;
  }
  .container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    // border: 1px solid var(--el-border-color);
  }
  .cont {
    flex: 1;
    padding: 10px 10px;
    overflow: hidden;
    .table {
      .file-name {
        display: flex;
        align-items: center;
        .icon {
          width: 18px;
          height: 18px;
          margin-right: 6px;
          flex-shrink: 0;
        }
      }
    }
  }
  .footer {
    padding: 10px 20px;
    text-align: right;
  }
}
</style>
