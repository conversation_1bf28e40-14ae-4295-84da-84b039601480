import { ref, useTemplateRef } from "vue";


export function TeamCtrl() {
  const curType = ref(0)
  const typeList = ref([
    { name: '成员管理', load: true, },
    { name: '角色管理', load: false, },
    { name: '角色权限', load: false, },
  ])
  function changeType(idx: number) {
    typeList.value[idx].load = true;
    curType.value = idx;
  }
  
  const memberRef = useTemplateRef<any>('memberRef')
  const roleRef = useTemplateRef<any>('roleRef');
  function openColumn(){
    if(curType.value == 0) {
      memberRef.value.openColumn();
    } else if (curType.value == 1) {
      roleRef.value.openColumn();
    }
  }

  const search = ref({
    name: "",
  });
  function toSearch(val: string) {
    console.log(val);
  }
 

  return {
    curType,
    typeList,
    changeType,
    openColumn,
    search,
    toSearch,
  };
}
