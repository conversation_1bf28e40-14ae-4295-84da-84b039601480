import { ref, computed, useTemplateRef } from "vue";
import { ElMessageBox } from 'element-plus'


export function FileCtrl() {
  const curType = ref(0)
  const typeList = ref([
    { name: '项目文件',},
    { name: '我的项目文件',},
  ])
  function changeType(idx: number) {
    curType.value = idx;
  }
  
  const columnRef = useTemplateRef<any>('columnRef')
  const columnList = ref([
    { 
      field: 'name', label: '文件名', width: '', minWidth: '321', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'size', label: '文件大小', width: '103', minWidth: '', show: true,
    },
    { 
      field: 'type', label: '格式', width: '103', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: 'PPT', value: '1', },
          { label: 'WORD', value: '2', },
          { label: 'EXCEL', value: '3', },
        ],
      }
    },
    { 
      field: 'creater', label: '上传人', width: '103', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'createTime', label: '上传时间', width: '150', minWidth: '', show: true,
      filter: {
        search: true,
        order: true,
      },
      filterValue: {},
    },
    { 
      field: 'lastEditer', label: '最后修改人', width: '140', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'updateTime', label: '最后修改时间', width: '150', minWidth: '', show: true,
      filter: {
        search: true,
        order: true,
      },
      filterValue: {},
    },
  ])
  const showColumn = computed(()=>{
    return columnList.value.filter(v=>v.show);
  })
  function openColumn(){
    columnRef.value.open();
  }
  function changeFilter(info: any, type: string) {
    console.log(type,"字段筛选变化-------------",info);
  }


  const tableData = ref(<any>[]);
  let fileIcon = ['folder','ppt','doc'];
  for(let i = 0; i < 5; i++) {
    tableData.value.push({
      name: '这是文件名这是文件名这是文件名这是文件名这是文件名这是文件名这是文件名这是文件名',
      size: '32kb',
      type: 'ppt文件',
      creater: '001张三',
      createTime: '2025-01-04',
      lastEditer: '001张三',
      updateTime: '2025-02-03',
      icon: fileIcon[i%3],
    })
  }
  function handleDel(){
    ElMessageBox.confirm('是否删除该文件？','提示',{type: 'warning',});
  }

  const page = ref(1);
  function changePage(val: number) {
    page.value = val;
  }

  const search = ref({
    name: "",
  });
  function toSearch(val: string) {
    console.log(val);
  }

  return {
    curType,
    typeList,
    changeType,
    columnList,
    showColumn,
    openColumn,
    tableData,
    handleDel,
    page,
    changePage,
    search,
    toSearch,
    changeFilter,
  };
}
