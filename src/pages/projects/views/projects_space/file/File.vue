<script setup lang="ts">
import { tableConfig } from "@/app-config.ts";
import { FileCtrl } from "./FileCtrl.ts";
import { MoreFilled } from "@element-plus/icons-vue";

const { 
  curType, typeList, changeType,
  columnList, showColumn, tableData,
  handleDel, page, changePage,
  search, toSearch, changeFilter,
} = FileCtrl();

const getImageSrc = (filename:string, extname:string = '.png') => {
	return `${import.meta.env.BASE_URL}file_type/${filename}${extname}`;
}


</script>
<template>
  <div class="space-file">
    <div  class="container">
      <div class="header">
        <BtTab :list="typeList" :value="curType" @change="changeType">
          <template #right>
            <div class="right-opt flex flex-center">
              <el-button class="btn item" >上传</el-button>
              <el-button class="btn item" >新建文件夹</el-button>
              <BtSearch
                class="item"
                v-model="search.name"
                placeholder="请输入搜索内容"
                @search="toSearch"
              ></BtSearch>
              <BtColumn ref="columnRef" v-model="columnList"></BtColumn>
              <!-- <el-icon class="item icon" @click="openColumn"><Setting /></el-icon> -->
            </div>
          </template>
        </BtTab>
      </div>
      <div class="cont">
        <el-table
          class="table"
          :data="tableData"
          style="width: 100%; height: 100%"
        >
          <el-table-column type="selection" width="33" />
          <el-table-column label="#" width="40" align="center">
            <template #default="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <template v-for="column in showColumn" :key="column.field" >
            <el-table-column v-if="column.field == 'name'" :property="column.field" :label="column.label" :width="column.width" :min-width="column.minWidth" >
              <template #header>
                <BtThFilter v-if="column.filter" :title="column.label" :info="column.filter" v-model="column.filterValue" @change="changeFilter($event,column.field)"></BtThFilter>
                <span v-else >{{ column.label }}</span>
              </template>
              <template #default="{row}">
                <div class="file-name">
                  <el-image class="icon" :src="getImageSrc(row.icon)" ></el-image>
                  <el-text truncated>{{ row.name }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-else :property="column.field" :label="column.label" :width="column.width" :min-width="column.minWidth" >
              <template #header>
                <BtThFilter v-if="column.filter" :title="column.label" :info="column.filter" v-model="column.filterValue" @change="changeFilter($event,column.field)"></BtThFilter>
                <span v-else >{{ column.label }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column fixed="right" label="操作" v-bind="tableConfig.optColumnAttr">
            <template #default>
              <el-dropdown placement="bottom-end">
                <div class="more-opt">
                  <el-icon class="icon"><MoreFilled /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu class="border" style="width: 140px">
                    <el-dropdown-item @click="handleDel">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="footer">
        <BtPage v-model="page" :total="100" @change="changePage"></BtPage>
      </div>
    </div>
    
    
  </div>
</template>
<style scoped lang="scss">
.space-file {
  height: 100%;
  display: flex;
  .sidebar {
    width: 300px;
    height: 100%;
    margin-right: 10px;
    flex-shrink: 0;
  }
  .container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    // border: 1px solid var(--el-border-color);
  }
  .cont {
    flex: 1;
    padding: 10px 10px;
    overflow: hidden;
    .table {
      .file-name {
        display: flex;
        align-items: center;
        .icon {
          width: 18px;
          height: 18px;
          margin-right: 6px;
          flex-shrink: 0;
        }
      }
    }
  }
  .footer {
    padding: 10px 20px;
    text-align: right;
  }
}
</style>
