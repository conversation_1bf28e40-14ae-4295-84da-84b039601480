import { ref, computed, useTemplateRef } from "vue";

export function ModuleCtrl() {
  const curType = ref(0)
  const typeList = ref([
    { name: '项目需求',},
    { name: '模块需求',},
  ])
  function changeType(idx: number) {
    curType.value = idx;
  }
  
  const columnRef = useTemplateRef<any>('columnRef')
  const columnList = ref([
    { 
      field: 'name', label: '需求名称', width: '', minWidth: '206', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'desc', label: '需求描述', width: '', minWidth: '206', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'system', label: 'IT系统', width: '152', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '系统1', value: '1', },
          { label: '系统2', value: '2', },
          { label: '系统3', value: '3', },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'dept', label: '需求提出部门', width: '162', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '部门1', value: '1', },
          { label: '部门2', value: '2', },
          { label: '部门3', value: '3', },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'user', label: '需求提出人', width: '169', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'time', label: '需求提出时间', width: '153', minWidth: '', show: true,
      filter: {
        search: true,
        order: true,
      },
      filterValue: {},
    },
  ])
  const showColumn = computed(()=>{
    return columnList.value.filter(v=>v.show);
  })
  function openColumn(){
    columnRef.value.open();
  }
  function changeFilter(info: any, type: string) {
    console.log(type,"字段筛选变化-------------",info);
  }


  const tableData = ref(<any>[]);
  for(let i = 0; i < 10; i++) {
    tableData.value.push({
      name: 'XXXXXXXXXXXXXXXXXX',
      desc: 'XXXXXXXXXXXXXXXXX',
      system: 'SAP系统',
      dept: '人力资源部',
      user: '张三 s3940',
      time: '2025-01-20',
    })
  }

  const page = ref(1);
  function changePage(val: number) {
    page.value = val;
  }

  const tabList = ref([
    { name: '模块' },
  ])
  const curTab = ref(0);
  function tabChange(idx: number) {
    curTab.value = idx;
    treeList.value = treeAll[idx];
  }
  function tabAdd(val: string) {
    tabList.value.push({
      name: val,
    })
    const idx = tabList.value.length - 1;
    treeAll[curTab.value] = treeList.value;
    treeAll[idx] = treeAll[idx] || [];
    curTab.value = idx;
    treeList.value = treeAll[idx];
  }

  const treeAll = [
    [
      {
        label: '001 HR系统',
        value: '1',
        children: [
          { label: '002 功能模块2', value: '1.1' },
          { label: '003 功能模块3', value: '1.2' },
          { label: '002 功能模块4', value: '1.3' },
        ]
      },
    ]
  ];
  const treeList = ref(treeAll[curTab.value]);
  function treeChange(){
    console.log(treeList.value);
  }

  const search = ref({
    name: "",
  });
  function toSearch(val: string) {
    console.log(val);
  }

  return {
    curType,
    typeList,
    changeType,
    columnList,
    showColumn,
    openColumn,
    tableData,
    page,
    changePage,
    tabList,
    curTab,
    tabChange,
    tabAdd,
    treeList,
    treeChange,
    search,
    toSearch,
    changeFilter,
  };
}
