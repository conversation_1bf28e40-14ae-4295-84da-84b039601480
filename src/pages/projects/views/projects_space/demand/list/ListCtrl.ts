import { computed, ref, useTemplateRef } from "vue";
import { useRouter } from "vue-router";

export function ListCtrl() {
  const router = useRouter();

  const curType = ref(0)
  const typeList = ref([
    { name: '项目需求',},
    { name: '我的项目需求',},
  ])
  function changeType(idx: number) {
    curType.value = idx;
  }
  
  const columnRef = useTemplateRef<any>('columnRef')
  const columnList = ref([
    { field: 'id', label: '需求ID', width: '80', minWidth: '', show: false },
    { 
      field: 'name', label: '需求名称', width: '', minWidth: '206', show: true, 
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'desc', label: '需求描述', width: '', minWidth: '206', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { field: 'type', label: '需求类型', width: '80', minWidth: '', show: false },
    { field: 'benefit', label: '利益相关方', width: '80', minWidth: '', show: false },
    { field: 'source', label: '来源决策体系', width: '80', minWidth: '', show: false },
    { field: 'business_domain', label: '业务域', width: '80', minWidth: '', show: false },
    { field: 'business_component', label: '业务组件', width: '80', minWidth: '', show: false },
    { field: 'business_flow', label: '业务流程', width: '80', minWidth: '', show: false },
    { field: 'app_flow', label: '应用域', width: '80', minWidth: '', show: false },
    { field: 'budget', label: '预算', width: '80', minWidth: '', show: false },
    { 
      field: 'system', label: 'IT系统', width: '152', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '系统1', value: '1', },
          { label: '系统2', value: '2', },
          { label: '系统3', value: '3', },
        ],
      },
      filterValue: {},
    },
    { field: 'module', label: '模块', width: '80', minWidth: '', show: false },
    { field: 'is_bs', label: '是否影响业务流程', width: '80', minWidth: '', show: false },
    { field: 'is_data', label: '是否影响数据', width: '80', minWidth: '', show: false },
    { field: 'level', label: '迫切程度', width: '80', minWidth: '', show: false },
    { field: 'level_desc', label: '迫切程度描述', width: '80', minWidth: '', show: false },
    { 
      field: 'dept', label: '需求提出部门', width: '162', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '部门1', value: '1', },
          { label: '部门2', value: '2', },
          { label: '部门3', value: '3', },
        ],
      },
      filterValue: {},
    },
    { 
      field: 'user', label: '需求提出人', width: '169', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'time', label: '需求提出时间', width: '153', minWidth: '', show: true,
      filter: {
        search: true,
        order: true,
      },
      filterValue: {},
    },
    { field: 'dept2', label: '需求责任部门', width: '80', minWidth: '', show: false },
  ])
  const showColumn = computed(()=>{
    return columnList.value.filter(v=>v.show);
  })
  function openColumn(){
    columnRef.value.open();
  }
  function changeFilter(info: any, type: string) {
    console.log(type,"字段筛选变化-------------",info);
  }


  const tableData = ref(<any>[]);
  for(let i = 0; i < 10; i++) {
    tableData.value.push({
      name: '这是需求名称',
      desc: 'XXXXXXXXXXXXXXXXX',
      system: 'SAP系统',
      dept: '人力资源部',
      user: '张三 s3940',
      time: '2025-01-20',
    })
  }

  const page = ref(1);
  function changePage(val: number) {
    page.value = val;
  }


  function handleAdd() {
    router.push('/space/demand/add')
  }
  function handleDistribution() {
    router.push('/space/demand/distribution')
  }
  function checkDetail() {
    router.push('/space/demand/detail')
  }

  const search = ref({
    name: "",
  });
  function toSearch(val: string) {
    console.log(val);
  }



  return {
    curType,
    typeList,
    changeType,
    tableData,
    page,
    changePage,
    columnList,
    showColumn,
    openColumn,
    handleAdd,
    handleDistribution,
    checkDetail,
    search,
    toSearch,
    changeFilter,
  };
}
