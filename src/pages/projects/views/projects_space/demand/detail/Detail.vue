<script setup lang="ts">
import BtPicker from "@/components/non_biz/bt_picker/BtPicker.vue";
import { DetailCtrl } from "./DetailCtrl.ts";

const { 
  formData, formRules, submitForm, levelOption, typeOption, tree, back,
  logList,
} = DetailCtrl();
</script>
<template>
  <div class="demand-detail">
    <el-row  class="row-box" >
      <el-col :span="16" class="col-left">
        <div class="subtitle">需求信息</div>
        <el-form
          ref="ruleFormRef"
          class="form"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="11">
              <el-form-item label="需求层级" prop="level">
                <el-input
                  class="ipt readonly"
                  v-model="formData.level"
                  placeholder="请输入"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="父需求" prop="parent">
                <BtPicker
                  class="select"
                  title="父需求"
                  :list="typeOption"
                  showSearch
                  v-model="formData.parent"
                ></BtPicker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="11">
              <el-form-item label="IT系统" prop="system">
                <BtPicker
                  class="select"
                  title="IT系统"
                  :list="typeOption"
                  showSearch
                  v-model="formData.system"
                ></BtPicker>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="模块" prop="module">
                <BtPicker
                  class="select"
                  title="模块"
                  :list="tree"
                  showSearch
                  v-model="formData.module"
                ></BtPicker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="24">
              <el-form-item label="需求名称" prop="name">
                <el-input
                  class="w-100"
                  v-model="formData.name"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="24">
              <el-form-item label="需求描述" prop="desc">
                <BtInput
                  class="textarea w-100"
                  v-model="formData.desc"
                  :rows="3"
                  type="textarea"
                  maxlength="300"
                  show-word-limit
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="24">
              <el-form-item label="验收标准" prop="ysbz">
                <BtInput
                  class="textarea w-100"
                  v-model="formData.ysbz"
                  :rows="3"
                  type="textarea"
                  maxlength="300"
                  show-word-limit
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="11">
              <el-form-item label="需求类型" prop="type">
                <BtSelect
                  class="select"
                  :list="typeOption"
                  v-model="formData.type"
                ></BtSelect>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="优先级" prop="ysj">
                <BtSelect
                  class="select"
                  :list="levelOption"
                  v-model="formData.ysj"
                ></BtSelect>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="11">
              <el-form-item label="工时" prop="gs">
                <el-input
                  class="ipt"
                  v-model="formData.gs"
                  placeholder="请输入"
                >
                  <template #suffix>
                    <span class="unit">人天</span>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="需求责任人" prop="xqzrr">
                <BtPicker
                  class="select"
                  title="需求责任人"
                  :list="typeOption"
                  showSearch
                  v-model="formData.xqzrr"
                ></BtPicker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="11">
              <el-form-item label="需求评审人" prop="xqpsr">
                <BtPicker
                  class="select"
                  title="需求评审人"
                  :list="typeOption"
                  showSearch
                  v-model="formData.xqpsr"
                ></BtPicker>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="需求提出部门" prop="dept">
                <BtPicker
                  class="select"
                  title="需求提出部门"
                  :list="typeOption"
                  showSearch
                  v-model="formData.dept"
                ></BtPicker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="11">
              <el-form-item label="产品经理" prop="cpjl">
                <BtPicker
                  class="select"
                  title="产品经理"
                  :list="typeOption"
                  showSearch
                  v-model="formData.cpjl"
                ></BtPicker>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="研发经理" prop="yfjl">
                <BtPicker
                  class="select"
                  title="研发经理"
                  :list="typeOption"
                  showSearch
                  v-model="formData.yfjl"
                ></BtPicker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="11">
              <el-form-item label="测试经理" prop="csjl">
                <BtPicker
                  class="select"
                  title="测试经理"
                  :list="typeOption"
                  showSearch
                  v-model="formData.csjl"
                ></BtPicker>
              </el-form-item>
            </el-col>
            <el-col :span="11"></el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="24">
              <el-form-item label="附件" prop="file">
                <FileUpload class="upload-file" drag multiple>
                  <div class="title">选择文件</div>
                  <div class="tip">
                    支持：.rar .zip .doc .docx .pdf .jpg，且单个文件不能超过5M
                  </div>
                </FileUpload>
              </el-form-item>
            </el-col>
            <el-col :span="8"></el-col>
          </el-row>
        </el-form>
        <div class="footer">
          <el-button @click="back">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </el-col>
      <el-col :span="8" class="col-right">
        <HistoryLog title="历史记录" class="log-box" :list="logList" ></HistoryLog>
      </el-col>
    </el-row>
  </div>
</template>
<style scoped lang="scss">
.demand-detail {
  // display: flex;
  // flex-direction: column;
  height: 100%;
  padding: 16px 20px;
  overflow-y: auto;
  .subtitle {
    font-size: 16px;
    font-weight: bold;
  }
  .col-right {
    padding-left: 5%;
  }
  .col-left {
    padding-right: 5%;
    border-right: 1px solid var(--border-color);
  }
  .form {
    margin: 0 auto;
    margin-top: 16px;
    .row {
      display: flex;
      justify-content: space-between;
    }
    .select,
    .ipt {
      min-width: 215px;
      width: 100%;
    }
    .textarea {
      width: 100%;
    }
    .unit {
      color: rgba(85, 85, 85, 1);
    }
    :deep(.date-select) {
      min-width: 215px;
      width: 100%;
    }
    :deep(.el-row) {
      .el-col {
        max-width: unset;
      }
    }
  }
  .footer {
    text-align: right;
    margin: 0 auto;
    padding: 0 20px;
    padding-bottom: 0;
  }
}
</style>
