<script setup lang="ts">
import { AddCtrl } from "./AddCtrl.ts";
import bt_select from "@/components/non_biz/bt_select/BtSelect.vue";
import bt_transfer from "@/components/non_biz/bt_transfer/BtTransfer.vue";

const { 
  formData, formRules, submitForm, levelOption, typeOption, tree,
  back,
} = AddCtrl();

</script>
<template>
  <div class="add-demand">
    <div class="title">新建需求</div>
    <el-form
      ref="ruleFormRef"
      class="form"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row class="row-bg" :gutter="60" >
        <el-col :span="8">
          <el-form-item label="需求层级" prop="level">
            <el-input
              class="ipt readonly"
              v-model="formData.level"
              placeholder="请输入"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="父需求" prop="parent">
            <bt_transfer
              class="select"
              title="父需求"
              type="tree"
              :list="tree"
              showSearch
              v-model="formData.parent"
            ></bt_transfer>
          </el-form-item>
        </el-col>
        <el-col  :span="8"></el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60" >
        <el-col :span="8">
          <el-form-item label="IT系统" prop="it">
            <bt_transfer
              class="select"
              title="IT系统"
              type="tree"
              :list="tree"
              showSearch
              v-model="formData.it"
            ></bt_transfer>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="模块" prop="module">
            <bt_transfer
              class="select"
              title="模块"
              type="tree"
              :list="tree"
              showSearch
              v-model="formData.module"
            ></bt_transfer>
          </el-form-item>
        </el-col>
        <el-col  :span="8"></el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60" >
        <el-col :span="24">
          <el-form-item label="需求名称" prop="name">
            <el-input
              class="w-100"
              v-model="formData.name"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60" >
        <el-col :span="24">
          <el-form-item label="需求描述" prop="desc">
            <el-input
              class="textarea w-100"
              v-model="formData.desc"
              :rows="3"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60" >
        <el-col :span="24">
          <el-form-item label="验收标准" prop="ysbz">
            <el-input
              class="textarea w-100"
              v-model="formData.ysbz"
              :rows="3"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60">
        <el-col :span="8">
          <el-form-item label="需求类型" prop="type">
            <bt_select
              class="select"
              :list="typeOption"
              v-model="formData.type"
            ></bt_select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="优先级" prop="ysj">
            <bt_select
              class="select"
              :list="levelOption"
              v-model="formData.ysj"
            ></bt_select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="工时" prop="gs">
            <el-input class="ipt" v-model="formData.gs" placeholder="请输入">
              <template #suffix>
                <span class="unit">人天</span>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60" >
        <el-col :span="8">
          <el-form-item label="需求评审人" prop="xqpsr">
            <bt_transfer
              class="select"
              title="需求评审人"
              type="tree"
              :list="tree"
              showSearch
              v-model="formData.xqpsr"
            ></bt_transfer>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="需求提出部门" prop="dept">
            <bt_transfer
              class="select"
              title="需求提出部门"
              type="tree"
              :list="tree"
              showSearch
              v-model="formData.dept"
            ></bt_transfer>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="需求责任人" prop="xqzrr">
            <bt_transfer
              class="select"
              title="需求责任人"
              type="tree"
              :list="tree"
              showSearch
              v-model="formData.xqzrr"
            ></bt_transfer>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60" >
        <el-col :span="8">
          <el-form-item label="产品经理" prop="cpjl">
            <bt_transfer
              class="select"
              title="产品经理"
              type="tree"
              :list="tree"
              showSearch
              v-model="formData.cpjl"
            ></bt_transfer>
          </el-form-item>
        </el-col>
        <el-col :span="8" >
          <el-form-item label="研发经理" prop="yfjl">
            <bt_transfer
              class="select"
              title="研发经理"
              type="tree"
              :list="tree"
              showSearch
              v-model="formData.yfjl"
            ></bt_transfer>
          </el-form-item>
        </el-col>
        <el-col :span="8" >
          <el-form-item label="测试经理" prop="csjl">
            <bt_transfer
              class="select"
              title="测试经理"
              type="tree"
              :list="tree"
              showSearch
              v-model="formData.csjl"
            ></bt_transfer>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60" >
        <el-col :span="16">
          <el-form-item label="附件" prop="file">
            <el-upload class="upload-file" drag multiple>
              <div class="title">选择文件</div>
              <div class="tip">
                支持：.rar .zip .doc .docx .pdf .jpg，且单个文件不能超过5M
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="8"></el-col>
      </el-row>
    </el-form>
    <div class="footer">
      <el-button @click="back">取消</el-button>
      <el-button type="primary" @click="submitForm" >确定</el-button>
    </div>
  </div>
</template>
<style scoped lang="scss">
.add-demand {
  // display: flex;
  // flex-direction: column;
  height: 100%;
  padding: 16px 20px;
  overflow-y: auto;
  >.title {
    font-size: 16px;
    line-height: 19px;
    color: rgba(22,22,22,1);
    font-weight: bold;
  }
  .form {
    // flex: 1;
    padding-right: 60px;
    margin-top: 16px;
    // overflow-y: auto;
    .row {
      display: flex;
      justify-content: space-between;
    }
    .select,.ipt {
      min-width: 215px;
      width: 100%;
    }
    .textarea {
      width: 100%;
    }
    .unit {
      color: rgba(85,85,85,1);
    }
    :deep(.date-select) {
      min-width: 215px;
      width: 100%;
    }
    :deep(.el-row) {
      .el-col {
        max-width: 100%;
      }
    }
  }
  .footer {
    text-align: right;
    padding: 0 60px;
    padding-bottom: 0;
  }
}

@media(max-width: 1432px) {
  .el-col-8 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}
</style>
