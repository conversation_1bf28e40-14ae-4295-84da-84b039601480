import { ref, useTemplateRef } from "vue";
import { useRouter } from "vue-router"


export function AddCtrl() {
  const router = useRouter();

  const formData = ref({
    level: '数字化需求',
    parent: [],
    it: [],
    module: [],
    name: '',
    desc: '',
    ysbz: '',
    type: '',
    ysj: '',
    gs: '',
    xqpsr: [],
    dept: [],
    xqzrr: [],
    cpjl: [],
    yfjl: [],
    csjl: [],
    file: [],
  });
  const formRules = ref({
    name: [{required: true, message: '请输入需求名称', trigger: 'blur' }],
    type: [{ required: true, message: "请选择需求类型", trigger: "change" }],
    xqpsr: [{ required: true, message: "请选择需求评审人", trigger: "change" }],
  });
  const ruleFormRef = useTemplateRef<any>('ruleFormRef');
  async function submitForm() {
    await ruleFormRef.value.validate();
    back();
  };

  function back(){
    router.back()
  }

  const levelOption = [
    { label: '一级', value: '1', },
    { label: '二级', value: '2', },
    { label: '三级', value: '3', },
  ];

  const typeOption = [
    { label: '类型1', value: '1' },
    { label: '类型2', value: '2' },
    { label: '类型3', value: '3' },
  ];

  const tree = ref([
    {
      label: "一级选项一",
      value: "1",
      children: [
        {
          label: "二级选项一",
          value: "1.1",
        },
        {
          label: "二级选项二",
          value: "1.2",
        },
        {
          label: "二级选项三",
          value: "1.3",
          children: [
            { label: "三级选项一", value: "1.3.1" },
            { label: "三级选项二", value: "1.3.2" },
          ],
        },
      ],
    },
    {
      label: "一级选项二",
      value: "2",
      children: [
        { label: "二级选项阿是分身乏术方式防守打法说法", value: "2.1" },
        { label: "二级选项二", value: "2.2" },
      ],
    },
    {
      label: "一级选项三",
      value: "3",
    },
  ]);

  return {
    formData,
    formRules,
    submitForm,
    back,
    levelOption,
    typeOption,
    tree,
  };
}
