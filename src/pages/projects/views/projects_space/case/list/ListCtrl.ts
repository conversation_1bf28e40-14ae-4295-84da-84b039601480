import { ref, computed, useTemplateRef } from "vue";
import { useRouter } from "vue-router"

export function ListCtrl() {
  const router = useRouter();

  const curType = ref(0)
  const typeList = ref([
    { name: '项目用例',},
    { name: '我的项目用例',},
  ])
  function changeType(idx: number) {
    curType.value = idx;
  }
  
  const columnRef = useTemplateRef<any>('columnRef')
  const columnList = ref([
    { 
      field: 'name', label: '用例名称', width: '', minWidth: '250', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'type', label: '用例类型', width: '128', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '类型1', value: '1', },
          { label: '类型2', value: '2', },
          { label: '类型3', value: '3', },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'priority', label: '优先级', width: '110', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '高', value: '1', },
          { label: '中', value: '2', },
          { label: '低', value: '3', },
        ]
      },
      filterValue: {},
    },
    { 
      field: 'creater', label: '创建人', width: '129', minWidth: '', show: true,
      filter: {
        search: true,
      },
      filterValue: {},
    },
    { 
      field: 'createTime', label: '创建时间', width: '138', minWidth: '', show: true,
      filter: {
        search: true,
        order: true,
      },
      filterValue: {},
    },
    { 
      field: 'exTime', label: '执行时间', width: '138', minWidth: '', show: true,
      filter: {
        search: true,
        order: true,
      },
      filterValue: {},
    },
    { 
      field: 'result', label: '执行结果', width: '132', minWidth: '', show: true,
      filter: {
        search: false,
        options: [
          { label: '通过', value: '1', },
          { label: '未通过', value: '2', },
        ]
      },
      filterValue: {},
    },
  ])
  const showColumn = computed(()=>{
    return columnList.value.filter(v=>v.show);
  })
  function openColumn(){
    columnRef.value.open();
  }
  function changeFilter(info: any, type: string) {
    console.log(type,"字段筛选变化-------------",info);
  }


  const tableData = ref(<any>[]);
  for(let i = 0; i < 5; i++) {
    tableData.value.push({
      name: 'xxxxxxxxxxxx',
      type: '功能用例',
      priority: '高',
      creater: '李四 s7890',
      createTime: '2025-01-04',
      exTime: '2025-02-03',
      result: '通过',
    })
  }

  const page = ref(1);
  function changePage(val: number) {
    page.value = val;
  }

  const search = ref({
    name: "",
  });
  function toSearch(val: string) {
    console.log(val);
  }

  function handleAdd() {
    router.push('/space/case/add')
  }
  function checkDetail(){
    router.push('/space/case/detail')
  }

  return {
    curType,
    typeList,
    changeType,
    columnList,
    showColumn,
    openColumn,
    tableData,
    page,
    changePage,
    search,
    toSearch,
    changeFilter,
    handleAdd,
    checkDetail,
  };
}
