<script setup lang="ts">
import { tableConfig } from "@/app-config.ts";
import { DetailCtrl } from "./DetailCtrl.ts";
import { MoreFilled } from "@element-plus/icons-vue";

const {
  formData,
  formRules,
  submitForm,
  typeOption,
  tree,
  back,
  logList,
  addStep, addStepChild, delStep,
} = DetailCtrl();
</script>
<template>
  <div class="demand-detail">
    <el-row class="row-box">
      <el-col :span="16" class="col-left">
        <div class="subtitle">用例信息</div>
        <el-form
          ref="ruleFormRef"
          class="form"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="11">
              <el-form-item label="用例类型" prop="type">
                <BtSelect
                  class="select"
                  :list="typeOption"
                  v-model="formData.type"
                ></BtSelect>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="IT系统" prop="system">
                <BtPicker
                  class="select"
                  title="IT系统"
                  :list="tree"
                  showSearch
                  v-model="formData.system"
                ></BtPicker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="11">
              <el-form-item label="模块" prop="module">
                <el-input
                  class="w-100 ipt"
                  v-model="formData.module"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="场景" prop="cj">
                <el-input
                  class="w-100 ipt"
                  v-model="formData.cj"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="11">
              <el-form-item label="优先级" prop="yxj">
                <BtSelect
                  class="select"
                  :list="typeOption"
                  v-model="formData.yxj"
                ></BtSelect>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="24">
              <el-form-item label="需求名称" prop="name">
                <BtPicker
                  class="select"
                  title="需求名称"
                  :list="typeOption"
                  showSearch
                  v-model="formData.name"
                ></BtPicker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="11">
              <el-form-item label="测试经理" prop="csjl">
                <el-input
                  class="ipt w-100"
                  v-model="formData.csjl"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="测试人员" prop="csry">
                <BtPicker
                  class="select"
                  title="测试人员"
                  :list="tree"
                  showSearch
                  v-model="formData.csry"
                ></BtPicker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="24">
              <el-form-item label="用例名称" prop="name">
                <el-input
                  class="ipt w-100"
                  v-model="formData.name"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20">
            <el-col :span="24">
              <el-form-item label="前置条件" prop="qztj">
                <BtInput
                  class="textarea w-100"
                  v-model="formData.qztj"
                  :rows="3"
                  type="textarea"
                  maxlength="300"
                  show-word-limit
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20">
            <el-col :span="24">
              <el-form-item label="步骤" prop="bz">
                <BtInput
                  class="textarea w-100"
                  v-model="formData.bz"
                  :rows="3"
                  type="textarea"
                  maxlength="300"
                  show-word-limit
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="row-bg" :gutter="20">
            <el-col :span="24">
              <el-form-item label="预期" prop="yq">
                <BtInput
                  class="textarea w-100"
                  v-model="formData.yq"
                  :rows="3"
                  type="textarea"
                  maxlength="300"
                  show-word-limit
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="formData.type == '1'" class="row-bg" :gutter="20" >
        <el-col :span="24">
          <el-form-item label="用例步骤" prop="bzList">
            <el-table 
              class="table"
              row-key="id"
              border
              :data="formData.stepList" 
              default-expand-all
              style="width: 100%;"
            >
              <el-table-column type="index" label="#" width="40"></el-table-column>
              <el-table-column props="step" label="步骤">
                <template #default="{row}">
                  <el-input v-model="row.step"></el-input>
                </template>
              </el-table-column>
              <el-table-column props="expect" label="预期">
                <template #default="{row}">
                  <el-input v-model="row.expect"></el-input>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" v-bind="tableConfig.optColumnAttr">
                <template #default="scope">
                  <el-dropdown placement="bottom-end">
                    <div class="more-opt">
                      <el-icon class="icon"><MoreFilled /></el-icon>
                    </div>
                    <template #dropdown>
                      <el-dropdown-menu class="border" style="width: 140px">
                        <el-dropdown-item @click="addStep(scope.$index)" >新建同级</el-dropdown-item>
                        <el-dropdown-item @click="addStepChild(scope.row)">新建子级</el-dropdown-item>
                        <el-dropdown-item @click="delStep(scope.row)" >删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-table-column>
              <template #empty>
                <div class="link" @click="addStep(formData.stepList.length)">添加用例步骤</div>
              </template>
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :span="8"></el-col>
      </el-row>
          <el-row class="row-bg" :gutter="20" justify="space-between">
            <el-col :span="24">
              <el-form-item label="附件" prop="file">
                <FileUpload class="upload-file" drag multiple>
                  <div class="title">选择文件</div>
                  <div class="tip">
                    支持：.rar .zip .doc .docx .pdf .jpg，且单个文件不能超过5M
                  </div>
                </FileUpload>
              </el-form-item>
            </el-col>
            <el-col :span="8"></el-col>
          </el-row>
        </el-form>
        <div class="footer">
          <el-button @click="back">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </el-col>
      <el-col :span="8" class="col-right">
        <HistoryLog
          title="历史记录"
          class="log-box"
          :list="logList"
        ></HistoryLog>
      </el-col>
    </el-row>
  </div>
</template>
<style scoped lang="scss">
.demand-detail {
  // display: flex;
  // flex-direction: column;
  height: 100%;
  padding: 16px 20px;
  overflow-y: auto;
  .subtitle {
    font-size: 16px;
    font-weight: bold;
  }
  .col-right {
    padding-left: 5%;
  }
  .col-left {
    padding-right: 5%;
    border-right: 1px solid var(--border-color);
  }
  .form {
    margin: 0 auto;
    margin-top: 16px;
    .row {
      display: flex;
      justify-content: space-between;
    }
    .select,
    .ipt {
      min-width: 215px;
      width: 100%;
    }
    .textarea {
      width: 100%;
    }
    .unit {
      color: rgba(85, 85, 85, 1);
    }
    :deep(.table) {
      width: 100%;
      th {
        background-color: rgba(76,118,192,0.04);
        .cell {
          line-height: 16px;
        }
      }
      td {
        .cell {
          display: flex;
          align-items: center;
        }
      }
    }
    :deep(.date-select) {
      min-width: 215px;
      width: 100%;
    }
    :deep(.el-row) {
      .el-col {
        max-width: unset;
      }
    }
  }
  .footer {
    text-align: right;
    margin: 0 auto;
    padding: 0 20px;
    padding-bottom: 0;
  }
}
</style>
