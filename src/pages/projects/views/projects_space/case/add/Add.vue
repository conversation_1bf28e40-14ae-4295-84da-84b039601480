<script setup lang="ts">
import { tableConfig } from "@/app-config.ts";
import { AddCtrl } from "./AddCtrl.ts";
import { MoreFilled } from "@element-plus/icons-vue";

const { 
  formData, formRules, submitForm, levelOption, typeOption, tree,
  back, addStep, addStepChild, delStep,
} = AddCtrl();

</script>
<template>
  <div class="add-case">
    <div class="title">新建项目用例</div>
    <el-form
      ref="ruleFormRef"
      class="form"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row class="row-bg" :gutter="20" >
        <el-col :span="8">
          <el-form-item label="用例类型" prop="type">
            <BtSelect
              class="select"
              :list="typeOption"
              v-model="formData.type"
            ></BtSelect>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="IT系统" prop="system">
            <BtPicker
              class="select"
              title="IT系统"
              :list="tree"
              showSearch
              v-model="formData.system"
            ></BtPicker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="模块" prop="module">
            <el-input
              class="ipt w-100"
              v-model="formData.module"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row class="row-bg" :gutter="20" >
        <el-col :span="16">
          <el-form-item label="需求名称" prop="xqmc">
            <BtPicker
              class="select"
              title="需求名称"
              :list="tree"
              showSearch
              v-model="formData.xqmc"
            ></BtPicker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="场景" prop="cj">
            <el-input
              class="ipt w-100"
              v-model="formData.cj"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="20" >
        <el-col :span="8">
          <el-form-item label="测试经理" prop="csjl">
            <el-input
              class="ipt w-100"
              v-model="formData.csjl"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="测试人员" prop="csry">
            <BtPicker
              class="select"
              title="测试人员"
              :list="tree"
              showSearch
              v-model="formData.csry"
            ></BtPicker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="20" >
        <el-col :span="16">
          <el-form-item label="用例名称" prop="name">
            <el-input
              class="ipt w-100"
              v-model="formData.name"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="优先级" prop="yxj">
            <BtSelect
              class="select"
              :list="levelOption"
              v-model="formData.yxj"
            ></BtSelect>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="20" >
        <el-col :span="24">
          <el-form-item label="前置条件" prop="qztj">
            <BtInput
              class="textarea w-100"
              v-model="formData.qztj"
              :rows="3"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="20" >
        <el-col :span="24">
          <el-form-item label="步骤" prop="bz">
            <BtInput
              class="textarea w-100"
              v-model="formData.bz"
              :rows="3"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="20" >
        <el-col :span="24">
          <el-form-item label="预期" prop="yq">
            <BtInput
              class="textarea w-100"
              v-model="formData.yq"
              :rows="3"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="formData.type == '1'" class="row-bg" :gutter="20" >
        <el-col :span="8">
          <el-form-item label="" >
            <el-button class="c-link">智能识别</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="formData.type == '1'" class="row-bg" :gutter="20" >
        <el-col :span="24">
          <el-form-item label="用例步骤" prop="bzList">
            <el-table 
              class="table"
              row-key="id"
              border
              :data="formData.stepList" 
              default-expand-all
              style="width: 100%;"
            >
              <!-- <el-table-column label="#" width="80" align="center">
                <template #default="scope">{{ scope.$index + 1 }}</template>
              </el-table-column> -->
              <el-table-column type="index" label="#" width="40"></el-table-column>
              <el-table-column props="step" label="步骤">
                <template #default="{row}">
                  <el-input v-model="row.step"></el-input>
                </template>
              </el-table-column>
              <el-table-column props="expect" label="预期">
                <template #default="{row}">
                  <el-input v-model="row.expect"></el-input>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" v-bind="tableConfig.optColumnAttr">
                <template #default="scope">
                  <el-dropdown placement="bottom-end">
                    <div class="more-opt">
                      <el-icon class="icon"><MoreFilled /></el-icon>
                    </div>
                    <template #dropdown>
                      <el-dropdown-menu class="border" style="width: 140px">
                        <el-dropdown-item @click="addStep(scope.$index)" >新建同级</el-dropdown-item>
                        <el-dropdown-item @click="addStepChild(scope.row)">新建子级</el-dropdown-item>
                        <el-dropdown-item @click="delStep(scope.row)" >删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-table-column>
              <template #empty>
                <div class="link" @click="addStep(formData.stepList.length)">添加用例步骤</div>
              </template>
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :span="8"></el-col>
      </el-row>
      <el-row class="row-bg" :gutter="20" >
        <el-col :span="16">
          <el-form-item label="附件" prop="file">
            <FileUpload class="upload-file" drag multiple>
              <div class="title">选择文件</div>
              <div class="tip">
                支持：.rar .zip .doc .docx .pdf .jpg，且单个文件不能超过5M
              </div>
            </FileUpload>
          </el-form-item>
        </el-col>
        <el-col :span="8"></el-col>
      </el-row>
      <el-row v-if="formData.type == '2'" class="row-bg" :gutter="20" >
        <el-col :span="8">
          <el-form-item label="" >
            <el-button class="c-link">跳转至性能测试模块</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer">
      <el-button @click="back">取消</el-button>
      <el-button type="primary" @click="submitForm" >确定</el-button>
    </div>
  </div>
</template>
<style scoped lang="scss">
.add-case {
  // display: flex;
  // flex-direction: column;
  height: 100%;
  padding: 16px 20px;
  overflow-y: auto;
  >.title {
    font-size: 16px;
    line-height: 19px;
    color: rgba(22,22,22,1);
    font-weight: bold;
  }
  .form {
    // flex: 1;
    padding-right: 60px;
    margin-top: 16px;
    // overflow-y: auto;
    .row {
      display: flex;
      justify-content: space-between;
    }
    .select,.ipt {
      min-width: 215px;
      width: 100%;
    }
    .textarea {
      width: 100%;
    }
    .unit {
      color: rgba(85,85,85,1);
    }
    .c-link {
      color: var(--el-color-primary);
    }
    :deep(.table) {
      width: 100%;
      th {
        background-color: rgba(76,118,192,0.04);
        .cell {
          line-height: 16px;
        }
      }
      td {
        .cell {
          display: flex;
          align-items: center;
        }
      }
    }
    :deep(.date-select) {
      min-width: 215px;
      width: 100%;
    }
    :deep(.el-row) {
      .el-col {
        max-width: 100%;
      }
    }
  }
  .footer {
    text-align: right;
    padding: 0 60px;
    padding-bottom: 0;
  }
}
@media(max-width: 1432px) {
  .el-col-16 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .el-col-8 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}
</style>
