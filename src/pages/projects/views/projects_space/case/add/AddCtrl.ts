import { computed, ref, useTemplateRef } from "vue";
import { useRouter } from "vue-router"

function getTreeIdx(list: any[], pidx: number[] = []): any {
  let result: any = {};
  for(let i = 0; i < list.length; i++) {
    result[list[i].id] = [...pidx,i];
    if(list[i].children) {
      result = Object.assign({},result,getTreeIdx(list[i].children,result[list[i].id]));
    }
  }
  return result;
}

export function AddCtrl() {
  const router = useRouter()

  const formData = ref({
    type: '1',
    system: '',
    module: '',
    xqmc: [],
    cj: '',
    csjl: '',
    csry: [],
    name: '',
    yxj: '',
    qztj: '',
    bz: '',
    yq: '',
    stepList: <any>[],
    file: [],
  });
  const formRules = ref({
    type: [{ required: true, message: "请选择用例类型", trigger: "change" }],
    csjl: [{ required: true, message: '请输入测试经理', trigger: 'blur' }],
    csry: [{ required: true, message: "请选择测试人员", trigger: "change" }],
    name: [{ required: true, message: '请输入用例名称', trigger: 'blur' }],
  });
  const ruleFormRef = useTemplateRef<any>('ruleFormRef');
  async function submitForm() {
    await ruleFormRef.value.validate();
    back();
  };

  const stepIdx = computed(()=>{
    return getTreeIdx(formData.value.stepList);
  })
  function addStep(idx: number) {
    const data = {
      id: new Date().getTime().toString(), step: '', expect: '', children: [],
    };
    formData.value.stepList.splice(idx+1,0,data);
  }
  function addStepChild(info: any) {
    const ids = stepIdx.value[info.id];
    let list = formData.value.stepList;
    for(let i = 1; i < ids.length; i++) {
      list = list[ids[i-1]].children;
    }
    list = list[ids[ids.length-1]];
    console.log(list);
    list.children.push({
      id: new Date().getTime().toString(), step: '', expect: '', children: [],
    })
    
  }
  function delStep(info: any) {
    const ids = stepIdx.value[info.id];
    let list = formData.value.stepList;
    for(let i = 1; i < ids.length; i++) {
      list = list[ids[i-1]].children;
    }
    list.splice(ids[ids.length-1],1);
  }

  function back(){
    router.back();
  }

  const levelOption = [
    { label: '一级', value: '1', },
    { label: '二级', value: '2', },
    { label: '三级', value: '3', },
  ];

  const typeOption = [
    { label: '功能用例', value: '1' },
    { label: '性能用例', value: '2' },
  ];

  const tree = ref([
    {
      label: "选项一",
      value: "1",
    },
    {
      label: "选项二",
      value: "2",
    },
    {
      label: "选项三",
      value: "3",
    },
  ]);

  return {
    formData,
    formRules,
    submitForm,
    back,
    levelOption,
    typeOption,
    tree,
    addStep,
    addStepChild,
    delStep,
  };
}
