import { ref } from "vue";
import { useRouter } from "vue-router";

export function TemplateCtrl() {
  const router = useRouter();


  const list = ref([{ name: "空白项目" }]);
  for (let i = 0; i < 5; i++) {
    list.value.push({
      name: "课题项目",
    });
  }

  const handlePreview = () => {
    router.push("/space/plan/templateDetail");
  };

  const keyword = ref('');
  function handleSearch(val: string) {
    console.log(val);
  }

  return {
    list,
    handlePreview,
    keyword,
    handleSearch,
  };
}
