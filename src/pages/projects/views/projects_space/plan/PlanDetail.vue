<script setup lang="ts">
import { PlanDetailCtrl } from "./PlanDetailCtrl.ts";
import { Plus } from "@element-plus/icons-vue"


const { 
  formData, formRules, levelOption, typeOption, tree,
  tabList, curTab, handleAdd,
} = PlanDetailCtrl();
</script>
<template>
  <div class="plan-detail">
    <div class="title">项目任务信息</div>
    <el-form
      ref="ruleFormRef"
      class="form"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row class="row-bg" :gutter="60">
        <el-col :span="8">
          <el-form-item label="项目任务ID" prop="id">
            <el-input
              class="ipt readonly"
              v-model="formData.id"
              placeholder="请输入"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="父需求" prop="parent">
            <BtPicker
              class="select"
              title="父需求"
              :list="levelOption"
              showSearch
              v-model="formData.parent"
            ></BtPicker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60">
        <el-col :span="8">
          <el-form-item label="责任人" prop="response">
            <BtPicker
              class="select"
              title="责任人"
              :list="levelOption"
              showSearch
              v-model="formData.response"
            ></BtPicker>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="参与人" prop="join">
            <BtPicker
              class="select"
              title="模块"
              :list="tree"
              showSearch
              multiple
              v-model="formData.join"
            ></BtPicker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60">
        <el-col :span="16">
          <el-form-item label="项目任务名称" prop="name">
            <el-input
              class="w-100"
              v-model="formData.name"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目任务类型" prop="type">
            <BtSelect
              class="select"
              :list="typeOption"
              v-model="formData.type"
            ></BtSelect>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60">
        <el-col :span="24">
          <el-form-item label="项目任务说明" prop="desc">
            <BtInput
              class="textarea w-100"
              v-model="formData.desc"
              :rows="3"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60">
        <el-col :span="24">
          <el-form-item label="验收标准" prop="ysbz">
            <BtInput
              class="textarea w-100"
              v-model="formData.ysbz"
              :rows="3"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60">
        <el-col :span="8">
          <el-form-item label="预计开始时间" prop="startDate">
            <el-date-picker
              class="date-select"
              v-model="formData.startDate"
              type="date"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实际开始时间" prop="beginTime">
            <el-date-picker
              class="date-select"
              v-model="formData.beginTime"
              type="date"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划完成时间" prop="endDate">
            <el-date-picker
              class="date-select"
              v-model="formData.endDate"
              type="date"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row-bg" :gutter="60">
        <el-col :span="8">
          <el-form-item label="进度" prop="startDate">
            <el-progress
              class="progress"
              color="#52C41A"
              :stroke-width="12"
              :percentage="formData.progress"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="任务评审" prop="rwps">
            <BtPicker
              class="select"
              title="任务评审"
              :list="levelOption"
              showSearch
              v-model="formData.rwps"
            ></BtPicker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <div class="switch-box">
            <el-form-item label="里程碑" prop="milestone">
              <el-switch
                v-model="formData.milestone"
                :active-value="1"
                :inactive-value="0"
              />
            </el-form-item>
            <el-form-item
              label="自动进度"
              prop="autoProgress"
              label-width="100px"
              style="margin-right: 30px"
            >
              <el-switch
                v-model="formData.autoProgress"
                :active-value="1"
                :inactive-value="0"
              />
            </el-form-item>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <el-tabs
      v-model="curTab"
      type="card"
      class="tabs"
    >
      <el-tab-pane v-for="(item,index) in tabList" :key="index"  :label="item.label" :name="item.name">
        <template #label>
          <div class="tabs-label">
            <span class="txt">{{ item.label }}</span>
            <el-icon v-if="item.add" class="icon" @click="handleAdd(index)"><Plus /></el-icon>
          </div>
        </template>
        <component :is="item.component" ref="tabComponents"></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<style scoped lang="scss">
.plan-detail {
  height: 100%;
  padding: 16px 20px;
  overflow-y: auto;
  > .title {
    font-size: 16px;
    line-height: 19px;
    color: rgba(22, 22, 22, 1);
    font-weight: bold;
  }
  .form {
    padding-right: 60px;
    margin-top: 16px;
    .row {
      display: flex;
      justify-content: space-between;
    }
    .select,
    .ipt {
      min-width: 215px;
      width: 100%;
    }
    .textarea {
      width: 100%;
    }
    .unit {
      color: rgba(85, 85, 85, 1);
    }
    :deep(.date-select) {
      min-width: 215px;
      width: 100%;
    }
    :deep(.el-row) {
      .el-col {
        max-width: 100%;
      }
    }
    .progress {
      width: 100%;
      :deep(.el-progress__text) {
        font-size: 14px !important;
      }
    }
    .switch-box {
      display: flex;
      align-items: center;
    }
  }
  .footer {
    text-align: right;
    padding: 0 60px;
    padding-bottom: 0;
  }
  .tabs {
    --el-tabs-header-height: 40px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    .tabs-label {
      display: flex;
      align-items: center;
      .icon {
        font-size: 16px;
        margin-left: 8px;
      }
    }
    :deep(.el-tabs__nav) {
      border: 0;
      .el-tabs__item {
        border: 1px solid var(--border-color);
        border-top-left-radius: var(--border-radius);
        border-top-right-radius: var(--border-radius);
        padding: 0 16px;
        margin: 0 -1px 0 0;
        &.is-active {
          border-bottom-color: #fff;
          .tabs-label .txt {
            color: #000;
            font-weight: bold;
          }
        }
      }
    }
  }
}

@media (max-width: 1432px) {
  .el-col-8 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}
</style>
