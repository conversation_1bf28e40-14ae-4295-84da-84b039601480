export interface ListItem<T = any> {
  [key: string]: T;
}

export type Attachment = {
  attaName: string;
  attaKey: string;
  attaId?: number;
  attaPath: string;
  attaSize: number;
  attaType: string;
}
export type AddForm = {
    projectId: number;
    planId?: number;
    parentId: number;
    planName: string;
    responsible: number;
    participants: { userId: number }[];
    startDate: string;
    endDate: string;
    milestone: number;
    autoProgress: number;
    planDesc: string;
    dataType: number;
    businessTyp: number;
    taskType: number;
    attachments: Attachment[]
}

export type UpdateForm = {
  planId: number;
  status: number;
}

export type Participant = {
    userId: number;
    userName: string;
}
export type PlanItem = {
    parentId: number;
    planId: number;
    planName: string;
    responsible: number;
    responsibleName: string;
    startDate: string;
    endDate: string;
    status: number;
    participants: Participant[];
    milestone: number;
    autoProgress: number;
    dataType: number;
    taskType: number;
    children?: PlanItem[];
    progress: number;
}

export type PlanSearchForm = {
  planName?: string;
  startDate?: string;
  endDate?: string;
  startDateOrder?: string;
  endDateOrder?: string;
  status?: number;
}

export interface PlanListResponseData {
  list: PlanItem[];
  total: number;
}

export type DeleteForm = {
  planId: number;
}
