<script setup lang="ts">
import { TemplateCtrl } from "./TemplateCtrl";

const { 
  list, handlePreview, keyword, handleSearch 
} = TemplateCtrl();


</script>
<template>
  <div class="plan-template">
    <div class="header">
      <div class="title">软件项目计划模板</div>
      <div class="right">
        <BtSearch
          class="item"
          v-model="keyword"
          placeholder="请输入搜索内容"
          @search="handleSearch"
        ></BtSearch>
      </div>
    </div>
    <div class="container">
      <div class="list">
        <div v-for="(item,index) in list" :key="index" class="li">
          <el-image class="img" src="/brand/logo-img.png" />
          <div class="info">
            <div class="title">{{ item.name }}</div>
            <div class="txt">这是详细文本这是详细文本这是详细文本这是详细文本这是详细文本</div>
          </div>
          <div class="opt">
            <el-button class="btn" @click="handlePreview" >预览</el-button>
            <el-button class="btn" type="primary" >使用模板</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.plan-template {
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 22px 0 22px;
    .title {
      font-size: 16px;
      color: #161616;
      font-weight: bold;
      line-height: 19px;
    }
  }
  .container {
    padding: 10px 20px;
    .select {
      width: 200px;
    }
    .list {
      .li {
        display: flex;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid var(--el-border-color);
        .img {
          width: 38px;
          height: 38px;
          margin-right: 16px;
          flex-shrink: 0;
        }
        .info {
          flex: 1;
          line-height: 17px;
          .title {
            color: #161616;
          }
          .txt {
            color: #666666;
          }
        }
        .opt {
          margin-left: 16px;
          flex-shrink: 0;
          .btn {
            padding-left: 10px;
            padding-right: 10px;
          }
        }
      }
    }
  }
}

</style>
