import { batchDeletePlan } from "@/service_url/projects.ts";
import { BtitSequenceCommand } from "@/libs/btit/BtitSequenceCommand.ts";
import { VCairnEvent } from 'v-cairn';
import {EventTypes} from "@pages/projects/common/EventTypes.ts";
import type { DeleteForm } from '@pages/projects/views/projects_space/plan/types'
import { ElMessage } from "element-plus";
import { usePlanStore } from "../PlanStore"

export class BatchDeletePlanCommand extends BtitSequenceCommand {

  execute(evt:VCairnEvent) {
    const planStore = usePlanStore()
    planStore.isLoading = true
    const data= evt.data as DeleteForm[];
    this.service.send({
      method: "post",
      url: batchDeletePlan,
      data: data,
    });
  }

  onSuccess(): void {
    ElMessage({
      type: 'success',
      message: `删除成功`,
    })
    
    this.nextEvents = [
        new VCairnEvent(EventTypes.GET_PLAN_LIST)
    ];
    this.executeNextCommand();
  }

  onFailDefaultHandle(error: any): void {
    const planStore = usePlanStore()
    planStore.isLoading = false
    ElMessage({
      type: "error",
      message: error.message || '删除失败',
    });
  }
}
