import {planList2} from "@/service_url/projects.ts";
import {BtitCommand} from "@/libs/btit/BtitCommand.ts";
import type { AxiosResponse } from "v-cairn";
import {usePlanStore} from "../PlanStore.ts";
import type {PlanItem} from "../types.ts"

export class GetPlanListCommand extends BtitCommand {

    execute() {
        const planStore = usePlanStore();
        planStore.isLoading = true
        this.service.send({
            method: 'post',
            url: planList2, // planList(planStore.pageSize, planStore.pageNum),
            data: planStore.searchForm
        });
    }

    onSuccess(response: AxiosResponse): void {
        const planStore = usePlanStore();
        // const data = response.data.data as PlanItem[];
        planStore.planList = response.data.data as PlanItem[];
    }

    onFinally(): void {
        const planStore = usePlanStore();
        planStore.isLoading = false
    }

}
