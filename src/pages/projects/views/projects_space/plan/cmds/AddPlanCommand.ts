import { addPlan } from "@/service_url/projects.ts";
import { BtitSequenceCommand } from "@/libs/btit/BtitSequenceCommand.ts";
import { usePlanStore } from "../PlanStore.ts";
import { VCairnEvent } from 'v-cairn';
import type { AddForm } from "@pages/projects/views/projects_space/plan/types.ts";
import { EventTypes } from "@pages/projects/common/EventTypes.ts";
import { ElMessage } from "element-plus";

export class AddPlanCommand extends BtitSequenceCommand {

  execute(evt:VCairnEvent) {
    const planStore = usePlanStore();
    planStore.addLoading = true;
    const data = evt.data as AddForm;
    this.service.send({
      method: "post",
      url: addPlan,
      data: data,
    });
  }

  onSuccess(): void {
    ElMessage({
      type: "success",
      message: '添加成功',
    });
    const planStore = usePlanStore();
    planStore.showAdd = false;

    this.nextEvents = [
        new VCairnEvent(EventTypes.GET_PLAN_LIST)
    ];
    this.executeNextCommand();
  }

  onFinally(): void {
    const planStore = usePlanStore();
    planStore.addLoading = false;
  }
}
