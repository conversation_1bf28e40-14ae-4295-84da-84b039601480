import { updatePlan } from "@/service_url/projects.ts";
import { BtitSequenceCommand } from "@/libs/btit/BtitSequenceCommand.ts";
import { usePlanStore } from "../PlanStore.ts";
import { VCairnEvent } from 'v-cairn';
import type { UpdateForm } from "@pages/projects/views/projects_space/plan/types.ts";
import { EventTypes } from "@pages/projects/common/EventTypes.ts";
import { ElMessage } from "element-plus";

export class UpdatePlanCommand extends BtitSequenceCommand {
  execute(evt:VCairnEvent) {
    const planStore = usePlanStore();
    planStore.isLoading = true;
    const data = evt.data as UpdateForm;
    this.service.send({
      method: "post",
      url: updatePlan,
      data: data,
    });
  }

  onSuccess(): void {
    const planStore = usePlanStore();
    if(--planStore.remainTaskNum <= 0) {
      ElMessage({
        type: "success",
        message: '提交成功',
      });
      planStore.isLoading = false;
      planStore.remainTaskNum = 0
      this.nextEvents = [
          new VCairnEvent(EventTypes.GET_PLAN_LIST)
      ];
      this.executeNextCommand();
    }
  }

  onFailDefaultHandle(): void {
    const planStore = usePlanStore();
    planStore.isLoading = false;
  }

}
