import { deletePlan } from "@/service_url/projects.ts";
import { BtitCommand } from "@/libs/btit/BtitCommand.ts";
import { VCairnEvent } from "v-cairn";
import type { DeleteForm } from '@pages/projects/views/projects_space/plan/types'
import { ElMessage } from "element-plus";
export class DeletePlanCommand extends BtitCommand {

  execute(evt:VCairnEvent) {
    const data = evt.data as DeleteForm;
    this.service.send({
      method: "post",
      url: deletePlan,
      data: data,
    });
  }

  onSuccess(): void {
    ElMessage({
      type: 'success',
      message: `删除成功`,
    })
  }

  onFailDefaultHandle(error: any): void {
    ElMessage({
      type: "error",
      message: error.message || '删除失败',
    });
  }
}
