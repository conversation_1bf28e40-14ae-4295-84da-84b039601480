import { ref, computed } from "vue";
import { defineStore } from "pinia";
import type { PlanItem, PlanSearchForm } from "./types"
import { handleTree } from '@/utils/toolUtil'

export const usePlanStore = defineStore('planStore', () => {

    const pageSize = ref(1000)
    const pageNum = ref(1)
    const isLoading = ref(false);
    const searchForm = ref<PlanSearchForm>({})
    const planList = ref<PlanItem[]>([])
    const planTree = computed(()=>{
        return handleTree(planList.value || [],'planId','parentId')
    })

    const remainTaskNum = ref(0)

    const addLoading = ref(false)
    const showAdd = ref(false)

    return { isLoading, pageSize, pageNum, searchForm, planList, planTree, addLoading, showAdd, remainTaskNum }
});