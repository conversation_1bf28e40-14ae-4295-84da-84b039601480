import { ref } from "vue";

export function ApprovalListCtrl() {
  const columnList = ref([
    { 
      field: 'node', label: '审批节点', width: '', minWidth: '200', show: true,
    },
    { 
      field: 'user', label: '审批人', width: '120', minWidth: '', show: true,
    },
    { 
      field: 'startDate', label: '开始日期', width: '210', minWidth: '', show: true,
    },
    { 
      field: 'endDate', label: '完成日期', width: '150', minWidth: '', show: true,
    },
    { 
      field: 'opinion', label: '审批意见', width: '120', minWidth: '', show: true,
    },
    { 
      field: 'score', label: '评分', width: '80', minWidth: '', show: true,
    },
  ])
  
  const tableData = ref([])

  const page = ref(1)
  function changePage(val: number) {
    console.log(val)
  }

  return {
    columnList,
    tableData,
    page,
    changePage,
  };
}
