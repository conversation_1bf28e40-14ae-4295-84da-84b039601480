import { ref } from "vue";

export function SubtaskListCtrl() {
  const columnList = ref([
    { 
      field: 'name', label: '任务名称', width: '', minWidth: '200', show: true,
    },
    { 
      field: 'user', label: '审批人', width: '120', minWidth: '', show: true,
    },
    { 
      field: 'status', label: '状态', width: '120', minWidth: '', show: true,
    },
    { 
      field: 'startDate', label: '开始日期', width: '170', minWidth: '', show: true,
    },
    { 
      field: 'endDate', label: '截止日期', width: '170', minWidth: '', show: true,
    },
    { 
      field: 'finishTime', label: '实际完成', width: '170', minWidth: '', show: true,
    },
    { 
      field: 'progress', label: '实际进度', width: '170', minWidth: '', show: true,
    },
  ])
  
  const tableData = ref([])

  const page = ref(1)
  function changePage(val: number) {
    console.log(val)
  }

  const showAdd = ref(false)
  function openAdd(){
    showAdd.value = true
  }

  return {
    columnList,
    tableData,
    page,
    changePage,
    showAdd,
    openAdd,
  };
}
