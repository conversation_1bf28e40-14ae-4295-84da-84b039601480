<script setup lang="ts">
import { FileListCtrl } from "./FileListCtrl.ts";

const { 
  fileList,
} = FileListCtrl();

</script>
<template>
  <div class="file-list">
    <FileUpload class="upload-file" drag multiple v-model="fileList">
      <div class="title">选择文件</div>
      <div class="tip">
        支持：.rar .zip .doc .docx .pdf .jpg，且单个文件不能超过5M
      </div>
    </FileUpload>
  </div>
</template>
<style scoped lang="scss">
.file-list {
 width: 600px;
}
</style>
