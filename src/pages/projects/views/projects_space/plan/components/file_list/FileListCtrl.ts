import { ref } from "vue";
import type { FileItem } from "@/components/biz/file_upload/types"

export function FileListCtrl() {
  const fileList = ref<FileItem[]>([
    { 
      etag: "123",
      fileKey: "132",
      fileName: "性能测试CodeArt 最佳实践.pdf",
      filePath: "性能测试CodeArt 最佳实践.pdf",
      fileSize: 500,
      fileType: "pdf",
     },
     { 
      etag: "123",
      fileKey: "132",
      fileName: "性能测试CodeArt 最佳实践.pdf",
      filePath: "性能测试CodeArt 最佳实践.pdf",
      fileSize: 500,
      fileType: "pdf",
     }
  ]);

  return {
    fileList,
  };
}
