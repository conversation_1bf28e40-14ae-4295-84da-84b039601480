import { onMounted, ref, computed, useTemplateRef } from "vue";
import type {FileItem} from "@/components/biz/file_upload/types";
import type { AddForm } from '../../types';
import type { PropsType, EventsType } from './types';
import {EventTypes} from "@pages/projects/common/EventTypes.ts";
import { useUserListStore } from '@pages/projects/common/stores/UserListStore';
import { useProjectStore } from '@pages/projects/common/stores/ProjectStore';
import { useDictStore } from '@pages/projects/common/stores/DictStore';
import { ElForm } from "element-plus";
import { VCairnEvent } from "v-cairn";


export function AddPlanCtrl(props: PropsType, emits: EventsType) {
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (val: boolean) => {
      emits("update:modelValue",val)
      emits("change",val)
    }
  })

  const dictStore = useDictStore()
  const typeOption = computed(()=>{
    return dictStore.dictObj['sys_task_type'] || []
  })

  const projectStore = useProjectStore()

  const userListStore = useUserListStore()
  const userList = computed(()=>{
    return userListStore.userList.map(v=>{
      v.label = v.userName
      v.value = v.userId
      return v
    })
  })

  function open() {
    dialogVisible.value = true;
  }
  function close() {
    dialogVisible.value = false;
  }
  function handleClosed() {
    ruleFormRef.value?.resetFields()
  }

  const formData = ref({
    planName: "",
    planDesc: "",
    responsible: '',
    taskType: '',
    participants: <number[]>[],
    startDate: "",
    endDate: "",
    progress: "",
    autoProgress: 0,
    milestone: 0,
    attachments: <FileItem[]>[],
  });
  const formRules = ref({
    planName: [{required: true, message: '请输入项目名称', trigger: 'blur' }],
    // type: [{ required: true, message: "请选择项目类型", trigger: "change" }],
    responsible: [{ required: true, message: "请选择项目负责人", trigger: "change" }],
  });
  const ruleFormRef = useTemplateRef<InstanceType<typeof ElForm>>('ruleFormRef');
  function getSubmitFormData(): AddForm  {
    return {
      ...formData.value,
      parentId: props.parentId || 0,
      dataType: 1, // 1:项目数据，2:项目模板数据
      businessTyp: 2, // 1:计划，2:任务
      projectId: projectStore.curProject.projectId,
      taskType: parseInt(formData.value.taskType),
      responsible: parseInt(formData.value.responsible),
      participants: formData.value.participants.map(v=> {
        return { userId: v }
      }),
      attachments: formData.value.attachments.map(v => {
        return {
          pmType: 1,
          attaKey: v.fileKey,
          attaName: v.fileKey,
          attaPath: v.filePath,
          attaSize: v.fileSize,
          attaType: v.fileType
        }
      })
    }
  }
  async function submitForm() {
    await ruleFormRef.value?.validate();
    const data = getSubmitFormData();
    new VCairnEvent(EventTypes.ADD_PLAN,data).emit()
  };

  const levelOption = [
    { label: '一级', value: '1', },
    { label: '二级', value: '2', },
    { label: '三级', value: '3', },
  ];


  const tree = ref([
    {
      label: "一级选项一",
      value: "1",
      children: [
        {
          label: "二级选项一",
          value: "1.1",
        },
        {
          label: "二级选项二",
          value: "1.2",
        },
        {
          label: "二级选项三",
          value: "1.3",
          children: [
            { label: "三级选项一", value: "1.3.1" },
            { label: "三级选项二", value: "1.3.2" },
          ],
        },
      ],
    },
    {
      label: "一级选项二",
      value: "2",
      children: [
        { label: "二级选项阿是分身乏术方式防守打法说法", value: "2.1" },
        { label: "二级选项二", value: "2.2" },
      ],
    },
    {
      label: "一级选项三",
      value: "3",
    },
    {
      label: "一级选项三",
      value: "4",
    },
    {
      label: "一级选项三",
      value: "5",
    },
    {
      label: "一级选项三",
      value: "6",
    },
  ]);

  function getUserList() {
    new VCairnEvent(EventTypes.GET_USER_LIST).emit()
  }
  
  onMounted(() => {
    new VCairnEvent(EventTypes.GET_DICT_DATA,'sys_task_type').emit()
    getUserList()
  })

  return {
    dialogVisible,
    open,
    close,
    formData,
    formRules,
    submitForm,
    levelOption,
    typeOption,
    tree,
    userList,
    handleClosed,
  };
}
