<script setup lang="ts">
import { storeToRefs } from "pinia";
import { AddPlanCtrl } from "./AddPlanCtrl.ts";
import { Search as ElSearch } from "@element-plus/icons-vue";
import type { PropsType, EventsType } from "./types.ts"
import { usePlanStore } from "@/pages/projects/views/projects_space/plan/PlanStore.ts";

const { addLoading } = storeToRefs(usePlanStore());

const props = withDefaults(
    defineProps<PropsType>(),
    {
      modelValue: false,
      title: '新建项目任务',
      parentId: 0,
    }
);

const emits = defineEmits<EventsType>();

const {
  dialogVisible,
  open,
  close,
  formData,
  formRules,
  submitForm,
  typeOption,
  userList,
  handleClosed,
} = AddPlanCtrl(props,emits);

defineExpose({ open, close });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="796"
    :before-close="close"
    @closed="handleClosed"
  >
    <el-form
      ref="ruleFormRef"
      class="form"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="项目任务名称" prop="planName">
        <el-input
          v-model="formData.planName"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="项目任务说明" prop="planDesc">
        <BtInput
          class="textarea"
          v-model="formData.planDesc"
          :rows="3"
          type="textarea"
          maxlength="300"
          show-word-limit
          placeholder="请输入"
        />
      </el-form-item>
      <div class="row">
        <el-form-item label="项目负责人" prop="responsible">
          <BtPicker
            class="select"
            title="项目负责人"
            :list="userList"
            showSearch
            v-model="formData.responsible"
          ></BtPicker>
        </el-form-item>
        <el-form-item label="项目任务类型" prop="taskType">
          <BtSelect
            class="select"
            :list="typeOption"
            v-model="formData.taskType"
          ></BtSelect>
        </el-form-item>
      </div>
      <el-form-item label="参与人" prop="participants">
        <BtPicker
          class="w-100"
          title="参与人"
          :list="userList"
          showSearch
          multiple
          v-model="formData.participants"
          :maxCollapseTags="4"
        ></BtPicker>
      </el-form-item>
      <div class="row">
        <el-form-item label="计划开始时间" prop="startDate">
          <el-date-picker
            class="date-select"
            v-model="formData.startDate"
            type="date"
            placeholder="请选择"
          />
        </el-form-item>
        <el-form-item label="计划完成时间" prop="endDate">
          <el-date-picker
            class="date-select"
            v-model="formData.endDate"
            type="date"
            placeholder="请选择"
          />
        </el-form-item>
      </div>
      <div class="row">
        <el-form-item label="审批流程" prop="progress">
          <el-input
            v-model="formData.progress"
            placeholder="项目任务审批流程（可选）"
            :suffix-icon="ElSearch"
          />
        </el-form-item>
        <div class="row ">
          <el-form-item label="里程碑" prop="milestone">
            <el-switch v-model="formData.milestone" :active-value="1" :inactive-value="0" />
          </el-form-item>
          <el-form-item label="自动进度" prop="autoProgress" label-width="100px" style="margin-right: 30px;">
            <el-switch v-model="formData.autoProgress" :active-value="1" :inactive-value="0" />
          </el-form-item>
        </div>
      </div>
      <el-form-item label="附件" prop="attachments">
        <FileUpload v-model="formData.attachments" class="upload-file" drag multiple	 >
          <div class="title">选择文件</div>
          <div class="tip">支持：.rar .zip .doc .docx .pdf .jpg，且单个文件不能超过5M</div>
        </FileUpload>
      </el-form-item>
      
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button :loading="addLoading" type="primary" @click="submitForm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped lang="scss">
.form {
  .row {
    display: flex;
    justify-content: space-between;
  }
  .select {
    width: 215px;
  }
  :deep(.date-select) {
    width: 215px;
  }
  .textarea {
    width: 100%;
  }
}
</style>
