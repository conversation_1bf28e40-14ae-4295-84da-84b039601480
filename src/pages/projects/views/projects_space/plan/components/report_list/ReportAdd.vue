<script setup lang="ts">
import { ReportAddCtrl } from "./ReportAddCtrl.ts";
import type { PropsType, EventsType } from "./ReportAddTypes.ts"

const props = withDefaults(
    defineProps<PropsType>(),
    {
      modelValue: false,
    }
);

const emits = defineEmits<EventsType>();

const {
  dialogVisible,
  open,
  close,
  formData,
  formRules,
  submitForm,
  submitLoading,
} = ReportAddCtrl(props, emits);

defineExpose({ open, close });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    title="新增汇报"
    width="796"
    :before-close="close"
  >
    <el-form
      ref="ruleFormRef"
      class="form"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="汇报内容" prop="content">
        <BtInput
          class="textarea"
          v-model="formData.content"
          :rows="4"
          type="textarea"
          maxlength="600"
          show-word-limit
          placeholder="请输入"
        />
      </el-form-item>
      <div class="row">
        <el-form-item label="汇报进度" prop="progress">
          <el-input
            v-model="formData.progress"
            placeholder="请输入"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="开始时间" prop="startDate">
          <el-date-picker
            class="date-select"
            v-model="formData.startDate"
            type="date"
            placeholder="请选择"
          />
        </el-form-item>
      </div>
      <el-form-item label="附件" prop="attachments">
        <FileUpload v-model="formData.attachments" class="upload-file" drag multiple	 >
          <div class="title">选择文件</div>
          <div class="tip">支持：.rar .zip .doc .docx .pdf .jpg，且单个文件不能超过5M</div>
        </FileUpload>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button :loading="submitLoading" type="primary" @click="submitForm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped lang="scss">
.form {
  .row {
    display: flex;
    justify-content: space-between;
  }
  .select {
    width: 215px;
  }
  :deep(.date-select) {
    width: 100%;
  }
  .textarea {
    width: 100%;
  }
}
</style>
