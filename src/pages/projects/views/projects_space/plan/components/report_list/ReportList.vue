<script setup lang="ts">
import { MoreFilled } from "@element-plus/icons-vue";
import { ReportListCtrl } from "./ReportListCtrl.ts";
import ReportAdd from "./ReportAdd.vue";

const { 
  dataList, showAdd, openAdd,
} = ReportListCtrl();

defineExpose({ openAdd })
</script>
<template>
  <div class="report-list">
    <div class="list">
      <div v-for="item in dataList" :key="item.userId" class="li">
        <div class="user">
          <el-image
            class="img"
            src="/brand/logo-img.png"
            fit="cover"
          ></el-image>
          <div class="info">
            <div class="name">wuyanhua</div>
            <div class="date">14分钟前</div>
          </div>
        </div>
        <div class="txt">未通过</div>
        <div class="file">
          <span class="txt">文件：</span>
          <SvgIcon size="14" class="icon" name="文件" />
          <span class="">信能测试CodeArts PerfTest常见问题.PDF</span>
        </div>
        <div class="row">
          <div class="progress">
            <span>任务进度：</span>
            <el-progress
              class="line"
              color="#52C41A"
              :stroke-width="12"
              :percentage="68"
            />
          </div>
          <el-dropdown placement="bottom-end">
            <div class="more-opt">
              <el-icon class="icon"><MoreFilled /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu class="border" style="width: 140px">
                <el-dropdown-item>删除</el-dropdown-item>
                <el-dropdown-item>评论</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div class="comment">
          <div class="item">
            <div class="txt">
              热物业费一番风顺虎虎生威热物业费一番风顺虎虎生威热物业费一番风顺虎虎生威热物业费一番风顺虎虎生威热物业费一番风顺虎虎生威
            </div>
            <div class="date">wwwww评论于2025-08-08 12:30:05</div>
          </div>
        </div>
      </div>
    </div>
    <ReportAdd v-model="showAdd" />
  </div>
</template>
<style scoped lang="scss">
.report-list {
  .list {
    .li {
      padding-bottom: 24px;
      margin-bottom: 24px;
      border-bottom: 1px solid var(--border-color);
      .user {
        display: flex;
        color: #000;
        line-height: 1.4;
        margin-bottom: 8px;
        .img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 8px;
        }
        .name {
          font-weight: bold;
        }
      }
      .file {
        display: flex;
        align-items: center;
        color: #999999;
        margin: 8px 0;
        padding: 8px 16px;
        background: #F3F3F3;
        .txt {
          flex-shrink: 0;
        }
        .icon {
          color: #000;
          margin: 0 8px;
        }
      }
      .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .progress {
        display: flex;
        align-items: center;
        .line {
          width: 200px;
          :deep(.el-progress__text) {
            font-size: 14px !important;
          }
        }
      }
      .more-opt {
        position: relative;
        width: 32px;
        height: 32px;
        margin-left: -100%;
        outline: unset;
        .icon {
          position: absolute;
          top: 50%;
          left: 100%;
          display: flex;
          align-items: center;
          width: 100%;
          height: 100%;
          font-size: 16px;
          font-weight: bold;
          transform: translateY(-50%) rotate(90deg);
          cursor: pointer;
          border-radius: var(--border-radius);
        }
        &:hover {
          .icon {
            background: var(--hover-bg-color);
          }
        }
        &:active {
          .icon {
            box-shadow: var(--active-shadow);
          }
        }
      }
      .comment {
        .item {
          line-height: 1.4;
          padding: 8px 16px;
          background: #F3F3F3;
          margin-top: 8px;
          .date {
            color: #999999;
            margin-top: 4px;
          }
        }
      }
    }
  }
}
</style>
