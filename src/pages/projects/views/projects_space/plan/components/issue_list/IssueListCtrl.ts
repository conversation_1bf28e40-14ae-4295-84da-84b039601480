import { ref } from "vue";

export function IssueListCtrl() {
  const columnList = ref([
    { 
      field: 'name', label: '标题', width: '', minWidth: '200', show: true,
    },
    { 
      field: 'level', label: '级别', width: '120', minWidth: '', show: true,
    },
    { 
      field: 'endDate', label: '截止日期', width: '170', minWidth: '', show: true,
    },
    { 
      field: 'finishTime', label: '完成日期', width: '170', minWidth: '', show: true,
    },
    { 
      field: 'status', label: '状态', width: '120', minWidth: '', show: true,
    },
    { 
      field: 'creater', label: '提出者', width: '120', minWidth: '', show: true,
    },
    { 
      field: 'handler', label: '负责人', width: '120', minWidth: '', show: true,
    },
  ])
  
  const tableData = ref([
    { name: '这是问题这是问题', level: '严重', endDate: '2025-08-01', finishTime: '未完成', status: '', creater: '张三', handler: '王五' },
  ])

  const page = ref(1)
  function changePage(val: number) {
    console.log(val)
  }

  const showAdd = ref(false)
  function openAdd() {
    showAdd.value = true;
  }

  return {
    columnList,
    tableData,
    page,
    changePage,
    showAdd,
    openAdd,
  };
}
