<script setup lang="ts">
import { IssueAddCtrl } from "./IssueAddCtrl.ts";
import type { PropsType, EventsType } from "./IssueAddTypes.ts"

const props = withDefaults(
    defineProps<PropsType>(),
    {
      modelValue: false,
    }
);

const emits = defineEmits<EventsType>();

const {
  dialogVisible,
  open,
  close,
  levelOption,
  userList,
  formData,
  formRules,
  submitForm,
  submitLoading,
} = IssueAddCtrl(props, emits);

defineExpose({ open, close });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    title="新增问题"
    width="796"
    :before-close="close"
  >
    <el-form
      ref="ruleFormRef"
      class="form"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="问题标题" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>
      <div class="row">
        <el-form-item label="问题级别" prop="level">
          <BtSelect
            class="select"
            :list="levelOption"
            v-model="formData.level"
          ></BtSelect>
        </el-form-item>
        <el-form-item label="问题状态" prop="status">
          <BtSelect
            class="select"
            :list="levelOption"
            v-model="formData.status"
          ></BtSelect>
        </el-form-item>
      </div>
      <div class="row">
        <el-form-item label="截止日期" prop="endDate">
          <el-date-picker
            class="date-select"
            v-model="formData.endDate"
            type="date"
            placeholder="请选择"
          />
        </el-form-item>
        <el-form-item label="完成日期" prop="finishDate">
          <el-date-picker
            class="date-select"
            v-model="formData.endDate"
            type="date"
            placeholder="请选择"
          />
        </el-form-item>
      </div>
      <div class="row ">
        <el-form-item label="提出者" prop="creater">
          <BtPicker
            class="select"
            title="提出者"
            :list="userList"
            v-model="formData.creater"
          ></BtPicker>
        </el-form-item>
        <el-form-item label="负责人" prop="handler">
          <BtPicker
            class="select"
            title="负责人"
            :list="userList"
            v-model="formData.handler"
          ></BtPicker>
        </el-form-item>
      </div>
      <el-form-item label="问题说明" prop="desc">
        <BtInput
          class="textarea"
          v-model="formData.desc"
          :rows="3"
          type="textarea"
          maxlength="300"
          show-word-limit
          placeholder="请输入"
        />
      </el-form-item>
      
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button :loading="submitLoading" type="primary" @click="submitForm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped lang="scss">
.form {
  .row {
    display: flex;
    justify-content: space-between;
  }
  .select {
    width: 215px;
  }
  :deep(.date-select) {
    width: 100%;
  }
  .textarea {
    width: 100%;
  }
}
</style>
