import { ref, computed, useTemplateRef } from "vue";
import type { PropsType, EventsType } from "./RiskAddTypes"

export function RiskAddCtrl(props: PropsType, emits: EventsType) {
  // const dialogVisible = ref(props.modelValue);

  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (val: boolean) => {
      emits("update:modelValue",val)
      emits("change",val)
    }
  })


  function open() {
    dialogVisible.value = true;
  }
  function close() {
    dialogVisible.value = false;
  }

  const submitLoading = ref(false)
  const formData = ref({
    name: '',
    level: '',
    status: '',
    endDate: '',
    finishDate: '',
    creater: '',
    handler: '',
    desc: '',
  });
  const formRules = ref({
    name: [{required: true, message: '请输入风险标题', trigger: 'blur' }],
    level: [{ required: true, message: "请选择风险级别", trigger: "change" }],
    // member: [{ required: true, message: "请选择项目负责人", trigger: "change" }],
  });
  const ruleFormRef = useTemplateRef<any>('ruleFormRef');
  async function submitForm() {
    await ruleFormRef.value.validate();
  };

  const levelOption = [
    { label: '一级', value: '1', },
    { label: '二级', value: '2', },
    { label: '三级', value: '3', },
  ];


  const userList = [
    { label: '用户1', value: '1', },
    { label: '用户2', value: '1', },
    { label: '用户3', value: '1', },
    { label: '用户4', value: '1', },
    { label: '用户5', value: '1', },
  ]


  return {
    dialogVisible,
    open,
    close,
    formData,
    formRules,
    submitForm,
    levelOption,
    userList,
    submitLoading,
  };
}
