<script setup lang="ts">
import { RiskListCtrl } from "./RiskListCtrl.ts";
import RiskAdd from "./RiskAdd.vue"
import {withBaseUrl} from "@/utils/routeUtil.ts";

const { 
  columnList, tableData,
  page, changePage, showAdd, openAdd,
} = RiskListCtrl();

defineExpose({ openAdd })
</script>
<template>
  <div class="issue-list">
    <div class="cont">
      <el-table
        class="table ipt-table"
        :data="tableData"
        style="width: 100%; height: 100%"
      >
        <el-table-column type="selection" width="33" />
        <el-table-column label="#" width="40" align="center">
          <template #default="scope">{{ scope.$index + 1 }}</template>
        </el-table-column>
        <template v-for="column in columnList" :key="column.field" >
          <el-table-column :property="column.field" :label="column.label" :width="column.width" :min-width="column.minWidth" >
          </el-table-column>
        </template>
        <template #empty>
          <div class="no-data">
            <el-image class="img" :src="withBaseUrl('no-data.png')" fit="contain"></el-image>
            <div class="txt">未找到审批记录</div>
          </div>
        </template>
      </el-table>
    </div>
    <div class="footer">
      <BtPage v-model="page" :total="1" @change="changePage"></BtPage>
    </div>
    <RiskAdd v-model="showAdd" />
  </div>
</template>
<style scoped lang="scss">
.issue-list {
  .footer {
    text-align: right;
    padding: 20px;
  }
}
</style>
