<script setup lang="ts">
import { storeToRefs } from "pinia";
import { tableConfig } from "@/app-config.ts";
import { PlanCtrl } from "./PlanCtrl.ts";
import { MoreFilled, Setting, FullScreen } from "@element-plus/icons-vue";
import AddPlan from "./components/add_plan/AddPlan.vue";
import { useLayoutStore } from "@/components/non_biz/layout/LayoutStore.ts";
import { usePlanStore } from "@/pages/projects/views/projects_space/plan/PlanStore.ts";
import {withBaseUrl} from "@/utils/routeUtil.ts";

const { planTree, isLoading, showAdd } = storeToRefs(usePlanStore());
const {
  curType, typeList, changeType,
  handleAdd,
  columnList, showColumn,
  dateType, dateTypeList,
  ganttFilter, ganttConfig, ganttData, openLevel,ganttOperate,
  keyword, toSearch,
  changeFilter,
  handleDelete, checkTemp, checkDetail,
  parentId, statusNameMap, handleClose, handleUp, handleDown,
  planTreeIds,
} = PlanCtrl();

const layoutStore = useLayoutStore();
layoutStore.setAddAction(handleAdd);
</script>
<template>
  <div class="space-plan">
    <div class="container">
      <div class="header">
        <BtTab :list="typeList" :value="curType" @change="changeType">
          <template #right>
            <div v-show="curType == 0" class="right-opt">
              <div class="flex flex-center">
                <el-button class="btn item" @click="handleAdd(0)">
                  新建项目任务
                </el-button>
                <el-dropdown placement="bottom-end" class="item">
                  <el-button class="btn">Excel</el-button>
                  <template #dropdown>
                    <el-dropdown-menu style="width: 215px">
                      <el-dropdown-item>导入列表（Excel）</el-dropdown-item>
                      <el-dropdown-item>导出列表（Excel）</el-dropdown-item>
                      <el-dropdown-item>下载模板（Excel）</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <BtSearch
                  class="item"
                  v-model="keyword"
                  placeholder="请输入搜索内容"
                  @search="toSearch"
                  @clear="toSearch"
                ></BtSearch>
                <BtTooltip
                  class="icon-tooltip"
                  effect="dark"
                  content="项目计划模版"
                  placement="bottom"
                >
                  <div class="item" @click="checkTemp">
                    <SvgIcon class="icon " name="collapse" />
                  </div>
                </BtTooltip>
                <BtTooltip
                  class="icon-tooltip"
                  effect="dark"
                  content="刷新项目任务进度"
                  placement="bottom"
                >
                  <div class="item">
                    <SvgIcon class="icon " name="刷新" />
                  </div>
                </BtTooltip>
                <BtColumn ref="columnRef" v-model="columnList"></BtColumn>
                <!-- <el-icon class="item icon" @click="openColumn"><Setting /></el-icon> -->
              </div>
            </div>
            <div v-show="curType == 1" class="right-opt">
              <div class="flex flex-center">
                <el-dropdown placement="bottom-end" class="item">
                  <el-button class="btn">展开</el-button>
                  <template #dropdown>
                    <el-dropdown-menu style="width: 215px">
                      <el-dropdown-item @click="openLevel(1)">
                        展开第一层
                      </el-dropdown-item>
                      <el-dropdown-item @click="openLevel(2)">
                        展开第二层
                      </el-dropdown-item>
                      <el-dropdown-item @click="openLevel(3)">
                        展开第三层
                      </el-dropdown-item>
                      <el-dropdown-item @click="openLevel(4)">
                        展开第四层
                      </el-dropdown-item>
                      <el-dropdown-item @click="openLevel(0)">
                        全部展开
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-dropdown placement="bottom-end" class="item">
                  <el-button class="btn">Excel</el-button>
                  <template #dropdown>
                    <el-dropdown-menu style="width: 215px">
                      <el-dropdown-item>
                        导入列表（Excel）
                      </el-dropdown-item>
                      <el-dropdown-item>
                        导出列表（Excel）
                      </el-dropdown-item>
                      <el-dropdown-item>
                        下载模板（Excel）
                      </el-dropdown-item>
                      <el-dropdown-item @click="ganttOperate('export_png')">
                        导出甘特图（PNG）
                      </el-dropdown-item>
                      <el-dropdown-item @click="ganttOperate('export_pdf')">
                        导出甘特图（PDF）
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-radio-group class="item" v-model="dateType">
                  <el-radio-button
                    v-for="(item, index) in dateTypeList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-radio-group>
                <BtTooltip
                  class="icon-tooltip"
                  effect="dark"
                  content="全屏"
                  placement="bottom"
                >
                  <el-icon class="item icon" @click="ganttOperate('expand')"
                    ><FullScreen
                  /></el-icon>
                </BtTooltip>
                <BtSearch
                  class="item"
                  v-model="ganttFilter"
                  placeholder="请输入搜索内容"
                  @search="toSearch"
                  @clear="toSearch"
                ></BtSearch>
                <!-- <BtColumn ref="columnRef" v-model="columnList"></BtColumn> -->
                <el-icon class="item icon"><Setting /></el-icon>
              </div>
            </div>
          </template>
        </BtTab>
      </div>
      <div v-show="curType == 0" class="cont">
        <el-table
          v-loading="isLoading"
          class="table"
          row-key="planId"
          :data="planTree"
          default-expand-all	
          style="width: 100%; height: 100%"
        >
          <template v-for="column in showColumn" :key="column.field">
            <el-table-column
              v-if="column.field == 'status'"
              :property="column.field"
              :label="column.label"
              :width="column.width"
              :min-width="column.minWidth"
            >
              <template #header>
                <BtThFilter
                  v-if="column.filter"
                  :title="column.label"
                  v-bind="column.filter"
                  v-model="column.filterValue"
                  @change="changeFilter($event, column.field)"
                ></BtThFilter>
                <span v-else>{{ column.label }}</span>
              </template>
              <template #default="{ row }">
                <div :class="{ status: true, ing: row.status == 1 }">
                  {{ statusNameMap[row.status] || '未知状态' }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="column.field == 'progress'"
              :property="column.field"
              :label="column.label"
              :width="column.width"
              :min-width="column.minWidth"
            >
              <template #header>
                <BtThFilter
                  v-if="column.filter"
                  :title="column.label"
                  v-bind="column.filter"
                  v-model="column.filterValue"
                  v-model:order="column.filterOrder"
                  @change="changeFilter($event, column.field)"
                >
                  <template #inputSuffix>%</template>
                </BtThFilter>
                <span v-else>{{ column.label }}</span>
              </template>
              <template #default="{ row }">
                <el-progress
                  class="progress"
                  color="#52C41A"
                  :stroke-width="12"
                  :percentage="row.progress"
                />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="column.field == 'name'"
              :property="column.field"
              :label="column.label"
              :width="column.width"
              :min-width="column.minWidth"
              class-name="tr-name"
            >
              <template #header>
                <BtThFilter
                  v-if="column.filter"
                  :title="column.label"
                  v-bind="column.filter"
                  v-model="column.filterValue"
                  @change="changeFilter($event, column.field)"
                ></BtThFilter>
                <span v-else>{{ column.label }}</span>
              </template>
              <template #default="{ row }">
                <div class="plan-name">
                  <el-text truncated>{{ row.name }}</el-text>
                  <el-image
                    v-if="row.hasFlag"
                    class="icon"
                    :src="withBaseUrl('flag-red.png')"
                  ></el-image>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-else
              :property="column.field"
              :label="column.label"
              :width="column.width"
              :min-width="column.minWidth"
              :formatter="column.formatter"
            >
              <template #header>
                <BtThFilter
                  v-if="column.filter"
                  :title="column.label"
                  v-bind="column.filter"
                  v-model="column.filterValue"
                  v-model:order="column.filterOrder"
                  @change="changeFilter($event, column.field)"
                ></BtThFilter>
                <span v-else>{{ column.label }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column
            fixed="right"
            label="操作"
            v-bind="tableConfig.optColumnAttr"
          >
            <template #default="{row}">
              <el-dropdown placement="bottom-end">
                <div class="more-opt">
                  <el-icon class="icon"><MoreFilled /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu class="border drop-opt" >
                    <el-dropdown-item @click="checkDetail">查看详情</el-dropdown-item>
                    <el-dropdown-item @click="handleAdd(row.parentId)">新建项目任务</el-dropdown-item>
                    <el-dropdown-item @click="handleAdd(row.planId)">新建项目子任务</el-dropdown-item>
                    <el-dropdown-item v-if="row.status != 3" @click="handleClose(row)">关闭项目任务</el-dropdown-item>
                    <el-dropdown-item >刷新项目进度</el-dropdown-item>
                    <el-dropdown-item :class="{disabled:row.parentId == 0}" @click="handleUp(row)">升级</el-dropdown-item>
                    <el-dropdown-item :class="{disabled: planTreeIds[row.planId][planTreeIds[row.planId].length-1] == 0 }" @click="handleDown(row)">降级</el-dropdown-item>
                    <el-dropdown-item @click="handleDelete(row)">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-show="curType == 1" class="cont">
        <BtGantt
          ref="ganttRef"
          v-if="typeList[1].load"
          :config="ganttConfig"
          :dateType="dateType"
          :data="ganttData"
          :filterValue="ganttFilter"
        ></BtGantt>
      </div>
    </div>
    <AddPlan v-model="showAdd" :parent-id="parentId" />
  </div>
</template>
<style scoped lang="scss">
.space-plan {
  height: 100%;
  display: flex;
  .sidebar {
    width: 300px;
    height: 100%;
    margin-right: 10px;
    flex-shrink: 0;
  }
  .container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    // border: 1px solid var(--el-border-color);
  }
  .cont {
    flex: 1;
    padding: 10px 10px;
    overflow: hidden;
    .table {
      .plan-name {
        display: flex;
        align-items: center;
        .icon {
          width: 13px;
          height: 13px;
          margin-left: 6px;
          flex-shrink: 0;
        }
      }
      .status {
        &::before {
          position: relative;
          top: -2px;
          content: "";
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #f53f3f;
          margin-right: 6px;
        }
        &.ing {
          &::before {
            background: #165dff;
          }
        }
      }
      .progress {
        :deep(.el-progress__text) {
          font-size: 12px !important;
        }
      }
      :deep(.tr-name) {
        .cell {
          display: flex;
          align-items: center;
          .el-table__expand-icon {
            flex-shrink: 0;
          }
        }
      }
    }
  }
  .footer {
    padding: 10px 20px;
    text-align: right;
  }
}
.drop-opt {
  width: 140px;
  :deep(.el-dropdown-menu__item.disabled)  {
    opacity: .5;
    cursor: not-allowed;
  }
}
</style>
