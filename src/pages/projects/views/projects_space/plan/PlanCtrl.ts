import { ref, computed, useTemplateRef, onMounted, nextTick } from "vue";
import { useRouter } from "vue-router"
import {EventTypes} from "@pages/projects/common/EventTypes.ts";
import { ElMessageBox } from 'element-plus'
import type { ListItem, PlanItem, Participant, DeleteForm } from "./types"
import type { GanttItem } from "@/components/non_biz/bt_gantt/types"
import BtGantt from "@/components/non_biz/bt_gantt/BtGantt.vue"
import { useDictStore } from '@pages/projects/common/stores/DictStore'
import { usePlanStore } from "@/pages/projects/views/projects_space/plan/PlanStore.ts";
import {VCairnEvent} from "v-cairn";

function getTreeIdx(list: ListItem[], pidx: number[] = []): ListItem {
  let result: ListItem = {};
  for(let i = 0; i < list.length; i++) {
    result[list[i].planId] = [...pidx,i];
    if(list[i].children) {
      result = Object.assign({},result,getTreeIdx(list[i].children,result[list[i].planId]));
    }
  }
  return result;
}

export function PlanCtrl() {
  const planStore = usePlanStore()

  new VCairnEvent(EventTypes.GET_DICT_DATA,'project_task_status').emit()
  const dictStore = useDictStore()
  const statusOption = computed(()=>{
    return dictStore.dictObj['project_task_status'] || []
  })
  const statusNameMap = computed(()=>{
    const map: Record<string, string> = {}
    statusOption.value.forEach(v => { map[v.value] = v.label })
    return map
  })

  const curType = ref(0)
  const typeList = ref([
    { name: '项目计划', load: true},
    { name: '甘特图', load: false, },
  ])
  function changeType(idx: number) {
    typeList.value[idx].load = true;
    curType.value = idx;
    if(idx == 1) {
      nextTick(()=>{
        ganttRef.value?.render()
      })
    }
  }

  function formatterDate(str: string) {
    return str ? str.substring(0,10) : ''
  }
  function formatParticipant(list: Participant[]) {
    const names = list.map(v=>{ return v.userName })
    return names.join(', ')
  }
  
  const columnRef = useTemplateRef<any>('columnRef')
  const columnList = ref([
    { 
      field: 'planName', label: '项目任务名称', width: '', minWidth: '260', show: true,
    },
    { 
      field: 'responsibleName', label: '负责人', width: '120', minWidth: '', show: true,
    },
    { 
      field: 'join', label: '参与人', width: '140', minWidth: '', show: true,
      formatter: (row: PlanItem) => { return formatParticipant(row.participants || []) }
    },
    { 
      field: 'startDate', label: '计划开始时间', width: '150', minWidth: '', show: true,
      filter: {
        type: 'input',
        inputType: 'date',
        showOrder: true,
      },
      filterValue: '',
      filterOrder: '',
      formatter: (row: PlanItem) => { return formatterDate(row.startDate) }
    },
    { 
      field: 'endDate', label: '计划完成时间', width: '150', minWidth: '', show: true,
      filter: {
        type: 'input',
        inputType: 'date',
        showOrder: true,
      },
      filterValue: '',
      filterOrder: '',
      formatter: (row: PlanItem) => { return formatterDate(row.endDate) }
    },
    { 
      field: 'status', label: '状态', width: '120', minWidth: '', show: true,
      filter: {
        type: 'radio',
        options: statusOption,
      },
      filterValue: '',
    },
    { 
      field: 'progress', label: '完成进度', width: '153', minWidth: '', show: true,
      filter: {
        type: 'input',
        showOrder: true,
        placeholder: '请输入完成进度',
      },
      filterValue: '',
      filterOrder: '',
    },
  ])
  const showColumn = computed(()=>{
    return columnList.value.filter(v=>v.show);
  })
  function openColumn(){
    columnRef.value.open();
  }
  function changeFilter(val: any, field: string) {
    const column = columnList.value.find(v => v.field == field);
    if(field == 'startDate' || field == 'endDate') {
      planStore.searchForm[field] = val
      planStore.searchForm[`${field}Order`] = column?.filterOrder
    } else if (field == 'status') {
      planStore.searchForm.status = val
    }
    getPlanList();
  }

  
  const parentId = ref(0)
  function handleAdd(pid: number = 0) {
    parentId.value = pid;
    planStore.showAdd = true
  }

  const ganttFilter = ref('')
  const ganttConfig = ref({
    columns: [
      { name: "text", label: "任务名称", width: 200, tree: true, },
      { name: 'user', label: '负责人', width: 90, },
      { name: "start_date", label: "开始时间", width: 125, },
      { name: "end_date", label: "结束时间", width: 125, },
      {
        name: "progress",
        label: "进度",
        width: 69,
        template: function (task: GanttItem) {
          return Math.floor(task.progress * 100)+ '%';
        },
      },
    ],
    // start_date: '2025-01-01',
    // end_date: '2025-02-26',
  })
  const ganttData = computed(() => {
    const list = planStore.planList;
    const result = <GanttItem[]>[];
    for(const item of list) {
      result.push({
        id: item.planId,
        parent: item.parentId,
        text: item.planName,
        user: item.responsibleName,
        start_date: item.startDate,
        end_date: item.endDate,
        progress: item.progress ? item.progress / 100 : 0,
      })
    }
    return { data: result }
  })
  const dateTypeList = ref([
    { label: '日', value: 'day' },
    { label: '周', value: 'week' },
    { label: '月', value: 'month' },
    { label: '年', value: 'year' },
  ]) 
  const dateType = ref('day');

  const ganttRef = useTemplateRef<InstanceType<typeof BtGantt>>('ganttRef');
  function openLevel(val: number) {
    ganttRef.value!.treeOpen(val);
  }
  function ganttOperate(type: string) {
    if(type == 'expand') {
      ganttRef.value!.expand();
    } else if (type == 'export_png') {
      ganttRef.value!.exportTo('png',{
        name: "项目计划甘特图.png",
      })
    } else if (type == 'export_pdf') {
      ganttRef.value!.exportTo('pdf',{
        name: "项目计划甘特图.pdf",
      })
    }
  }

  const keyword = ref('');
  function toSearch() {
    planStore.searchForm.planName = keyword.value
    getPlanList()
  }
  function getPlanList() {
    new VCairnEvent(EventTypes.GET_PLAN_LIST).emit();
  }

  const planTreeIds = computed(()=>{
    return getTreeIdx(planStore.planTree);
  })
  function getPlanChildIds(list: PlanItem[]): DeleteForm[] {
    const result = [];
    for(const info of list) {
      result.push({
        planId: info.planId
      })
      if(info.children) {
        result.push(...getPlanChildIds(info.children))
      }
    }
    return result;
  }
  function handleDelete(info: PlanItem) {
    let message = "确定要删除该任务吗？";
    const delIds = [{planId: info.planId}];
    if(info.children) {
      const childIds = getPlanChildIds(info.children);
      message = `确定要删除该任务及其下 ${childIds.length} 个子任务吗？`
      delIds.push(...childIds)
    }
    ElMessageBox({
      title: '提示',
      message: message,
      type: 'warning',
      showCancelButton: true,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
    }).then(() => {
      new VCairnEvent(EventTypes.BATCH_DELETE_PLAN,delIds).emit();
    })
  }
  function handleClose(info: PlanItem) {
    let message = "确定要关闭该任务吗？";
    const ids = [{planId: info.planId, status: 3}];
    if(info.children) {
      const childIds = getPlanChildIds(info.children);
      message = `确定要关闭该任务及其下 ${childIds.length} 个子任务吗？`
      childIds.forEach(v=> {
        ids.push({ planId: v.planId, status: 3 })
      })
    }
    ElMessageBox({
      title: '提示',
      message: message,
      type: 'warning',
      showCancelButton: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }).then(() => {
      planStore.remainTaskNum = ids.length;
      for(const form of ids) {
        new VCairnEvent(EventTypes.UPDATE_PLAN,form).emit();
      }
    })
  }
  function handleUp(info: PlanItem) {
    if(info.parentId == 0) return
    const parent = planStore.planList.find(v=> v.planId == info.parentId)
    new VCairnEvent(EventTypes.UPDATE_PLAN,{
      planId: info.planId,
      parentId: parent?.parentId
    }).emit();
  }
  function handleDown(info: PlanItem) {
    const ids = planTreeIds.value[info.planId]
    if(ids[ids.length-1] == 0) return;
    let list = planStore.planTree;
    for(let i = 1; i < ids.length; i++) {
      list = list[ids[i-1]].children;
    }
    const lastIds = ids[ids.length-1];
    const siblings = list[lastIds - 1];
    
    new VCairnEvent(EventTypes.UPDATE_PLAN,{
      planId: info.planId,
      parentId: siblings.planId
    }).emit();
  }


  const router = useRouter()
  function checkTemp() {
    router.push('/space/plan/template')
  }
  function checkDetail(){
    router.push('/space/plan/detail')
  }

  onMounted(()=>{
    getPlanList()
  })
  


  return {
    curType,
    typeList,
    changeType,
    columnList,
    showColumn,
    openColumn,
    handleAdd,
    dateType,
    dateTypeList,
    ganttFilter,
    ganttConfig,
    ganttData,
    openLevel,
    ganttOperate,
    keyword,
    toSearch,
    changeFilter,
    handleDelete,
    checkTemp,
    checkDetail,
    parentId,
    statusNameMap,
    handleClose,
    handleUp,
    handleDown,
    planTreeIds,
  };
}
