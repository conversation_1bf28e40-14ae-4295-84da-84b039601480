import { ref, useTemplateRef } from "vue";
import { useRouter } from "vue-router"
import ReportList from "./components/report_list/ReportList.vue"
import ApprovalList from "./components/approval_list/ApprovalList.vue"
import SubtaskList from "./components/subtask_list/SubtaskList.vue"
import IssueList from "./components/issue_list/IssueList.vue"
import FileList from "./components/file_list/FileList.vue"
import RiskList from "./components/risk_list/RiskList.vue"
import HistoryList from "./components/history_list/HistoryList.vue"

export function PlanDetailCtrl() {
  const router = useRouter();

  const formData = ref({
    id: '335544456',
    parent: '',
    response: '',
    join: [],
    name: '',
    type: '',
    desc: '',
    ysbz: '',
    startDate: '',
    endDate: '',
    beginTime: '',
    progress: 69,
    rwps: [],
    milestone: 1,
    autoProgress: 0,
  });
  const formRules = ref({
    name: [{required: true, message: '请输入需求名称', trigger: 'blur' }],
    type: [{ required: true, message: "请选择需求类型", trigger: "change" }],
    xqpsr: [{ required: true, message: "请选择需求评审人", trigger: "change" }],
  });
  const ruleFormRef = useTemplateRef<any>('ruleFormRef');
  async function submitForm() {
    await ruleFormRef.value.validate();
    back();
  };

  function back(){
    router.back()
  }

  const levelOption = [
    { label: '一级', value: '1', },
    { label: '二级', value: '2', },
    { label: '三级', value: '3', },
  ];

  const typeOption = [
    { label: '类型1', value: '1' },
    { label: '类型2', value: '2' },
    { label: '类型3', value: '3' },
  ];

  const tree = ref([
    {
      label: "一级选项一",
      value: "1",
      children: [
        {
          label: "二级选项一",
          value: "1.1",
        },
        {
          label: "二级选项二",
          value: "1.2",
        },
        {
          label: "二级选项三",
          value: "1.3",
          children: [
            { label: "三级选项一", value: "1.3.1" },
            { label: "三级选项二", value: "1.3.2" },
          ],
        },
      ],
    },
    {
      label: "一级选项二",
      value: "2",
      children: [
        { label: "二级选项阿是分身乏术方式防守打法说法", value: "2.1" },
        { label: "二级选项二", value: "2.2" },
      ],
    },
    {
      label: "一级选项三",
      value: "3",
    },
  ]);


  const tabList = [
    { label: '任务汇报', name: 'report', add: true, component: ReportList },
    { label: '审批记录', name: 'approval', add: false, component: ApprovalList },
    { label: '子任务', name: 'subtask', add: true, component: SubtaskList },
    { label: '问题', name: 'issue', add: true, component: IssueList },
    { label: '风险', name: 'risk', add: true, component: RiskList, },
    { label: '附件', name: 'file', add: false, component: FileList },
    { label: '历史记录', name: 'history', add: false, component: HistoryList, },
  ]
  const curTab = ref('report')

  const tabComponents = useTemplateRef<any>('tabComponents')
  function handleAdd(index: number) {
    const dom = tabComponents.value[index]
    dom.openAdd && dom.openAdd()
  }

  return {
    formData,
    formRules,
    submitForm,
    back,
    levelOption,
    typeOption,
    tree,
    tabList,
    curTab,
    handleAdd,
  };
}
