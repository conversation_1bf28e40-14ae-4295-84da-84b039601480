import { ref } from "vue";
import { useRouter } from "vue-router"

export function TemplateDetailCtrl() {

  function getTableIndex(list: any[]): string[] {
    let res = [];
    for (let item of list) {
      res.push(item.id);
      if (item.children) {
        res.push(...getTableIndex(item.children));
      }
    }
    return res;
  }

  const table = ref([
    {
      id: "1",
      name: "项目整体计划初步安排项目整体计划初步安排项目整体计划初步",
      user: "张三",
      start: "2025-01-01",
      end: "2025-01-28",
      children: [
        {
          id: "1.1",
          name: "项目整体计划初步安排项目整体计划初步安排项目整体计划初步",
          user: "张三",
          start: "2025-01-01",
          end: "2025-01-28",
          children: [
            {
              id: "1.1.1",
              name: "项目整体计划初步安排项目整体计划初步安排项目整体计划初步",
              user: "张三",
              start: "2025-01-01",
              end: "2025-01-28",
            },
          ],
        },
      ],
    },
    {
      id: "2",
      name: "项目整体计划初步安排项目整体计划初步安排项目整体计划初步",
      user: "张三",
      start: "2025-01-01",
      end: "2025-01-28",
    },
  ]);
  const indexMap = getTableIndex(table.value);
  const indexMethod = (index: number) => {
    return indexMap[index];
  };


  const router = useRouter();
  function back(){
    router.back();
  }

  return {
    table,
    indexMethod,
    back,
  }
}
