import type { RouteRecordRaw } from "vue-router";
import { RouterView } from "vue-router";
import Layout from "@/components/non_biz/layout/index.vue";


const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/",
    name: "projects",
    component: Layout,
    meta: {
      title: "项目",
    },
    redirect: "/library",
    children: [
      {
        path: "/library",
        name: "Library",
        component: () => import("@pages/projects/views/projects_library/ProjectsLibrary.vue"),
        meta: {
          title: "项目库",
          icon: "项目库",
        },
      },
      {
        path: "/group",
        name: "Group",
        component: RouterView,
        meta: {
          title: "项目群",
          icon: "项目群",
        },
        redirect: "/group/index",
        children: [
          {
            path: "/group/index",
            name: "GroupIndex",
            component: () => import("@pages/projects/views/projects_group/ProjectsGroup.vue"),
            meta: {
              title: "",
              icon: "",
            },
          },
          {
            path: "/group/template",
            name: "Template",
            component: () => import("@pages/projects/views/projects_group/ProjectsTemplate.vue"),
            meta: {
              title: "项目模板",
              icon: "",
            },
          },
        ],
      },
      {
        path: "/my",
        name: "My",
        component: () => import("@pages/projects/views/projects_my/ProjectsMy.vue"),
        meta: {
          title: "我的项目",
          icon: "我的项目",
        },
      },
      {
        path: "/space",
        name: "ProjectsSpace",
        component: () => import("@pages/projects/views/projects_space/main/Main.vue"),
        meta: {
          title: '项目空间',
          icon: "我的项目",
          isFullPage: true,
          hideMenu: true,
        },
        redirect: '/space/overview',
        children: [
          {
            path: "overview",
            name: "ProjectsSpaceOverview",
            component: () => import("@pages/projects/views/projects_space/overview/Overview.vue"),
          },
          {
            path: "/space/module",
            name: "ProjectsSpaceModule",
            component: () => import("@pages/projects/views/projects_space/module/Module.vue"),
            meta: {
              title: '模块',
              icon: "模块",
            }
          },
          {
            path: "/space/plan",
            name: "ProjectsSpacePlan",
            component: RouterView,
            meta: {
              title: "计划",
              icon: "计划",
            },
            redirect: '/space/plan/list',
            children: [
              {
                path: '/space/plan/list',
                name: 'ProjectSpacePlanList',
                component: () => import("@pages/projects/views/projects_space/plan/Plan.vue"),
                meta: {
                  title: "计划列表",
                  icon: "计划",
                }
              },
              {
                path: '/space/plan/detail',
                name: 'ProjectSpacePlanDetail',
                component: () => import("@pages/projects/views/projects_space/plan/PlanDetail.vue"),
                meta: {
                  title: "计划详情",
                  icon: "计划",
                }
              },
              {
                path: '/space/plan/template',
                name: 'ProjectSpacePlanTemplate',
                component: () => import("@pages/projects/views/projects_space/plan/Template.vue"),
                meta: {
                  title: "计划模板",
                  icon: "计划",
                }
              },
              {
                path: '/space/plan/templateDetail',
                name: 'ProjectSpacePlanTemplateDetail',
                component: () => import("@pages/projects/views/projects_space/plan/TemplateDetail.vue"),
                meta: {
                  title: "模板详情",
                  icon: "计划",
                }
              }
            ]
          },
          {
            path: "/space/demand",
            name: "ProjectsSpaceDemand",
            component: RouterView,
            meta: {
              title: "需求",
              icon: "需求",
            },
            redirect: '/space/demand/list',
            children: [
              {
                path: "/space/demand/list",
                name: "ProjectsSpaceDemandList",
                component: () => import("@pages/projects/views/projects_space/demand/list/List.vue"),
                meta: {
                  title: '需求列表',
                  icon: '需求'
                }
              },
              {
                path: "/space/demand/add",
                name: "ProjectsSpaceDemandAdd",
                component: () => import("@pages/projects/views/projects_space/demand/add/Add.vue"),
                meta: {
                  title: '新增需求',
                  icon: '需求'
                }
              },
              {
                path: "/space/demand/distribution",
                name: "ProjectsSpaceDemandDistribution",
                component: () => import("@pages/projects/views/projects_space/demand/distribution/Distribution.vue"),
                meta: {
                  title: '需求分发',
                  icon: '需求'
                }
              },
              {
                path: "/space/demand/detail",
                name: "ProjectsSpaceDemandDetail",
                component: () => import("@pages/projects/views/projects_space/demand/detail/Detail.vue"),
                meta: {
                  title: '需求详情',
                  icon: '需求'
                }
              },
            ]
          },
          {
            path: "/space/case",
            name: "ProjectsSpaceCase",
            component: RouterView,
            meta: {
              title: "用例",
              icon: "测试",
            },
            redirect: '/space/case/list',
            children: [
              {
                path: "/space/case/list",
                name: "ProjectsSpaceCaseList",
                component: () => import("@pages/projects/views/projects_space/case/list/List.vue"),
                meta: {
                  title: '用例列表',
                  icon: '测试'
                }
              },
              {
                path: "/space/case/add",
                name: "ProjectsSpaceCaseAdd",
                component: () => import("@pages/projects/views/projects_space/case/add/Add.vue"),
                meta: {
                  title: '新增用例',
                  icon: '测试'
                }
              },
              {
                path: "/space/case/detail",
                name: "ProjectsSpaceCaseDetail",
                component: () => import("@pages/projects/views/projects_space/case/detail/Detail.vue"),
                meta: {
                  title: '用例详情',
                  icon: '测试'
                }
              }
            ]
          },
          {
            path: "/space/defect",
            name: "ProjectsSpaceDefect",
            component: () => import("@pages/projects/views/projects_space/defect/Defect.vue"),
            meta: {
              title: "缺陷",
              icon: "BUG",
            }
          },
          {
            path: "/space/file",
            name: "ProjectsSpaceFile",
            component: () => import("@pages/projects/views/projects_space/file/File.vue"),
            meta: {
              title: "文件",
              icon: "文件夹",
            }
          },
          {
            path: "/space/team",
            name: "ProjectsSpaceTeam",
            component: () => import("@pages/projects/views/projects_space/team/Team.vue"),
            meta: {
              title: "团队",
              icon: "团队",
            }
          },
          {
            path: "/space/board",
            name: "ProjectsSpaceBoard",
            component: () => import("@pages/projects/views/projects_space/board/Board.vue"),
            meta: {
              title: "看板",
              icon: "项目看板",
            }
          }
        ]
      }
    ],
  },
  
];
export default constantRoutes;
