import { ref } from "vue";
import { defineStore } from "pinia";
import type { DictItem, DictStoreItem } from "@pages/projects/common/types";

export const useDictStore = defineStore( "dictStore", () => {
    const dictObj = ref<Record<string, DictStoreItem[]>>({});;

    function setDict(dictType: string, data: DictItem[]) {
        dictObj.value[dictType] = data.map(v=>{
            return {...v, value: v.dictValue, label: v.dictLabel}
        })
    }

    return { dictObj, setDict };
  }
);
