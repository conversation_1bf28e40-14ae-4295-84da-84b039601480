import { ref } from "vue";
import { defineStore } from "pinia";
import type { ProjectItem } from "@pages/projects/common/types";

export const useProjectStore = defineStore( "projectStore", () => {
    const curProject = ref<ProjectItem>({
        projectId: 10,
        projectName: '项目测试2',
        projectCode: '',
        projectDesc: '',
        responsible: 1,
        responsibleName: 'admin',
        startTime: '2025-02-27 16:00:00',
        endTime: '2025-02-28 16:00:00',
        status: 1,
        templateId: 1,
        tenantId: 1,
    });

    return { curProject };
  },
  {
    persist: {
      pick: ["curProject"],
    },
  }
);
