import {EventTypes} from "@pages/projects/common/EventTypes.ts";
import { GetDictDataCommand } from './cmds/GetDictDataCommand'
import { GetUserListCommand } from './cmds/GetUserListCommand'
import {GetPlanListCommand} from "@pages/projects/views/projects_space/plan/cmds/GetPlanListCommand.ts";
import {AddPlanCommand} from "@pages/projects/views/projects_space/plan/cmds/AddPlanCommand";
import {DeletePlanCommand} from "@pages/projects/views/projects_space/plan/cmds/DeletePlanCommand";
import {BatchDeletePlanCommand} from "@pages/projects/views/projects_space/plan/cmds/BatchDeletePlanCommand";
import {UpdatePlanCommand} from "@pages/projects/views/projects_space/plan/cmds/UpdatePlanCommand";
import {VCairnConnector} from "v-cairn";

export class ProjectsConnector extends VCairnConnector {
    constructor() {
        super();
        this.initCommands();
    }

    initCommands() {
        this.addCommand(EventTypes.GET_DICT_DATA, GetDictDataCommand);
        this.addCommand(EventTypes.GET_USER_LIST, GetUserListCommand);
        this.addCommand(EventTypes.GET_PLAN_LIST, GetPlanListCommand);
        this.addCommand(EventTypes.ADD_PLAN, AddPlanCommand);
        this.addCommand(EventTypes.DELETE_PLAN, DeletePlanCommand);
        this.addCommand(EventTypes.BATCH_DELETE_PLAN, BatchDeletePlanCommand);
        this.addCommand(EventTypes.UPDATE_PLAN, UpdatePlanCommand);
    }
}