import type {ResponseData} from "@/libs/btit/types.ts";

export type UserItem = {
  userId: number;
  userName: string;
  userAccount: string;
  userEmail: string;
  [key: string]: any;
};

export type UserList = UserItem[];

export type ProjectItem = {
  projectId: number;
  projectName: string;
  projectCode: string;
  projectDesc: string;
  responsible: number;
  responsibleName: string;
  startTime: string;
  endTime: string;
  status: number;
  templateId: number;
  tenantId: number;
};

export type ProjectList = ProjectItem[];

export type DictItem = {
  dictCode: number;
  dictSort: number;
  dictLabel: string;
  dictValue: string;
  dictType: string;
  isDefault: string;
  status: number;
}

export interface DictStoreItem extends DictItem {
  label: string;
  value: string;
}

export interface DictResponse extends ResponseData {
  data: DictItem[]
}