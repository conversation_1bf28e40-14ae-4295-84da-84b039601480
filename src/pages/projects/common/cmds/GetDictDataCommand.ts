import {dictDataList} from "@/service_url/shared.ts";
import {BtitCommand} from "@/libs/btit/BtitCommand.ts";
import type { AxiosResponse } from "v-cairn";
import {useDictStore} from "../stores/DictStore.ts";
import type {VCairnEvent} from "v-cairn";


export class GetDictDataCommand extends BtitCommand {
    public dictType: string = ''

    execute(evt: VCairnEvent) {
        const dictType = evt.data
        this.dictType = dictType
        this.service.send({
            method: 'post',
            url: dictDataList,
            data: { dictType }
        });
    }

    onSuccess(response: AxiosResponse): void {
        const dictStore = useDictStore();
        const { data } = response.data
        dictStore.setDict(this.dictType,data)
    }

}
