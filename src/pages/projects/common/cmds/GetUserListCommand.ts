import {userList} from "@/service_url/shared.ts";
import {BtitCommand} from "@/libs/btit/BtitCommand.ts";
import type { AxiosResponse } from "v-cairn";
import {useUserListStore} from "../stores/UserListStore.ts";

export class GetUserListCommand extends BtitCommand {

    execute() {
        this.service.send({
            method: 'post',
            url: userList,
            data: {}
        });
    }

    onSuccess(response: AxiosResponse): void {
        const userListStore = useUserListStore();
        const { data } = response.data
        userListStore.userList = data
    }

    onFail(): void {
        
    }
}
