# 任务列表 Mock 数据优化说明

## 优化概述

为了确保左侧树形组件的筛选功能能够正确工作，对右侧任务列表的 mock 数据进行了全面优化，使其与左侧项目树形结构完全匹配。

## 优化前的问题

1. **项目ID不匹配**：mock 数据中的 projectId 与树形结构中的项目ID不完全对应
2. **缺少项目数据**：缺少 project_c2（子项目C2）的任务数据
3. **任务名称不直观**：所有任务名称都是 "xxxxxxxxx"，无法区分不同项目的任务
4. **数据不够丰富**：缺少不同状态、不同负责人的任务，无法充分测试筛选功能

## 优化后的数据结构

### 项目A (project_a) - 前端开发项目
```typescript
{
  id: '1',
  name: '项目A - 前端开发任务',
  projectId: 'project_a',
  projectName: '项目A',
  responsibleUser: '张三',
  participants: ['张三', '李四'],
  createdBy: '王五',
  workType: '开发',
  department: '前端开发组',
  children: [
    {
      id: '1-1',
      name: '项目A - 页面开发',
      // ... 子任务详情
    },
    {
      id: '1-2', 
      name: '项目A - 组件开发',
      // ... 子任务详情
    }
  ]
}
```

### 项目B (project_b) - 系统测试项目
```typescript
{
  id: '2',
  name: '项目B - 系统测试任务',
  projectId: 'project_b',
  projectName: '项目B',
  responsibleUser: '李四',
  participants: ['张三', '王五'],
  createdBy: '张三',
  workType: '测试',
  department: '测试组',
  children: [
    {
      id: '2-1',
      name: '项目B - 功能测试',
      // ... 子任务详情
    },
    {
      id: '2-2',
      name: '项目B - 性能测试',
      // ... 子任务详情
    }
  ]
}
```

### 子项目C1 (project_c1) - UI设计项目
```typescript
{
  id: '3',
  name: '子项目C1 - UI设计任务',
  projectId: 'project_c1',
  projectName: '子项目C1',
  responsibleUser: '张三',
  participants: ['张三', '王五'],
  createdBy: '李四',
  workType: '设计',
  department: '设计组',
  children: [
    {
      id: '3-1',
      name: '子项目C1 - 界面设计',
      status: '已完成',
      progress: 100,
      // ... 子任务详情
    },
    {
      id: '3-2',
      name: '子项目C1 - 交互设计',
      status: '进行中',
      progress: 50,
      // ... 子任务详情
    }
  ]
}
```

### 子项目C2 (project_c2) - 后端开发项目 【新增】
```typescript
{
  id: '4',
  name: '子项目C2 - 后端开发任务',
  projectId: 'project_c2',
  projectName: '子项目C2',
  responsibleUser: '李四',
  participants: ['张三', '李四', '王五'],
  createdBy: '张三',
  workType: '开发',
  department: '后端开发组',
  children: [
    {
      id: '4-1',
      name: '子项目C2 - API开发',
      status: '待开始',
      progress: 0,
      // ... 子任务详情
    },
    {
      id: '4-2',
      name: '子项目C2 - 数据库设计',
      status: '待开始',
      progress: 0,
      // ... 子任务详情
    }
  ]
}
```

## 数据特点优化

### 1. 项目ID完全匹配
- **project_a** → 项目A的所有任务
- **project_b** → 项目B的所有任务  
- **project_c1** → 子项目C1的所有任务
- **project_c2** → 子项目C2的所有任务

### 2. 任务名称语义化
- 每个任务名称都包含项目标识和具体任务内容
- 便于在表格中快速识别任务所属项目
- 子任务名称也遵循相同的命名规范

### 3. 多样化的任务状态
- **已完成**：子项目C1的界面设计
- **进行中**：大部分开发和测试任务
- **待开始**：子项目C2的所有任务

### 4. 不同的负责人分配
- **张三**：主要负责项目A和子项目C1
- **李四**：主要负责项目B和子项目C2
- **王五**：主要作为参与者和创建者

### 5. 丰富的参与者组合
- 单人任务：部分子任务只有一个参与者
- 多人协作：主任务通常有多个参与者
- 跨部门协作：不同部门的人员参与同一任务

## 筛选测试场景

### 1. 根节点筛选
勾选"我的项目" → 显示所有4个项目的任务（共4个主任务 + 8个子任务）

### 2. 单项目筛选
- 勾选"项目A" → 显示1个主任务 + 2个子任务
- 勾选"项目B" → 显示1个主任务 + 2个子任务
- 勾选"子项目C1" → 显示1个主任务 + 2个子任务
- 勾选"子项目C2" → 显示1个主任务 + 2个子任务

### 3. 多项目筛选
- 勾选"项目A" + "项目B" → 显示2个主任务 + 4个子任务
- 勾选"子项目C1" + "子项目C2" → 显示2个主任务 + 4个子任务

### 4. 项目集筛选
勾选"项目集C" → 自动包含子项目C1和C2的所有任务

### 5. 用户角色筛选
- **我负责的**：张三负责的任务（项目A、子项目C1的部分任务、子项目C2的部分任务）
- **我参与的**：张三参与的任务（几乎所有任务）
- **我分配的**：张三分配的任务（项目B、子项目C2）

## 数据一致性保证

1. **projectId 字段**：确保与树形结构的 value 字段完全匹配
2. **projectName 字段**：确保与树形结构的 label 字段完全匹配
3. **层级结构**：主任务和子任务的层级关系清晰
4. **索引排序**：每个任务都有正确的 level 和 index 字段用于排序

## 测试验证

现在可以通过以下方式验证筛选功能：

1. **打开页面**：访问 `http://localhost:5174/#/project-tasks`
2. **查看数据**：右侧表格显示所有任务，任务名称清晰易懂
3. **测试筛选**：
   - 勾选不同的项目节点
   - 观察右侧表格的实时更新
   - 验证筛选结果是否正确
4. **查看日志**：打开浏览器控制台，查看筛选过程的详细日志

通过这次优化，左侧树形组件的勾选功能现在可以完美地联动右侧表格的筛选，用户可以清晰地看到不同项目的任务分布和筛选效果。
