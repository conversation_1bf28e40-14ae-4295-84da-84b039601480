import * as util from 'node:util';
import type { UserConfig, PluginOption } from 'vite';
import { defineConfig, loadEnv } from 'vite';
import chalk from 'chalk';
import vue from '@vitejs/plugin-vue';
import { getMpOption } from './scripts/utils';
import { elementAutoImport } from './scripts/plugins';
import { buildConfig } from './scripts/config';
import mpb from 'vite-plugin-mpb';
import legacy from '@vitejs/plugin-legacy';
// import { viteMockServe } from 'vite-plugin-mock';

// https://vite.dev/config/
/** @type {import('vite').UserConfig} */
export default defineConfig(
  async ({ mode, command, isPreview }): Promise<UserConfig> => {
    const env = loadEnv(mode, process.cwd(), 'VITE_');
    // console.log('env', env);
    const base = env.VITE_BASE_PATH;
    console.log(
      chalk.green(
        '当前构建环境：',
        `mode = ${mode}, command = ${command}, isPreview = ${isPreview}, base = ${base}`
      )
    );
    const mpOption = await getMpOption(command, isPreview);
    console.log(chalk.green('当前构建页面配置：', util.inspect(mpOption)));
    return {
      base,
      plugins: [
        vue(),
        ...elementAutoImport(),
        mpb(mpOption),
        legacy({
          targets: ['defaults', 'not IE 11'],
        }),
        // viteMockServe({
        //   mockPath: 'mock',
        //   enable: command === 'serve',
        //   watchFiles: true,
        //   logger: true,
        // }),
      ] as PluginOption[],
      resolve: {
        alias: {
          '@': buildConfig.srcDir,
          '@pages': buildConfig.pagesDir,
        },
      },
      server: {
        proxy: {
          '/ipd/api/platform': {
            target: env.VITE_API_PROXY,
            changeOrigin: true,
            // rewrite: (path) => path.replace(/^\/ipd\/api\/platform/, ''),
          },
        },
      },
      build: {
        rollupOptions: {
          output: {
            assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
            chunkFileNames: 'js/[name]-[hash].js',
            entryFileNames: 'js/[name]-[hash].js',
            manualChunks: {
              vue3: ['vue', 'pinia', 'vue-router'],
              eleUi: ['element-plus'],
              bpmnJs: ['bpmn-js'],
              gantt: ['dhtmlx-gantt'],
              x6: ['@antv/x6'],
            },
          },
        },
      },
    };
  }
);
