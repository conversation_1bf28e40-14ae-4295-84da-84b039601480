export default [
  {
    url: '/projects/my/list',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '操作成功',
        data: {
          total: 3,
          rows: [
            {
              projectId: '1',
              projectName: '示例项目1',
              projectDesc: '这是一个示例项目',
              status: '0',
              createTime: '2023-01-01 00:00:00',
              createBy: 'admin',
            },
            {
              projectId: '2',
              projectName: '示例项目2',
              projectDesc: '这是另一个示例项目',
              status: '0',
              createTime: '2023-01-02 00:00:00',
              createBy: 'admin',
            },
            {
              projectId: '3',
              projectName: '示例项目3',
              projectDesc: '这是第三个示例项目',
              status: '1',
              createTime: '2023-01-03 00:00:00',
              createBy: 'admin',
            },
          ],
        },
      };
    },
  },
  {
    url: '/projects/group/list',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '操作成功',
        data: {
          total: 2,
          rows: [
            {
              groupId: '1',
              groupName: '示例项目组1',
              groupDesc: '这是一个示例项目组',
              createTime: '2023-01-01 00:00:00',
              createBy: 'admin',
            },
            {
              groupId: '2',
              groupName: '示例项目组2',
              groupDesc: '这是另一个示例项目组',
              createTime: '2023-01-02 00:00:00',
              createBy: 'admin',
            },
          ],
        },
      };
    },
  },
];
