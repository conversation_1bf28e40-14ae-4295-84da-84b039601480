export default [
  {
    url: '/permission/login',
    method: 'post',
    response: () => {
      return {
        code: 200,
        message: '操作成功',
        data: {
          token: 'mock-token-123456789',
        },
      };
    },
  },
  {
    url: '/permission/getInfo',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '操作成功',
        data: {
          user: {
            userId: '1',
            username: 'admin',
            nickname: '管理员',
            email: '<EMAIL>',
            avatar: '',
          },
          roles: ['admin'],
          permissions: ['*:*:*'],
        },
      };
    },
  },
  {
    url: '/permission/getRouters',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '操作成功',
        data: [
          {
            name: 'Projects',
            path: '/projects',
            component: 'Layout',
            meta: {
              title: '项目管理',
              icon: 'el-icon-s-home',
            },
            children: [
              {
                name: 'ProjectsMy',
                path: 'my',
                component: 'projects_my/ProjectsMy',
                meta: {
                  title: '我的项目',
                  icon: 'el-icon-s-grid',
                },
              },
              {
                name: 'ProjectsGroup',
                path: 'group',
                component: 'projects_group/ProjectsGroup',
                meta: {
                  title: '项目组',
                  icon: 'el-icon-s-grid',
                },
              },
            ],
          },
        ],
      };
    },
  },
];
