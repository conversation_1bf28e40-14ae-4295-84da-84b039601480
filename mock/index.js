import { createProdMockServer } from 'vite-plugin-mock/client';

// 导入所有 mock 文件
import loginMock from './permission/login';
import userMock from './permission/user';
import dictMock from './basic/dict';
import fileMock from './basic/file';
import projectsMock from './projects/index';
import tasksMock from './tasks/index';

export function setupProdMockServer() {
  createProdMockServer([
    ...loginMock,
    ...userMock,
    ...dictMock,
    ...fileMock,
    ...projectsMock,
    ...tasksMock,
  ]);
}

export const mockModules = [
  ...loginMock,
  ...userMock,
  ...dictMock,
  ...fileMock,
  ...projectsMock,
  ...tasksMock,
];
