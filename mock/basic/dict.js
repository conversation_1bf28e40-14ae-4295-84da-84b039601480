export default [
  {
    url: '/basic/sys-dict-data/list',
    method: 'get',
    response: ({ query }) => {
      const dictType = query?.dictType;

      const dictData = {
        sys_user_sex: [
          { dictCode: 1, dictLabel: '男', dictValue: '0' },
          { dictCode: 2, dictLabel: '女', dictValue: '1' },
        ],
        sys_normal_disable: [
          { dictCode: 3, dictLabel: '正常', dictValue: '0' },
          { dictCode: 4, dictLabel: '停用', dictValue: '1' },
        ],
        project_status: [
          { dictCode: 5, dictLabel: '进行中', dictValue: '0' },
          { dictCode: 6, dictLabel: '已完成', dictValue: '1' },
          { dictCode: 7, dictLabel: '已取消', dictValue: '2' },
        ],
      };

      return {
        code: 200,
        message: '操作成功',
        data: dictType ? dictData[dictType] || [] : [],
      };
    },
  },
];
