import { success } from '../utils';

// 模拟用户数据
const MOCK_USERS = {
  1: { id: 1, name: '张三', deptId: 1, deptName: '前端开发组' },
  2: { id: 2, name: '李四', deptId: 2, deptName: '后端开发组' },
  3: { id: 3, name: '王五', deptId: 3, deptName: '测试组' },
  4: { id: 4, name: '赵六', deptId: 4, deptName: '设计组' },
  5: { id: 5, name: '项目经理', deptId: 5, deptName: '项目管理部' },
  6: { id: 6, name: '技术总监', deptId: 1, deptName: '前端开发组' },
};

// 当前登录用户ID
const CURRENT_USER_ID = 1;

// 任务状态枚举 - 对应 TypeScript 定义
const TASK_STATUS = {
  NOT_STARTED: 1, // 未开始
  IN_PROGRESS: 2, // 进行中
  CLOSED: 3, // 已关闭
};

// 独立任务状态枚举
const INDEPENDENT_TASK_STATUS = {
  NOT_STARTED: 1, // 未开始
  IN_PROGRESS: 2, // 进行中
  CLOSED: 3, // 已关闭
};

// 流程任务状态（字符串类型）
const PROCESS_TASK_STATUS = {
  PENDING_ACCEPT: '待接受',
  REJECTED: '已驳回',
  NOT_STARTED: '未开始',
  IN_PROGRESS: '进行中',
  COMPLETED: '已完成',
  CLOSED: '已关闭',
};

// 标签页类型枚举
const TAB_TYPE = {
  ALL: 1, // 全部
  RESPONSIBLE: 2, // 我负责的
  PARTICIPANT: 3, // 我参与的
  ASSIGNED: 4, // 我指派的
  TODO: 5, // 我待办的
};

// 任务标签mock数据
const MOCK_TASK_TAGS = [
  {
    taskTagId: 1,
    parentTaskTagId: null,
    tagGroupFlag: 1, // 是标签组
    taskTagName: '优先级',
    taskTagDesc: '任务优先级分类',
    taskTagColor: '#409EFF',
    taskTagStatus: 1, // 启用
    sortNum: 1,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createUser: 1,
    updateUser: 1,
    remark: '优先级标签组',
    tenantId: 1,
  },
  {
    taskTagId: 2,
    parentTaskTagId: 1,
    tagGroupFlag: 0, // 不是标签组
    taskTagName: '高优先级',
    taskTagDesc: '高优先级任务',
    taskTagColor: '#F56C6C',
    taskTagStatus: 1,
    sortNum: 1,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createUser: 1,
    updateUser: 1,
    remark: '高优先级标签',
    tenantId: 1,
  },
  {
    taskTagId: 3,
    parentTaskTagId: 1,
    tagGroupFlag: 0,
    taskTagName: '中优先级',
    taskTagDesc: '中优先级任务',
    taskTagColor: '#E6A23C',
    taskTagStatus: 1,
    sortNum: 2,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createUser: 1,
    updateUser: 1,
    remark: '中优先级标签',
    tenantId: 1,
  },
  {
    taskTagId: 4,
    parentTaskTagId: 1,
    tagGroupFlag: 0,
    taskTagName: '低优先级',
    taskTagDesc: '低优先级任务',
    taskTagColor: '#67C23A',
    taskTagStatus: 1,
    sortNum: 3,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createUser: 1,
    updateUser: 1,
    remark: '低优先级标签',
    tenantId: 1,
  },
  {
    taskTagId: 5,
    parentTaskTagId: null,
    tagGroupFlag: 1,
    taskTagName: '任务类型',
    taskTagDesc: '任务类型分类',
    taskTagColor: '#909399',
    taskTagStatus: 1,
    sortNum: 2,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createUser: 1,
    updateUser: 1,
    remark: '任务类型标签组',
    tenantId: 1,
  },
  {
    taskTagId: 6,
    parentTaskTagId: 5,
    tagGroupFlag: 0,
    taskTagName: '开发任务',
    taskTagDesc: '软件开发相关任务',
    taskTagColor: '#409EFF',
    taskTagStatus: 1,
    sortNum: 1,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createUser: 1,
    updateUser: 1,
    remark: '开发任务标签',
    tenantId: 1,
  },
  {
    taskTagId: 7,
    parentTaskTagId: 5,
    tagGroupFlag: 0,
    taskTagName: '测试任务',
    taskTagDesc: '软件测试相关任务',
    taskTagColor: '#67C23A',
    taskTagStatus: 1,
    sortNum: 2,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createUser: 1,
    updateUser: 1,
    remark: '测试任务标签',
    tenantId: 1,
  },
  {
    taskTagId: 8,
    parentTaskTagId: 5,
    tagGroupFlag: 0,
    taskTagName: '设计任务',
    taskTagDesc: 'UI/UX设计相关任务',
    taskTagColor: '#E6A23C',
    taskTagStatus: 1,
    sortNum: 3,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createUser: 1,
    updateUser: 1,
    remark: '设计任务标签',
    tenantId: 1,
  },
  {
    taskTagId: 9,
    parentTaskTagId: null,
    tagGroupFlag: 0,
    taskTagName: '紧急',
    taskTagDesc: '紧急处理任务',
    taskTagColor: '#F56C6C',
    taskTagStatus: 1,
    sortNum: 1,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createUser: 1,
    updateUser: 1,
    remark: '紧急标签',
    tenantId: 1,
  },
  {
    taskTagId: 10,
    parentTaskTagId: null,
    tagGroupFlag: 0,
    taskTagName: 'Bug修复',
    taskTagDesc: 'Bug修复相关任务',
    taskTagColor: '#F56C6C',
    taskTagStatus: 1,
    sortNum: 2,
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00',
    createUser: 1,
    updateUser: 1,
    remark: 'Bug修复标签',
    tenantId: 1,
  },
];

// 任务标签页面专用的任务mock数据 - 基于 Swagger ProjectTaskResponse
const MOCK_TASKS_WITH_TAGS = [
  {
    projTaskId: 1001,
    parentProjTaskId: null,
    projId: 1,
    phaseProjTaskId: null,
    projTaskName: '用户登录功能开发',
    projTaskDesc: '实现用户登录、注册、密码重置功能',
    projTaskStatus: 2, // 进行中
    projTaskResponsible: 1,
    projTaskResponsibleProjRole: '前端开发工程师',
    projTaskResponsibleDept: 1,
    planProgress: 60,
    actualProgress: 60,
    overdueDays: 0,
    taskType: 2, // 任务作业
    taskActivityType: 1,
    major: '前端开发',
    systemId: 1,
    projTaskAcceptCriteria: '功能正常运行，通过测试',
    planStartDate: '2024-01-01',
    actualStartDate: '2024-01-01',
    planFinishDate: '2024-01-05',
    actualFinishDate: null,
    duration: 5,
    workflowId: 'workflow_001',
    phaseFlag: 0,
    autoProgressFlag: 0,
    createTime: '2024-01-01T09:00:00Z',
    updateTime: '2024-01-01T09:00:00Z',
    createUser: 1,
    updateUser: 1,
    remark: '用户登录模块开发',
    tenantId: 1,
    responsibleName: '张三',
    projectName: '电商平台项目',
    tags: [
      {
        taskTagId: 2,
        taskTagName: '高优先级',
        taskTagColor: '#F56C6C',
      },
      {
        taskTagId: 6,
        taskTagName: '开发任务',
        taskTagColor: '#409EFF',
      },
    ],
  },
  {
    projTaskId: 1002,
    parentProjTaskId: null,
    projId: 1,
    phaseProjTaskId: null,
    projTaskName: '商品管理模块测试',
    projTaskDesc: '对商品增删改查功能进行全面测试',
    projTaskStatus: 1, // 未开始
    projTaskResponsible: 3,
    projTaskResponsibleProjRole: '测试工程师',
    projTaskResponsibleDept: 3,
    planProgress: 0,
    actualProgress: 0,
    overdueDays: 0,
    taskType: 2, // 任务作业
    taskActivityType: 2,
    major: '软件测试',
    systemId: 1,
    projTaskAcceptCriteria: '测试用例全部通过',
    planStartDate: '2024-01-06',
    actualStartDate: null,
    planFinishDate: '2024-01-10',
    actualFinishDate: null,
    duration: 5,
    workflowId: 'workflow_002',
    phaseFlag: 0,
    autoProgressFlag: 0,
    createTime: '2024-01-02T10:00:00Z',
    updateTime: '2024-01-02T10:00:00Z',
    createUser: 3,
    updateUser: 3,
    remark: '商品管理模块测试',
    tenantId: 1,
    responsibleName: '王五',
    projectName: '电商平台项目',
    tags: [
      {
        taskTagId: 3,
        taskTagName: '中优先级',
        taskTagColor: '#E6A23C',
      },
      {
        taskTagId: 7,
        taskTagName: '测试任务',
        taskTagColor: '#67C23A',
      },
    ],
  },
  {
    projTaskId: 1003,
    parentProjTaskId: null,
    projId: 1,
    phaseProjTaskId: null,
    projTaskName: '首页UI设计优化',
    projTaskDesc: '优化首页布局和视觉效果',
    projTaskStatus: 3, // 已关闭
    projTaskResponsible: 4,
    projTaskResponsibleProjRole: 'UI设计师',
    projTaskResponsibleDept: 4,
    planProgress: 100,
    actualProgress: 100,
    overdueDays: 0,
    taskType: 2, // 任务作业
    taskActivityType: 3,
    major: 'UI设计',
    systemId: 1,
    projTaskAcceptCriteria: '设计稿通过评审',
    planStartDate: '2024-01-01',
    actualStartDate: '2024-01-01',
    planFinishDate: '2024-01-03',
    actualFinishDate: '2024-01-03',
    duration: 3,
    workflowId: 'workflow_003',
    phaseFlag: 0,
    autoProgressFlag: 0,
    createTime: '2024-01-01T14:00:00Z',
    updateTime: '2024-01-03T16:30:00Z',
    createUser: 4,
    updateUser: 4,
    remark: '首页UI设计优化',
    tenantId: 1,
    responsibleName: '赵六',
    projectName: '电商平台项目',
    tags: [
      {
        taskTagId: 4,
        taskTagName: '低优先级',
        taskTagColor: '#67C23A',
      },
      {
        taskTagId: 8,
        taskTagName: '设计任务',
        taskTagColor: '#E6A23C',
      },
    ],
  },
  {
    taskId: 1004,
    taskName: '支付接口集成',
    taskDesc: '集成第三方支付接口，支持微信、支付宝支付',
    taskStatus: 2, // 进行中
    responsibleUserId: 2,
    responsibleUserName: '李四',
    createTime: '2024-01-03 09:00:00',
    planStartTime: '2024-01-03 09:00:00',
    planEndTime: '2024-01-08 18:00:00',
    actualStartTime: '2024-01-03 09:00:00',
    actualEndTime: null,
    progress: 30,
    projectId: 1,
    projectName: '电商平台项目',
    tags: [
      {
        taskTagId: 2,
        taskTagName: '高优先级',
        taskTagColor: '#F56C6C',
      },
      {
        taskTagId: 6,
        taskTagName: '开发任务',
        taskTagColor: '#409EFF',
      },
      {
        taskTagId: 9,
        taskTagName: '紧急',
        taskTagColor: '#F56C6C',
      },
    ],
  },
  {
    taskId: 1005,
    taskName: '数据库性能优化',
    taskDesc: '优化数据库查询性能，添加索引',
    taskStatus: 1, // 未开始
    responsibleUserId: 2,
    responsibleUserName: '李四',
    createTime: '2024-01-04 11:00:00',
    planStartTime: '2024-01-09 09:00:00',
    planEndTime: '2024-01-12 18:00:00',
    actualStartTime: null,
    actualEndTime: null,
    progress: 0,
    projectId: 2,
    projectName: '内部管理系统',
    tags: [
      {
        taskTagId: 3,
        taskTagName: '中优先级',
        taskTagColor: '#E6A23C',
      },
      {
        taskTagId: 6,
        taskTagName: '开发任务',
        taskTagColor: '#409EFF',
      },
    ],
  },
  {
    taskId: 1006,
    taskName: '系统安全漏洞修复',
    taskDesc: '修复系统中发现的安全漏洞',
    taskStatus: 2, // 进行中
    responsibleUserId: 1,
    responsibleUserName: '张三',
    createTime: '2024-01-05 08:00:00',
    planStartTime: '2024-01-05 08:00:00',
    planEndTime: '2024-01-06 18:00:00',
    actualStartTime: '2024-01-05 08:00:00',
    actualEndTime: null,
    progress: 80,
    projectId: 2,
    projectName: '内部管理系统',
    tags: [
      {
        taskTagId: 2,
        taskTagName: '高优先级',
        taskTagColor: '#F56C6C',
      },
      {
        taskTagId: 9,
        taskTagName: '紧急',
        taskTagColor: '#F56C6C',
      },
      {
        taskTagId: 10,
        taskTagName: 'Bug修复',
        taskTagColor: '#F56C6C',
      },
    ],
  },
  {
    taskId: 1007,
    taskName: '移动端适配测试',
    taskDesc: '测试移动端页面适配效果',
    taskStatus: 1, // 未开始
    responsibleUserId: 3,
    responsibleUserName: '王五',
    createTime: '2024-01-06 10:00:00',
    planStartTime: '2024-01-11 09:00:00',
    planEndTime: '2024-01-15 18:00:00',
    actualStartTime: null,
    actualEndTime: null,
    progress: 0,
    projectId: 1,
    projectName: '电商平台项目',
    tags: [
      {
        taskTagId: 4,
        taskTagName: '低优先级',
        taskTagColor: '#67C23A',
      },
      {
        taskTagId: 7,
        taskTagName: '测试任务',
        taskTagColor: '#67C23A',
      },
    ],
  },
  {
    taskId: 1008,
    taskName: '用户体验优化',
    taskDesc: '根据用户反馈优化界面交互',
    taskStatus: 3, // 已完成
    responsibleUserId: 4,
    responsibleUserName: '赵六',
    createTime: '2024-01-02 15:00:00',
    planStartTime: '2024-01-02 15:00:00',
    planEndTime: '2024-01-04 18:00:00',
    actualStartTime: '2024-01-02 15:00:00',
    actualEndTime: '2024-01-04 17:00:00',
    progress: 100,
    projectId: 1,
    projectName: '电商平台项目',
    tags: [
      {
        taskTagId: 3,
        taskTagName: '中优先级',
        taskTagColor: '#E6A23C',
      },
      {
        taskTagId: 8,
        taskTagName: '设计任务',
        taskTagColor: '#E6A23C',
      },
    ],
  },
];

// 任务类型枚举
const TASK_TYPE = {
  MILESTONE: 1, // 里程碑作业
  TASK: 2, // 任务作业
  WBS: 3, // WBS作业
  COOPERATION: 4, // 配合作业
};

// 项目树数据 - 符合 ProjectTreeNode 类型
const PROJECT_TREE_DATA = [
  {
    tenantId: 1,
    projId: 1001,
    projCode: 'PROJ-001',
    parentProjId: null,
    projName: '电商平台升级项目',
    projType: 1,
    projLevel: 1,
    projCategory: 1,
    planStartDate: '2024-01-01',
    planFinishDate: '2024-06-30',
    actualStartDate: '2024-01-01',
    actualFinishDate: null,
    overdueDays: 0,
    actualProgress: 65,
    planProgress: 70,
    status: TASK_STATUS.IN_PROGRESS,
    responsible: 5,
    responsibleName: '项目经理',
    deptId: 5,
    deptName: '项目管理部',
    createUser: 5,
    createTime: '2023-12-15T08:00:00Z',
    updateUser: 5,
    updateTime: '2024-01-15T10:30:00Z',
    remark: '重要项目，优先级高',
    children: [
      {
        tenantId: 1,
        projId: 1002,
        projCode: 'PROJ-001-01',
        parentProjId: 1001,
        projName: '前端模块升级',
        projType: 2,
        projLevel: 2,
        projCategory: 1,
        planStartDate: '2024-01-01',
        planFinishDate: '2024-03-31',
        actualStartDate: '2024-01-01',
        actualFinishDate: null,
        overdueDays: 0,
        actualProgress: 75,
        planProgress: 80,
        status: TASK_STATUS.IN_PROGRESS,
        responsible: 1,
        responsibleName: '张三',
        deptId: 1,
        deptName: '前端开发组',
        createUser: 5,
        createTime: '2023-12-15T08:00:00Z',
        updateUser: 1,
        updateTime: '2024-01-10T14:20:00Z',
        remark: '前端技术栈升级',
      },
      {
        tenantId: 1,
        projId: 1003,
        projCode: 'PROJ-001-02',
        parentProjId: 1001,
        projName: '后端API重构',
        projType: 2,
        projLevel: 2,
        projCategory: 1,
        planStartDate: '2024-02-01',
        planFinishDate: '2024-05-31',
        actualStartDate: '2024-02-01',
        actualFinishDate: null,
        overdueDays: 0,
        actualProgress: 55,
        planProgress: 60,
        status: TASK_STATUS.IN_PROGRESS,
        responsible: 2,
        responsibleName: '李四',
        deptId: 2,
        deptName: '后端开发组',
        createUser: 5,
        createTime: '2023-12-15T08:00:00Z',
        updateUser: 2,
        updateTime: '2024-02-05T09:15:00Z',
        remark: 'API架构优化',
      },
    ],
  },
  {
    tenantId: 1,
    projId: 2001,
    projCode: 'PROJ-002',
    parentProjId: null,
    projName: '移动端应用开发',
    projType: 1,
    projLevel: 1,
    projCategory: 2,
    planStartDate: '2024-02-01',
    planFinishDate: '2024-08-31',
    actualStartDate: '2024-02-01',
    actualFinishDate: null,
    overdueDays: 0,
    actualProgress: 30,
    planProgress: 35,
    status: TASK_STATUS.IN_PROGRESS,
    responsible: 4,
    responsibleName: '赵六',
    deptId: 4,
    deptName: '设计组',
    createUser: 5,
    createTime: '2024-01-10T08:00:00Z',
    updateUser: 4,
    updateTime: '2024-02-10T11:45:00Z',
    remark: '移动端产品线',
  },
  {
    tenantId: 1,
    projId: 3001,
    projCode: 'PROJ-003',
    parentProjId: null,
    projName: '数据分析平台',
    projType: 1,
    projLevel: 1,
    projCategory: 3,
    planStartDate: '2024-03-01',
    planFinishDate: '2024-12-31',
    actualStartDate: null,
    actualFinishDate: null,
    overdueDays: 0,
    actualProgress: 0,
    planProgress: 0,
    status: TASK_STATUS.NOT_STARTED,
    responsible: 6,
    responsibleName: '技术总监',
    deptId: 1,
    deptName: '前端开发组',
    createUser: 5,
    createTime: '2024-02-20T08:00:00Z',
    updateUser: 5,
    updateTime: '2024-02-20T08:00:00Z',
    remark: '大数据分析系统',
  },
];

// 项目任务数据 - 符合 ProjectTaskResponse 类型
const PROJECT_TASKS_DATA = [
  // 电商平台升级项目的任务
  {
    tenantId: 1,
    projTaskId: 10001,
    parentProjTaskId: null,
    projId: 1001,
    phaseProjTaskId: null,
    projTaskName: '需求分析与设计',
    projTaskStatus: TASK_STATUS.CLOSED,
    projTaskResponsible: 5,
    projTaskResponsibleProjRole: '项目经理',
    projTaskResponsibleDept: 5,
    planProgress: 100,
    actualProgress: 100,
    overdueDays: 0,
    taskType: TASK_TYPE.MILESTONE,
    taskActivityType: 1,
    planStartDate: '2024-01-01',
    actualStartDate: '2024-01-01',
    planFinishDate: '2024-01-15',
    actualFinishDate: '2024-01-14',
    duration: 14,
    workflowId: 'workflow_001',
    phaseFlag: 1,
    autoProgressFlag: 0,
    createTime: '2023-12-20T08:00:00Z',
    updateTime: '2024-01-14T18:00:00Z',
    createUser: 5,
    updateUser: 5,
    deleted: 0,
    remark: '项目启动阶段',
    responsibleName: '项目经理',
    projectName: '电商平台升级项目',
    major: '软件开发',
    level: 1,
    index: 1,
    taskNumber: '1',
    tags: [
      {
        taskTagId: 2,
        taskTagName: '高优先级',
        taskTagColor: '#F56C6C',
      },
      {
        taskTagId: 1,
        taskTagName: '紧急',
        taskTagColor: '#F56C6C',
      },
    ],
    children: [
      {
        tenantId: 1,
        projTaskId: 10002,
        parentProjTaskId: 10001,
        projId: 1001,
        phaseProjTaskId: 10001,
        projTaskName: '业务需求调研',
        projTaskStatus: TASK_STATUS.CLOSED,
        projTaskResponsible: 1,
        projTaskResponsibleProjRole: '前端负责人',
        projTaskResponsibleDept: 1,
        planProgress: 100,
        actualProgress: 100,
        overdueDays: 0,
        taskType: TASK_TYPE.TASK,
        taskActivityType: 2,
        planStartDate: '2024-01-02',
        actualStartDate: '2024-01-02',
        planFinishDate: '2024-01-08',
        actualFinishDate: '2024-01-07',
        duration: 6,
        workflowId: 'workflow_002',
        phaseFlag: 0,
        autoProgressFlag: 1,
        createTime: '2023-12-20T08:00:00Z',
        updateTime: '2024-01-07T17:30:00Z',
        createUser: 5,
        updateUser: 1,
        deleted: 0,
        remark: '用户需求收集',
        responsibleName: '张三',
        projectName: '电商平台升级项目',
        major: '软件开发',
        level: 2,
        index: 1,
        taskNumber: '1.1',
        tags: [
          {
            taskTagId: 6,
            taskTagName: '开发任务',
            taskTagColor: '#409EFF',
          },
          {
            taskTagId: 3,
            taskTagName: '中优先级',
            taskTagColor: '#E6A23C',
          },
        ],
      },
    ],
  },
  {
    tenantId: 1,
    projTaskId: 10004,
    parentProjTaskId: null,
    projId: 1002,
    phaseProjTaskId: null,
    projTaskName: '前端页面开发',
    projTaskStatus: TASK_STATUS.IN_PROGRESS,
    projTaskResponsible: 1,
    projTaskResponsibleProjRole: '前端负责人',
    projTaskResponsibleDept: 1,
    planProgress: 80,
    actualProgress: 75,
    overdueDays: 0,
    taskType: TASK_TYPE.TASK,
    taskActivityType: 2,
    planStartDate: '2024-01-16',
    actualStartDate: '2024-01-16',
    planFinishDate: '2024-03-15',
    actualFinishDate: null,
    duration: 59,
    workflowId: 'workflow_004',
    phaseFlag: 0,
    autoProgressFlag: 1,
    createTime: '2024-01-10T08:00:00Z',
    updateTime: '2024-02-20T14:30:00Z',
    createUser: 5,
    updateUser: 1,
    deleted: 0,
    remark: '前端界面实现',
    responsibleName: '张三',
    projectName: '前端模块升级',
    major: '前端开发',
    level: 1,
    index: 2,
    taskNumber: '2',
    tags: [
      {
        taskTagId: 6,
        taskTagName: '开发任务',
        taskTagColor: '#409EFF',
      },
      {
        taskTagId: 2,
        taskTagName: '高优先级',
        taskTagColor: '#F56C6C',
      },
    ],
  },
  {
    tenantId: 1,
    projTaskId: 10005,
    parentProjTaskId: null,
    projId: 2001,
    phaseProjTaskId: null,
    projTaskName: 'UI设计规范制定',
    projTaskStatus: TASK_STATUS.IN_PROGRESS,
    projTaskResponsible: 4,
    projTaskResponsibleProjRole: '设计负责人',
    projTaskResponsibleDept: 4,
    planProgress: 50,
    actualProgress: 45,
    overdueDays: 0,
    taskType: TASK_TYPE.TASK,
    taskActivityType: 2,
    planStartDate: '2024-02-01',
    actualStartDate: '2024-02-01',
    planFinishDate: '2024-02-20',
    actualFinishDate: null,
    duration: 19,
    workflowId: 'workflow_005',
    phaseFlag: 0,
    autoProgressFlag: 1,
    createTime: '2024-01-25T08:00:00Z',
    updateTime: '2024-02-10T15:20:00Z',
    createUser: 5,
    updateUser: 4,
    deleted: 0,
    remark: '移动端设计规范',
    responsibleName: '赵六',
    projectName: '移动端应用开发',
    major: 'UI设计',
    level: 1,
    index: 3,
    taskNumber: '3',
    tags: [
      {
        taskTagId: 8,
        taskTagName: '设计任务',
        taskTagColor: '#E6A23C',
      },
      {
        taskTagId: 3,
        taskTagName: '中优先级',
        taskTagColor: '#E6A23C',
      },
    ],
  },
  {
    tenantId: 1,
    projTaskId: 10006,
    parentProjTaskId: null,
    projId: 3001,
    phaseProjTaskId: null,
    projTaskName: '数据分析需求调研',
    projTaskStatus: TASK_STATUS.NOT_STARTED,
    projTaskResponsible: 6,
    projTaskResponsibleProjRole: '技术总监',
    projTaskResponsibleDept: 1,
    planProgress: 0,
    actualProgress: 0,
    overdueDays: 0,
    taskType: TASK_TYPE.MILESTONE,
    taskActivityType: 1,
    planStartDate: '2024-03-01',
    actualStartDate: null,
    planFinishDate: '2024-03-15',
    actualFinishDate: null,
    duration: 14,
    workflowId: 'workflow_006',
    phaseFlag: 1,
    autoProgressFlag: 0,
    createTime: '2024-02-20T08:00:00Z',
    updateTime: '2024-02-20T08:00:00Z',
    createUser: 5,
    updateUser: 5,
    deleted: 0,
    remark: '数据分析平台启动',
    responsibleName: '技术总监',
    projectName: '数据分析平台',
    major: '数据分析',
    level: 1,
    index: 4,
    taskNumber: '4',
  },
];

// 独立任务数据 - 符合 IndependentTaskResponse 类型，包含完整的父子关系和层级结构
const INDEPENDENT_TASKS_DATA = [
  // 主任务1：代码质量检查 - 包含子任务
  {
    tenantId: 1,
    indeTaskId: 20001,
    parentIndeTaskId: null,
    indeTaskName: '代码质量检查',
    indeTaskStatus: INDEPENDENT_TASK_STATUS.IN_PROGRESS,
    indeTaskResponsible: 1,
    responsibleName: '张三',
    indeTaskResponsibleDept: 1,
    indeTaskType: TASK_TYPE.TASK,
    planStartDate: '2024-01-01',
    actualStartDate: '2024-01-01',
    planFinishDate: '2024-01-10',
    actualFinishDate: null,
    planProgress: 60,
    actualProgress: 55,
    overdueDays: 0,
    duration: 9,
    workflowId: 'workflow_quality_check',
    autoProgressFlag: 0,
    indeTaskDesc: '对项目代码进行全面的质量检查和优化',
    indeTaskAcceptCriteria: '代码质量达到团队标准，无严重问题',
    createTime: '2023-12-25T08:00:00Z',
    updateTime: '2024-01-05T14:30:00Z',
    createUser: 1,
    updateUser: 1,
    deleted: 0,
    remark: '定期代码质量检查任务',
    level: 1,
    index: 1,
    taskNumber: '1',
    children: [
      {
        tenantId: 1,
        indeTaskId: 20011,
        parentIndeTaskId: 20001,
        indeTaskName: '前端代码审查',
        indeTaskStatus: INDEPENDENT_TASK_STATUS.CLOSED,
        indeTaskResponsible: 1,
        responsibleName: '张三',
        indeTaskResponsibleDept: 1,
        indeTaskType: TASK_TYPE.TASK,
        planStartDate: '2024-01-01',
        actualStartDate: '2024-01-01',
        planFinishDate: '2024-01-03',
        actualFinishDate: '2024-01-03',
        planProgress: 100,
        actualProgress: 100,
        overdueDays: 0,
        duration: 2,
        workflowId: 'workflow_frontend_review',
        autoProgressFlag: 0,
        indeTaskDesc: '审查前端代码规范和质量',
        indeTaskAcceptCriteria: '前端代码符合ESLint规范',
        createTime: '2023-12-25T08:00:00Z',
        updateTime: '2024-01-03T18:00:00Z',
        createUser: 1,
        updateUser: 1,
        deleted: 0,
        remark: '前端代码质量检查',
        level: 2,
        index: 1,
        taskNumber: '1.1',
        children: [],
      },
      {
        tenantId: 1,
        indeTaskId: 20012,
        parentIndeTaskId: 20001,
        indeTaskName: '后端代码审查',
        indeTaskStatus: INDEPENDENT_TASK_STATUS.IN_PROGRESS,
        indeTaskResponsible: 2,
        responsibleName: '李四',
        indeTaskResponsibleDept: 2,
        indeTaskType: TASK_TYPE.TASK,
        planStartDate: '2024-01-02',
        actualStartDate: '2024-01-02',
        planFinishDate: '2024-01-05',
        actualFinishDate: null,
        planProgress: 60,
        actualProgress: 45,
        overdueDays: 0,
        duration: 3,
        workflowId: 'workflow_backend_review',
        autoProgressFlag: 0,
        indeTaskDesc: '审查后端代码规范和质量',
        indeTaskAcceptCriteria: '后端代码符合团队开发规范',
        createTime: '2023-12-25T08:00:00Z',
        updateTime: '2024-01-05T14:30:00Z',
        createUser: 1,
        updateUser: 2,
        deleted: 0,
        remark: '后端代码质量检查',
        level: 2,
        index: 2,
        taskNumber: '1.2',
        children: [],
      },
      {
        tenantId: 1,
        indeTaskId: 20013,
        parentIndeTaskId: 20001,
        indeTaskName: '单元测试覆盖率检查',
        indeTaskStatus: INDEPENDENT_TASK_STATUS.NOT_STARTED,
        indeTaskResponsible: 3,
        responsibleName: '王五',
        indeTaskResponsibleDept: 3,
        indeTaskType: TASK_TYPE.TASK,
        planStartDate: '2024-01-06',
        actualStartDate: null,
        planFinishDate: '2024-01-08',
        actualFinishDate: null,
        planProgress: 0,
        actualProgress: 0,
        overdueDays: 0,
        duration: 2,
        workflowId: 'workflow_test_coverage',
        autoProgressFlag: 0,
        indeTaskDesc: '检查单元测试覆盖率是否达标',
        indeTaskAcceptCriteria: '单元测试覆盖率达到80%以上',
        createTime: '2023-12-25T08:00:00Z',
        updateTime: '2023-12-25T08:00:00Z',
        createUser: 1,
        updateUser: 1,
        deleted: 0,
        remark: '单元测试覆盖率检查',
        level: 2,
        index: 3,
        taskNumber: '1.3',
        children: [],
      },
    ],
  },

  // 主任务2：技术文档编写 - 包含子任务
  {
    tenantId: 1,
    indeTaskId: 20002,
    parentIndeTaskId: null,
    indeTaskName: '技术文档编写',
    indeTaskStatus: INDEPENDENT_TASK_STATUS.NOT_STARTED,
    indeTaskResponsible: 2,
    responsibleName: '李四',
    indeTaskResponsibleDept: 2,
    indeTaskType: TASK_TYPE.MILESTONE,
    planStartDate: '2024-01-15',
    actualStartDate: null,
    planFinishDate: '2024-01-25',
    actualFinishDate: null,
    planProgress: 0,
    actualProgress: 0,
    overdueDays: 0,
    duration: 10,
    workflowId: 'workflow_documentation',
    autoProgressFlag: 0,
    indeTaskDesc: '编写项目相关的技术文档',
    indeTaskAcceptCriteria: '文档内容完整，格式规范',
    createTime: '2024-01-01T08:00:00Z',
    updateTime: '2024-01-01T08:00:00Z',
    createUser: 5,
    updateUser: 5,
    deleted: 0,
    remark: 'API接口文档编写',
    level: 1,
    index: 2,
    taskNumber: '2',
    children: [
      {
        tenantId: 1,
        indeTaskId: 20021,
        parentIndeTaskId: 20002,
        indeTaskName: 'API接口文档',
        indeTaskStatus: INDEPENDENT_TASK_STATUS.NOT_STARTED,
        indeTaskResponsible: 2,
        responsibleName: '李四',
        indeTaskResponsibleDept: 2,
        indeTaskType: TASK_TYPE.TASK,
        planStartDate: '2024-01-15',
        actualStartDate: null,
        planFinishDate: '2024-01-18',
        actualFinishDate: null,
        planProgress: 0,
        actualProgress: 0,
        overdueDays: 0,
        duration: 3,
        workflowId: 'workflow_api_doc',
        autoProgressFlag: 0,
        indeTaskDesc: '编写API接口详细文档',
        indeTaskAcceptCriteria: 'API文档包含所有接口说明',
        createTime: '2024-01-01T08:00:00Z',
        updateTime: '2024-01-01T08:00:00Z',
        createUser: 5,
        updateUser: 5,
        deleted: 0,
        remark: 'API接口文档编写',
        level: 2,
        index: 1,
        taskNumber: '2.1',
        children: [],
      },
      {
        tenantId: 1,
        indeTaskId: 20022,
        parentIndeTaskId: 20002,
        indeTaskName: '数据库设计文档',
        indeTaskStatus: INDEPENDENT_TASK_STATUS.NOT_STARTED,
        indeTaskResponsible: 2,
        responsibleName: '李四',
        indeTaskResponsibleDept: 2,
        indeTaskType: TASK_TYPE.TASK,
        planStartDate: '2024-01-19',
        actualStartDate: null,
        planFinishDate: '2024-01-22',
        actualFinishDate: null,
        planProgress: 0,
        actualProgress: 0,
        overdueDays: 0,
        duration: 3,
        workflowId: 'workflow_db_doc',
        autoProgressFlag: 0,
        indeTaskDesc: '编写数据库设计文档',
        indeTaskAcceptCriteria: '数据库文档包含所有表结构说明',
        createTime: '2024-01-01T08:00:00Z',
        updateTime: '2024-01-01T08:00:00Z',
        createUser: 5,
        updateUser: 5,
        deleted: 0,
        remark: '数据库设计文档',
        level: 2,
        index: 2,
        taskNumber: '2.2',
        children: [],
      },
    ],
  },

  // 主任务3：性能优化分析 - 已完成的任务
  {
    tenantId: 1,
    indeTaskId: 20003,
    parentIndeTaskId: null,
    indeTaskName: '性能优化分析',
    indeTaskStatus: INDEPENDENT_TASK_STATUS.CLOSED,
    indeTaskResponsible: 6,
    responsibleName: '技术总监',
    indeTaskResponsibleDept: 1,
    indeTaskType: TASK_TYPE.MILESTONE,
    planStartDate: '2023-12-20',
    actualStartDate: '2023-12-20',
    planFinishDate: '2023-12-30',
    actualFinishDate: '2023-12-28',
    planProgress: 100,
    actualProgress: 100,
    overdueDays: 0,
    duration: 8,
    workflowId: 'workflow_performance',
    autoProgressFlag: 0,
    indeTaskDesc: '对系统进行全面的性能分析和优化',
    indeTaskAcceptCriteria: '系统性能提升20%以上',
    createTime: '2023-12-15T08:00:00Z',
    updateTime: '2023-12-28T17:00:00Z',
    createUser: 5,
    updateUser: 6,
    deleted: 0,
    remark: '系统性能分析和优化建议',
    level: 1,
    index: 3,
    taskNumber: '3',
    children: [
      {
        tenantId: 1,
        indeTaskId: 20031,
        parentIndeTaskId: 20003,
        indeTaskName: '前端性能分析',
        indeTaskStatus: INDEPENDENT_TASK_STATUS.CLOSED,
        indeTaskResponsible: 6,
        responsibleName: '技术总监',
        indeTaskResponsibleDept: 1,
        indeTaskType: TASK_TYPE.TASK,
        planStartDate: '2023-12-20',
        actualStartDate: '2023-12-20',
        planFinishDate: '2023-12-23',
        actualFinishDate: '2023-12-22',
        planProgress: 100,
        actualProgress: 100,
        overdueDays: 0,
        duration: 2,
        workflowId: 'workflow_frontend_perf',
        autoProgressFlag: 0,
        indeTaskDesc: '分析前端页面加载性能',
        indeTaskAcceptCriteria: '前端页面加载时间优化30%',
        createTime: '2023-12-15T08:00:00Z',
        updateTime: '2023-12-22T17:00:00Z',
        createUser: 5,
        updateUser: 6,
        deleted: 0,
        remark: '前端性能优化分析',
        level: 2,
        index: 1,
        taskNumber: '3.1',
        children: [],
      },
      {
        tenantId: 1,
        indeTaskId: 20032,
        parentIndeTaskId: 20003,
        indeTaskName: '后端接口性能分析',
        indeTaskStatus: INDEPENDENT_TASK_STATUS.CLOSED,
        indeTaskResponsible: 2,
        responsibleName: '李四',
        indeTaskResponsibleDept: 2,
        indeTaskType: TASK_TYPE.TASK,
        planStartDate: '2023-12-24',
        actualStartDate: '2023-12-24',
        planFinishDate: '2023-12-27',
        actualFinishDate: '2023-12-26',
        planProgress: 100,
        actualProgress: 100,
        overdueDays: 0,
        duration: 2,
        workflowId: 'workflow_backend_perf',
        autoProgressFlag: 0,
        indeTaskDesc: '分析后端接口响应性能',
        indeTaskAcceptCriteria: '接口响应时间优化50%',
        createTime: '2023-12-15T08:00:00Z',
        updateTime: '2023-12-26T17:00:00Z',
        createUser: 5,
        updateUser: 2,
        deleted: 0,
        remark: '后端接口性能优化分析',
        level: 2,
        index: 2,
        taskNumber: '3.2',
        children: [],
      },
    ],
  },

  // 主任务4：安全漏洞扫描 - 包含子任务
  {
    tenantId: 1,
    indeTaskId: 20004,
    parentIndeTaskId: null,
    indeTaskName: '安全漏洞扫描',
    indeTaskStatus: INDEPENDENT_TASK_STATUS.IN_PROGRESS,
    indeTaskResponsible: 3,
    responsibleName: '王五',
    indeTaskResponsibleDept: 3,
    indeTaskType: TASK_TYPE.TASK,
    planStartDate: '2024-01-08',
    actualStartDate: '2024-01-08',
    planFinishDate: '2024-01-18',
    actualFinishDate: null,
    planProgress: 40,
    actualProgress: 35,
    overdueDays: 0,
    duration: 10,
    workflowId: 'workflow_security_scan',
    autoProgressFlag: 0,
    indeTaskDesc: '对系统进行全面的安全漏洞扫描',
    indeTaskAcceptCriteria: '发现并修复所有高危漏洞',
    createTime: '2024-01-05T08:00:00Z',
    updateTime: '2024-01-12T16:20:00Z',
    createUser: 1,
    updateUser: 3,
    deleted: 0,
    remark: '系统安全性检查',
    level: 1,
    index: 4,
    taskNumber: '4',
    hasChildren: true,
    children: [
      {
        tenantId: 1,
        indeTaskId: 20041,
        parentIndeTaskId: 20004,
        indeTaskName: 'SQL注入检测',
        indeTaskStatus: INDEPENDENT_TASK_STATUS.CLOSED,
        indeTaskResponsible: 3,
        responsibleName: '王五',
        indeTaskResponsibleDept: 3,
        indeTaskType: TASK_TYPE.TASK,
        planStartDate: '2024-01-08',
        actualStartDate: '2024-01-08',
        planFinishDate: '2024-01-10',
        actualFinishDate: '2024-01-10',
        planProgress: 100,
        actualProgress: 100,
        overdueDays: 0,
        duration: 2,
        workflowId: 'workflow_sql_injection',
        autoProgressFlag: 0,
        indeTaskDesc: '检测系统中的SQL注入漏洞',
        indeTaskAcceptCriteria: '无SQL注入漏洞',
        createTime: '2024-01-05T08:00:00Z',
        updateTime: '2024-01-10T18:00:00Z',
        createUser: 1,
        updateUser: 3,
        deleted: 0,
        remark: 'SQL注入安全检测',
        level: 2,
        index: 1,
        taskNumber: '4.1',
        hasChildren: false,
        children: [],
      },
      {
        tenantId: 1,
        indeTaskId: 20042,
        parentIndeTaskId: 20004,
        indeTaskName: 'XSS漏洞检测',
        indeTaskStatus: INDEPENDENT_TASK_STATUS.IN_PROGRESS,
        indeTaskResponsible: 3,
        responsibleName: '王五',
        indeTaskResponsibleDept: 3,
        indeTaskType: TASK_TYPE.TASK,
        planStartDate: '2024-01-11',
        actualStartDate: '2024-01-11',
        planFinishDate: '2024-01-13',
        actualFinishDate: null,
        planProgress: 60,
        actualProgress: 50,
        overdueDays: 0,
        duration: 2,
        workflowId: 'workflow_xss_detection',
        autoProgressFlag: 0,
        indeTaskDesc: '检测系统中的XSS跨站脚本漏洞',
        indeTaskAcceptCriteria: '无XSS漏洞',
        createTime: '2024-01-05T08:00:00Z',
        updateTime: '2024-01-12T16:20:00Z',
        createUser: 1,
        updateUser: 3,
        deleted: 0,
        remark: 'XSS漏洞安全检测',
        level: 2,
        index: 2,
        taskNumber: '4.2',
        hasChildren: false,
        children: [],
      },
    ],
  },

  // 主任务5：UI组件库维护 - 包含子任务
  {
    tenantId: 1,
    indeTaskId: 20005,
    parentIndeTaskId: null,
    indeTaskName: 'UI组件库维护',
    indeTaskStatus: INDEPENDENT_TASK_STATUS.NOT_STARTED,
    indeTaskResponsible: 4,
    responsibleName: '赵六',
    indeTaskResponsibleDept: 4,
    indeTaskType: TASK_TYPE.MILESTONE,
    planStartDate: '2024-02-01',
    actualStartDate: null,
    planFinishDate: '2024-02-15',
    actualFinishDate: null,
    planProgress: 0,
    actualProgress: 0,
    overdueDays: 0,
    duration: 14,
    workflowId: 'workflow_ui_maintenance',
    autoProgressFlag: 0,
    indeTaskDesc: '维护和更新UI组件库',
    indeTaskAcceptCriteria: '组件库功能完善，文档齐全',
    createTime: '2024-01-20T08:00:00Z',
    updateTime: '2024-01-20T08:00:00Z',
    createUser: 6,
    updateUser: 6,
    deleted: 0,
    remark: '组件库更新和维护',
    level: 1,
    index: 5,
    taskNumber: '5',
    hasChildren: true,
    children: [
      {
        tenantId: 1,
        indeTaskId: 20051,
        parentIndeTaskId: 20005,
        indeTaskName: '按钮组件优化',
        indeTaskStatus: INDEPENDENT_TASK_STATUS.NOT_STARTED,
        indeTaskResponsible: 4,
        responsibleName: '赵六',
        indeTaskResponsibleDept: 4,
        indeTaskType: TASK_TYPE.TASK,
        planStartDate: '2024-02-01',
        actualStartDate: null,
        planFinishDate: '2024-02-05',
        actualFinishDate: null,
        planProgress: 0,
        actualProgress: 0,
        overdueDays: 0,
        duration: 4,
        workflowId: 'workflow_button_optimize',
        autoProgressFlag: 0,
        indeTaskDesc: '优化按钮组件的样式和交互',
        indeTaskAcceptCriteria: '按钮组件符合设计规范',
        createTime: '2024-01-20T08:00:00Z',
        updateTime: '2024-01-20T08:00:00Z',
        createUser: 6,
        updateUser: 6,
        deleted: 0,
        remark: '按钮组件优化',
        level: 2,
        index: 1,
        taskNumber: '5.1',
        hasChildren: false,
        children: [],
      },
      {
        tenantId: 1,
        indeTaskId: 20052,
        parentIndeTaskId: 20005,
        indeTaskName: '表格组件更新',
        indeTaskStatus: INDEPENDENT_TASK_STATUS.NOT_STARTED,
        indeTaskResponsible: 1,
        responsibleName: '张三',
        indeTaskResponsibleDept: 1,
        indeTaskType: TASK_TYPE.TASK,
        planStartDate: '2024-02-06',
        actualStartDate: null,
        planFinishDate: '2024-02-10',
        actualFinishDate: null,
        planProgress: 0,
        actualProgress: 0,
        overdueDays: 0,
        duration: 4,
        workflowId: 'workflow_table_update',
        autoProgressFlag: 0,
        indeTaskDesc: '更新表格组件功能',
        indeTaskAcceptCriteria: '表格组件功能完善',
        createTime: '2024-01-20T08:00:00Z',
        updateTime: '2024-01-20T08:00:00Z',
        createUser: 6,
        updateUser: 6,
        deleted: 0,
        remark: '表格组件功能更新',
        level: 2,
        index: 2,
        taskNumber: '5.2',
        hasChildren: false,
        children: [],
      },
    ],
  },
];

// 我的任务数据 - 符合 ProjectTaskResponse 类型，包含项目任务和独立任务的混合数据
const MY_TASKS_DATA = [
  // 项目任务1：用户登录功能开发 - 包含子任务
  {
    tenantId: 1,
    projTaskId: 20001,
    parentProjTaskId: null,
    projId: 1001,
    phaseProjTaskId: null,
    projTaskName: '用户登录功能开发',
    projTaskStatus: TASK_STATUS.IN_PROGRESS,
    projTaskResponsible: 1,
    projTaskResponsibleProjRole: '前端开发工程师',
    projTaskResponsibleDept: 1,
    planProgress: 60,
    actualProgress: 45,
    overdueDays: 0,
    taskType: TASK_TYPE.TASK,
    taskActivityType: 1,
    major: '前端开发',
    systemId: 1,
    projTaskDesc: '实现用户登录功能，包括前端页面和后端接口',
    projTaskAcceptCriteria: '登录功能正常，界面美观，安全性符合要求',
    planStartDate: '2024-01-01',
    actualStartDate: '2024-01-01',
    planFinishDate: '2024-01-15',
    duration: 14,
    workflowId: 'workflow_login',
    phaseFlag: 0,
    autoProgressFlag: 0,
    actualFinishDate: null,
    createTime: '2024-01-01T08:00:00Z',
    updateTime: '2024-01-08T10:30:00Z',
    createUser: 5,
    updateUser: 1,
    deleted: 0,
    remark: '重要功能模块',
    responsibleName: '张三',
    projectName: '电商平台升级项目',
    level: 1,
    index: 1,
    taskNumber: '1',
    children: [
      {
        tenantId: 1,
        projTaskId: 20011,
        parentProjTaskId: 20001,
        projId: 1001,
        phaseProjTaskId: null,
        projTaskName: '登录页面UI设计',
        projTaskStatus: TASK_STATUS.CLOSED,
        projTaskResponsible: 1,
        projTaskResponsibleProjRole: '前端开发工程师',
        projTaskResponsibleDept: 1,
        planProgress: 100,
        actualProgress: 100,
        overdueDays: 0,
        taskType: TASK_TYPE.TASK,
        taskActivityType: 1,
        major: '前端开发',
        systemId: 1,
        projTaskDesc: '设计用户登录页面的UI界面',
        projTaskAcceptCriteria: 'UI设计符合产品要求',
        planStartDate: '2024-01-01',
        actualStartDate: '2024-01-01',
        planFinishDate: '2024-01-03',
        actualFinishDate: '2024-01-03',
        duration: 2,
        workflowId: 'workflow_login_ui',
        phaseFlag: 0,
        autoProgressFlag: 0,
        createTime: '2024-01-01T08:00:00Z',
        updateTime: '2024-01-03T18:00:00Z',
        createUser: 5,
        updateUser: 1,
        deleted: 0,
        remark: '登录页面UI设计',
        responsibleName: '张三',
        projectName: '电商平台升级项目',
        level: 2,
        index: 1,
        taskNumber: '1.1',
        children: [],
      },
      {
        tenantId: 1,
        projTaskId: 20012,
        parentProjTaskId: 20001,
        projId: 1001,
        phaseProjTaskId: null,
        projTaskName: '登录接口开发',
        projTaskStatus: TASK_STATUS.IN_PROGRESS,
        projTaskResponsible: 2,
        projTaskResponsibleProjRole: '后端开发工程师',
        projTaskResponsibleDept: 2,
        planProgress: 70,
        actualProgress: 60,
        overdueDays: 0,
        taskType: TASK_TYPE.TASK,
        taskActivityType: 1,
        major: '后端开发',
        systemId: 1,
        projTaskDesc: '开发用户登录的后端接口',
        projTaskAcceptCriteria: '接口功能完整，安全性符合要求',
        planStartDate: '2024-01-04',
        actualStartDate: '2024-01-04',
        planFinishDate: '2024-01-08',
        actualFinishDate: null,
        duration: 4,
        workflowId: 'workflow_login_api',
        phaseFlag: 0,
        autoProgressFlag: 0,
        createTime: '2024-01-01T08:00:00Z',
        updateTime: '2024-01-08T10:30:00Z',
        createUser: 5,
        updateUser: 2,
        deleted: 0,
        remark: '登录接口开发',
        responsibleName: '李四',
        projectName: '电商平台升级项目',
        level: 2,
        index: 2,
        taskNumber: '1.2',
        children: [],
      },
    ],
  },

  // 项目任务2：数据库设计评审
  {
    tenantId: 1,
    projTaskId: 20002,
    parentProjTaskId: null,
    projId: 1001,
    phaseProjTaskId: null,
    projTaskName: '数据库设计评审',
    projTaskStatus: TASK_STATUS.IN_PROGRESS,
    projTaskResponsible: 1,
    projTaskResponsibleProjRole: '前端开发工程师',
    projTaskResponsibleDept: 1,
    planProgress: 40,
    actualProgress: 30,
    overdueDays: 0,
    taskType: TASK_TYPE.TASK,
    taskActivityType: 1,
    major: '后端开发',
    systemId: 1,
    projTaskDesc: '评审数据库表结构设计',
    projTaskAcceptCriteria: '数据库设计文档完整，表结构合理',
    planStartDate: '2024-01-05',
    actualStartDate: '2024-01-05',
    planFinishDate: '2024-01-20',
    duration: 15,
    workflowId: 'workflow_db',
    phaseFlag: 0,
    autoProgressFlag: 0,
    actualFinishDate: null,
    createTime: '2024-01-05T09:00:00Z',
    updateTime: '2024-01-08T14:20:00Z',
    createUser: 5,
    updateUser: 1,
    deleted: 0,
    remark: '核心数据设计',
    responsibleName: '张三',
    projectName: '电商平台升级项目',
    level: 1,
    index: 2,
    taskNumber: '2',
    children: [],
  },

  // 项目任务3：商品管理模块开发 - 包含子任务
  {
    tenantId: 1,
    projTaskId: 20003,
    parentProjTaskId: null,
    projId: 1002,
    phaseProjTaskId: null,
    projTaskName: '商品管理模块开发',
    projTaskStatus: TASK_STATUS.NOT_STARTED,
    projTaskResponsible: 1,
    projTaskResponsibleProjRole: '前端开发工程师',
    projTaskResponsibleDept: 1,
    planProgress: 0,
    actualProgress: 0,
    overdueDays: 0,
    taskType: TASK_TYPE.MILESTONE,
    taskActivityType: 1,
    major: '前端开发',
    systemId: 2,
    projTaskDesc: '开发商品管理相关功能模块',
    projTaskAcceptCriteria: '商品管理功能完整，操作流畅',
    planStartDate: '2024-01-05',
    actualStartDate: '2024-01-05',
    planFinishDate: '2024-02-10',
    duration: 21,
    workflowId: 'workflow_product',
    phaseFlag: 1,
    createTime: '2024-01-10T08:00:00Z',
    updateTime: '2024-01-10T08:00:00Z',
    createUser: 5,
    updateUser: 5,
    deleted: 0,
    remark: '重要业务模块',
    responsibleName: '张三',
    projectName: '前端模块升级',
    level: 1,
    index: 3,
    taskNumber: '3',
    children: [
      {
        tenantId: 1,
        projTaskId: 20031,
        parentProjTaskId: 20003,
        projId: 1002,
        phaseProjTaskId: null,
        projTaskName: '商品列表页面',
        projTaskStatus: TASK_STATUS.NOT_STARTED,
        projTaskResponsible: 1,
        projTaskResponsibleProjRole: '前端开发工程师',
        projTaskResponsibleDept: 1,
        planProgress: 0,
        actualProgress: 0,
        overdueDays: 0,
        taskType: TASK_TYPE.TASK,
        taskActivityType: 1,
        major: '前端开发',
        systemId: 2,
        projTaskDesc: '开发商品列表展示页面',
        projTaskAcceptCriteria: '列表功能完整，支持搜索和筛选',
        planStartDate: '2024-01-05',
        actualStartDate: '2024-01-05',
        planFinishDate: '2024-02-10',
        duration: 5,
        workflowId: 'workflow_product_list',
        phaseFlag: 0,
        autoProgressFlag: 0,
        createTime: '2024-01-10T08:00:00Z',
        updateTime: '2024-01-10T08:00:00Z',
        createUser: 5,
        updateUser: 5,
        deleted: 0,
        remark: '商品列表页面开发',
        responsibleName: '张三',
        projectName: '前端模块升级',
        level: 2,
        index: 1,
        taskNumber: '3.1',
        children: [],
      },
    ],
  },

  // 项目任务4：测试用例编写
  {
    tenantId: 1,
    projTaskId: 20004,
    parentProjTaskId: null,
    projId: 1003,
    phaseProjTaskId: null,
    projTaskName: '测试用例编写',
    projTaskStatus: TASK_STATUS.NOT_STARTED,
    projTaskResponsible: 1,
    projTaskResponsibleProjRole: '测试工程师',
    projTaskResponsibleDept: 3,
    planProgress: 0,
    actualProgress: 0,
    overdueDays: 0,
    taskType: TASK_TYPE.TASK,
    taskActivityType: 2,
    major: '软件测试',
    systemId: 3,
    projTaskDesc: '编写完整的测试用例',
    projTaskAcceptCriteria: '测试用例覆盖所有功能点',
    planStartDate: '2024-01-25',
    actualStartDate: null,
    planFinishDate: '2024-02-05',
    duration: 11,
    workflowId: 'workflow_test_case',
    phaseFlag: 0,
    autoProgressFlag: 0,
    actualFinishDate: null,
    createTime: '2024-01-20T08:00:00Z',
    updateTime: '2024-01-20T08:00:00Z',
    createUser: 5,
    updateUser: 5,
    deleted: 0,
    remark: '测试用例编写任务',
    responsibleName: '张三',
    projectName: '移动端应用开发',
    level: 1,
    index: 4,
    taskNumber: '4',
    hasChildren: false,
    children: [],
  },

  // 项目任务5：部署上线
  {
    tenantId: 1,
    projTaskId: 20005,
    parentProjTaskId: null,
    projId: 1004,
    phaseProjTaskId: null,
    projTaskName: '部署上线',
    projTaskStatus: TASK_STATUS.CLOSED,
    projTaskResponsible: 1,
    projTaskResponsibleProjRole: '运维工程师',
    projTaskResponsibleDept: 5,
    planProgress: 100,
    actualProgress: 100,
    overdueDays: 0,
    taskType: TASK_TYPE.MILESTONE,
    taskActivityType: 3,
    major: '系统运维',
    systemId: 4,
    projTaskDesc: '将系统部署到生产环境',
    projTaskAcceptCriteria: '系统正常运行，性能稳定',
    planStartDate: '2024-01-10',
    actualStartDate: '2024-01-10',
    planFinishDate: '2024-01-12',
    actualFinishDate: '2024-01-11',
    duration: 1,
    workflowId: 'workflow_deploy',
    phaseFlag: 1,
    autoProgressFlag: 0,
    createTime: '2024-01-08T08:00:00Z',
    updateTime: '2024-01-11T18:00:00Z',
    createUser: 5,
    updateUser: 1,
    deleted: 0,
    remark: '生产环境部署',
    responsibleName: '张三',
    projectName: '数据分析平台',
    level: 1,
    index: 5,
    taskNumber: '5',
    hasChildren: false,
    children: [],
  },
];

export default [
  // 获取项目树列表 - 符合实际API路径
  {
    url: '/projectmanage/project/tree',
    method: 'post',
    response: () => {
      return success({
        list: PROJECT_TREE_DATA,
        total: PROJECT_TREE_DATA.length,
      });
    },
  },

  // 获取项目任务列表 - 符合实际API路径和参数
  {
    url: RegExp('/projectmanage/project-task/page/\\d+/\\d+'),
    method: 'post',
    response: (req) => {
      // 解析URL参数
      const urlParts = req.url.split('/');
      const pageSize = parseInt(urlParts[urlParts.length - 2], 10);
      const pageNum = parseInt(urlParts[urlParts.length - 1], 10);

      // 解析请求体参数 - 基于 ProjectTaskQueryRequest
      const { queryFlag, projIds, projTaskName } = req.body || {};

      console.log('Mock接口 - 获取项目任务列表:', {
        url: req.url,
        pageSize,
        pageNum,
        queryFlag,
        projIds,
        projTaskName,
      });

      // 使用符合 ProjectTaskResponse 类型的数据 - 保持树形结构
      let filteredTasks = JSON.parse(JSON.stringify(PROJECT_TASKS_DATA)); // 深拷贝保持children

      console.log('原始任务数据数量:', PROJECT_TASKS_DATA.length);
      console.log('原始任务数据示例:', PROJECT_TASKS_DATA[0]);
      console.log(
        '第一个任务是否有children:',
        PROJECT_TASKS_DATA[0].children
          ? PROJECT_TASKS_DATA[0].children.length
          : 0
      );

      // 根据项目ID筛选任务
      if (projIds?.length) {
        filteredTasks = filteredTasks.filter((task) =>
          projIds.includes(task.projId)
        );
      }

      // 根据任务名称关键词筛选
      if (projTaskName && projTaskName.trim() !== '') {
        const searchKeyword = projTaskName.trim().toLowerCase();
        filteredTasks = filteredTasks.filter(
          (task) =>
            task.projTaskName?.toLowerCase().includes(searchKeyword) ||
            task.responsibleName?.toLowerCase().includes(searchKeyword)
        );
      }

      // 根据 queryFlag (标签页类型) 筛选任务
      if (queryFlag && queryFlag !== TAB_TYPE.ALL) {
        const currentUserId = CURRENT_USER_ID;

        filteredTasks = filteredTasks.filter((task) => {
          switch (queryFlag) {
            case TAB_TYPE.RESPONSIBLE:
              // 我负责的：当前用户是责任人
              return task.projTaskResponsible === currentUserId;
            case TAB_TYPE.PARTICIPANT:
              // 我参与的：当前用户在参与者列表中（这里简化处理）
              return task.projTaskResponsible !== currentUserId;
            case TAB_TYPE.ASSIGNED:
              // 我指派的：当前用户是创建者
              return task.createUser === currentUserId;
            case TAB_TYPE.TODO:
              // 我待办的：当前用户是责任人且任务未完成
              return (
                task.projTaskResponsible === currentUserId &&
                task.projTaskStatus !== TASK_STATUS.CLOSED
              );
            default:
              return true;
          }
        });
      }

      // 分页处理
      const total = filteredTasks.length;
      const startIndex = (pageNum - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedTasks = filteredTasks.slice(startIndex, endIndex);

      console.log('项目任务数据返回:', {
        total,
        currentPage: pageNum,
        pageSize,
        queryFlag,
        projIds,
        tasksCount: paginatedTasks.length,
      });

      return success({
        list: paginatedTasks,
        total,
        page: pageNum,
        pageSize,
        pages: Math.ceil(total / pageSize),
      });
    },
  },

  // 获取我的任务列表 - 符合实际API路径
  {
    url: RegExp('/projectmanage/my-task/list'),
    method: 'post',
    response: (req) => {
      // 解析URL参数
      const urlParts = req.url.split('/');
      const pageSize = parseInt(urlParts[urlParts.length - 2], 10) || 20;
      const curPage = parseInt(urlParts[urlParts.length - 1], 10) || 1;

      // 解析请求体参数
      const { keyword } = req.body || {};

      console.log('Mock接口 - 获取我的任务列表:', {
        url: req.url,
        pageSize,
        curPage,
        keyword,
      });

      // 使用已定义的我的任务数据

      // 根据关键词筛选任务
      let filteredTasks = [...MY_TASKS_DATA];
      if (keyword && keyword.trim() !== '') {
        const searchKeyword = keyword.trim().toLowerCase();
        filteredTasks = filteredTasks.filter(
          (task) =>
            task.projTaskName?.toLowerCase().includes(searchKeyword) ||
            task.projectName?.toLowerCase().includes(searchKeyword) ||
            task.responsibleName?.toLowerCase().includes(searchKeyword)
        );
      }

      // 分页处理
      const total = filteredTasks.length;
      const startIndex = (curPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedTasks = filteredTasks.slice(startIndex, endIndex);

      console.log('我的任务数据返回:', {
        total,
        currentPage: curPage,
        pageSize,
        keyword,
        tasksCount: paginatedTasks.length,
      });

      return success({
        list: paginatedTasks,
        total,
        page: curPage,
        pageSize,
        pages: Math.ceil(total / pageSize),
        hasMore: curPage * pageSize < total,
      });
    },
  },

  // 获取独立任务列表 - 符合实际API路径
  {
    url: RegExp('/projectmanage/inde-task/page/\\d+/\\d+'),
    method: 'post',
    response: (req) => {
      // 解析URL参数
      const urlParts = req.url.split('/');
      const pageSize = parseInt(urlParts[urlParts.length - 2], 10);
      const pageNum = parseInt(urlParts[urlParts.length - 1], 10);

      // 解析请求体参数
      const { queryFlag, indeTaskName } = req.body || {};

      console.log('Mock接口 - 获取独立任务列表:', {
        url: req.url,
        pageSize,
        pageNum,
        queryFlag,
        indeTaskName,
      });

      // 使用已定义的独立任务数据

      // 根据任务名称关键词筛选
      let filteredTasks = [...INDEPENDENT_TASKS_DATA];
      if (indeTaskName && indeTaskName.trim() !== '') {
        const searchKeyword = indeTaskName.trim().toLowerCase();
        filteredTasks = filteredTasks.filter(
          (task) =>
            task.indeTaskName?.toLowerCase().includes(searchKeyword) ||
            task.responsibleName?.toLowerCase().includes(searchKeyword)
        );
      }

      // 根据 queryFlag (标签页类型) 筛选任务
      if (queryFlag && queryFlag !== TAB_TYPE.ALL) {
        const currentUserId = CURRENT_USER_ID;

        filteredTasks = filteredTasks.filter((task) => {
          switch (queryFlag) {
            case TAB_TYPE.RESPONSIBLE:
              // 我负责的：当前用户是责任人
              return task.indeTaskResponsible === currentUserId;
            case TAB_TYPE.PARTICIPANT:
              // 我参与的：当前用户在参与者列表中（这里简化处理）
              return task.indeTaskResponsible !== currentUserId;
            case TAB_TYPE.ASSIGNED:
              // 我指派的：当前用户是创建者
              return task.createUser === currentUserId;
            case TAB_TYPE.TODO:
              // 我待办的：当前用户是责任人且任务未完成
              return (
                task.indeTaskResponsible === currentUserId &&
                task.indeTaskStatus !== INDEPENDENT_TASK_STATUS.CLOSED
              );
            default:
              return true;
          }
        });
      }

      // 分页处理
      const total = filteredTasks.length;
      const startIndex = (pageNum - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedTasks = filteredTasks.slice(startIndex, endIndex);

      console.log('独立任务数据返回:', {
        total,
        currentPage: pageNum,
        pageSize,
        queryFlag,
        indeTaskName,
        tasksCount: paginatedTasks.length,
      });

      return success({
        list: paginatedTasks,
        total,
        page: pageNum,
        pageSize,
        pages: Math.ceil(total / pageSize),
      });
    },
  },

  // ==================== 任务标签相关接口 ====================

  // 获取任务标签列表
  {
    url: '/projectmanage/config-task-tag/list',
    method: 'post',
    response: ({ body }) => {
      console.log('获取任务标签列表请求参数:', body);

      let filteredTags = [...MOCK_TASK_TAGS];

      // 根据查询参数过滤
      if (body.taskTagName) {
        filteredTags = filteredTags.filter((tag) =>
          tag.taskTagName.includes(body.taskTagName)
        );
      }

      if (body.taskTagStatus !== undefined) {
        filteredTags = filteredTags.filter(
          (tag) => tag.taskTagStatus === body.taskTagStatus
        );
      }

      if (body.tagGroupFlag !== undefined) {
        filteredTags = filteredTags.filter(
          (tag) => tag.tagGroupFlag === body.tagGroupFlag
        );
      }

      console.log('任务标签列表返回:', filteredTags);

      return success(filteredTags);
    },
  },

  // 获取任务标签分页列表
  {
    url: /\/projectmanage\/config-task-tag\/page\/(\d+)\/(\d+)/,
    method: 'post',
    response: ({ body, url }) => {
      const matches = url.match(
        /\/projectmanage\/config-task-tag\/page\/(\d+)\/(\d+)/
      );
      const pageSize = parseInt(matches[1]);
      const pageNum = parseInt(matches[2]);

      console.log('获取任务标签分页列表请求参数:', { body, pageSize, pageNum });

      let filteredTags = [...MOCK_TASK_TAGS];

      // 根据查询参数过滤
      if (body.taskTagName) {
        filteredTags = filteredTags.filter((tag) =>
          tag.taskTagName.includes(body.taskTagName)
        );
      }

      if (body.taskTagStatus !== undefined) {
        filteredTags = filteredTags.filter(
          (tag) => tag.taskTagStatus === body.taskTagStatus
        );
      }

      const total = filteredTags.length;
      const startIndex = (pageNum - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedTags = filteredTags.slice(startIndex, endIndex);

      console.log('任务标签分页列表返回:', {
        list: paginatedTags,
        total,
        page: pageNum,
        pageSize,
        pages: Math.ceil(total / pageSize),
      });

      return success({
        list: paginatedTags,
        total,
        page: pageNum,
        pageSize,
        pages: Math.ceil(total / pageSize),
      });
    },
  },

  // 新增任务标签
  {
    url: '/projectmanage/config-task-tag/add',
    method: 'post',
    response: ({ body }) => {
      console.log('新增任务标签请求参数:', body);

      // 参数验证
      if (!body.taskTagName) {
        return fail('标签名称不能为空');
      }
      if (!body.taskTagColor) {
        return fail('标签颜色不能为空');
      }
      if (body.tagGroupFlag === undefined) {
        return fail('标签类型不能为空');
      }

      // 检查名称是否重复
      const existingTag = MOCK_TASK_TAGS.find(
        (tag) => tag.taskTagName === body.taskTagName
      );
      if (existingTag) {
        return fail('标签名称已存在');
      }

      // 如果是子标签，检查父标签是否存在
      if (body.tagGroupFlag === 0 && body.parentTaskTagId) {
        const parentTag = MOCK_TASK_TAGS.find(
          (tag) =>
            tag.taskTagId === body.parentTaskTagId && tag.tagGroupFlag === 1
        );
        if (!parentTag) {
          return fail('所属标签组不存在');
        }
      }

      // 生成新的标签ID
      const newId = Math.max(...MOCK_TASK_TAGS.map((tag) => tag.taskTagId)) + 1;
      const currentTime = new Date()
        .toISOString()
        .replace('T', ' ')
        .substring(0, 19);

      const newTag = {
        taskTagId: newId,
        parentTaskTagId: body.parentTaskTagId || null,
        tagGroupFlag: body.tagGroupFlag,
        taskTagName: body.taskTagName,
        taskTagDesc: body.taskTagDesc || '',
        taskTagColor: body.taskTagColor,
        taskTagStatus:
          body.taskTagStatus !== undefined ? body.taskTagStatus : 1,
        sortNum: MOCK_TASK_TAGS.length + 1,
        createTime: currentTime,
        updateTime: currentTime,
        createUser: 1,
        updateUser: 1,
        deleted: 0,
        remark: body.remark || '',
        tenantId: 1,
      };

      MOCK_TASK_TAGS.push(newTag);

      console.log('新增任务标签成功:', newTag);
      return success(newTag, '新增成功');
    },
  },

  // 编辑任务标签
  {
    url: '/projectmanage/config-task-tag/edit',
    method: 'post',
    response: ({ body }) => {
      console.log('编辑任务标签请求参数:', body);

      if (!body.taskTagId) {
        return fail('任务标签ID不能为空');
      }
      if (!body.taskTagName) {
        return fail('标签名称不能为空');
      }
      if (!body.taskTagColor) {
        return fail('标签颜色不能为空');
      }

      const index = MOCK_TASK_TAGS.findIndex(
        (tag) => tag.taskTagId === body.taskTagId
      );

      if (index === -1) {
        return fail('任务标签不存在');
      }

      // 检查名称是否与其他标签重复
      const existingTag = MOCK_TASK_TAGS.find(
        (tag) =>
          tag.taskTagName === body.taskTagName &&
          tag.taskTagId !== body.taskTagId
      );
      if (existingTag) {
        return fail('标签名称已存在');
      }

      // 更新标签信息
      const currentTime = new Date()
        .toISOString()
        .replace('T', ' ')
        .substring(0, 19);
      const updatedTag = {
        ...MOCK_TASK_TAGS[index],
        taskTagName: body.taskTagName,
        taskTagDesc: body.taskTagDesc || '',
        taskTagColor: body.taskTagColor,
        taskTagStatus:
          body.taskTagStatus !== undefined
            ? body.taskTagStatus
            : MOCK_TASK_TAGS[index].taskTagStatus,
        remark: body.remark || '',
        updateTime: currentTime,
        updateUser: 1,
      };

      MOCK_TASK_TAGS[index] = updatedTag;

      console.log('编辑任务标签成功:', updatedTag);
      return success(updatedTag, '编辑成功');
    },
  },

  // 删除任务标签
  {
    url: '/projectmanage/config-task-tag/delete',
    method: 'post',
    response: ({ body }) => {
      console.log('删除任务标签请求参数:', body);

      if (!body.taskTagId) {
        return fail('任务标签ID不能为空');
      }

      const index = MOCK_TASK_TAGS.findIndex(
        (tag) => tag.taskTagId === body.taskTagId
      );

      if (index === -1) {
        return fail('任务标签不存在');
      }

      // 检查是否有子标签
      const hasChildren = MOCK_TASK_TAGS.some(
        (tag) => tag.parentTaskTagId === body.taskTagId
      );

      if (hasChildren) {
        return fail('该标签组下还有子标签，请先删除子标签');
      }

      MOCK_TASK_TAGS.splice(index, 1);

      console.log('删除任务标签成功');
      return success(null, '删除成功');
    },
  },

  // 根据标签获取项目任务列表（任务标签页面专用）
  {
    url: '/projectmanage/project-task/list',
    method: 'post',
    response: ({ body }) => {
      console.log('根据标签获取项目任务列表请求参数:', body);

      let filteredTasks = [...PROJECT_TASKS_DATA];

      // 根据标签ID筛选
      if (body.tagIds && body.tagIds.length > 0) {
        filteredTasks = filteredTasks.filter(
          (task) =>
            task.tags &&
            task.tags.some((tag) => body.tagIds.includes(tag.taskTagId))
        );
      }

      // 根据关键词搜索
      if (body.keyword) {
        filteredTasks = filteredTasks.filter(
          (task) =>
            task.projTaskName.includes(body.keyword) ||
            (task.remark && task.remark.includes(body.keyword))
        );
      }

      // 根据任务状态筛选
      if (body.taskStatus !== undefined) {
        filteredTasks = filteredTasks.filter(
          (task) => task.projTaskStatus === body.taskStatus
        );
      }

      // 分页处理
      const page = body.page || 1;
      const pageSize = body.pageSize || 20;
      const total = filteredTasks.length;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedTasks = filteredTasks.slice(startIndex, endIndex);

      console.log('项目任务列表返回:', {
        list: paginatedTasks,
        total,
        page,
        pageSize,
        pages: Math.ceil(total / pageSize),
      });

      return success({
        list: paginatedTasks,
        total,
        page,
        pageSize,
        pages: Math.ceil(total / pageSize),
      });
    },
  },

  // 根据标签获取独立任务列表（任务标签页面专用）
  {
    url: '/projectmanage/inde-task/list',
    method: 'post',
    response: ({ body }) => {
      console.log('根据标签获取独立任务列表请求参数:', body);

      // 独立任务使用相同的mock数据，但可以根据需要调整
      let filteredTasks = [...MOCK_TASKS_WITH_TAGS];

      // 根据标签ID筛选
      if (body.tagIds && body.tagIds.length > 0) {
        filteredTasks = filteredTasks.filter(
          (task) =>
            task.tags &&
            task.tags.some((tag) => body.tagIds.includes(tag.taskTagId))
        );
      }

      // 根据关键词搜索
      if (body.keyword) {
        filteredTasks = filteredTasks.filter(
          (task) =>
            task.taskName.includes(body.keyword) ||
            (task.taskDesc && task.taskDesc.includes(body.keyword))
        );
      }

      // 分页处理
      const page = body.page || 1;
      const pageSize = body.pageSize || 20;
      const total = filteredTasks.length;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedTasks = filteredTasks.slice(startIndex, endIndex);

      console.log('独立任务列表返回:', {
        list: paginatedTasks,
        total,
        page,
        pageSize,
        pages: Math.ceil(total / pageSize),
      });

      return success({
        list: paginatedTasks,
        total,
        page,
        pageSize,
        pages: Math.ceil(total / pageSize),
      });
    },
  },
];
