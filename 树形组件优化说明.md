# 任务列表页面树形组件优化说明

## 优化概述

根据需求，对任务列表页面左侧树形组件的勾选功能和右侧表格的联动筛选机制进行了全面优化。

## 主要优化内容

### 1. 树形结构数据源优化

**优化前：**
- 根节点名称为"全部项目"
- 静态的项目数据结构
- 没有考虑用户权限

**优化后：**
- 根节点统一命名为"我的项目"
- 动态加载当前用户负责或参与的所有项目
- 支持项目集管理成员查看项目集
- 无论项目中是否包含当前用户的具体任务，都在树形结构中展示

### 2. 勾选联动筛选逻辑优化

**核心逻辑：**
1. **根节点勾选**：当用户勾选"我的项目"时，右侧表格展示当前用户在所有项目中的项目任务
2. **具体项目勾选**：当用户勾选某个具体项目时，右侧表格仅展示该项目的项目任务清单
3. **多选支持**：用户可以同时勾选多个项目，右侧表格展示所有被勾选项目的任务
4. **项目集处理**：勾选项目集时，自动包含其下所有子项目的任务

### 3. 技术实现优化

#### 3.1 优化的 `getSelectedProjectIds` 函数

```typescript
function getSelectedProjectIds(nodes: any[]): string[] {
  const ids: string[] = [];
  let isRootSelected = false;

  function traverse(node: any) {
    // 检查是否选中了根节点"我的项目"
    if (node.checked && node.value === 'my_projects') {
      isRootSelected = true;
      return;
    }
    
    // 如果是具体项目节点并且被选中
    if (node.checked && node.value && node.value !== 'my_projects') {
      // 只收集具体的项目ID，排除项目集
      if (!node.children || node.children.length === 0) {
        ids.push(node.value);
      } else {
        // 如果是项目集，收集其下的所有子项目
        if (node.children) {
          node.children.forEach((child: any) => {
            if (child.value) {
              ids.push(child.value);
            }
          });
        }
      }
    }
    
    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach((child: any) => traverse(child));
    }
  }

  // 遍历所有节点
  if (Array.isArray(nodes)) {
    nodes.forEach((node) => traverse(node));
  }

  // 如果选中了根节点，返回空数组表示显示所有项目
  if (isRootSelected) {
    return [];
  }

  return ids;
}
```

#### 3.2 优化的 `loadTasks` 筛选逻辑

```typescript
const filteredTasks = mockTasks.filter((task) => {
  // 项目筛选逻辑
  if (selectedProjects.value.length > 0) {
    // 如果有选中的项目，只显示这些项目的任务
    if (!selectedProjects.value.includes(task.projectId as string)) {
      return false;
    }
  }
  // 如果没有选中任何项目（包括选中根节点），显示当前用户相关的所有任务

  // 根据activeTab进行筛选 - 确保只显示当前用户相关的任务
  if (activeTab.value === 'responsible') {
    // 我负责的任务
    if (task.responsibleUser !== '张三') {
      return false;
    }
  } else if (activeTab.value === 'participant') {
    // 我参与的任务
    if (!task.participants?.includes('张三')) {
      return false;
    }
  } else if (activeTab.value === 'assigned') {
    // 我分配的任务
    if (task.createdBy !== '张三') {
      return false;
    }
  } else if (activeTab.value === 'all') {
    // 全部任务 - 显示当前用户负责、参与或分配的所有任务
    if (
      task.responsibleUser !== '张三' &&
      !task.participants?.includes('张三') &&
      task.createdBy !== '张三'
    ) {
      return false;
    }
  }

  // 根据搜索关键词筛选
  if (keyword.value && !task.name.includes(keyword.value)) {
    return false;
  }

  return true;
});
```

#### 3.3 新增的 `sortTasksByProjectPlan` 函数

确保右侧表格中的任务排序与项目计划中的任务排序保持一致：

```typescript
function sortTasksByProjectPlan(tasks: TaskItem[]): TaskItem[] {
  // 按项目ID分组
  const tasksByProject = tasks.reduce((acc, task) => {
    const projectId = task.projectId as string;
    if (!acc[projectId]) {
      acc[projectId] = [];
    }
    acc[projectId].push(task);
    return acc;
  }, {} as Record<string, TaskItem[]>);

  // 对每个项目内的任务按照层级和索引排序
  Object.keys(tasksByProject).forEach(projectId => {
    tasksByProject[projectId].sort((a, b) => {
      // 首先按层级排序
      if (a.level !== b.level) {
        return a.level - b.level;
      }
      // 同层级按索引排序
      return (a.index || 0) - (b.index || 0);
    });
  });

  // 合并所有项目的任务，保持项目间的顺序
  const sortedTasks: TaskItem[] = [];
  const projectOrder = ['project_a', 'project_b', 'project_c1', 'project_c2'];
  
  projectOrder.forEach(projectId => {
    if (tasksByProject[projectId]) {
      sortedTasks.push(...tasksByProject[projectId]);
    }
  });

  return sortedTasks;
}
```

#### 3.4 优化的 `updateTreeWithUserProjects` 函数

根据用户权限动态构建树形结构：

```typescript
function updateTreeWithUserProjects() {
  // 模拟当前用户相关的项目数据
  const userProjects = [
    { id: 'project_a', name: '项目A', isResponsible: true, isParticipant: false },
    { id: 'project_b', name: '项目B', isResponsible: false, isParticipant: true },
    { id: 'project_c1', name: '子项目C1', isResponsible: true, isParticipant: true },
    { id: 'project_c2', name: '子项目C2', isResponsible: false, isParticipant: true },
  ];

  // 构建树形结构
  treeList.value = [
    {
      label: '我的项目',
      value: 'my_projects',
      checked: false,
      children: [
        { label: '项目A', value: 'project_a', checked: false },
        { label: '项目B', value: 'project_b', checked: false },
        {
          label: '项目集C',
          value: 'project_set_c',
          checked: false,
          children: [
            { label: '子项目C1', value: 'project_c1', checked: false },
            { label: '子项目C2', value: 'project_c2', checked: false },
          ],
        },
      ],
    },
  ];
}
```

## 功能特点

### 用户体验优化
1. **直观的树形结构**：根节点"我的项目"清晰表达了数据范围
2. **灵活的筛选方式**：支持根节点全选和具体项目多选
3. **实时联动更新**：勾选状态变化时立即更新表格内容
4. **保持排序一致性**：表格中的任务排序与项目计划保持一致

### 技术优势
1. **智能筛选逻辑**：根据勾选状态智能判断筛选范围
2. **项目集支持**：正确处理项目集的层级关系
3. **性能优化**：避免不必要的数据加载和渲染
4. **扩展性良好**：易于添加新的筛选条件和排序规则

## 使用说明

1. **查看所有任务**：勾选"我的项目"根节点
2. **查看特定项目任务**：勾选具体的项目节点
3. **查看多个项目任务**：同时勾选多个项目节点
4. **查看项目集任务**：勾选项目集节点，自动包含所有子项目

## 后续扩展建议

1. **权限细化**：根据用户角色显示不同的项目权限标识
2. **任务统计**：在树节点上显示任务数量统计
3. **快速筛选**：添加快捷筛选按钮（如"我负责的"、"即将到期的"等）
4. **记忆功能**：记住用户的筛选偏好设置
