---
description: 
globs:
alwaysApply: true
---
# BTIT前端项目 AI编程工具开发文档

## 项目概览

### 技术栈

- **前端框架**: Vue 3 + TypeScript + Composition API
- **UI组件库**: Element Plus + 自定义组件库
- **状态管理**: Pinia + pinia-plugin-persistedstate
- **路由管理**: Vue Router 4
- **构建工具**: Vite 6.2.0
- **包管理器**: pnpm (>=10.8.1)
- **样式预处理**: SCSS
- **架构模式**: 多页应用(MPA) + v-cairn框架
- **网络请求**: Axios + 自定义request封装

### 核心第三方包

- **流程图**: bpmn-js (^18.4.0) + bpmn-js-properties-panel
- **图形编辑**: @antv/x6 (^2.18.1)
- **甘特图**: dhtmlx-gantt (^9.0.5)
- **图标**: @element-plus/icons-vue
- **自定义框架**: v-cairn (^1.0.12)
- **事件总线**: mitt

### 项目结构

```text
src/
├── app-config.ts           # 应用配置
├── components/             # 组件库
│   ├── biz/               # 业务组件
│   │   └── file_upload/   # 文件上传组件
│   ├── non_biz/           # 通用组件
│   │   ├── layout/        # 布局组件
│   │   ├── bt_select/     # 自定义选择器
│   │   ├── bt_tree/       # 自定义树组件
│   │   ├── bt_transfer/   # 穿梭框组件
│   │   └── ...           # 其他通用组件
│   └── directives/        # 自定义指令
├── libs/                  # 核心库
│   ├── btit/             # 项目核心库
│   └── v-carin/          # v-cairn框架集成
├── pages/                # 页面模块(多页应用)
│   ├── main/             # 主页面模块
│   ├── projects/         # 项目管理模块
│   ├── ea/               # 企业架构模块
│   ├── workflows/        # 工作流模块
│   └── ...              # 其他页面模块
├── service_url/          # API接口定义
├── utils/                # 工具函数库
└── assets/               # 静态资源
```

## 开发原则和优先级

### 第一优先级：复用现有代码

1. **页面复用**: 优先查找 `src/pages/` 下相似功能的页面进行复用
2. **组件复用**: 优先使用 `src/components/` 下的现有组件
3. **工具函数复用**: 优先使用 `src/utils/` 下的工具函数
4. **样式复用**: 复用 `src/assets/styles/` 下的样式变量和类

### 第二优先级：使用项目内第三方包

- Element Plus组件优先
- 使用已集成的专业库：bpmn-js、@antv/x6、dhtmlx-gantt
- 利用v-cairn框架的能力

### 第三优先级：引入新的开源包

- 评估包的必要性和维护状态
- 确保与现有技术栈兼容
- 使用pnpm安装新依赖

### 最后选择：自主开发

- 仅在无法找到合适解决方案时自主开发
- 遵循项目现有的架构模式

## 最新开发模式指导（基于登录页面实现）

### 标准页面结构模式

基于 `src/pages/main/views/login/` 的实现，项目采用以下标准结构：

```text
feature_name/
├── FeatureName.vue      # 主视图组件
├── FeatureNameStore.ts  # Pinia状态管理
├── FeatureNameService.ts # API服务层
└── types.ts            # TypeScript类型定义
```

### Vue 3 组件开发规范

#### 1. 组件结构标准

```vue
<script setup lang="ts">
// 1. 导入依赖 - 按类型分组
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useFeatureStore } from './FeatureStore'
import type { PropsType } from './types'

// 2. Store使用
const store = useFeatureStore()
const { data, isLoading } = storeToRefs(store)

// 3. Props定义（如果需要）
const props = withDefaults(defineProps<PropsType>(), {
  placeholder: '请输入'
})

// 4. 响应式数据
const formData = ref({
  field1: '',
  field2: ''
})

// 5. 计算属性
const isValid = computed(() => {
  return formData.value.field1 && formData.value.field2
})

// 6. 方法定义
function handleSubmit() {
  store.submitData(formData.value)
}

// 7. 生命周期
onMounted(() => {
  store.loadInitialData()
})
</script>

<template>
  <div
    class="feature-container"
    v-loading="isLoading"
    element-loading-text="加载中..."
  >
    <!-- 模板内容 -->
  </div>
</template>

<style scoped lang="scss">
.feature-container {
  // 使用CSS变量
  color: var(--color-primary);

  // 深度选择器覆盖Element Plus样式
  :deep(.el-form-item__label) {
    font-weight: bold;
    margin-bottom: 7px;
  }
}
</style>
```

#### 2. Pinia Store开发规范

```typescript
// FeatureStore.ts
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { UserInfo, FormData } from './types'
import * as featureService from './FeatureService'
import { gotoPrePage } from '@/utils/routeUtil'

export const useFeatureStore = defineStore('featureStore', () => {
  // 1. 响应式状态定义
  const formData = ref<FormData>({
    field1: '',
    field2: '',
    rememberMe: false
  })
  const isLoading = ref(false)
  const userInfo = ref<UserInfo>({} as UserInfo)

  // 2. 计算属性
  const isValid = computed(() => {
    return !!formData.value.field1 && !!formData.value.field2
  })

  // 3. 异步方法
  async function submitData() {
    isLoading.value = true
    try {
      const result = await featureService.submitForm(formData.value)
      if (result?.success) {
        // 处理成功逻辑
        setTimeout(gotoPrePage, 0)
      }
      return result
    } finally {
      isLoading.value = false
    }
  }

  // 4. 同步方法
  function resetForm() {
    formData.value = {
      field1: '',
      field2: '',
      rememberMe: false
    }
  }

  return {
    formData, isLoading, userInfo, isValid,
    submitData, resetForm
  }
}, {
  // 5. 持久化配置 - 只持久化必要数据
  persist: {
    pick: ['userInfo'] // 只持久化用户信息，不持久化表单数据和加载状态
  }
})
```

#### 3. Service层开发规范

```typescript
// FeatureService.ts
import { send } from '@/libs/request'
import { apiUrl1, apiUrl2 } from '@/service_url/feature'
import type { FormData, ApiResponse } from './types'

// 1. 基础API调用
export async function submitForm(data: FormData): Promise<ApiResponse> {
  return send({
    method: 'post',
    url: apiUrl1,
    data
  })
}

// 2. GET请求
export async function getFeatureInfo(id: string): Promise<ApiResponse> {
  return send({
    url: `${apiUrl2}/${id}`
  })
}

// 3. 带参数的请求
export async function getFeatureList(params: {
  pageSize: number
  pageNum: number
  keyword?: string
}): Promise<ApiResponse> {
  return send({
    url: apiUrl2,
    params
  })
}
```

#### 4. TypeScript类型定义规范

```typescript
// types.ts
export interface FormData {
  field1: string
  field2: string
  rememberMe: boolean
}

export interface UserInfo {
  userId: number
  username: string
  email?: string
  roles: string[]
  permissions: string[]
}

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success?: boolean
}

// 组件Props类型
export interface PropsType {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
}

// 事件类型
export interface EventsType {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}
```

## 代码复用指南

### 现有组件库使用

#### 通用组件 (src/components/non_biz/)

```typescript
// 自定义选择器 - 支持列表和树形数据
import BtSelect from '@/components/non_biz/bt_select/BtSelect.vue'

// 使用示例
<BtSelect
  v-model="selectedValue"
  :list="optionList"
  type="list"
  :multiple="true"
  placeholder="请选择"
  :show-search="true"
/>

// 自定义树组件 - 支持右键菜单、拖拽等
import BtTree from '@/components/non_biz/bt_tree/BtTree.vue'

// 自定义穿梭框
import BtTransfer from '@/components/non_biz/bt_transfer/BtTransfer.vue'

// 布局组件
import Layout from '@/components/non_biz/layout/Layout.vue'
```

#### 业务组件 (src/components/biz/)

```typescript
// 文件上传组件
import FileUpload from '@/components/biz/file_upload/FileUpload.vue'

// 使用示例
<FileUpload
  v-model="fileList"
  :multiple="true"
  @change="handleFileChange"
>
  <el-button type="primary">上传文件</el-button>
</FileUpload>
```

### 工具函数库使用

#### 日期工具 (src/utils/DateUtil.ts)

```typescript
import { getTimestamp } from '@/utils/DateUtil'

// 生成时间戳文件名: YYYYMMDD_HHmmss
const timestamp = getTimestamp()
```

#### 权限工具 (src/utils/authUtil.js)

```typescript
import authUtil from '@/utils/authUtil'

// 检查权限
if (authUtil.hasPermi('system:user:add')) {
  // 有权限执行操作
}

// 检查角色
if (authUtil.hasRole('admin')) {
  // 有角色权限
}
```

#### 树形数据处理 (src/utils/toolUtil.ts)

```typescript
import { handleTree } from '@/utils/toolUtil'

// 将扁平数据转换为树形结构
const treeData = handleTree(flatData, 'id', 'parentId', 'children')
```

#### Store工具 (src/utils/storeUtil.ts)

```typescript
import { getStore } from '@/utils/storeUtil'

// 获取跨页面共享的store
const loginStore = getStore('loginStore')
```

### 状态管理模式

#### Pinia Store定义

```typescript
// 定义Store
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useExampleStore = defineStore('exampleStore', () => {
  const data = ref([])
  const loading = ref(false)
  
  const filteredData = computed(() => {
    return data.value.filter(item => item.active)
  })
  
  function updateData(newData) {
    data.value = newData
  }
  
  return { data, loading, filteredData, updateData }
}, {
  // 持久化配置 - 仅持久化必要数据
  persist: {
    pick: ['data'] // 只持久化data字段
  }
})
```

## v-cairn框架使用规范

### 架构模式

每个功能模块需要包含：

1. **View文件** (.vue) - 负责UI展示和用户交互
2. **Controller文件** (.ts) - 负责业务逻辑处理
3. **Store文件** (.ts) - 负责状态管理(可选)
4. **Types文件** (.ts) - 负责类型定义
5. **Command文件** (.ts) - 负责API调用

### Controller示例

```typescript
import { VueCairnController } from 'v-cairn'

export class ExampleCtrl extends VueCairnController {
  constructor() {
    super()
    this.onSave = this.onSave.bind(this)
  }
  
  onSave() {
    this.fireEvent('SAVE_DATA')
  }
}
```

### Command示例

```typescript
import { BtitCommand } from '@/libs/btit/BtitCommand'

export class SaveDataCommand extends BtitCommand {
  constructor() {
    super()
    this.url = '/api/save'
    this.method = 'POST'
  }
  
  protected onSuccess(response) {
    // 处理成功响应
    console.log('保存成功', response.data)
  }
}
```

## 第三方包使用规范

### Element Plus

```typescript
// 自动导入已配置，直接使用
<el-button type="primary" @click="handleClick">
  <el-icon><Plus /></el-icon>
  新增
</el-button>

// 图标使用
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
```

### BPMN.js (工作流编辑)

```typescript
import BpmnModeler from 'bpmn-js/lib/Modeler'
import BpmnPropertiesPanelModule from 'bpmn-js-properties-panel'

const modeler = new BpmnModeler({
  container: canvasRef.value,
  additionalModules: [BpmnPropertiesPanelModule]
})
```

### AntV X6 (图形编辑)

```typescript
import { Graph } from '@antv/x6'

const graph = new Graph({
  container: containerRef.value,
  width: 800,
  height: 600
})
```

### DHTMLX Gantt (甘特图)

```typescript
import { gantt } from 'dhtmlx-gantt'

gantt.init(containerRef.value)
gantt.parse(data)
```

## 编码规范

### TypeScript类型定义

```typescript
// 接口定义
export interface UserInfo {
  userId: number
  username: string
  email?: string
}

// 组件Props类型
export type PropsType = {
  modelValue: string[]
  placeholder?: string
  multiple?: boolean
}

// 事件类型
export type EventsType = {
  (e: 'update:modelValue', value: string[]): void
  (e: 'change', value: string[]): void
}
```

### Vue 3 Composition API规范

```typescript
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { PropsType, EventsType } from './types'

// Props定义
const props = withDefaults(defineProps<PropsType>(), {
  placeholder: '请输入',
  multiple: false
})

// 事件定义
const emits = defineEmits<EventsType>()

// 响应式数据
const loading = ref(false)
const data = ref([])

// 计算属性
const filteredData = computed(() => {
  return data.value.filter(item => item.active)
})

// 生命周期
onMounted(() => {
  loadData()
})

// 方法定义
function loadData() {
  loading.value = true
  // 加载数据逻辑
}
</script>
```

### 文件命名规范

- **组件文件**: PascalCase (如: `UserList.vue`)
- **工具文件**: camelCase (如: `dateUtil.ts`)
- **类型文件**: `types.ts`
- **控制器文件**: `XxxCtrl.ts`
- **Store文件**: `XxxStore.ts`
- **Command文件**: `XxxCommand.ts`

### 样式开发规范

#### 1. CSS变量使用

项目定义了完整的CSS变量系统，必须优先使用：

```scss
// 主要颜色变量
:root {
  --color-primary: #4d75c4;        // 主色调
  --color-danger: #dd2b0e;         // 危险色
  --color-icon: #737278;           // 图标色
  --text-color-placeholder: #999999; // 占位符文字色
  --hover-bg-color: #ECECEF;       // 悬停背景色
  --border-color: #dcdfe6;         // 边框色
  --form-border-color: #C1C1C3;    // 表单边框色
  --form-hover-border-color: #333237; // 表单悬停边框色
  --table-row-hover-bg-color: #E8F3FC; // 表格行悬停色

  // 尺寸变量
  --base-margin: 8px;              // 基础间距
  --border-radius: 4px;            // 圆角
  --icon-size: 16px;               // 图标尺寸

  // 阴影变量
  --active-shadow: inset 0 0 0 1px #fff,0 0 0 1px #fff,0 0 0 3px var(--color-primary);
  --error-shadow: inset 0 0 0 1px #C1C1C3;
}
```

#### 2. 组件样式编写规范

```scss
.feature-component {
  // 1. 使用CSS变量
  color: var(--color-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);

  // 2. 状态样式
  &:hover {
    background-color: var(--hover-bg-color);
  }

  &.active {
    box-shadow: var(--active-shadow);
  }

  &.error {
    border-color: var(--color-danger);
    box-shadow: var(--error-shadow);
  }

  // 3. Element Plus样式覆盖 - 使用:deep()
  :deep(.el-form-item__label) {
    font-weight: bold;
    line-height: 17px;
    margin-bottom: 7px;
  }

  :deep(.el-input__wrapper) {
    border: 1px solid var(--form-border-color);
    box-shadow: unset;

    &:hover {
      border-color: var(--form-hover-border-color);
    }

    &.is-focus {
      box-shadow: var(--active-shadow);
    }
  }

  // 4. 响应式设计
  @media (max-width: 768px) {
    padding: var(--base-margin);
  }
}
```

#### 3. 布局样式规范

```scss
// 1. Flexbox布局类（项目已定义）
.flex {
  display: flex;

  &.flex-center {
    align-items: center;
  }

  &.flex-jc-sb {
    justify-content: space-between;
  }
}

// 2. 页面容器样式
.page-container {
  max-width: 483px;  // 参考登录页面
  margin: 0 auto;
  padding-top: 25vh; // 垂直居中
}

// 3. 表单样式
.form-container {
  .el-form-item {
    margin-bottom: 20px;
  }

  .el-button {
    width: 100%;
    height: 42px;
  }
}
```

## 网络请求和API管理

### 1. 统一请求封装

项目使用自定义的request封装，基于axios：

```typescript
// 使用统一的send方法
import { send } from '@/libs/request'

// GET请求
const data = await send({ url: '/api/users' })

// POST请求
const result = await send({
  method: 'post',
  url: '/api/users',
  data: { name: 'John', email: '<EMAIL>' }
})

// 带参数的请求
const list = await send({
  url: '/api/users',
  params: { page: 1, size: 10 }
})
```

### 2. API地址管理

所有API地址统一在 `src/service_url/` 目录管理：

```typescript
// src/service_url/feature.ts
export const featureList = '/api/feature/list'
export const featureDetail = (id: string) => `/api/feature/detail/${id}`
export const addFeature = '/api/feature/add'
export const updateFeature = '/api/feature/update'
export const deleteFeature = '/api/feature/delete'

// 分页接口
export const featurePageList = (pageSize: number, pageNum: number) =>
  `/api/feature/page/${pageSize}/${pageNum}`
```

### 3. 错误处理机制

项目内置了完整的错误处理机制：

```typescript
// app-config.ts中定义特殊错误码处理
export const SPECIAL_ERROR_CODES = {
  '401': {
    label: '未授权',
    desc: '用户未登录或登录已过期',
    handler: (item, response) => {
      // 自动跳转到登录页
      window.location.href = '/login'
    }
  },
  '403': {
    label: '权限不足',
    desc: '用户权限不足'
  }
}
```

## 路由管理规范

### 1. 路由配置标准

```typescript
// src/pages/module/router/routes.ts
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'module_home',
    component: () => import('@pages/module/views/Home.vue')
  },
  {
    path: '/feature',
    name: 'module_feature',
    component: () => import('@pages/module/views/Feature.vue'),
    meta: {
      title: '功能页面',
      isFullPage: false // 是否全页面显示
    }
  }
]

export default routes
```

### 2. 路由守卫使用

```typescript
// src/pages/module/router/index.ts
import { createRouter, createWebHashHistory } from 'vue-router'
import routes from './routes'
import { onBeforeEach } from '@/utils/routeUtil'

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 使用统一的路由守卫
router.beforeEach(onBeforeEach)

export default router
```

### 3. 路由工具函数

```typescript
// 页面跳转工具
import { gotoPrePage } from '@/utils/routeUtil'

// 登录成功后跳转到之前页面
setTimeout(gotoPrePage, 0)

// 权限检查
import { getStore } from '@/libs/pinia'
import { useLoginStore } from '@pages/main/views/login/LoginStore'

const loginStore = getStore('loginStore') || useLoginStore()
if (loginStore.isLoggedIn) {
  // 已登录逻辑
}
```

## 具体实现指导

### 查找现有功能

1. 使用IDE全局搜索相关关键词
2. 查看 `src/components/` 下的组件列表
3. 查看 `src/pages/` 下相似页面的实现
4. 查看 `src/utils/` 下的工具函数
5. 查看 `src/service_url/` 下的API定义

### 扩展现有组件

```typescript
// 继承现有组件
import BaseComponent from '@/components/non_biz/base/BaseComponent.vue'

// 在新组件中扩展功能
export default defineComponent({
  extends: BaseComponent,
  setup(props, { emit }) {
    // 扩展逻辑
  }
})
```

## 最佳实践总结

### 1. 开发流程最佳实践

1. **需求分析**: 明确功能需求，查找类似实现
2. **架构设计**: 按照标准结构组织文件（Vue + Store + Service + Types）
3. **优先复用**: 开发前先搜索现有实现，复用组件和工具函数
4. **类型安全**: 充分利用TypeScript类型检查，定义完整的类型
5. **状态管理**: 合理使用Pinia，遵循响应式数据管理原则
6. **样式规范**: 使用CSS变量，遵循项目样式规范
7. **API管理**: 统一管理API地址，使用标准的Service层
8. **错误处理**: 利用项目内置的错误处理机制

### 2. 代码质量最佳实践

```typescript
// ✅ 推荐的代码组织方式
export const useFeatureStore = defineStore('featureStore', () => {
  // 1. 状态定义 - 使用ref和computed
  const data = ref([])
  const loading = ref(false)
  const filteredData = computed(() => data.value.filter(item => item.active))

  // 2. 方法定义 - 异步方法处理loading状态
  async function loadData() {
    loading.value = true
    try {
      const result = await featureService.getData()
      data.value = result
    } finally {
      loading.value = false
    }
  }

  // 3. 返回值 - 明确导出的状态和方法
  return { data, loading, filteredData, loadData }
}, {
  // 4. 持久化配置 - 只持久化必要数据
  persist: { pick: ['data'] }
})
```

### 3. 性能优化最佳实践

1. **响应式优化**: 使用`shallowRef`处理大型对象
2. **计算属性**: 合理使用`computed`缓存计算结果
3. **组件懒加载**: 路由组件使用动态导入
4. **样式优化**: 使用CSS变量减少重复计算
5. **请求优化**: 合理使用loading状态，避免重复请求

### 4. 维护性最佳实践

1. **文件命名**: 遵循项目命名规范（PascalCase组件，camelCase工具）
2. **代码注释**: 关键业务逻辑添加注释说明
3. **类型定义**: 完整的TypeScript类型定义
4. **错误边界**: 合理的错误处理和用户提示
5. **代码分层**: 清晰的View-Store-Service分层架构

## 常见问题解决方案

### 跨页面数据共享

```typescript
// 使用getStore获取共享Store
import { getStore } from '@/libs/pinia'
import { useLoginStore } from '@pages/main/views/login/LoginStore'

const loginStore = getStore('loginStore') || useLoginStore()
```

### 权限控制

```typescript
// 使用权限工具函数
import authUtil from '@/utils/authUtil'

if (authUtil.hasPermi('system:user:add')) {
  // 有权限执行操作
}
```

### Element Plus样式覆盖

```scss
// 使用:deep()选择器
.custom-component {
  :deep(.el-input__wrapper) {
    border-color: var(--form-border-color);

    &:hover {
      border-color: var(--form-hover-border-color);
    }

    &.is-focus {
      box-shadow: var(--active-shadow);
    }
  }
}
```

### 文件上传

```vue
<template>
  <FileUpload
    v-model="fileList"
    :multiple="true"
    @change="handleFileChange"
  >
    <el-button type="primary">上传文件</el-button>
  </FileUpload>
</template>

<script setup lang="ts">
import FileUpload from '@/components/biz/file_upload/FileUpload.vue'
</script>
```

### 表单验证

```vue
<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-position="top"
  >
    <el-form-item label="用户名" prop="username">
      <el-input v-model="formData.username" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import type { FormRules } from 'element-plus'

const rules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ]
}
</script>
```

---

## 总结

此文档基于项目最新的登录页面实现，提供了完整的Vue 3 + TypeScript + Element Plus开发指导。通过遵循这些规范和最佳实践，可以确保：

1. **代码一致性**: 所有新功能都遵循统一的架构模式
2. **开发效率**: 充分复用现有组件和工具函数
3. **维护性**: 清晰的代码结构和完整的类型定义
4. **用户体验**: 统一的UI风格和交互模式
5. **项目稳定性**: 标准化的错误处理和状态管理

开发新功能时，请严格按照此文档的指导进行，确保项目的整体质量和一致性。
