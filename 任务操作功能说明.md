# 任务操作功能说明

## 功能概述

根据您的要求，我已经成功实现了任务列表页面的三种操作功能，并使用弹窗形式复用了计划列表中的新建项目任务弹窗设计：

1. **接受任务** - 用户可以通过弹窗查看任务详情并填写接受意见
2. **驳回任务** - 用户可以通过弹窗查看任务详情并填写驳回意见
3. **删除任务** - 智能删除功能，自动检测子任务并提供相应的确认信息

## 实现的修改

### 1. 事件类型定义 (EventTypes.ts)

在 `src/pages/tasks/common/EventTypes.ts` 中添加了新的事件类型：

```typescript
// 任务操作相关事件
static ACCEPT_TASK = 'ACCEPT_TASK';
static REJECT_TASK = 'REJECT_TASK';
static DELETE_TASK = 'DELETE_TASK';
```

### 2. 弹窗组件 (AcceptTask.vue & RejectTask.vue)

创建了两个新的弹窗组件，复用了计划列表中新建项目任务弹窗的设计：

**接受任务弹窗** (`src/pages/tasks/views/task_list/components/accept_task/`)

- 显示任务的基本信息（只读）
- 提供接受任务意见输入框（必填）
- 支持上传附件
- 表单验证和提交处理

**驳回任务弹窗** (`src/pages/tasks/views/task_list/components/reject_task/`)

- 显示任务的基本信息（只读）
- 提供驳回任务意见输入框（必填）
- 支持上传附件
- 表单验证和提交处理

### 3. 控制器函数 (TaskListCtrl.ts)

在 `src/pages/tasks/views/task_list/TaskListCtrl.ts` 中更新了处理函数：

- `handleAccept(row: TaskItem)` - 打开接受任务弹窗并传递任务数据
- `handleReject(row: TaskItem)` - 打开驳回任务弹窗并传递任务数据
- `handleDelete(row: TaskItem)` - 智能删除任务，参考计划列表的删除实现
- `getChildTaskIds(children: TaskItem[])` - 递归获取所有子任务ID

弹窗相关状态管理：

- 弹窗显示状态控制
- 当前操作任务数据存储
- 操作成功后的回调处理

### 4. 界面更新 (TaskList.vue)

在 `src/pages/tasks/views/task_list/TaskList.vue` 中更新了操作列的下拉菜单：

- 移除了原有的"查看详情"、"新建子任务"、"编辑任务"选项
- 添加了"接受任务"、"驳回任务"、"删除"三个操作选项

## 功能特点

### 用户体验

- **弹窗式操作**：接受和驳回任务使用弹窗形式，提供更好的用户体验
- **任务信息展示**：弹窗中显示完整的任务信息，用户可以清楚了解任务详情
- **意见填写**：必须填写操作意见，确保操作的可追溯性
- **附件支持**：支持上传相关附件，丰富操作内容
- **表单验证**：确保必填项不为空，提高数据质量
- **智能删除**：删除操作会自动检测子任务，显示相应的确认信息
- **批量处理**：删除父任务时会同时删除所有子任务

### 删除功能特色

参考计划列表的删除实现，任务删除功能具有以下特点：

1. **智能检测**：自动检测任务是否有子任务
2. **动态提示**：
   - 无子任务：显示"确定要删除该任务吗？"
   - 有子任务：显示"确定要删除该任务及其下 X 个子任务吗？"
3. **批量删除**：一次操作删除父任务及所有子任务
4. **递归处理**：支持多层级子任务的递归删除

### 技术实现

- **弹窗组件**：使用 Vue 3 + TypeScript + Element Plus 构建
- **表单验证**：使用 Element Plus 的表单验证功能
- **事件通信**：通过 VCairnEvent 发送事件到后端
- **状态管理**：使用 ref 和 computed 管理组件状态
- **文件上传**：集成项目中的 FileUpload 组件
- **删除逻辑**：参考计划列表的删除实现，支持子任务检测

### 扩展性

- 事件类型统一管理，便于后续扩展
- 函数模块化设计，易于维护
- 符合 Vue 3 + TypeScript 的最佳实践

## 访问方式

1. 启动项目：`npm run dev`
2. 访问地址：`http://localhost:5174/#/project-tasks`
3. 在任务列表的操作列中点击三点图标，即可看到三种操作选项

## 后续开发建议

1. **后端接口对接**：需要实现对应的后端接口来处理这三种操作
2. **权限控制**：可以根据用户角色和任务状态来控制操作的可见性
3. **状态更新**：操作成功后可以考虑局部更新任务状态，而不是重新加载整个列表
4. **操作日志**：可以添加操作日志记录功能

## 注意事项

- 当前实现使用的是模拟数据，实际使用时需要对接真实的后端接口
- 建议根据实际业务需求调整确认对话框的文案和样式
- 可以根据任务状态动态显示/隐藏某些操作选项
